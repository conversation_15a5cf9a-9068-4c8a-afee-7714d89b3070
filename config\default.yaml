app:
  name: "Portfolio Optimizer"
  version: "0.1.0"
  description: "A portfolio optimization and stock analysis tool"
  environment: "development"
  debug: true

api:
  host: "0.0.0.0"
  port: 8000
  debug: true
  prefix: "/api/v1"
  cors_origins: ["http://localhost:8501", "http://127.0.0.1:8501"]
  timeout: 60
  max_connections: 100

logging:
  level: "DEBUG"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/app.log"
  console: true

cache:
  type: "memory"  # Changed from cache_type to type
  redis_url: "redis://localhost:6379/0"
  expiry: 3600
  max_size: 1000

streamlit:
  host: "0.0.0.0"
  port: 8501
  theme:
    primary_color: "#1E88E5"
    background_color: "#FFFFFF"
    text_color: "#262730"
    font: "sans serif"

database:
  url: sqlite:///./data/portfolio_optimizer.db
  echo: false
  pool_size: 5
  max_overflow: 10
  pool_timeout: 30
  pool_recycle: 1800
  connect_args:
    check_same_thread: false

data_sources:
  yahoo_finance:
    enabled: true
    cache_expiry: 86400  # 24 hours in seconds
    timeout: 10  # seconds
  alpha_vantage:
    enabled: true
    rate_limit: 5  # requests per minute
    api_key: ${ALPHA_VANTAGE_API_KEY:}
    timeout: 10  # seconds
    cache_expiry: 86400  # 24 hours in seconds
  financial_modeling_prep:
    enabled: true
    rate_limit: 10  # requests per minute
    api_key: ${FINANCIAL_MODELING_PREP_API_KEY:}
    timeout: 10  # seconds
    cache_expiry: 86400  # 24 hours in seconds

portfolio:
  optimization:
    default_risk_free_rate: 0.03  # 3% annual
    default_time_period: 252  # trading days
    default_frequency: daily  # Options: daily, weekly, monthly
    constraints:
      max_weight: 0.3  # 30% maximum weight per asset
      min_weight: 0.0  # 0% minimum weight per asset
  risk:
    var_confidence_level: 0.95  # 95% confidence for Value at Risk
    stress_test_threshold: -0.05  # -5% monthly return for stress periods

technical_analysis:
  default_indicators:
    - sma
    - ema
    - rsi
    - macd
    - bollinger
  lookback_periods:
    short: 20
    medium: 50
    long: 200

security:
  jwt_secret: ${JWT_SECRET:default_insecure_secret}
  jwt_algorithm: HS256
  jwt_expiry: 86400  # 24 hours in seconds
  password_min_length: 8
  password_require_special: true
  password_require_number: true
  password_require_uppercase: true

performance:
  enable_profiling: false
  slow_query_threshold: 1.0  # seconds





