"""
API router for v1 endpoints.
"""
from fastapi import APIRouter

from app.api.v1.endpoints import analysis, auth, data, portfolio, risk, risk_test, risk_v2, rebalance, advanced_risk, fundamental, economic, sentiment, advanced_indicators

# Create API router
api_router = APIRouter()

# Include endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])
api_router.include_router(data.router, prefix="/data", tags=["data"])
api_router.include_router(analysis.router, prefix="/analysis", tags=["analysis"])
api_router.include_router(portfolio.router, prefix="/portfolio", tags=["portfolio"])
api_router.include_router(risk.router, prefix="/risk", tags=["risk"])
api_router.include_router(risk_test.router, prefix="/risk_test", tags=["risk_test"])
api_router.include_router(risk_v2.router, prefix="/risk_v2", tags=["risk_v2"])
api_router.include_router(rebalance.router, tags=["rebalance"])
api_router.include_router(advanced_risk.router, prefix="/advanced-risk", tags=["advanced-risk"])
api_router.include_router(fundamental.router, prefix="/fundamental", tags=["fundamental"])
api_router.include_router(economic.router, prefix="/economic", tags=["economic"])
api_router.include_router(sentiment.router, prefix="/sentiment", tags=["sentiment"])
api_router.include_router(advanced_indicators.router, prefix="/advanced-indicators", tags=["advanced-indicators"])
