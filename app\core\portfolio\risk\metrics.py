"""
Risk metrics calculation.
"""
from typing import Dict, List, Optional, Union

import numpy as np
import pandas as pd
from scipy import stats

from app.utils.constants import DEFAULT_RISK_FREE_RATE


class RiskMetrics:
    """
    Risk metrics calculation.
    
    This class provides methods for calculating various risk metrics
    for portfolios and individual assets.
    """
    
    def __init__(self, risk_free_rate: float = DEFAULT_RISK_FREE_RATE):
        """
        Initialize the risk metrics calculator.
        
        Args:
            risk_free_rate: Risk-free rate (default: 2%)
        """
        self.risk_free_rate = risk_free_rate
    
    def calculate_volatility(
        self,
        returns: Union[pd.Series, pd.DataFrame],
        annualize: bool = True,
        trading_days: int = 252
    ) -> Union[float, pd.Series]:
        """
        Calculate volatility (standard deviation of returns).
        
        Args:
            returns: Series or DataFrame of returns
            annualize: Whether to annualize the volatility
            trading_days: Number of trading days in a year
            
        Returns:
            Volatility (float or Series)
        """
        if isinstance(returns, pd.DataFrame):
            vol = returns.std()
        else:
            vol = returns.std()
        
        if annualize:
            vol = vol * np.sqrt(trading_days)
        
        return vol
    
    def calculate_sharpe_ratio(
        self,
        returns: Union[pd.Series, pd.DataFrame],
        annualize: bool = True,
        trading_days: int = 252
    ) -> Union[float, pd.Series]:
        """
        Calculate Sharpe ratio.
        
        Args:
            returns: Series or DataFrame of returns
            annualize: Whether to annualize the Sharpe ratio
            trading_days: Number of trading days in a year
            
        Returns:
            Sharpe ratio (float or Series)
        """
        if isinstance(returns, pd.DataFrame):
            mean_return = returns.mean()
            vol = returns.std()
        else:
            mean_return = returns.mean()
            vol = returns.std()
        
        if annualize:
            mean_return = mean_return * trading_days
            vol = vol * np.sqrt(trading_days)
        
        return (mean_return - self.risk_free_rate) / vol
    
    def calculate_sortino_ratio(
        self,
        returns: Union[pd.Series, pd.DataFrame],
        annualize: bool = True,
        trading_days: int = 252
    ) -> Union[float, pd.Series]:
        """
        Calculate Sortino ratio.
        
        Args:
            returns: Series or DataFrame of returns
            annualize: Whether to annualize the Sortino ratio
            trading_days: Number of trading days in a year
            
        Returns:
            Sortino ratio (float or Series)
        """
        if isinstance(returns, pd.DataFrame):
            mean_return = returns.mean()
            
            # Calculate downside deviation for each column
            downside_returns = returns.copy()
            for col in downside_returns.columns:
                downside_returns.loc[downside_returns[col] > 0, col] = 0
            
            downside_deviation = np.sqrt((downside_returns ** 2).mean())
        else:
            mean_return = returns.mean()
            
            # Calculate downside deviation
            downside_returns = returns.copy()
            downside_returns[downside_returns > 0] = 0
            downside_deviation = np.sqrt((downside_returns ** 2).mean())
        
        if annualize:
            mean_return = mean_return * trading_days
            downside_deviation = downside_deviation * np.sqrt(trading_days)
        
        return (mean_return - self.risk_free_rate) / downside_deviation
    
    def calculate_maximum_drawdown(
        self,
        prices: Union[pd.Series, pd.DataFrame]
    ) -> Union[float, pd.Series]:
        """
        Calculate maximum drawdown.
        
        Args:
            prices: Series or DataFrame of prices
            
        Returns:
            Maximum drawdown (float or Series)
        """
        if isinstance(prices, pd.DataFrame):
            max_drawdowns = pd.Series(index=prices.columns)
            
            for col in prices.columns:
                # Calculate the drawdown for this column
                rolling_max = prices[col].cummax()
                drawdown = (prices[col] - rolling_max) / rolling_max
                max_drawdowns[col] = drawdown.min()
            
            return max_drawdowns
        else:
            # Calculate the drawdown
            rolling_max = prices.cummax()
            drawdown = (prices - rolling_max) / rolling_max
            return drawdown.min()
    
    def calculate_var(
        self,
        returns: Union[pd.Series, pd.DataFrame],
        confidence_level: float = 0.95,
        method: str = "historical"
    ) -> Union[float, pd.Series]:
        """
        Calculate Value at Risk (VaR).
        
        Args:
            returns: Series or DataFrame of returns
            confidence_level: Confidence level for VaR
            method: Method for calculating VaR ('historical', 'gaussian')
            
        Returns:
            VaR (float or Series)
        """
        if method == "historical":
            if isinstance(returns, pd.DataFrame):
                return returns.quantile(1 - confidence_level)
            else:
                return returns.quantile(1 - confidence_level)
        elif method == "gaussian":
            if isinstance(returns, pd.DataFrame):
                mean = returns.mean()
                std = returns.std()
                return mean + stats.norm.ppf(1 - confidence_level) * std
            else:
                mean = returns.mean()
                std = returns.std()
                return mean + stats.norm.ppf(1 - confidence_level) * std
        else:
            raise ValueError(f"Unsupported method: {method}")
    
    def calculate_cvar(
        self,
        returns: Union[pd.Series, pd.DataFrame],
        confidence_level: float = 0.95
    ) -> Union[float, pd.Series]:
        """
        Calculate Conditional Value at Risk (CVaR).
        
        Args:
            returns: Series or DataFrame of returns
            confidence_level: Confidence level for CVaR
            
        Returns:
            CVaR (float or Series)
        """
        if isinstance(returns, pd.DataFrame):
            cvar = pd.Series(index=returns.columns)
            
            for col in returns.columns:
                # Calculate the VaR for this column
                var = returns[col].quantile(1 - confidence_level)
                
                # Calculate the CVaR (mean of returns below VaR)
                cvar[col] = returns[col][returns[col] <= var].mean()
            
            return cvar
        else:
            # Calculate the VaR
            var = returns.quantile(1 - confidence_level)
            
            # Calculate the CVaR (mean of returns below VaR)
            return returns[returns <= var].mean()
    
    def calculate_beta(
        self,
        returns: Union[pd.Series, pd.DataFrame],
        market_returns: pd.Series
    ) -> Union[float, pd.Series]:
        """
        Calculate beta (systematic risk).
        
        Args:
            returns: Series or DataFrame of returns
            market_returns: Series of market returns
            
        Returns:
            Beta (float or Series)
        """
        if isinstance(returns, pd.DataFrame):
            beta = pd.Series(index=returns.columns)
            
            for col in returns.columns:
                # Calculate the covariance between returns and market returns
                cov = returns[col].cov(market_returns)
                
                # Calculate the variance of market returns
                market_var = market_returns.var()
                
                # Calculate beta
                beta[col] = cov / market_var
            
            return beta
        else:
            # Calculate the covariance between returns and market returns
            cov = returns.cov(market_returns)
            
            # Calculate the variance of market returns
            market_var = market_returns.var()
            
            # Calculate beta
            return cov / market_var
    
    def calculate_alpha(
        self,
        returns: Union[pd.Series, pd.DataFrame],
        market_returns: pd.Series,
        annualize: bool = True,
        trading_days: int = 252
    ) -> Union[float, pd.Series]:
        """
        Calculate alpha (excess return).
        
        Args:
            returns: Series or DataFrame of returns
            market_returns: Series of market returns
            annualize: Whether to annualize the alpha
            trading_days: Number of trading days in a year
            
        Returns:
            Alpha (float or Series)
        """
        # Calculate beta
        beta = self.calculate_beta(returns, market_returns)
        
        if isinstance(returns, pd.DataFrame):
            # Calculate mean returns
            mean_return = returns.mean()
            mean_market_return = market_returns.mean()
            
            # Calculate alpha
            alpha = mean_return - self.risk_free_rate - beta * (mean_market_return - self.risk_free_rate)
            
            if annualize:
                alpha = alpha * trading_days
        else:
            # Calculate mean returns
            mean_return = returns.mean()
            mean_market_return = market_returns.mean()
            
            # Calculate alpha
            alpha = mean_return - self.risk_free_rate - beta * (mean_market_return - self.risk_free_rate)
            
            if annualize:
                alpha = alpha * trading_days
        
        return alpha
    
    def calculate_information_ratio(
        self,
        returns: Union[pd.Series, pd.DataFrame],
        benchmark_returns: pd.Series,
        annualize: bool = True,
        trading_days: int = 252
    ) -> Union[float, pd.Series]:
        """
        Calculate information ratio.
        
        Args:
            returns: Series or DataFrame of returns
            benchmark_returns: Series of benchmark returns
            annualize: Whether to annualize the information ratio
            trading_days: Number of trading days in a year
            
        Returns:
            Information ratio (float or Series)
        """
        if isinstance(returns, pd.DataFrame):
            # Calculate active returns
            active_returns = pd.DataFrame()
            for col in returns.columns:
                active_returns[col] = returns[col] - benchmark_returns
            
            # Calculate mean active returns
            mean_active_return = active_returns.mean()
            
            # Calculate tracking error
            tracking_error = active_returns.std()
            
            if annualize:
                mean_active_return = mean_active_return * trading_days
                tracking_error = tracking_error * np.sqrt(trading_days)
            
            # Calculate information ratio
            return mean_active_return / tracking_error
        else:
            # Calculate active returns
            active_returns = returns - benchmark_returns
            
            # Calculate mean active return
            mean_active_return = active_returns.mean()
            
            # Calculate tracking error
            tracking_error = active_returns.std()
            
            if annualize:
                mean_active_return = mean_active_return * trading_days
                tracking_error = tracking_error * np.sqrt(trading_days)
            
            # Calculate information ratio
            return mean_active_return / tracking_error
    
    def calculate_all_metrics(
        self,
        returns: Union[pd.Series, pd.DataFrame],
        prices: Optional[Union[pd.Series, pd.DataFrame]] = None,
        market_returns: Optional[pd.Series] = None,
        benchmark_returns: Optional[pd.Series] = None,
        annualize: bool = True,
        trading_days: int = 252
    ) -> Dict[str, Union[float, pd.Series]]:
        """
        Calculate all risk metrics.
        
        Args:
            returns: Series or DataFrame of returns
            prices: Series or DataFrame of prices
            market_returns: Series of market returns
            benchmark_returns: Series of benchmark returns
            annualize: Whether to annualize the metrics
            trading_days: Number of trading days in a year
            
        Returns:
            Dictionary with all risk metrics
        """
        metrics = {}
        
        # Calculate basic metrics
        metrics["volatility"] = self.calculate_volatility(returns, annualize, trading_days)
        metrics["sharpe_ratio"] = self.calculate_sharpe_ratio(returns, annualize, trading_days)
        metrics["sortino_ratio"] = self.calculate_sortino_ratio(returns, annualize, trading_days)
        metrics["var_95"] = self.calculate_var(returns, 0.95, "historical")
        metrics["cvar_95"] = self.calculate_cvar(returns, 0.95)
        
        # Calculate maximum drawdown if prices are provided
        if prices is not None:
            metrics["max_drawdown"] = self.calculate_maximum_drawdown(prices)
        
        # Calculate market-related metrics if market returns are provided
        if market_returns is not None:
            metrics["beta"] = self.calculate_beta(returns, market_returns)
            metrics["alpha"] = self.calculate_alpha(returns, market_returns, annualize, trading_days)
        
        # Calculate benchmark-related metrics if benchmark returns are provided
        if benchmark_returns is not None:
            metrics["information_ratio"] = self.calculate_information_ratio(
                returns, benchmark_returns, annualize, trading_days
            )
        
        return metrics


# Create a global risk metrics instance
risk_metrics = RiskMetrics()
