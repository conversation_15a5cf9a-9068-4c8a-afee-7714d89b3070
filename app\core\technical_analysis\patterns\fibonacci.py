"""
Fibonacci retracement and extension tools.
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union
from scipy.signal import find_peaks

from app.utils.logging import logger


class FibonacciTools:
    """
    Fibonacci retracement and extension tools.
    
    This class provides methods for calculating Fibonacci retracement and extension levels.
    """
    
    def __init__(self):
        """Initialize the Fibonacci tools."""
        # Standard Fibonacci ratios
        self.retracement_levels = [0.0, 0.236, 0.382, 0.5, 0.618, 0.786, 1.0]
        self.extension_levels = [0.0, 0.382, 0.618, 1.0, 1.382, 1.618, 2.0, 2.618]
    
    def calculate_retracement_levels(
        self,
        data: pd.DataFrame,
        start_idx: Optional[int] = None,
        end_idx: Optional[int] = None,
        price_column: str = 'close',
        trend: str = 'auto',
        levels: Optional[List[float]] = None
    ) -> Dict[float, float]:
        """
        Calculate Fibonacci retracement levels.
        
        Args:
            data: DataFrame with price data
            start_idx: Start index for the trend (if None, uses first point)
            end_idx: End index for the trend (if None, uses last point)
            price_column: Column name for price data
            trend: Trend direction ('up', 'down', or 'auto')
            levels: List of Fibonacci levels to calculate (if None, uses default levels)
            
        Returns:
            Dictionary mapping Fibonacci levels to price levels
        """
        try:
            # Get price data
            if price_column in data.columns:
                price = data[price_column]
            else:
                # Try with capitalized column name
                capitalized_column = price_column.capitalize()
                if capitalized_column in data.columns:
                    price = data[capitalized_column]
                else:
                    logger.error(f"Price column '{price_column}' not found in data")
                    return {}
            
            # Set default indices if not provided
            if start_idx is None:
                start_idx = 0
            if end_idx is None:
                end_idx = len(price) - 1
            
            # Get start and end prices
            start_price = price.iloc[start_idx]
            end_price = price.iloc[end_idx]
            
            # Determine trend direction
            if trend == 'auto':
                trend = 'up' if end_price > start_price else 'down'
            
            # Set levels if not provided
            if levels is None:
                levels = self.retracement_levels
            
            # Calculate retracement levels
            if trend == 'up':
                # Uptrend: start_price < end_price
                price_range = end_price - start_price
                retracement_levels = {
                    level: end_price - level * price_range
                    for level in levels
                }
            else:
                # Downtrend: start_price > end_price
                price_range = start_price - end_price
                retracement_levels = {
                    level: end_price + level * price_range
                    for level in levels
                }
            
            return retracement_levels
        except Exception as e:
            logger.error(f"Error calculating Fibonacci retracement levels: {str(e)}")
            return {}
    
    def calculate_extension_levels(
        self,
        data: pd.DataFrame,
        start_idx: Optional[int] = None,
        end_idx: Optional[int] = None,
        retracement_idx: Optional[int] = None,
        price_column: str = 'close',
        trend: str = 'auto',
        levels: Optional[List[float]] = None
    ) -> Dict[float, float]:
        """
        Calculate Fibonacci extension levels.
        
        Args:
            data: DataFrame with price data
            start_idx: Start index for the trend (if None, uses first point)
            end_idx: End index for the trend (if None, uses last point)
            retracement_idx: Index of the retracement point (if None, uses midpoint)
            price_column: Column name for price data
            trend: Trend direction ('up', 'down', or 'auto')
            levels: List of Fibonacci levels to calculate (if None, uses default levels)
            
        Returns:
            Dictionary mapping Fibonacci levels to price levels
        """
        try:
            # Get price data
            if price_column in data.columns:
                price = data[price_column]
            else:
                # Try with capitalized column name
                capitalized_column = price_column.capitalize()
                if capitalized_column in data.columns:
                    price = data[capitalized_column]
                else:
                    logger.error(f"Price column '{price_column}' not found in data")
                    return {}
            
            # Set default indices if not provided
            if start_idx is None:
                start_idx = 0
            if end_idx is None:
                end_idx = len(price) - 1
            if retracement_idx is None:
                # Use midpoint between start and end
                retracement_idx = (start_idx + end_idx) // 2
            
            # Get prices
            start_price = price.iloc[start_idx]
            end_price = price.iloc[end_idx]
            retracement_price = price.iloc[retracement_idx]
            
            # Determine trend direction
            if trend == 'auto':
                trend = 'up' if end_price > start_price else 'down'
            
            # Set levels if not provided
            if levels is None:
                levels = self.extension_levels
            
            # Calculate extension levels
            if trend == 'up':
                # Uptrend: start_price < end_price
                wave1_range = end_price - start_price
                extension_levels = {
                    level: retracement_price + level * wave1_range
                    for level in levels
                }
            else:
                # Downtrend: start_price > end_price
                wave1_range = start_price - end_price
                extension_levels = {
                    level: retracement_price - level * wave1_range
                    for level in levels
                }
            
            return extension_levels
        except Exception as e:
            logger.error(f"Error calculating Fibonacci extension levels: {str(e)}")
            return {}
    
    def identify_retracement_levels(
        self,
        data: pd.DataFrame,
        price_column: str = 'close',
        window: int = 100,
        min_swing_size: float = 0.03,
        levels: Optional[List[float]] = None,
        tolerance: float = 0.01
    ) -> pd.DataFrame:
        """
        Identify price interactions with Fibonacci retracement levels.
        
        Args:
            data: DataFrame with price data
            price_column: Column name for price data
            window: Window size for swing identification
            min_swing_size: Minimum swing size as a percentage of price
            levels: List of Fibonacci levels to check (if None, uses default levels)
            tolerance: Tolerance for level identification as a percentage of price
            
        Returns:
            DataFrame with Fibonacci retracement level signals
        """
        try:
            # Create a copy of the input data
            result = data.copy()
            
            # Get price data
            if price_column in result.columns:
                price = result[price_column]
            else:
                # Try with capitalized column name
                capitalized_column = price_column.capitalize()
                if capitalized_column in result.columns:
                    price = result[capitalized_column]
                else:
                    logger.error(f"Price column '{price_column}' not found in data")
                    return result
            
            # Set levels if not provided
            if levels is None:
                levels = self.retracement_levels
            
            # Initialize columns for Fibonacci levels
            for level in levels:
                result[f'fib_retracement_{level:.3f}'] = None
            
            result['fib_retracement_signal'] = None
            
            # Identify peaks and troughs
            peaks, troughs = self._identify_peaks_and_troughs(price, min_swing_size)
            
            # Combine peaks and troughs into a single list of swing points
            swing_points = sorted(peaks + troughs)
            
            # Need at least 2 swing points to calculate retracement levels
            if len(swing_points) < 2:
                return result
            
            # Iterate through swing points to identify retracement levels
            for i in range(len(swing_points) - 1):
                # Get start and end indices
                start_idx = swing_points[i]
                end_idx = swing_points[i+1]
                
                # Calculate retracement levels
                retracement_levels = self.calculate_retracement_levels(
                    result, start_idx, end_idx, price_column, 'auto', levels
                )
                
                # Check if price interacts with retracement levels
                for j in range(end_idx + 1, min(end_idx + window, len(result))):
                    current_price = price.iloc[j]
                    
                    # Check each retracement level
                    for level, level_price in retracement_levels.items():
                        # Calculate tolerance band
                        tolerance_band = price.iloc[end_idx] * tolerance
                        
                        # Check if price is within tolerance of the level
                        if abs(current_price - level_price) <= tolerance_band:
                            # Price is at a Fibonacci retracement level
                            result.loc[result.index[j], f'fib_retracement_{level:.3f}'] = level_price
                            result.loc[result.index[j], 'fib_retracement_signal'] = level
            
            return result
        except Exception as e:
            logger.error(f"Error identifying Fibonacci retracement levels: {str(e)}")
            return data
    
    def _identify_peaks_and_troughs(
        self,
        price: pd.Series,
        min_swing_size: float
    ) -> Tuple[List[int], List[int]]:
        """
        Identify peaks and troughs in price data.
        
        Args:
            price: Series with price data
            min_swing_size: Minimum swing size as a percentage of price
            
        Returns:
            Tuple of (peaks, troughs) as lists of indices
        """
        # Calculate minimum height for peaks and troughs
        min_height = price.mean() * min_swing_size
        
        # Find peaks
        peaks, _ = find_peaks(price, height=min_height, distance=5)
        
        # Find troughs (peaks in negative price)
        troughs, _ = find_peaks(-price, height=min_height, distance=5)
        
        return peaks.tolist(), troughs.tolist()
