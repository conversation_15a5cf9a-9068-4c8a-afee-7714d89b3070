"""
Logging configuration for the application.
"""
import logging
import os
from logging.handlers import RotatingFileHandler
from app.settings import get_settings

def setup_logging():
    """Set up logging configuration."""
    settings = get_settings()
    
    # Create logger
    logger = logging.getLogger("portfolio_optimizer")
    
    # Set level from settings
    log_level = settings.logging.get("level", "INFO")
    logger.setLevel(getattr(logging, log_level))
    
    # Create formatters and handlers
    formatter = logging.Formatter(
        settings.logging.get("format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    )
    
    # Console handler
    if settings.logging.get("console", True):
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # File handler
    log_file = settings.logging.get("file", "logs/app.log")
    os.makedirs(os.path.dirname(log_file), exist_ok=True)
    
    file_handler = RotatingFile<PERSON>andler(
        log_file,
        maxBytes=10485760,  # 10MB
        backupCount=5
    )
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    return logger

logger = setup_logging()

