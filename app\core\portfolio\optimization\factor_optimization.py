"""
Factor-based portfolio optimization.

This module provides functions to implement factor-based portfolio optimization,
which optimizes portfolios based on factor exposures.
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union
import scipy.optimize as sco
import cvxpy as cp

from app.core.portfolio.optimization.base import OptimizationStrategy
from app.utils.constants import DEFAULT_RISK_FREE_RATE
from app.utils.logging import logger


class FactorOptimization(OptimizationStrategy):
    """
    Factor-based portfolio optimization.
    
    This class implements portfolio optimization methods that account for
    factor exposures and factor returns.
    """
    
    def __init__(self, risk_free_rate: float = DEFAULT_RISK_FREE_RATE):
        """
        Initialize the factor-based optimization model.
        
        Args:
            risk_free_rate: Risk-free rate
        """
        super().__init__("Factor Optimization")
        self.risk_free_rate = risk_free_rate
    
    def optimize(
        self,
        returns: pd.DataFrame,
        factor_exposures: pd.DataFrame,
        factor_returns: Optional[pd.DataFrame] = None,
        objective: str = "factor_sharpe",
        **kwargs
    ) -> Dict[str, float]:
        """
        Optimize portfolio weights using factor-based optimization.
        
        Args:
            returns: DataFrame of asset returns
            factor_exposures: DataFrame of factor exposures (assets in rows, factors in columns)
            factor_returns: DataFrame of factor returns (optional)
            objective: Optimization objective ('factor_sharpe', 'min_factor_risk', 'target_factor')
            **kwargs: Additional parameters
            
        Returns:
            Dictionary mapping asset names to weights
        """
        # Validate inputs
        if returns is None or returns.empty:
            logger.error("Returns must be provided for factor optimization")
            return {}
        
        if factor_exposures is None or factor_exposures.empty:
            logger.error("Factor exposures must be provided for factor optimization")
            return {}
        
        # Get asset names
        assets = returns.columns.tolist()
        n_assets = len(assets)
        
        if n_assets == 0:
            logger.warning("No assets provided for optimization")
            return {}
        
        # Ensure factor exposures only include assets in returns
        common_assets = [asset for asset in factor_exposures.index if asset in assets]
        factor_exposures = factor_exposures.loc[common_assets]
        
        # Get factor names
        factors = factor_exposures.columns.tolist()
        n_factors = len(factors)
        
        if n_factors == 0:
            logger.warning("No factors provided for optimization")
            return {}
        
        # If factor returns are not provided, estimate them from asset returns
        if factor_returns is None or factor_returns.empty:
            factor_returns = self._estimate_factor_returns(returns, factor_exposures)
        
        # Get additional parameters
        target_return = kwargs.get('target_return', None)
        target_risk = kwargs.get('target_risk', None)
        min_weight = kwargs.get('min_weight', 0.0)
        max_weight = kwargs.get('max_weight', 1.0)
        target_factor_exposures = kwargs.get('target_factor_exposures', None)
        
        # Choose optimization method based on objective
        if objective == "factor_sharpe":
            weights = self._optimize_factor_sharpe(
                returns=returns,
                factor_exposures=factor_exposures,
                factor_returns=factor_returns,
                min_weight=min_weight,
                max_weight=max_weight
            )
        elif objective == "min_factor_risk":
            weights = self._optimize_min_factor_risk(
                returns=returns,
                factor_exposures=factor_exposures,
                factor_returns=factor_returns,
                min_weight=min_weight,
                max_weight=max_weight,
                target_return=target_return
            )
        elif objective == "target_factor":
            if target_factor_exposures is None:
                logger.warning("Target factor exposures not provided, using factor_sharpe objective")
                weights = self._optimize_factor_sharpe(
                    returns=returns,
                    factor_exposures=factor_exposures,
                    factor_returns=factor_returns,
                    min_weight=min_weight,
                    max_weight=max_weight
                )
            else:
                weights = self._optimize_target_factor(
                    returns=returns,
                    factor_exposures=factor_exposures,
                    target_factor_exposures=target_factor_exposures,
                    min_weight=min_weight,
                    max_weight=max_weight
                )
        else:
            logger.warning(f"Unknown objective '{objective}', using factor_sharpe")
            weights = self._optimize_factor_sharpe(
                returns=returns,
                factor_exposures=factor_exposures,
                factor_returns=factor_returns,
                min_weight=min_weight,
                max_weight=max_weight
            )
        
        # Convert weights to dictionary
        weights_dict = {asset: weight for asset, weight in zip(common_assets, weights)}
        
        # Add zero weights for assets not in factor_exposures
        for asset in assets:
            if asset not in weights_dict:
                weights_dict[asset] = 0.0
        
        return weights_dict
    
    def _estimate_factor_returns(
        self,
        returns: pd.DataFrame,
        factor_exposures: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Estimate factor returns from asset returns and factor exposures.
        
        Args:
            returns: DataFrame of asset returns
            factor_exposures: DataFrame of factor exposures
            
        Returns:
            DataFrame of estimated factor returns
        """
        try:
            # Get common assets
            common_assets = [asset for asset in factor_exposures.index if asset in returns.columns]
            
            if len(common_assets) == 0:
                logger.error("No common assets between returns and factor exposures")
                return pd.DataFrame()
            
            # Filter returns and factor exposures
            filtered_returns = returns[common_assets]
            filtered_exposures = factor_exposures.loc[common_assets]
            
            # Transpose returns to have time in rows
            returns_T = filtered_returns.T
            
            # Estimate factor returns for each time period
            factor_returns_list = []
            
            for t in range(len(returns_T.columns)):
                # Get asset returns for time t
                r_t = returns_T.iloc[:, t].values
                
                # Solve for factor returns using linear regression
                # r_t = B * f_t + e_t, where B is the factor exposure matrix
                B = filtered_exposures.values
                
                # Use least squares to estimate factor returns
                f_t, residuals, rank, s = np.linalg.lstsq(B, r_t, rcond=None)
                
                factor_returns_list.append(f_t)
            
            # Convert to DataFrame
            factor_returns = pd.DataFrame(
                factor_returns_list,
                index=returns_T.columns,
                columns=filtered_exposures.columns
            )
            
            return factor_returns
        
        except Exception as e:
            logger.error(f"Error estimating factor returns: {str(e)}")
            return pd.DataFrame()
    
    def _optimize_factor_sharpe(
        self,
        returns: pd.DataFrame,
        factor_exposures: pd.DataFrame,
        factor_returns: pd.DataFrame,
        min_weight: float = 0.0,
        max_weight: float = 1.0
    ) -> np.ndarray:
        """
        Optimize portfolio weights by maximizing factor-based Sharpe ratio.
        
        Args:
            returns: DataFrame of asset returns
            factor_exposures: DataFrame of factor exposures
            factor_returns: DataFrame of factor returns
            min_weight: Minimum weight for each asset
            max_weight: Maximum weight for each asset
            
        Returns:
            Array of optimal weights
        """
        try:
            # Get common assets
            common_assets = [asset for asset in factor_exposures.index if asset in returns.columns]
            n_assets = len(common_assets)
            
            # Filter returns and factor exposures
            filtered_returns = returns[common_assets]
            filtered_exposures = factor_exposures.loc[common_assets]
            
            # Calculate factor covariance matrix
            factor_cov = factor_returns.cov().values
            
            # Calculate specific risk (residual variance)
            # Estimate specific risk as the variance of residuals
            specific_risk = np.zeros(n_assets)
            
            for i, asset in enumerate(common_assets):
                # Get asset returns
                asset_returns = filtered_returns[asset].values
                
                # Get factor exposures for this asset
                asset_exposures = filtered_exposures.loc[asset].values
                
                # Calculate expected returns based on factor model
                expected_returns = np.dot(factor_returns.values, asset_exposures)
                
                # Calculate residuals
                residuals = asset_returns - expected_returns
                
                # Calculate specific risk (variance of residuals)
                specific_risk[i] = np.var(residuals)
            
            # Calculate expected returns based on factor model
            expected_returns = np.dot(filtered_exposures.values, factor_returns.mean().values)
            
            # Define the optimization problem
            w = cp.Variable(n_assets)
            
            # Calculate portfolio factor exposures
            portfolio_exposures = filtered_exposures.values.T @ w
            
            # Calculate portfolio factor risk
            factor_risk = cp.quad_form(portfolio_exposures, factor_cov)
            
            # Calculate portfolio specific risk
            specific_risk_diag = np.diag(specific_risk)
            specific_risk_term = cp.quad_form(w, specific_risk_diag)
            
            # Calculate total portfolio risk
            portfolio_risk = cp.sqrt(factor_risk + specific_risk_term)
            
            # Objective: maximize Sharpe ratio
            # Since we can't directly maximize the ratio, we'll maximize the numerator
            # while constraining the denominator (risk) to be 1
            objective = cp.Maximize(w @ expected_returns - self.risk_free_rate)
            
            # Constraints
            constraints = [
                cp.sum(w) == 1,  # Weights sum to 1
                w >= min_weight,  # Minimum weight
                w <= max_weight,  # Maximum weight
                portfolio_risk <= 1  # Risk constraint
            ]
            
            # Solve the problem
            problem = cp.Problem(objective, constraints)
            problem.solve()
            
            if problem.status == 'optimal':
                # Get optimal weights
                weights = w.value
                
                # Normalize weights to sum to 1 (in case of numerical issues)
                weights = weights / np.sum(weights)
                
                return weights
            else:
                logger.error(f"Factor Sharpe optimization failed: {problem.status}")
                # Fallback to equal weights
                return np.ones(n_assets) / n_assets
        
        except Exception as e:
            logger.error(f"Error in factor Sharpe optimization: {str(e)}")
            # Fallback to equal weights
            return np.ones(len(common_assets)) / len(common_assets)
    
    def _optimize_min_factor_risk(
        self,
        returns: pd.DataFrame,
        factor_exposures: pd.DataFrame,
        factor_returns: pd.DataFrame,
        min_weight: float = 0.0,
        max_weight: float = 1.0,
        target_return: Optional[float] = None
    ) -> np.ndarray:
        """
        Optimize portfolio weights by minimizing factor-based risk.
        
        Args:
            returns: DataFrame of asset returns
            factor_exposures: DataFrame of factor exposures
            factor_returns: DataFrame of factor returns
            min_weight: Minimum weight for each asset
            max_weight: Maximum weight for each asset
            target_return: Target portfolio return (optional)
            
        Returns:
            Array of optimal weights
        """
        try:
            # Get common assets
            common_assets = [asset for asset in factor_exposures.index if asset in returns.columns]
            n_assets = len(common_assets)
            
            # Filter returns and factor exposures
            filtered_returns = returns[common_assets]
            filtered_exposures = factor_exposures.loc[common_assets]
            
            # Calculate factor covariance matrix
            factor_cov = factor_returns.cov().values
            
            # Calculate specific risk (residual variance)
            specific_risk = np.zeros(n_assets)
            
            for i, asset in enumerate(common_assets):
                # Get asset returns
                asset_returns = filtered_returns[asset].values
                
                # Get factor exposures for this asset
                asset_exposures = filtered_exposures.loc[asset].values
                
                # Calculate expected returns based on factor model
                expected_returns = np.dot(factor_returns.values, asset_exposures)
                
                # Calculate residuals
                residuals = asset_returns - expected_returns
                
                # Calculate specific risk (variance of residuals)
                specific_risk[i] = np.var(residuals)
            
            # Calculate expected returns based on factor model
            expected_returns = np.dot(filtered_exposures.values, factor_returns.mean().values)
            
            # Define the optimization problem
            w = cp.Variable(n_assets)
            
            # Calculate portfolio factor exposures
            portfolio_exposures = filtered_exposures.values.T @ w
            
            # Calculate portfolio factor risk
            factor_risk = cp.quad_form(portfolio_exposures, factor_cov)
            
            # Calculate portfolio specific risk
            specific_risk_diag = np.diag(specific_risk)
            specific_risk_term = cp.quad_form(w, specific_risk_diag)
            
            # Calculate total portfolio risk
            portfolio_risk = factor_risk + specific_risk_term
            
            # Objective: minimize portfolio risk
            objective = cp.Minimize(portfolio_risk)
            
            # Constraints
            constraints = [
                cp.sum(w) == 1,  # Weights sum to 1
                w >= min_weight,  # Minimum weight
                w <= max_weight  # Maximum weight
            ]
            
            # Add target return constraint if specified
            if target_return is not None:
                constraints.append(w @ expected_returns >= target_return)
            
            # Solve the problem
            problem = cp.Problem(objective, constraints)
            problem.solve()
            
            if problem.status == 'optimal':
                # Get optimal weights
                weights = w.value
                
                # Normalize weights to sum to 1 (in case of numerical issues)
                weights = weights / np.sum(weights)
                
                return weights
            else:
                logger.error(f"Min factor risk optimization failed: {problem.status}")
                # Fallback to equal weights
                return np.ones(n_assets) / n_assets
        
        except Exception as e:
            logger.error(f"Error in min factor risk optimization: {str(e)}")
            # Fallback to equal weights
            return np.ones(len(common_assets)) / len(common_assets)
    
    def _optimize_target_factor(
        self,
        returns: pd.DataFrame,
        factor_exposures: pd.DataFrame,
        target_factor_exposures: Dict[str, float],
        min_weight: float = 0.0,
        max_weight: float = 1.0
    ) -> np.ndarray:
        """
        Optimize portfolio weights to match target factor exposures.
        
        Args:
            returns: DataFrame of asset returns
            factor_exposures: DataFrame of factor exposures
            target_factor_exposures: Dictionary mapping factor names to target exposures
            min_weight: Minimum weight for each asset
            max_weight: Maximum weight for each asset
            
        Returns:
            Array of optimal weights
        """
        try:
            # Get common assets
            common_assets = [asset for asset in factor_exposures.index if asset in returns.columns]
            n_assets = len(common_assets)
            
            # Filter returns and factor exposures
            filtered_returns = returns[common_assets]
            filtered_exposures = factor_exposures.loc[common_assets]
            
            # Calculate covariance matrix
            cov_matrix = filtered_returns.cov().values
            
            # Create target factor exposures vector
            factors = filtered_exposures.columns.tolist()
            target_exposures = np.zeros(len(factors))
            
            for i, factor in enumerate(factors):
                if factor in target_factor_exposures:
                    target_exposures[i] = target_factor_exposures[factor]
            
            # Define the optimization problem
            w = cp.Variable(n_assets)
            
            # Calculate portfolio factor exposures
            portfolio_exposures = filtered_exposures.values.T @ w
            
            # Objective: minimize tracking error to target factor exposures
            # and minimize portfolio variance
            tracking_error = cp.sum_squares(portfolio_exposures - target_exposures)
            portfolio_variance = cp.quad_form(w, cov_matrix)
            
            # Combined objective with a trade-off parameter
            lambda_param = 0.5  # Trade-off between tracking error and variance
            objective = cp.Minimize(tracking_error + lambda_param * portfolio_variance)
            
            # Constraints
            constraints = [
                cp.sum(w) == 1,  # Weights sum to 1
                w >= min_weight,  # Minimum weight
                w <= max_weight  # Maximum weight
            ]
            
            # Solve the problem
            problem = cp.Problem(objective, constraints)
            problem.solve()
            
            if problem.status == 'optimal':
                # Get optimal weights
                weights = w.value
                
                # Normalize weights to sum to 1 (in case of numerical issues)
                weights = weights / np.sum(weights)
                
                return weights
            else:
                logger.error(f"Target factor optimization failed: {problem.status}")
                # Fallback to equal weights
                return np.ones(n_assets) / n_assets
        
        except Exception as e:
            logger.error(f"Error in target factor optimization: {str(e)}")
            # Fallback to equal weights
            return np.ones(len(common_assets)) / len(common_assets)
    
    def calculate_factor_exposures(
        self,
        weights: Dict[str, float],
        factor_exposures: pd.DataFrame
    ) -> Dict[str, float]:
        """
        Calculate portfolio factor exposures.
        
        Args:
            weights: Dictionary mapping asset names to weights
            factor_exposures: DataFrame of factor exposures
            
        Returns:
            Dictionary mapping factor names to portfolio exposures
        """
        try:
            # Convert weights to Series
            weights_series = pd.Series(weights)
            
            # Filter factor exposures to include only assets in weights
            common_assets = [asset for asset in weights_series.index if asset in factor_exposures.index]
            filtered_exposures = factor_exposures.loc[common_assets]
            filtered_weights = weights_series[common_assets]
            
            # Normalize weights to sum to 1
            filtered_weights = filtered_weights / filtered_weights.sum()
            
            # Calculate portfolio factor exposures
            portfolio_exposures = filtered_exposures.T.dot(filtered_weights)
            
            # Convert to dictionary
            exposures_dict = portfolio_exposures.to_dict()
            
            return exposures_dict
        
        except Exception as e:
            logger.error(f"Error calculating factor exposures: {str(e)}")
            return {}
