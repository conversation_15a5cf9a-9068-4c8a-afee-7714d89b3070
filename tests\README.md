# Testing Framework

This directory contains tests for the Portfolio Optimization project.

## Test Structure

- **unit/**: Unit tests for individual components
  - **api/**: Tests for API endpoints
  - **core/**: Tests for core business logic
    - **data_collection/**: Tests for data collection modules
    - **portfolio/**: Tests for portfolio optimization modules
    - **technical_analysis/**: Tests for technical analysis modules
  - **db/**: Tests for database models and repositories
  - **utils/**: Tests for utility functions

- **integration/**: Integration tests for component interactions
  - **api/**: Tests for API integrations
  - **data/**: Tests for data pipeline integrations
  - **portfolio/**: Tests for portfolio optimization integrations

- **e2e/**: End-to-end tests for complete workflows
  - **api/**: Tests for API workflows
  - **streamlit/**: Tests for Streamlit UI workflows

## Running Tests

### Running All Tests

```bash
pytest
```

### Running Unit Tests

```bash
pytest tests/unit
```

### Running Integration Tests

```bash
pytest tests/integration
```

### Running End-to-End Tests

```bash
pytest tests/e2e
```

### Running Tests with Coverage

```bash
pytest --cov=app
```

### Running Tests with Verbose Output

```bash
pytest -v
```

## Test Configuration

The test configuration is defined in `conftest.py`. This file contains fixtures that are shared across tests, such as:

- Database session
- API client
- Test data
- Mock objects

## Writing Tests

### Unit Tests

Unit tests should test individual components in isolation. Use mocks to isolate the component from its dependencies.

Example:

```python
def test_calculate_performance_metrics(sample_returns):
    # Arrange
    risk_free_rate = 0.02
    
    # Act
    metrics = calculate_performance_metrics(
        returns=sample_returns,
        risk_free_rate=risk_free_rate
    )
    
    # Assert
    assert "sharpe_ratio" in metrics
    assert metrics["sharpe_ratio"] > 0
```

### Integration Tests

Integration tests should test the interaction between components. Use real dependencies when possible.

Example:

```python
async def test_data_pipeline(db_session):
    # Arrange
    symbols = ["AAPL", "MSFT"]
    start_date = date(2020, 1, 1)
    end_date = date(2020, 12, 31)
    
    # Act
    data = await data_pipeline.run(
        symbols=symbols,
        start_date=start_date,
        end_date=end_date,
        db_session=db_session
    )
    
    # Assert
    assert len(data) == 2
    assert "AAPL" in data
    assert "MSFT" in data
```

### End-to-End Tests

End-to-end tests should test complete workflows from the user's perspective.

Example:

```python
def test_portfolio_optimization_workflow(client):
    # Arrange
    request_data = {
        "symbols": ["AAPL", "MSFT", "GOOGL"],
        "start_date": "2020-01-01",
        "end_date": "2020-12-31",
        "optimization_criterion": "max_sharpe"
    }
    
    # Act
    response = client.post("/api/v1/portfolio/optimize", json=request_data)
    
    # Assert
    assert response.status_code == 200
    assert "weights" in response.json()
    assert "performance" in response.json()
```
