"""
Mean-variance portfolio optimization.
"""
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
from scipy import optimize

from app.core.portfolio.optimization.base import OptimizationStrategy
from app.utils.constants import DEFAULT_RISK_FREE_RATE
from app.utils.logging import logger


class MeanVarianceOptimization(OptimizationStrategy):
    """
    Mean-variance portfolio optimization.

    This class implements the classic Markowitz mean-variance optimization
    for finding efficient portfolios.
    """

    def __init__(self, risk_free_rate: float = DEFAULT_RISK_FREE_RATE):
        """
        Initialize the mean-variance optimization strategy.

        Args:
            risk_free_rate: Risk-free rate (default: 2%)
        """
        super().__init__("Mean-Variance")
        self.risk_free_rate = risk_free_rate

    def optimize(
        self,
        returns: pd.DataFrame,
        objective: str = "sharpe",
        target_return: Optional[float] = None,
        target_risk: Optional[float] = None,
        constraints: Optional[Dict] = None,
        **kwargs
    ) -> Dict[str, float]:
        """
        Optimize portfolio weights using mean-variance optimization.

        Args:
            returns: DataFrame of asset returns
            objective: Optimization objective ('sharpe', 'min_risk', 'max_return', 'target_return', 'target_risk')
            target_return: Target portfolio return (for 'target_risk' objective)
            target_risk: Target portfolio risk (for 'target_return' objective)
            constraints: Additional constraints for optimization
            **kwargs: Additional parameters

        Returns:
            Dictionary mapping asset names to weights
        """
        # Validate inputs
        if returns is None or returns.empty:
            logger.error("Returns must be provided for mean-variance optimization")
            return {}

        # Get asset names
        assets = returns.columns.tolist()
        n_assets = len(assets)

        if n_assets == 0:
            logger.warning("No assets provided for optimization")
            return {}

        # Calculate expected returns and covariance matrix
        expected_returns = returns.mean()
        cov_matrix = returns.cov()

        # Initial guess: equal weights
        initial_weights = np.ones(n_assets) / n_assets

        # Default bounds: weights must be between 0 and 1
        bounds = [(0.0, 1.0) for _ in range(n_assets)]

        # Default constraints: weights must sum to 1
        base_constraints = [{'type': 'eq', 'fun': lambda x: np.sum(x) - 1.0}]

        # Add custom constraints if provided
        all_constraints = base_constraints
        if constraints is not None:
            if 'min_weight' in constraints:
                min_weight = constraints['min_weight']
                bounds = [(max(0.0, min_weight), 1.0) for _ in range(n_assets)]

            if 'max_weight' in constraints:
                max_weight = constraints['max_weight']
                bounds = [(bounds[i][0], min(1.0, max_weight)) for i in range(n_assets)]

            if 'asset_constraints' in constraints:
                for asset_idx, (min_w, max_w) in constraints['asset_constraints'].items():
                    if asset_idx < n_assets:
                        bounds[asset_idx] = (max(0.0, min_w), min(1.0, max_w))

        # Define objective functions
        def portfolio_return(weights):
            return np.sum(expected_returns * weights)

        def portfolio_risk(weights):
            return np.sqrt(np.dot(weights.T, np.dot(cov_matrix, weights)))

        def sharpe_ratio(weights):
            ret = portfolio_return(weights)
            risk = portfolio_risk(weights)
            return -(ret - self.risk_free_rate) / risk  # Negative for minimization

        def min_risk_objective(weights):
            return portfolio_risk(weights)

        def max_return_objective(weights):
            return -portfolio_return(weights)  # Negative for minimization

        def target_return_objective(weights):
            ret = portfolio_return(weights)
            target = target_return if target_return is not None else expected_returns.mean()
            return (ret - target)**2  # Minimize squared difference

        def target_risk_objective(weights):
            risk = portfolio_risk(weights)
            target = target_risk if target_risk is not None else cov_matrix.values.mean()**0.5
            return (risk - target)**2  # Minimize squared difference

        # Select objective function based on objective parameter
        if objective == "sharpe" or objective == "max_sharpe":
            obj_function = sharpe_ratio
        elif objective == "min_risk" or objective == "min_volatility":
            obj_function = min_risk_objective
        elif objective == "max_return":
            obj_function = max_return_objective
        elif objective == "target_return":
            if target_return is None:
                logger.warning("No target return provided, using mean of expected returns")
            obj_function = target_return_objective
            # Add constraint to ensure risk is minimized
            all_constraints.append({'type': 'eq', 'fun': lambda x: portfolio_return(x) - (target_return if target_return is not None else expected_returns.mean())})
        elif objective == "target_risk":
            if target_risk is None:
                logger.warning("No target risk provided, using mean of covariance matrix")
            obj_function = target_risk_objective
            # Add constraint to ensure return is maximized
            all_constraints.append({'type': 'eq', 'fun': lambda x: portfolio_risk(x) - (target_risk if target_risk is not None else cov_matrix.values.mean()**0.5)})
        elif objective == "efficient_return":
            # For efficient_return, we want to minimize risk for a given return
            if target_return is None:
                logger.warning("No target return provided for efficient_return, using mean of expected returns")
                target_return = expected_returns.mean()

            # Use min_risk as the objective
            obj_function = min_risk_objective

            # Add constraint to ensure we achieve the target return
            all_constraints.append({'type': 'eq', 'fun': lambda x: portfolio_return(x) - target_return})
        elif objective == "efficient_risk":
            # For efficient_risk, we want to maximize return for a given risk
            if target_risk is None:
                logger.warning("No target risk provided for efficient_risk, using mean of covariance matrix")
                target_risk = cov_matrix.values.mean()**0.5

            # Use max_return as the objective
            obj_function = max_return_objective

            # Add constraint to ensure we don't exceed the target risk
            all_constraints.append({'type': 'eq', 'fun': lambda x: portfolio_risk(x) - target_risk})
        else:
            logger.warning(f"Unknown objective: {objective}, using Sharpe ratio")
            obj_function = sharpe_ratio

        # Optimize weights
        try:
            result = optimize.minimize(
                obj_function,
                initial_weights,
                method='SLSQP',
                bounds=bounds,
                constraints=all_constraints,
                options={'maxiter': 1000}
            )

            if result.success:
                # Get optimized weights
                optimized_weights = result.x

                # Create weights dictionary
                weights = {asset: weight for asset, weight in zip(assets, optimized_weights)}

                # Calculate portfolio metrics
                port_return = portfolio_return(optimized_weights)
                port_risk = portfolio_risk(optimized_weights)
                port_sharpe = (port_return - self.risk_free_rate) / port_risk

                logger.info(f"Optimized portfolio: return={port_return:.4f}, risk={port_risk:.4f}, sharpe={port_sharpe:.4f}")

                return weights
            else:
                logger.warning(f"Optimization failed: {result.message}")
                # Fall back to equal weights
                weight = 1.0 / n_assets
                return {asset: weight for asset in assets}

        except Exception as e:
            logger.error(f"Error in optimization: {str(e)}")
            # Fall back to equal weights
            weight = 1.0 / n_assets
            return {asset: weight for asset in assets}

    def efficient_frontier(
        self,
        returns: pd.DataFrame,
        num_portfolios: int = 50,
        **kwargs
    ) -> pd.DataFrame:
        """
        Generate the efficient frontier.

        Args:
            returns: DataFrame of asset returns
            num_portfolios: Number of portfolios to generate
            **kwargs: Additional parameters

        Returns:
            DataFrame with portfolio weights and metrics
        """
        # Validate inputs
        if returns is None or returns.empty:
            logger.error("Returns must be provided for efficient frontier")
            return pd.DataFrame()

        # Get asset names
        assets = returns.columns.tolist()
        n_assets = len(assets)

        if n_assets == 0:
            logger.warning("No assets provided for efficient frontier")
            return pd.DataFrame()

        # Calculate expected returns and covariance matrix
        expected_returns = returns.mean()
        cov_matrix = returns.cov()

        # Find minimum risk portfolio
        min_risk_weights = self.optimize(returns, objective="min_risk", **kwargs)
        min_risk_return = sum(expected_returns[asset] * weight for asset, weight in min_risk_weights.items())
        min_risk_risk = np.sqrt(
            sum(min_risk_weights[asset_i] * min_risk_weights[asset_j] * cov_matrix.loc[asset_i, asset_j]
                for asset_i in assets for asset_j in assets)
        )

        # Find maximum return portfolio
        max_return_weights = self.optimize(returns, objective="max_return", **kwargs)
        max_return_return = sum(expected_returns[asset] * weight for asset, weight in max_return_weights.items())
        max_return_risk = np.sqrt(
            sum(max_return_weights[asset_i] * max_return_weights[asset_j] * cov_matrix.loc[asset_i, asset_j]
                for asset_i in assets for asset_j in assets)
        )

        # Generate target returns between min and max
        target_returns = np.linspace(min_risk_return, max_return_return, num_portfolios)

        # Initialize results DataFrame
        results = []

        # Generate portfolios for each target return
        for target_return in target_returns:
            weights = self.optimize(
                returns,
                objective="target_return",
                target_return=target_return,
                **kwargs
            )

            # Calculate portfolio metrics
            port_return = sum(expected_returns[asset] * weight for asset, weight in weights.items())
            port_risk = np.sqrt(
                sum(weights[asset_i] * weights[asset_j] * cov_matrix.loc[asset_i, asset_j]
                    for asset_i in assets for asset_j in assets)
            )
            port_sharpe = (port_return - self.risk_free_rate) / port_risk

            # Add to results
            result = {
                'return': port_return,
                'risk': port_risk,
                'sharpe': port_sharpe
            }

            # Add weights
            for asset, weight in weights.items():
                result[f'weight_{asset}'] = weight

            results.append(result)

        # Create DataFrame
        frontier_df = pd.DataFrame(results)

        return frontier_df

    def optimal_portfolio(
        self,
        returns: pd.DataFrame,
        **kwargs
    ) -> Tuple[Dict[str, float], Dict[str, float]]:
        """
        Find the optimal portfolio on the efficient frontier.

        Args:
            returns: DataFrame of asset returns
            **kwargs: Additional parameters

        Returns:
            Tuple of (weights, metrics) for the optimal portfolio
        """
        try:
            # Validate inputs
            if returns is None or returns.empty:
                logger.error("Returns must be provided for optimal portfolio")
                return {}, {'return': 0, 'risk': 0, 'sharpe': 0}

            # Get asset names
            assets = returns.columns.tolist()
            if not assets:
                logger.warning("No assets provided for optimal portfolio")
                return {}, {'return': 0, 'risk': 0, 'sharpe': 0}

            # Optimize for maximum Sharpe ratio
            weights = self.optimize(returns, objective="sharpe", **kwargs)

            # Calculate portfolio metrics
            expected_returns = returns.mean()
            cov_matrix = returns.cov()

            # Calculate portfolio return
            port_return = sum(expected_returns[asset] * weight for asset, weight in weights.items())

            # Calculate portfolio risk with explicit error handling
            try:
                port_risk = np.sqrt(
                    sum(weights[asset_i] * weights[asset_j] * cov_matrix.loc[asset_i, asset_j]
                        for asset_i in assets for asset_j in assets)
                )

                # Ensure risk is not zero to avoid division by zero
                if port_risk <= 1e-10:
                    logger.warning("Portfolio risk is too close to zero, using small positive value")
                    port_risk = 1e-6  # Use a small positive value

                # Calculate Sharpe ratio
                port_sharpe = (port_return - self.risk_free_rate) / port_risk

            except Exception as e:
                logger.error(f"Error calculating portfolio risk: {e}")
                # Use fallback values
                port_risk = 0.1  # Default risk
                port_sharpe = 0.0  # Default Sharpe ratio

            metrics = {
                'return': port_return,
                'risk': port_risk,
                'sharpe': port_sharpe
            }

            return weights, metrics

        except Exception as e:
            logger.error(f"Error in optimal_portfolio: {e}")
            # Return fallback values
            return {}, {'return': 0, 'risk': 0, 'sharpe': 0}
