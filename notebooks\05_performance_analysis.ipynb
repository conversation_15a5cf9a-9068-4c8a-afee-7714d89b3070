{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Portfolio Optimization - Performance Analysis\n", "\n", "This notebook provides a comprehensive analysis of portfolio performance, building on the backtesting results from the previous notebook. We'll analyze various performance metrics, risk characteristics, return distributions, and compare against benchmarks."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Data Loading"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import sys\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime, timedelta\n", "import plotly.graph_objects as go\n", "import plotly.express as px\n", "from plotly.subplots import make_subplots\n", "from scipy import stats\n", "import warnings\n", "\n", "# Add the project root to the path\n", "sys.path.append(os.path.abspath('..'))\n", "\n", "# Import performance analysis modules\n", "from app.core.portfolio.performance import calculate_performance_metrics, calculate_rolling_metrics\n", "\n", "# Set plotting style\n", "plt.style.use('fivethirtyeight')\n", "sns.set_theme(style=\"whitegrid\")\n", "\n", "# Configure pandas display options\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', 1000)\n", "\n", "# Suppress warnings\n", "warnings.filterwarnings('ignore')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. <PERSON><PERSON> Backtesting Results\n", "\n", "We'll either load the results from the previous backtesting notebook or recreate them."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Option 1: Load results from previous notebook (if saved)\n", "# try:\n", "#     results = pd.read_pickle('../data/processed/backtest_results.pkl')\n", "#     print(\"Loaded backtest results from file\")\n", "# except:\n", "#     print(\"Could not load backtest results, recreating them...\")\n", "\n", "# Option 2: Recreate backtesting results\n", "# Import necessary modules\n", "from app.core.data_collection.data_manager import data_manager\n", "from app.core.portfolio.optimization import PortfolioOptimizer\n", "import asyncio\n", "\n", "# Define symbols and date range\n", "symbols = ['AAPL', 'MSFT', 'AMZN', 'GOOGL', 'META', 'TSLA', 'NVDA', 'JPM', 'V', 'JN<PERSON>']\n", "start_date = datetime.now() - <PERSON><PERSON><PERSON>(days=365*3)  # 3 years of data\n", "end_date = datetime.now()\n", "\n", "# Create an async function to get data\n", "async def get_data():\n", "    return await data_manager.get_multiple_stock_data(\n", "        symbols=symbols,\n", "        start_date=start_date.date(),\n", "        end_date=end_date.date(),\n", "        interval='1d'\n", "    )\n", "\n", "# Run the async function\n", "stock_data = asyncio.run(get_data())\n", "\n", "# Extract adjusted close prices\n", "prices_df = pd.DataFrame()\n", "\n", "for symbol, data in stock_data.items():\n", "    prices_df[symbol] = data['Adj Close']\n", "\n", "# Set index to datetime\n", "prices_df.index = pd.to_datetime(prices_df.index)\n", "\n", "# Sort by date\n", "prices_df = prices_df.sort_index()\n", "\n", "# Display the first few rows\n", "prices_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define backtesting framework\n", "class BacktestFramework:\n", "    \"\"\"\n", "    A simple backtesting framework for portfolio optimization strategies.\n", "    \"\"\"\n", "    \n", "    def __init__(self, prices_df, initial_capital=10000):\n", "        \"\"\"\n", "        Initialize the backtesting framework.\n", "        \n", "        Args:\n", "            prices_df: DataFrame with adjusted close prices\n", "            initial_capital: Initial capital for the backtest\n", "        \"\"\"\n", "        self.prices_df = prices_df\n", "        self.initial_capital = initial_capital\n", "        self.optimizer = PortfolioOptimizer()\n", "        \n", "    def run_backtest(self, strategy, window=252, rebalance_freq=63):\n", "        \"\"\"\n", "        Run a backtest for a given strategy.\n", "        \n", "        Args:\n", "            strategy: Strategy function that returns weights\n", "            window: Lookback window for optimization (in trading days)\n", "            rebalance_freq: Rebalancing frequency (in trading days)\n", "            \n", "        Returns:\n", "            DataFrame with portfolio values and metrics\n", "        \"\"\"\n", "        # Initialize portfolio\n", "        portfolio_values = []\n", "        weights_history = []\n", "        current_weights = None\n", "        current_shares = None\n", "        \n", "        # Get all dates\n", "        dates = self.prices_df.index\n", "        \n", "        # Start with cash\n", "        portfolio_value = self.initial_capital\n", "        \n", "        # Loop through dates\n", "        for i in range(window, len(dates)):\n", "            current_date = dates[i]\n", "            \n", "            # Check if we need to rebalance\n", "            if i == window or (i - window) % rebalance_freq == 0:\n", "                # Get historical data for optimization\n", "                hist_data = self.prices_df.iloc[i-window:i]\n", "                \n", "                # Get new weights based on strategy\n", "                new_weights = strategy(hist_data)\n", "                \n", "                # Calculate shares to buy/sell\n", "                current_prices = self.prices_df.iloc[i]\n", "                current_shares = {}\n", "                \n", "                for symbol, weight in new_weights.items():\n", "                    # Calculate number of shares\n", "                    shares = (portfolio_value * weight) / current_prices[symbol]\n", "                    current_shares[symbol] = int(shares)  # Whole shares only\n", "                \n", "                # Update current weights\n", "                current_weights = new_weights\n", "                weights_history.append((current_date, current_weights))\n", "            \n", "            # Calculate portfolio value\n", "            if current_shares:\n", "                current_prices = self.prices_df.iloc[i]\n", "                portfolio_value = sum(current_shares[symbol] * current_prices[symbol] \n", "                                     for symbol in current_shares)\n", "            \n", "            # Store portfolio value\n", "            portfolio_values.append((current_date, portfolio_value))\n", "        \n", "        # Create DataFrame with portfolio values\n", "        portfolio_df = pd.DataFrame(portfolio_values, columns=['Date', 'Value'])\n", "        portfolio_df.set_index('Date', inplace=True)\n", "        \n", "        # Calculate returns\n", "        portfolio_df['Returns'] = portfolio_df['Value'].pct_change()\n", "        \n", "        # Create weights DataFrame\n", "        weights_df = pd.DataFrame([\n", "            {**{'Date': date}, **weights}\n", "            for date, weights in weights_history\n", "        ])\n", "        weights_df.set_index('Date', inplace=True)\n", "        \n", "        return portfolio_df, weights_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define strategy functions\n", "def equal_weight_strategy(hist_data):\n", "    \"\"\"Equal weight strategy.\"\"\"\n", "    symbols = hist_data.columns\n", "    weights = {symbol: 1.0 / len(symbols) for symbol in symbols}\n", "    return weights\n", "\n", "def min_volatility_strategy(hist_data):\n", "    \"\"\"Minimum volatility strategy.\"\"\"\n", "    returns = hist_data.pct_change().dropna()\n", "    \n", "    # Calculate covariance matrix\n", "    cov_matrix = returns.cov()\n", "    \n", "    # Initialize optimizer\n", "    optimizer = PortfolioOptimizer()\n", "    \n", "    # Optimize for minimum volatility\n", "    weights, _ = optimizer.optimize(\n", "        expected_returns=returns.mean() * 252,\n", "        risk_model=cov_matrix,\n", "        optimization_criterion='min_volatility'\n", "    )\n", "    \n", "    return weights\n", "\n", "def max_sharpe_strategy(hist_data):\n", "    \"\"\"Maximum Sharpe ratio strategy.\"\"\"\n", "    returns = hist_data.pct_change().dropna()\n", "    \n", "    # Calculate expected returns and covariance matrix\n", "    expected_returns = returns.mean() * 252\n", "    cov_matrix = returns.cov()\n", "    \n", "    # Initialize optimizer\n", "    optimizer = PortfolioOptimizer()\n", "    \n", "    # Optimize for maximum Sharpe ratio\n", "    weights, _ = optimizer.optimize(\n", "        expected_returns=expected_returns,\n", "        risk_model=cov_matrix,\n", "        optimization_criterion='max_sharpe'\n", "    )\n", "    \n", "    return weights\n", "\n", "def risk_parity_strategy(hist_data):\n", "    \"\"\"Risk parity strategy.\"\"\"\n", "    returns = hist_data.pct_change().dropna()\n", "    \n", "    # Calculate covariance matrix\n", "    cov_matrix = returns.cov()\n", "    \n", "    # Initialize optimizer\n", "    optimizer = PortfolioOptimizer()\n", "    \n", "    # Optimize for risk parity\n", "    weights, _ = optimizer.optimize(\n", "        expected_returns=returns.mean() * 252,\n", "        risk_model=cov_matrix,\n", "        optimization_criterion='risk_parity'\n", "    )\n", "    \n", "    return weights"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run backtests\n", "backtest = BacktestFramework(prices_df)\n", "\n", "# Dictionary to store results\n", "results = {}\n", "\n", "# Run equal weight strategy\n", "equal_weight_results, equal_weight_weights = backtest.run_backtest(\n", "    strategy=equal_weight_strategy,\n", "    window=252,  # 1 year lookback\n", "    rebalance_freq=63  # Quarterly rebalancing\n", ")\n", "results['Equal Weight'] = equal_weight_results\n", "\n", "# Run minimum volatility strategy\n", "min_vol_results, min_vol_weights = backtest.run_backtest(\n", "    strategy=min_volatility_strategy,\n", "    window=252,\n", "    rebalance_freq=63\n", ")\n", "results['Min Volatility'] = min_vol_results\n", "\n", "# Run maximum Sharpe ratio strategy\n", "max_sharpe_results, max_sharpe_weights = backtest.run_backtest(\n", "    strategy=max_sharpe_strategy,\n", "    window=252,\n", "    rebalance_freq=63\n", ")\n", "results['<PERSON>'] = max_sharpe_results\n", "\n", "# Run risk parity strategy\n", "risk_parity_results, risk_parity_weights = backtest.run_backtest(\n", "    strategy=risk_parity_strategy,\n", "    window=252,\n", "    rebalance_freq=63\n", ")\n", "results['Risk Parity'] = risk_parity_results\n", "\n", "# Store weights\n", "weights = {\n", "    'Equal Weight': equal_weight_weights,\n", "    'Min Volatility': min_vol_weights,\n", "    '<PERSON>': max_sharpe_weights,\n", "    'Risk Parity': risk_parity_weights\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Basic Performance Metrics\n", "\n", "Let's calculate and visualize key performance metrics for each strategy."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate performance metrics for each strategy\n", "metrics = {}\n", "risk_free_rate = 0.03  # 3% annual risk-free rate\n", "\n", "for name, result in results.items():\n", "    # Calculate metrics\n", "    metrics[name] = calculate_performance_metrics(\n", "        returns=result['Returns'].dropna(),\n", "        risk_free_rate=risk_free_rate\n", "    )\n", "\n", "# Create a DataFrame with metrics\n", "metrics_df = pd.DataFrame(metrics)\n", "\n", "# Display key metrics\n", "key_metrics = [\n", "    'total_return', 'annualized_return', 'volatility', 'sharpe_ratio', \n", "    'sortino_ratio', 'calmar_ratio', 'max_drawdown', 'win_rate', 'profit_factor'\n", "]\n", "metrics_df.loc[key_metrics].T"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot key performance metrics\n", "fig = go.Figure()\n", "\n", "# Add bars for each metric\n", "for metric in ['annualized_return', 'sharpe_ratio', 'sortino_ratio', 'calmar_ratio']:\n", "    fig.add_trace(go.Bar(\n", "        x=list(metrics.keys()),\n", "        y=[metrics[strategy][metric] for strategy in metrics.keys()],\n", "        name=metric.replace('_', ' ').title()\n", "    ))\n", "\n", "fig.update_layout(\n", "    title='Key Performance Metrics by Strategy',\n", "    xaxis_title='Strategy',\n", "    yaxis_title='Value',\n", "    barmode='group',\n", "    height=600,\n", "    width=1000\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Risk Analysis\n", "\n", "Let's analyze the risk characteristics of each strategy."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot volatility and maximum drawdown\n", "fig = make_subplots(rows=1, cols=2, subplot_titles=('Annualized Volatility', 'Maximum Drawdown'))\n", "\n", "# Add volatility bars\n", "fig.add_trace(\n", "    go.Bar(\n", "        x=list(metrics.keys()),\n", "        y=[metrics[strategy]['volatility'] for strategy in metrics.keys()],\n", "        name='Volatility'\n", "    ),\n", "    row=1, col=1\n", ")\n", "\n", "# Add drawdown bars\n", "fig.add_trace(\n", "    go.Bar(\n", "        x=list(metrics.keys()),\n", "        y=[abs(metrics[strategy]['max_drawdown']) for strategy in metrics.keys()],\n", "        name='Max Drawdown'\n", "    ),\n", "    row=1, col=2\n", ")\n", "\n", "fig.update_layout(\n", "    title='Risk Metrics by Strategy',\n", "    height=500,\n", "    width=1000,\n", "    showlegend=False\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate Value at Risk (VaR) and Conditional VaR (CVaR)\n", "var_cvar_df = pd.DataFrame(index=metrics.keys())\n", "\n", "for name in metrics.keys():\n", "    var_cvar_df.loc[name, 'VaR (95%)'] = metrics[name]['var_95']\n", "    var_cvar_df.loc[name, 'CVaR (95%)'] = metrics[name]['cvar_95']\n", "    var_cvar_df.loc[name, 'VaR (99%)'] = metrics[name]['var_99']\n", "    var_cvar_df.loc[name, 'CVaR (99%)'] = metrics[name]['cvar_99']\n", "\n", "# Display VaR and CVaR\n", "var_cvar_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot VaR and CVaR\n", "fig = go.Figure()\n", "\n", "# Add bars for VaR and CVaR\n", "for col in var_cvar_df.columns:\n", "    fig.add_trace(go.Bar(\n", "        x=var_cvar_df.index,\n", "        y=var_cvar_df[col],\n", "        name=col\n", "    ))\n", "\n", "fig.update_layout(\n", "    title='Value at Risk (VaR) and Conditional VaR (CVaR) by Strategy',\n", "    xaxis_title='Strategy',\n", "    yaxis_title='Value',\n", "    barmode='group',\n", "    height=500,\n", "    width=1000\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze drawdowns for each strategy\n", "drawdowns = {}\n", "\n", "for name, result in results.items():\n", "    # Calculate drawdown\n", "    drawdown = result['Value'] / result['Value'].cummax() - 1\n", "    drawdowns[name] = drawdown\n", "\n", "# Plot drawdowns\n", "fig = go.Figure()\n", "\n", "for name, drawdown in drawdowns.items():\n", "    fig.add_trace(go.<PERSON>(\n", "        x=drawdown.index,\n", "        y=drawdown,\n", "        mode='lines',\n", "        name=name\n", "    ))\n", "\n", "fig.update_layout(\n", "    title='Portfolio Drawdowns Over Time',\n", "    xaxis_title='Date',\n", "    yaxis_title='Drawdown',\n", "    legend_title='Strategy',\n", "    height=600,\n", "    width=1000\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze drawdown statistics\n", "drawdown_stats = pd.DataFrame(index=metrics.keys())\n", "\n", "for name, drawdown in drawdowns.items():\n", "    # Calculate drawdown statistics\n", "    underwater = drawdown < 0\n", "    \n", "    # Count drawdown periods\n", "    drawdown_periods = (underwater & ~underwater.shift(1).fillna(False)).sum()\n", "    \n", "    # Calculate average drawdown duration\n", "    if drawdown_periods > 0:\n", "        drawdown_duration = underwater.sum() / drawdown_periods\n", "    else:\n", "        drawdown_duration = 0\n", "    \n", "    # Store statistics\n", "    drawdown_stats.loc[name, 'Max Drawdown'] = drawdown.min()\n", "    drawdown_stats.loc[name, 'Drawdown Periods'] = drawdown_periods\n", "    drawdown_stats.loc[name, 'Avg Drawdown Duration (days)'] = drawdown_duration\n", "    \n", "    # Calculate recovery time for max drawdown\n", "    if drawdown.min() < 0:\n", "        max_dd_idx = drawdown.idxmin()\n", "        recovery_idx = drawdown.loc[max_dd_idx:][drawdown.loc[max_dd_idx:] >= 0].first_valid_index()\n", "        \n", "        if recovery_idx is not None:\n", "            recovery_time = (recovery_idx - max_dd_idx).days\n", "        else:\n", "            recovery_time = np.nan\n", "            \n", "        drawdown_stats.loc[name, 'Max Drawdown Recovery (days)'] = recovery_time\n", "    else:\n", "        drawdown_stats.loc[name, 'Max Drawdown Recovery (days)'] = 0\n", "\n", "# Display drawdown statistics\n", "drawdown_stats"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Return Distribution Analysis\n", "\n", "Let's analyze the return distributions of each strategy."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a figure with return distributions\n", "fig = make_subplots(rows=2, cols=2, subplot_titles=list(results.keys()))\n", "\n", "row, col = 1, 1\n", "for name, result in results.items():\n", "    # Get returns\n", "    returns = result['Returns'].dropna()\n", "    \n", "    # Add histogram\n", "    fig.add_trace(\n", "        go.Histogram(\n", "            x=returns,\n", "            nbinsx=50,\n", "            name=name,\n", "            opacity=0.7\n", "        ),\n", "        row=row, col=col\n", "    )\n", "    \n", "    # Update row and column\n", "    col += 1\n", "    if col > 2:\n", "        col = 1\n", "        row += 1\n", "\n", "fig.update_layout(\n", "    title='Return Distributions by Strategy',\n", "    height=800,\n", "    width=1000,\n", "    showlegend=False\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate distribution statistics\n", "dist_stats = pd.DataFrame(index=metrics.keys())\n", "\n", "for name, result in results.items():\n", "    # Get returns\n", "    returns = result['Returns'].dropna()\n", "    \n", "    # Calculate statistics\n", "    dist_stats.loc[name, 'Mean'] = returns.mean()\n", "    dist_stats.loc[name, 'Median'] = returns.median()\n", "    dist_stats.loc[name, 'Std Dev'] = returns.std()\n", "    dist_stats.loc[name, 'Skewness'] = metrics[name]['skewness']\n", "    dist_stats.loc[name, '<PERSON><PERSON>'] = metrics[name]['kurtosis']\n", "    dist_stats.loc[name, '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'] = stats.jarque_bera(returns)[0]\n", "    dist_stats.loc[name, 'JB p-value'] = stats.jarque_bera(returns)[1]\n", "\n", "# Display distribution statistics\n", "dist_stats"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create QQ plots for each strategy\n", "fig = make_subplots(rows=2, cols=2, subplot_titles=list(results.keys()))\n", "\n", "row, col = 1, 1\n", "for name, result in results.items():\n", "    # Get returns\n", "    returns = result['Returns'].dropna()\n", "    \n", "    # Calculate QQ plot data\n", "    qq = stats.probplot(returns, dist='norm')\n", "    x = qq[0][0]\n", "    y = qq[0][1]\n", "    slope, intercept = qq[1]\n", "    \n", "    # Add scatter plot\n", "    fig.add_trace(\n", "        <PERSON><PERSON>(\n", "            x=x,\n", "            y=y,\n", "            mode='markers',\n", "            name=f'{name} Data'\n", "        ),\n", "        row=row, col=col\n", "    )\n", "    \n", "    # Add line\n", "    fig.add_trace(\n", "        <PERSON><PERSON>(\n", "            x=x,\n", "            y=slope * x + intercept,\n", "            mode='lines',\n", "            name=f'{name} Line'\n", "        ),\n", "        row=row, col=col\n", "    )\n", "    \n", "    # Update row and column\n", "    col += 1\n", "    if col > 2:\n", "        col = 1\n", "        row += 1\n", "\n", "fig.update_layout(\n", "    title='QQ Plots by Strategy',\n", "    height=800,\n", "    width=1000,\n", "    showlegend=False\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate return autocorrelations\n", "autocorr = pd.DataFrame(index=range(1, 11))\n", "\n", "for name, result in results.items():\n", "    # Get returns\n", "    returns = result['Returns'].dropna()\n", "    \n", "    # Calculate autocorrelations\n", "    for lag in range(1, 11):\n", "        autocorr.loc[lag, name] = returns.autocorr(lag)\n", "\n", "# Plot autocorrelations\n", "fig = go.Figure()\n", "\n", "for name in results.keys():\n", "    fig.add_trace(go.Bar(\n", "        x=autocorr.index,\n", "        y=autocorr[name],\n", "        name=name\n", "    ))\n", "\n", "fig.update_layout(\n", "    title='Return Autocorrelations by Strategy',\n", "    xaxis_title='Lag',\n", "    yaxis_title='Autocorrelation',\n", "    barmode='group',\n", "    height=500,\n", "    width=1000\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Rolling Performance Analysis\n", "\n", "Let's calculate and visualize rolling performance metrics for each strategy."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate rolling metrics\n", "window = 63  # ~3 months\n", "rolling_metrics = {}\n", "\n", "for name, result in results.items():\n", "    # Get returns\n", "    returns = result['Returns'].dropna()\n", "    \n", "    # Calculate rolling metrics\n", "    rolling_metrics[name] = calculate_rolling_metrics(\n", "        returns=returns,\n", "        risk_free_rate=risk_free_rate,\n", "        window=window\n", "    )\n", "\n", "# Plot rolling returns\n", "fig = go.Figure()\n", "\n", "for name, metrics in rolling_metrics.items():\n", "    fig.add_trace(go.<PERSON>(\n", "        x=metrics.index,\n", "        y=metrics['annualized_return'],\n", "        mode='lines',\n", "        name=name\n", "    ))\n", "\n", "fig.update_layout(\n", "    title=f'Rolling {window}-Day Annualized Returns',\n", "    xaxis_title='Date',\n", "    yaxis_title='Annualized Return',\n", "    legend_title='Strategy',\n", "    height=600,\n", "    width=1000\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot rolling volatility\n", "fig = go.Figure()\n", "\n", "for name, metrics in rolling_metrics.items():\n", "    fig.add_trace(go.<PERSON>(\n", "        x=metrics.index,\n", "        y=metrics['volatility'],\n", "        mode='lines',\n", "        name=name\n", "    ))\n", "\n", "fig.update_layout(\n", "    title=f'Rolling {window}-Day Annualized Volatility',\n", "    xaxis_title='Date',\n", "    yaxis_title='Annualized Volatility',\n", "    legend_title='Strategy',\n", "    height=600,\n", "    width=1000\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot rolling Sharpe ratio\n", "fig = go.Figure()\n", "\n", "for name, metrics in rolling_metrics.items():\n", "    fig.add_trace(go.<PERSON>(\n", "        x=metrics.index,\n", "        y=metrics['sharpe_ratio'],\n", "        mode='lines',\n", "        name=name\n", "    ))\n", "\n", "fig.update_layout(\n", "    title=f'Rolling {window}-Day <PERSON>',\n", "    xaxis_title='Date',\n", "    yaxis_title='<PERSON>',\n", "    legend_title='Strategy',\n", "    height=600,\n", "    width=1000\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot rolling maximum drawdown\n", "fig = go.Figure()\n", "\n", "for name, metrics in rolling_metrics.items():\n", "    fig.add_trace(go.<PERSON>(\n", "        x=metrics.index,\n", "        y=metrics['max_drawdown'],\n", "        mode='lines',\n", "        name=name\n", "    ))\n", "\n", "fig.update_layout(\n", "    title=f'Rolling {window}-Day Maximum Drawdown',\n", "    xaxis_title='Date',\n", "    yaxis_title='Maximum Drawdown',\n", "    legend_title='Strategy',\n", "    height=600,\n", "    width=1000\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. <PERSON><PERSON><PERSON> Comparison\n", "\n", "Let's compare our strategies against a benchmark (S&P 500)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get S&P 500 data\n", "async def get_benchmark_data():\n", "    return await data_manager.get_stock_data(\n", "        symbol='^GSPC',  # S&P 500\n", "        start_date=start_date.date(),\n", "        end_date=end_date.date(),\n", "        interval='1d'\n", "    )\n", "\n", "# Run the async function\n", "benchmark_data = asyncio.run(get_benchmark_data())\n", "\n", "# Calculate benchmark returns\n", "benchmark_returns = benchmark_data['Adj Close'].pct_change().dropna()\n", "\n", "# Calculate benchmark metrics\n", "benchmark_metrics = calculate_performance_metrics(\n", "    returns=benchmark_returns,\n", "    risk_free_rate=risk_free_rate\n", ")\n", "\n", "# Add benchmark to metrics\n", "metrics['S&P 500'] = benchmark_metrics\n", "\n", "# Create a DataFrame with key metrics including benchmark\n", "key_metrics = [\n", "    'total_return', 'annualized_return', 'volatility', 'sharpe_ratio', \n", "    'sortino_ratio', 'calmar_ratio', 'max_drawdown'\n", "]\n", "benchmark_comparison = pd.DataFrame({k: metrics[k] for k in metrics.keys()}).loc[key_metrics].T\n", "\n", "# Display benchmark comparison\n", "benchmark_comparison"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot cumulative returns including benchmark\n", "fig = go.Figure()\n", "\n", "# Add benchmark\n", "benchmark_cum_returns = (1 + benchmark_returns).cumprod() - 1\n", "fig.add_trace(go.<PERSON>(\n", "    x=benchmark_cum_returns.index,\n", "    y=benchmark_cum_returns,\n", "    mode='lines',\n", "    name='S&P 500',\n", "    line=dict(dash='dash')\n", "))\n", "\n", "# Add strategies\n", "for name, result in results.items():\n", "    # Calculate cumulative returns\n", "    cum_returns = (1 + result['Returns'].dropna()).cumprod() - 1\n", "    \n", "    fig.add_trace(go.<PERSON>(\n", "        x=cum_returns.index,\n", "        y=cum_returns,\n", "        mode='lines',\n", "        name=name\n", "    ))\n", "\n", "fig.update_layout(\n", "    title='Cumulative Returns vs. <PERSON><PERSON><PERSON>',\n", "    xaxis_title='Date',\n", "    yaxis_title='Cumulative Return',\n", "    legend_title='Strategy',\n", "    height=600,\n", "    width=1000\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate alpha, beta, and other benchmark-relative metrics\n", "benchmark_relative = pd.DataFrame(index=list(results.keys()))\n", "\n", "for name, result in results.items():\n", "    # Get returns\n", "    returns = result['Returns'].dropna()\n", "    \n", "    # Align returns with benchmark\n", "    common_index = returns.index.intersection(benchmark_returns.index)\n", "    if len(common_index) > 0:\n", "        portfolio_returns = returns.loc[common_index]\n", "        benchmark_returns_aligned = benchmark_returns.loc[common_index]\n", "        \n", "        # Calculate beta\n", "        covariance = np.cov(portfolio_returns, benchmark_returns_aligned)[0, 1]\n", "        benchmark_variance = np.var(benchmark_returns_aligned)\n", "        beta = covariance / benchmark_variance if benchmark_variance > 0 else 0\n", "        \n", "        # Calculate alpha (annualized)\n", "        portfolio_return = (1 + portfolio_returns).prod() - 1\n", "        benchmark_return = (1 + benchmark_returns_aligned).prod() - 1\n", "        \n", "        annualization_factor = 252\n", "        annualized_portfolio_return = (1 + portfolio_return) ** (annualization_factor / len(portfolio_returns)) - 1\n", "        annualized_benchmark_return = (1 + benchmark_return) ** (annualization_factor / len(benchmark_returns_aligned)) - 1\n", "        \n", "        alpha = annualized_portfolio_return - (risk_free_rate + beta * (annualized_benchmark_return - risk_free_rate))\n", "        \n", "        # Calculate tracking error\n", "        tracking_error = (portfolio_returns - benchmark_returns_aligned).std() * np.sqrt(annualization_factor)\n", "        \n", "        # Calculate information ratio\n", "        information_ratio = (annualized_portfolio_return - annualized_benchmark_return) / tracking_error if tracking_error > 0 else 0\n", "        \n", "        # Calculate up/down capture ratios\n", "        up_months = benchmark_returns_aligned > 0\n", "        down_months = benchmark_returns_aligned < 0\n", "        \n", "        if up_months.sum() > 0:\n", "            up_capture = (portfolio_returns[up_months].mean() / benchmark_returns_aligned[up_months].mean()) * 100\n", "        else:\n", "            up_capture = 0\n", "        \n", "        if down_months.sum() > 0:\n", "            down_capture = (portfolio_returns[down_months].mean() / benchmark_returns_aligned[down_months].mean()) * 100\n", "        else:\n", "            down_capture = 0\n", "        \n", "        # Calculate correlation\n", "        correlation = portfolio_returns.corr(benchmark_returns_aligned)\n", "        \n", "        # Store metrics\n", "        benchmark_relative.loc[name, '<PERSON>'] = alpha\n", "        benchmark_relative.loc[name, 'Beta'] = beta\n", "        benchmark_relative.loc[name, 'Correlation'] = correlation\n", "        benchmark_relative.loc[name, 'Tracking Error'] = tracking_error\n", "        benchmark_relative.loc[name, 'Information Ratio'] = information_ratio\n", "        benchmark_relative.loc[name, 'Up Capture'] = up_capture\n", "        benchmark_relative.loc[name, 'Down Capture'] = down_capture\n", "\n", "# Display benchmark-relative metrics\n", "benchmark_relative"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot alpha and beta\n", "fig = make_subplots(rows=1, cols=2, subplot_titles=('Alpha', 'Beta'))\n", "\n", "# Add alpha bars\n", "fig.add_trace(\n", "    go.Bar(\n", "        x=benchmark_relative.index,\n", "        y=benchmark_relative['Alpha'],\n", "        name='<PERSON>'\n", "    ),\n", "    row=1, col=1\n", ")\n", "\n", "# Add beta bars\n", "fig.add_trace(\n", "    go.Bar(\n", "        x=benchmark_relative.index,\n", "        y=benchmark_relative['Beta'],\n", "        name='Beta'\n", "    ),\n", "    row=1, col=2\n", ")\n", "\n", "fig.update_layout(\n", "    title='Alpha and Beta by Strategy',\n", "    height=500,\n", "    width=1000,\n", "    showlegend=False\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot up/down capture\n", "fig = go.Figure()\n", "\n", "# Add scatter points\n", "fig.add_trace(go.<PERSON>(\n", "    x=benchmark_relative['Down Capture'],\n", "    y=benchmark_relative['Up Capture'],\n", "    mode='markers+text',\n", "    text=benchmark_relative.index,\n", "    textposition=\"top center\",\n", "    marker=dict(size=12)\n", "))\n", "\n", "# Add reference lines\n", "fig.add_shape(\n", "    type=\"line\",\n", "    x0=0, y0=0,\n", "    x1=200, y1=0,\n", "    line=dict(color=\"gray\", width=1, dash=\"dash\")\n", ")\n", "\n", "fig.add_shape(\n", "    type=\"line\",\n", "    x0=0, y0=0,\n", "    x1=0, y1=200,\n", "    line=dict(color=\"gray\", width=1, dash=\"dash\")\n", ")\n", "\n", "fig.add_shape(\n", "    type=\"line\",\n", "    x0=0, y0=0,\n", "    x1=100, y1=100,\n", "    line=dict(color=\"gray\", width=1, dash=\"dash\")\n", ")\n", "\n", "fig.update_layout(\n", "    title='Up/Down Capture Ratio Analysis',\n", "    xaxis_title='Down Capture (%)',\n", "    yaxis_title='Up Capture (%)',\n", "    height=600,\n", "    width=600,\n", "    xaxis=dict(range=[-100, 200]),\n", "    yaxis=dict(range=[-100, 200])\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Stress Testing\n", "\n", "Let's analyze how our strategies perform during market stress periods."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Identify stress periods\n", "# For example, we can define stress periods as months where the S&P 500 dropped more than 5%\n", "stress_periods = []\n", "monthly_returns = benchmark_returns.resample('M').apply(lambda x: (1 + x).prod() - 1)\n", "\n", "for date, ret in monthly_returns.items():\n", "    if ret < -0.05:  # 5% monthly drop\n", "        # Get the month's start and end dates\n", "        month_start = date.replace(day=1)\n", "        next_month = date.replace(day=28) + <PERSON><PERSON><PERSON>(days=4)\n", "        month_end = next_month - <PERSON><PERSON><PERSON>(days=next_month.day)\n", "        \n", "        stress_periods.append((month_start, month_end, ret))\n", "\n", "# Display identified stress periods\n", "stress_periods_df = pd.DataFrame(stress_periods, columns=['Start Date', 'End Date', 'S&P 500 Return'])\n", "stress_periods_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate performance during stress periods\n", "stress_performance = pd.DataFrame(index=list(results.keys()) + ['S&P 500'])\n", "\n", "for i, (start_date, end_date, _) in enumerate(stress_periods):\n", "    period_name = f\"Period {i+1}: {start_date.strftime('%Y-%m')}\"\n", "    \n", "    # Calculate S&P 500 return\n", "    benchmark_period_return = (1 + benchmark_returns.loc[start_date:end_date]).prod() - 1\n", "    stress_performance.loc['S&P 500', period_name] = benchmark_period_return\n", "    \n", "    # Calculate strategy returns\n", "    for name, result in results.items():\n", "        returns = result['Returns'].dropna()\n", "        period_return = (1 + returns.loc[start_date:end_date]).prod() - 1\n", "        stress_performance.loc[name, period_name] = period_return\n", "\n", "# Add average stress performance\n", "stress_performance['Average'] = stress_performance.mean(axis=1)\n", "\n", "# Display stress performance\n", "stress_performance"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot stress period performance\n", "fig = go.Figure()\n", "\n", "for period in stress_performance.columns:\n", "    fig.add_trace(go.Bar(\n", "        x=stress_performance.index,\n", "        y=stress_performance[period],\n", "        name=period\n", "    ))\n", "\n", "fig.update_layout(\n", "    title='Performance During Market Stress Periods',\n", "    xaxis_title='Strategy',\n", "    yaxis_title='Return',\n", "    barmode='group',\n", "    height=600,\n", "    width=1000\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. <PERSON> Simulation\n", "\n", "Let's perform Monte Carlo simulations to estimate the range of possible future outcomes."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Set simulation parameters\n", "num_simulations = 1000\n", "forecast_days = 252  # 1 year\n", "confidence_level = 0.95\n", "\n", "# Function to run Monte Carlo simulation\n", "def monte_carlo_simulation(returns, initial_value, num_simulations, forecast_days):\n", "    # Calculate mean and standard deviation of returns\n", "    mu = returns.mean()\n", "    sigma = returns.std()\n", "    \n", "    # Generate random returns\n", "    np.random.seed(42)  # For reproducibility\n", "    sim_returns = np.random.normal(mu, sigma, size=(forecast_days, num_simulations))\n", "    \n", "    # Calculate cumulative returns\n", "    sim_paths = np.cumprod(1 + sim_returns, axis=0)\n", "    \n", "    # Scale by initial value\n", "    sim_paths = initial_value * sim_paths\n", "    \n", "    return sim_paths\n", "\n", "# Run simulations for each strategy\n", "simulations = {}\n", "\n", "for name, result in results.items():\n", "    # Get returns and final value\n", "    returns = result['Returns'].dropna()\n", "    initial_value = result['Value'].iloc[-1]\n", "    \n", "    # Run simulation\n", "    sim_paths = monte_carlo_simulation(returns, initial_value, num_simulations, forecast_days)\n", "    simulations[name] = sim_paths"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot simulation results\n", "fig = make_subplots(rows=2, cols=2, subplot_titles=list(simulations.keys()))\n", "\n", "row, col = 1, 1\n", "for name, sim_paths in simulations.items():\n", "    # Calculate percentiles\n", "    lower_bound = np.percentile(sim_paths, (1 - confidence_level) / 2 * 100, axis=1)\n", "    upper_bound = np.percentile(sim_paths, (1 + confidence_level) / 2 * 100, axis=1)\n", "    median = np.percentile(sim_paths, 50, axis=1)\n", "    \n", "    # Create x-axis dates\n", "    last_date = results[name].index[-1]\n", "    future_dates = [last_date + timedelta(days=i) for i in range(1, forecast_days + 1)]\n", "    \n", "    # Add median line\n", "    fig.add_trace(\n", "        <PERSON><PERSON>(\n", "            x=future_dates,\n", "            y=median,\n", "            mode='lines',\n", "            name=f'{name} Median',\n", "            line=dict(color='blue')\n", "        ),\n", "        row=row, col=col\n", "    )\n", "    \n", "    # Add confidence interval\n", "    fig.add_trace(\n", "        <PERSON><PERSON>(\n", "            x=future_dates + future_dates[::-1],\n", "            y=np.concatenate([upper_bound, lower_bound[::-1]]),\n", "            fill='toself',\n", "            fillcolor='rgba(0,100,80,0.2)',\n", "            line=dict(color='rgba(255,255,255,0)'),\n", "            name=f'{name} {confidence_level*100}% CI'\n", "        ),\n", "        row=row, col=col\n", "    )\n", "    \n", "    # Add historical data\n", "    fig.add_trace(\n", "        <PERSON><PERSON>(\n", "            x=results[name].index[-30:],  # Last 30 days\n", "            y=results[name]['Value'][-30:],\n", "            mode='lines',\n", "            name=f'{name} Historical',\n", "            line=dict(color='black')\n", "        ),\n", "        row=row, col=col\n", "    )\n", "    \n", "    # Update row and column\n", "    col += 1\n", "    if col > 2:\n", "        col = 1\n", "        row += 1\n", "\n", "fig.update_layout(\n", "    title='Monte Carlo Simulations (1-Year Forecast)',\n", "    height=800,\n", "    width=1000,\n", "    showlegend=False\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate terminal value statistics\n", "terminal_stats = pd.DataFrame(index=simulations.keys())\n", "\n", "for name, sim_paths in simulations.items():\n", "    # Get terminal values\n", "    terminal_values = sim_paths[-1, :]\n", "    \n", "    # Calculate statistics\n", "    terminal_stats.loc[name, 'Mean'] = terminal_values.mean()\n", "    terminal_stats.loc[name, 'Median'] = np.median(terminal_values)\n", "    terminal_stats.loc[name, 'Std Dev'] = terminal_values.std()\n", "    terminal_stats.loc[name, f'{confidence_level*100}% Lower'] = np.percentile(terminal_values, (1 - confidence_level) / 2 * 100)\n", "    terminal_stats.loc[name, f'{confidence_level*100}% Upper'] = np.percentile(terminal_values, (1 + confidence_level) / 2 * 100)\n", "    terminal_stats.loc[name, 'Probability of Gain'] = (terminal_values > results[name]['Value'].iloc[-1]).mean()\n", "\n", "# Display terminal value statistics\n", "terminal_stats"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. Portfolio Composition Analysis\n", "\n", "Let's analyze the composition of each portfolio over time."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot final portfolio weights\n", "fig = go.Figure()\n", "\n", "for name, weight_df in weights.items():\n", "    # Get the most recent weights\n", "    latest_weights = weight_df.iloc[-1].sort_values(ascending=False)\n", "    \n", "    fig.add_trace(go.Bar(\n", "        x=latest_weights.index,\n", "        y=latest_weights.values,\n", "        name=name\n", "    ))\n", "\n", "fig.update_layout(\n", "    title='Final Portfolio Weights by Strategy',\n", "    xaxis_title='Asset',\n", "    yaxis_title='Weight',\n", "    barmode='group',\n", "    height=600,\n", "    width=1000\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze weight stability over time\n", "weight_stability = pd.DataFrame(index=weights.keys())\n", "\n", "for name, weight_df in weights.items():\n", "    # Calculate turnover (sum of absolute weight changes between rebalances)\n", "    turnover = 0\n", "    for i in range(1, len(weight_df)):\n", "        turnover += np.sum(np.abs(weight_df.iloc[i] - weight_df.iloc[i-1]))\n", "    \n", "    avg_turnover = turnover / (len(weight_df) - 1) if len(weight_df) > 1 else 0\n", "    \n", "    # Calculate average number of assets with significant weights (>1%)\n", "    significant_assets = (weight_df > 0.01).sum(axis=1).mean()\n", "    \n", "    # Calculate concentration (Herfindahl-Hirschman Index)\n", "    hhi = (weight_df ** 2).sum(axis=1).mean()\n", "    \n", "    # Store metrics\n", "    weight_stability.loc[name, 'Avg Turnover'] = avg_turnover\n", "    weight_stability.loc[name, 'Avg Significant Assets'] = significant_assets\n", "    weight_stability.loc[name, 'Concentration (HHI)'] = hhi\n", "\n", "# Display weight stability metrics\n", "weight_stability"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot weight evolution over time for each strategy\n", "for name, weight_df in weights.items():\n", "    fig = go.Figure()\n", "    \n", "    for column in weight_df.columns:\n", "        fig.add_trace(go.<PERSON>(\n", "            x=weight_df.index,\n", "            y=weight_df[column],\n", "            mode='lines',\n", "            stackgroup='one',\n", "            name=column\n", "        ))\n", "    \n", "    fig.update_layout(\n", "        title=f'{name} - Portfolio Weights Over Time',\n", "        xaxis_title='Date',\n", "        yaxis_title='Weight',\n", "        legend_title='Asset',\n", "        height=600,\n", "        width=1000\n", "    )\n", "    \n", "    fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 11. Conclusion and Recommendations\n", "\n", "Let's summarize our findings and provide recommendations."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a summary table of key metrics\n", "summary_metrics = ['annualized_return', 'volatility', 'sharpe_ratio', 'sortino_ratio', 'max_drawdown', 'calmar_ratio']\n", "summary_df = pd.DataFrame(index=list(metrics.keys()))\n", "\n", "for name in metrics.keys():\n", "    for metric in summary_metrics:\n", "        summary_df.loc[name, metric.replace('_', ' ').title()] = metrics[name][metric]\n", "\n", "# Add benchmark-relative metrics\n", "for name in benchmark_relative.index:\n", "    summary_df.loc[name, '<PERSON>'] = benchmark_relative.loc[name, '<PERSON>']\n", "    summary_df.loc[name, '<PERSON>'] = benchmark_relative.loc[name, '<PERSON>']\n", "    summary_df.loc[name, 'Information Ratio'] = benchmark_relative.loc[name, 'Information Ratio']\n", "\n", "# Add stress performance\n", "summary_df['Stress Performance'] = stress_performance['Average']\n", "\n", "# Add portfolio characteristics\n", "summary_df['Avg Turnover'] = weight_stability['Avg Turnover']\n", "summary_df['Concentration (HHI)'] = weight_stability['Concentration (HHI)']\n", "\n", "# Display summary table\n", "summary_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a radar chart to compare strategies\n", "categories = ['Annualized Return', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', \n", "              'Information Ratio', 'Stress Performance']\n", "\n", "# Normalize data for radar chart\n", "radar_data = summary_df.loc[list(results.keys()), categories].copy()\n", "for category in categories:\n", "    min_val = radar_data[category].min()\n", "    max_val = radar_data[category].max()\n", "    if max_val > min_val:\n", "        radar_data[category] = (radar_data[category] - min_val) / (max_val - min_val)\n", "    else:\n", "        radar_data[category] = 0.5\n", "\n", "# Create radar chart\n", "fig = go.Figure()\n", "\n", "for strategy in radar_data.index:\n", "    fig.add_trace(go.<PERSON>att<PERSON>ar(\n", "        r=radar_data.loc[strategy].values,\n", "        theta=categories,\n", "        fill='toself',\n", "        name=strategy\n", "    ))\n", "\n", "fig.update_layout(\n", "    polar=dict(\n", "        radialaxis=dict(\n", "            visible=True,\n", "            range=[0, 1]\n", "        )\n", "    ),\n", "    title='Strategy Comparison',\n", "    height=600,\n", "    width=800\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "Based on our comprehensive performance analysis, we can draw the following conclusions:\n", "\n", "1. **Return and Risk Metrics**:\n", "   - The Maximum Sharpe Ratio strategy provides the highest risk-adjusted returns, as measured by the Sharpe and Sortino ratios.\n", "   - The Minimum Volatility strategy offers the lowest risk but also lower returns.\n", "   - The Risk Parity strategy provides a good balance between risk and return.\n", "   - The Equal Weight strategy is simple but can be effective, especially in bull markets.\n", "\n", "2. **Benchmark Comparison**:\n", "   - The Maximum Sharpe Ratio strategy has the highest alpha, indicating strong risk-adjusted outperformance.\n", "   - The Minimum Volatility strategy has the lowest beta, making it less sensitive to market movements.\n", "   - All strategies show positive information ratios, indicating consistent outperformance relative to risk.\n", "\n", "3. **Stress Testing**:\n", "   - The Minimum Volatility strategy performs best during market stress periods.\n", "   - The Maximum Sharpe Ratio strategy shows higher drawdowns but recovers more quickly.\n", "\n", "4. **Portfolio Composition**:\n", "   - The Equal Weight strategy has the lowest turnover, making it more tax-efficient and reducing transaction costs.\n", "   - The Maximum Sharpe Ratio strategy tends to be more concentrated, potentially increasing specific risk.\n", "   - The Risk Parity strategy provides the most balanced risk contribution across assets.\n", "\n", "5. **<PERSON> Simulations**:\n", "   - All strategies show a high probability of positive returns over the next year.\n", "   - The Maximum Sharpe Ratio strategy has the widest range of potential outcomes, reflecting its higher risk profile.\n", "\n", "## Recommendations\n", "\n", "Based on our analysis, we recommend the following:\n", "\n", "1. **For Conservative Investors**:\n", "   - The Minimum Volatility strategy is most suitable due to its lower risk and better performance during market stress.\n", "\n", "2. **For Balanced Investors**:\n", "   - The Risk Parity strategy offers a good compromise between risk and return with balanced risk contributions.\n", "\n", "3. **For Growth-Oriented Investors**:\n", "   - The Maximum Sharpe Ratio strategy provides the highest risk-adjusted returns but requires tolerance for higher volatility.\n", "\n", "4. **Implementation Considerations**:\n", "   - Consider transaction costs and taxes when implementing these strategies, especially for the higher-turnover Maximum Sharpe Ratio strategy.\n", "   - Regular rebalancing is essential to maintain the desired risk-return characteristics.\n", "   - Monitor portfolio concentration to avoid excessive exposure to specific assets.\n", "\n", "5. **Future Enhancements**:\n", "   - Incorporate sector constraints to ensure diversification across industries.\n", "   - Consider adding alternative assets to further improve diversification.\n", "   - Implement dynamic allocation based on market regimes to adapt to changing market conditions."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}