"""
Authentication service.
"""
from datetime import <PERSON><PERSON><PERSON>
from typing import Op<PERSON>

from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.db.database import get_db
from app.db.models import User
from app.middleware.auth import create_access_token, get_password_hash, verify_password
from app.settings import get_settings

# Get settings
settings = get_settings()


class AuthService:
    """
    Authentication service.
    
    This service provides methods for user authentication and authorization.
    """
    
    def authenticate_user(
        self,
        db: Session,
        username: str,
        password: str
    ) -> Optional[User]:
        """
        Authenticate a user.
        
        Args:
            db: Database session
            username: Username
            password: Password
            
        Returns:
            User if authentication is successful, None otherwise
        """
        # Get user from database
        user = db.query(User).filter(User.username == username).first()
        
        # Check if user exists and password is correct
        if not user or not verify_password(password, user.hashed_password):
            return None
        
        return user
    
    def create_user(
        self,
        db: Session,
        username: str,
        email: str,
        password: str
    ) -> User:
        """
        Create a new user.
        
        Args:
            db: Database session
            username: Username
            email: Email
            password: Password
            
        Returns:
            Created user
            
        Raises:
            HTTPException: If the username or email is already taken
        """
        # Check if username is already taken
        if db.query(User).filter(User.username == username).first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already taken"
            )
        
        # Check if email is already taken
        if db.query(User).filter(User.email == email).first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
        
        # Create user
        user = User(
            username=username,
            email=email,
            hashed_password=get_password_hash(password)
        )
        
        # Save user to database
        db.add(user)
        db.commit()
        db.refresh(user)
        
        return user
    
    def create_access_token_for_user(self, user: User) -> str:
        """
        Create an access token for a user.
        
        Args:
            user: User
            
        Returns:
            Access token
        """
        # Create token data
        token_data = {
            "sub": str(user.id),
            "username": user.username
        }
        
        # Create access token
        access_token = create_access_token(
            data=token_data,
            expires_delta=timedelta(minutes=settings.security.access_token_expire_minutes)
        )
        
        return access_token


# Create a singleton instance
auth_service = AuthService()
