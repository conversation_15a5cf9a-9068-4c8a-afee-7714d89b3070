"""
Stock data collection utilities.
"""
from datetime import date
from typing import Dict, Optional

import pandas as pd

from app.core.data_collection.data_manager import data_manager
from app.utils.logging import logger


async def get_historical_data(
    symbol: str,
    start_date: date,
    end_date: date,
    interval: str = "1d",
    source: Optional[str] = None
) -> pd.DataFrame:
    """
    Get historical stock data for a symbol.
    
    Args:
        symbol: The stock symbol
        start_date: Start date for historical data
        end_date: End date for historical data
        interval: Data interval (e.g., '1d', '1wk', '1mo')
        source: Data source to use (if None, uses default)
        
    Returns:
        DataFrame with historical stock data
    """
    try:
        # Get stock data from data manager
        df = await data_manager.get_stock_data(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            interval=interval,
            source=source
        )
        
        # Return the DataFrame
        return df
    except Exception as e:
        logger.error(f"Error getting historical data for {symbol}: {str(e)}")
        # Return empty DataFrame
        return pd.DataFrame()
