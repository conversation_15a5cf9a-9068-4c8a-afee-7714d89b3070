"""
Unit tests for portfolio allocation strategies.
"""
import unittest
import pandas as pd
import numpy as np

from app.core.portfolio.allocation.equal_weight import EqualWeightStrategy
from app.core.portfolio.allocation.market_cap import MarketCapWeightStrategy
from app.core.portfolio.allocation.risk_parity import RiskParityStrategy


class TestAllocationStrategies(unittest.TestCase):
    """Test cases for portfolio allocation strategies."""

    def setUp(self):
        """Set up test data."""
        # Create sample returns data
        self.dates = pd.date_range(start='2023-01-01', periods=100, freq='D')
        
        # Create returns for 5 assets with different characteristics
        np.random.seed(42)  # For reproducibility
        
        # Asset 1: Low volatility
        asset1_returns = np.random.normal(0.0005, 0.01, 100)
        
        # Asset 2: Medium volatility
        asset2_returns = np.random.normal(0.001, 0.015, 100)
        
        # Asset 3: High volatility
        asset3_returns = np.random.normal(0.0015, 0.02, 100)
        
        # Asset 4: Negative correlation with Asset 3
        asset4_returns = -0.5 * asset3_returns + np.random.normal(0.0005, 0.01, 100)
        
        # Asset 5: Uncorrelated
        asset5_returns = np.random.normal(0.001, 0.015, 100)
        
        # Create returns DataFrame
        self.returns = pd.DataFrame({
            'Asset1': asset1_returns,
            'Asset2': asset2_returns,
            'Asset3': asset3_returns,
            'Asset4': asset4_returns,
            'Asset5': asset5_returns
        }, index=self.dates)
        
        # Create prices DataFrame (starting at 100 and applying returns)
        prices = 100 * (1 + self.returns).cumprod()
        self.prices = prices
        
        # Create market caps dictionary
        self.market_caps = {
            'Asset1': 1000000000,  # 1B
            'Asset2': 500000000,   # 500M
            'Asset3': 2000000000,  # 2B
            'Asset4': 300000000,   # 300M
            'Asset5': 1500000000   # 1.5B
        }

    def test_equal_weight_strategy(self):
        """Test equal weight allocation strategy."""
        # Create strategy
        strategy = EqualWeightStrategy()
        
        # Allocate weights using returns
        weights = strategy.allocate(returns=self.returns)
        
        # Check if weights sum to 1
        self.assertAlmostEqual(sum(weights.values()), 1.0)
        
        # Check if all weights are equal
        expected_weight = 1.0 / len(self.returns.columns)
        for asset, weight in weights.items():
            self.assertAlmostEqual(weight, expected_weight)
        
        # Allocate weights using prices
        weights_prices = strategy.allocate(prices=self.prices)
        
        # Check if weights from prices are the same as from returns
        for asset in weights:
            self.assertAlmostEqual(weights[asset], weights_prices[asset])
        
        # Test rebalancing (should be the same as initial allocation for equal weight)
        current_weights = {
            'Asset1': 0.3,
            'Asset2': 0.1,
            'Asset3': 0.2,
            'Asset4': 0.15,
            'Asset5': 0.25
        }
        rebalanced_weights = strategy.rebalance(current_weights, returns=self.returns)
        
        # Check if rebalanced weights are equal
        for asset, weight in rebalanced_weights.items():
            self.assertAlmostEqual(weight, expected_weight)

    def test_market_cap_weight_strategy(self):
        """Test market cap weighted allocation strategy."""
        # Create strategy
        strategy = MarketCapWeightStrategy()
        
        # Allocate weights using market caps
        weights = strategy.allocate(returns=self.returns, market_caps=self.market_caps)
        
        # Check if weights sum to 1
        self.assertAlmostEqual(sum(weights.values()), 1.0)
        
        # Calculate expected weights
        total_market_cap = sum(self.market_caps.values())
        expected_weights = {asset: cap / total_market_cap for asset, cap in self.market_caps.items()}
        
        # Check if weights match expected weights
        for asset, weight in weights.items():
            self.assertAlmostEqual(weight, expected_weights[asset])
        
        # Test with missing market cap
        incomplete_market_caps = {
            'Asset1': 1000000000,
            'Asset2': 500000000,
            'Asset3': 2000000000
        }
        weights_incomplete = strategy.allocate(returns=self.returns, market_caps=incomplete_market_caps)
        
        # Check if only assets with market caps are included
        self.assertEqual(len(weights_incomplete), 3)
        self.assertNotIn('Asset4', weights_incomplete)
        self.assertNotIn('Asset5', weights_incomplete)
        
        # Check if weights sum to 1
        self.assertAlmostEqual(sum(weights_incomplete.values()), 1.0)
        
        # Test rebalancing
        current_weights = {
            'Asset1': 0.3,
            'Asset2': 0.1,
            'Asset3': 0.2,
            'Asset4': 0.15,
            'Asset5': 0.25
        }
        rebalanced_weights = strategy.rebalance(current_weights, returns=self.returns, market_caps=self.market_caps)
        
        # Check if rebalanced weights match expected weights
        for asset, weight in rebalanced_weights.items():
            self.assertAlmostEqual(weight, expected_weights[asset])

    def test_risk_parity_strategy(self):
        """Test risk parity allocation strategy."""
        # Create strategy
        strategy = RiskParityStrategy()
        
        # Allocate weights using returns
        weights = strategy.allocate(returns=self.returns)
        
        # Check if weights sum to 1
        self.assertAlmostEqual(sum(weights.values()), 1.0)
        
        # In risk parity, assets with lower volatility should have higher weights
        # Calculate volatilities
        vols = self.returns.std()
        
        # Asset1 has lowest volatility, so it should have highest weight
        self.assertEqual(vols.idxmin(), 'Asset1')
        self.assertEqual(max(weights, key=weights.get), 'Asset1')
        
        # Asset3 has highest volatility, so it should have lowest weight
        self.assertEqual(vols.idxmax(), 'Asset3')
        self.assertEqual(min(weights, key=weights.get), 'Asset3')
        
        # Test with risk target
        risk_target = 0.1  # 10% annualized volatility
        weights_target = strategy.allocate(returns=self.returns, risk_target=risk_target)
        
        # Check if weights with risk target sum to more than 1 (leveraged)
        self.assertGreater(sum(weights_target.values()), 1.0)
        
        # Test rebalancing
        current_weights = {
            'Asset1': 0.3,
            'Asset2': 0.1,
            'Asset3': 0.2,
            'Asset4': 0.15,
            'Asset5': 0.25
        }
        rebalanced_weights = strategy.rebalance(current_weights, returns=self.returns)
        
        # Check if rebalanced weights sum to 1
        self.assertAlmostEqual(sum(rebalanced_weights.values()), 1.0)


if __name__ == '__main__':
    unittest.main()
