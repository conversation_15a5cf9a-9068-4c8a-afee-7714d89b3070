"""
Chart pattern recognition.
"""
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
from scipy.signal import argrelextrema

from app.utils.logging import logger


class ChartPatterns:
    """
    Chart pattern recognition.
    
    This class provides methods for identifying common chart patterns
    in price data.
    """
    
    def __init__(self):
        """Initialize the chart pattern recognizer."""
        pass
    
    def recognize_patterns(
        self,
        data: pd.DataFrame,
        patterns: Optional[List[str]] = None,
        column: str = "close",
        window: int = 5
    ) -> pd.DataFrame:
        """
        Recognize chart patterns in the data.
        
        Args:
            data: DataFrame with price data
            patterns: List of patterns to recognize (if None, recognizes all)
            column: Column to use for pattern recognition
            window: Window size for peak detection
            
        Returns:
            DataFrame with pattern recognition columns
        """
        # Validate data
        if not self._validate_data(data, column):
            logger.warning(f"Invalid data for chart pattern recognition")
            return data
        
        # Make a copy of the data
        result = data.copy()
        
        # Define available patterns
        available_patterns = {
            "double_top": self._recognize_double_top,
            "double_bottom": self._recognize_double_bottom,
            "head_and_shoulders": self._recognize_head_and_shoulders,
            "inverse_head_and_shoulders": self._recognize_inverse_head_and_shoulders,
            "triangle": self._recognize_triangle,
            "channel": self._recognize_channel
        }
        
        # Determine which patterns to recognize
        if patterns is None:
            patterns = list(available_patterns.keys())
        else:
            # Validate requested patterns
            patterns = [p for p in patterns if p in available_patterns]
        
        # Find local peaks and troughs
        result = self._find_peaks_and_troughs(result, column, window)
        
        # Recognize each pattern
        for pattern in patterns:
            if pattern in available_patterns:
                result = available_patterns[pattern](result, column)
        
        return result
    
    def _validate_data(self, data: pd.DataFrame, column: str) -> bool:
        """
        Validate input data.
        
        Args:
            data: DataFrame to validate
            column: Column to validate
            
        Returns:
            True if the data is valid, False otherwise
        """
        # Check if DataFrame is empty
        if data is None or data.empty:
            return False
        
        # Check if column exists
        if column not in data.columns:
            return False
        
        return True
    
    def _find_peaks_and_troughs(
        self,
        data: pd.DataFrame,
        column: str = "close",
        window: int = 5
    ) -> pd.DataFrame:
        """
        Find local peaks and troughs in the data.
        
        Args:
            data: DataFrame with price data
            column: Column to use for peak detection
            window: Window size for peak detection
            
        Returns:
            DataFrame with peak and trough columns
        """
        # Make a copy of the data
        result = data.copy()
        
        # Find local maxima (peaks)
        max_idx = argrelextrema(result[column].values, np.greater, order=window)[0]
        result["is_peak"] = False
        result.loc[max_idx, "is_peak"] = True
        
        # Find local minima (troughs)
        min_idx = argrelextrema(result[column].values, np.less, order=window)[0]
        result["is_trough"] = False
        result.loc[min_idx, "is_trough"] = True
        
        return result
    
    def _recognize_double_top(
        self,
        data: pd.DataFrame,
        column: str = "close",
        tolerance: float = 0.03
    ) -> pd.DataFrame:
        """
        Recognize Double Top patterns.
        
        Args:
            data: DataFrame with price data and peaks/troughs
            column: Column to use for pattern recognition
            tolerance: Tolerance for price comparison
            
        Returns:
            DataFrame with Double Top pattern column
        """
        # Make a copy of the data
        result = data.copy()
        
        # Initialize pattern column
        result["double_top"] = False
        
        # Get peaks
        peaks = result[result["is_peak"]].copy()
        
        # Need at least 2 peaks to form a double top
        if len(peaks) < 2:
            return result
        
        # Check for double tops
        for i in range(1, len(peaks)):
            # Get current and previous peak
            curr_peak = peaks.iloc[i]
            prev_peak = peaks.iloc[i-1]
            
            # Calculate price difference
            price_diff = abs(curr_peak[column] - prev_peak[column]) / prev_peak[column]
            
            # Check if peaks are at similar levels
            if price_diff <= tolerance:
                # Check if there's a trough between the peaks
                between_idx = result.index[(result.index > prev_peak.name) & (result.index < curr_peak.name)]
                if len(between_idx) > 0:
                    between_data = result.loc[between_idx]
                    if any(between_data["is_trough"]):
                        # Mark the second peak as a double top
                        result.loc[curr_peak.name, "double_top"] = True
        
        return result
    
    def _recognize_double_bottom(
        self,
        data: pd.DataFrame,
        column: str = "close",
        tolerance: float = 0.03
    ) -> pd.DataFrame:
        """
        Recognize Double Bottom patterns.
        
        Args:
            data: DataFrame with price data and peaks/troughs
            column: Column to use for pattern recognition
            tolerance: Tolerance for price comparison
            
        Returns:
            DataFrame with Double Bottom pattern column
        """
        # Make a copy of the data
        result = data.copy()
        
        # Initialize pattern column
        result["double_bottom"] = False
        
        # Get troughs
        troughs = result[result["is_trough"]].copy()
        
        # Need at least 2 troughs to form a double bottom
        if len(troughs) < 2:
            return result
        
        # Check for double bottoms
        for i in range(1, len(troughs)):
            # Get current and previous trough
            curr_trough = troughs.iloc[i]
            prev_trough = troughs.iloc[i-1]
            
            # Calculate price difference
            price_diff = abs(curr_trough[column] - prev_trough[column]) / prev_trough[column]
            
            # Check if troughs are at similar levels
            if price_diff <= tolerance:
                # Check if there's a peak between the troughs
                between_idx = result.index[(result.index > prev_trough.name) & (result.index < curr_trough.name)]
                if len(between_idx) > 0:
                    between_data = result.loc[between_idx]
                    if any(between_data["is_peak"]):
                        # Mark the second trough as a double bottom
                        result.loc[curr_trough.name, "double_bottom"] = True
        
        return result
    
    def _recognize_head_and_shoulders(
        self,
        data: pd.DataFrame,
        column: str = "close",
        tolerance: float = 0.03
    ) -> pd.DataFrame:
        """
        Recognize Head and Shoulders patterns.
        
        Args:
            data: DataFrame with price data and peaks/troughs
            column: Column to use for pattern recognition
            tolerance: Tolerance for price comparison
            
        Returns:
            DataFrame with Head and Shoulders pattern column
        """
        # Make a copy of the data
        result = data.copy()
        
        # Initialize pattern column
        result["head_and_shoulders"] = False
        
        # Get peaks
        peaks = result[result["is_peak"]].copy()
        
        # Need at least 3 peaks to form a head and shoulders
        if len(peaks) < 3:
            return result
        
        # Check for head and shoulders
        for i in range(2, len(peaks)):
            # Get three consecutive peaks
            left_shoulder = peaks.iloc[i-2]
            head = peaks.iloc[i-1]
            right_shoulder = peaks.iloc[i]
            
            # Check if head is higher than shoulders
            if (head[column] > left_shoulder[column]) and (head[column] > right_shoulder[column]):
                # Check if shoulders are at similar levels
                shoulder_diff = abs(left_shoulder[column] - right_shoulder[column]) / left_shoulder[column]
                if shoulder_diff <= tolerance:
                    # Check if there are troughs between the peaks
                    between_left_head = result.index[(result.index > left_shoulder.name) & (result.index < head.name)]
                    between_head_right = result.index[(result.index > head.name) & (result.index < right_shoulder.name)]
                    
                    if len(between_left_head) > 0 and len(between_head_right) > 0:
                        left_trough_data = result.loc[between_left_head]
                        right_trough_data = result.loc[between_head_right]
                        
                        if any(left_trough_data["is_trough"]) and any(right_trough_data["is_trough"]):
                            # Mark the right shoulder as a head and shoulders
                            result.loc[right_shoulder.name, "head_and_shoulders"] = True
        
        return result
    
    def _recognize_inverse_head_and_shoulders(
        self,
        data: pd.DataFrame,
        column: str = "close",
        tolerance: float = 0.03
    ) -> pd.DataFrame:
        """
        Recognize Inverse Head and Shoulders patterns.
        
        Args:
            data: DataFrame with price data and peaks/troughs
            column: Column to use for pattern recognition
            tolerance: Tolerance for price comparison
            
        Returns:
            DataFrame with Inverse Head and Shoulders pattern column
        """
        # Make a copy of the data
        result = data.copy()
        
        # Initialize pattern column
        result["inverse_head_and_shoulders"] = False
        
        # Get troughs
        troughs = result[result["is_trough"]].copy()
        
        # Need at least 3 troughs to form an inverse head and shoulders
        if len(troughs) < 3:
            return result
        
        # Check for inverse head and shoulders
        for i in range(2, len(troughs)):
            # Get three consecutive troughs
            left_shoulder = troughs.iloc[i-2]
            head = troughs.iloc[i-1]
            right_shoulder = troughs.iloc[i]
            
            # Check if head is lower than shoulders
            if (head[column] < left_shoulder[column]) and (head[column] < right_shoulder[column]):
                # Check if shoulders are at similar levels
                shoulder_diff = abs(left_shoulder[column] - right_shoulder[column]) / left_shoulder[column]
                if shoulder_diff <= tolerance:
                    # Check if there are peaks between the troughs
                    between_left_head = result.index[(result.index > left_shoulder.name) & (result.index < head.name)]
                    between_head_right = result.index[(result.index > head.name) & (result.index < right_shoulder.name)]
                    
                    if len(between_left_head) > 0 and len(between_head_right) > 0:
                        left_peak_data = result.loc[between_left_head]
                        right_peak_data = result.loc[between_head_right]
                        
                        if any(left_peak_data["is_peak"]) and any(right_peak_data["is_peak"]):
                            # Mark the right shoulder as an inverse head and shoulders
                            result.loc[right_shoulder.name, "inverse_head_and_shoulders"] = True
        
        return result
    
    def _recognize_triangle(
        self,
        data: pd.DataFrame,
        column: str = "close",
        min_points: int = 5
    ) -> pd.DataFrame:
        """
        Recognize Triangle patterns.
        
        Args:
            data: DataFrame with price data and peaks/troughs
            column: Column to use for pattern recognition
            min_points: Minimum number of points to form a triangle
            
        Returns:
            DataFrame with Triangle pattern columns
        """
        # Make a copy of the data
        result = data.copy()
        
        # Initialize pattern columns
        result["ascending_triangle"] = False
        result["descending_triangle"] = False
        result["symmetric_triangle"] = False
        
        # Get peaks and troughs
        peaks = result[result["is_peak"]].copy()
        troughs = result[result["is_trough"]].copy()
        
        # Need at least 3 peaks and 3 troughs to form a triangle
        if len(peaks) < 3 or len(troughs) < 3:
            return result
        
        # Check for triangles in the last min_points peaks and troughs
        last_peaks = peaks.iloc[-min_points:] if len(peaks) >= min_points else peaks
        last_troughs = troughs.iloc[-min_points:] if len(troughs) >= min_points else troughs
        
        # Calculate trend lines
        if len(last_peaks) >= 2:
            # Fit a line to the peaks
            peak_indices = last_peaks.index.astype(int)
            peak_values = last_peaks[column].values
            peak_slope, peak_intercept = np.polyfit(peak_indices, peak_values, 1)
        else:
            peak_slope = 0
        
        if len(last_troughs) >= 2:
            # Fit a line to the troughs
            trough_indices = last_troughs.index.astype(int)
            trough_values = last_troughs[column].values
            trough_slope, trough_intercept = np.polyfit(trough_indices, trough_values, 1)
        else:
            trough_slope = 0
        
        # Identify triangle patterns
        if peak_slope < -0.001 and abs(trough_slope) < 0.001:
            # Descending triangle: Lower highs and flat lows
            result.iloc[-1, result.columns.get_loc("descending_triangle")] = True
        elif abs(peak_slope) < 0.001 and trough_slope > 0.001:
            # Ascending triangle: Flat highs and higher lows
            result.iloc[-1, result.columns.get_loc("ascending_triangle")] = True
        elif peak_slope < -0.001 and trough_slope > 0.001:
            # Symmetric triangle: Lower highs and higher lows
            result.iloc[-1, result.columns.get_loc("symmetric_triangle")] = True
        
        return result
    
    def _recognize_channel(
        self,
        data: pd.DataFrame,
        column: str = "close",
        min_points: int = 5,
        tolerance: float = 0.01
    ) -> pd.DataFrame:
        """
        Recognize Channel patterns.
        
        Args:
            data: DataFrame with price data and peaks/troughs
            column: Column to use for pattern recognition
            min_points: Minimum number of points to form a channel
            tolerance: Tolerance for slope comparison
            
        Returns:
            DataFrame with Channel pattern columns
        """
        # Make a copy of the data
        result = data.copy()
        
        # Initialize pattern columns
        result["ascending_channel"] = False
        result["descending_channel"] = False
        result["horizontal_channel"] = False
        
        # Get peaks and troughs
        peaks = result[result["is_peak"]].copy()
        troughs = result[result["is_trough"]].copy()
        
        # Need at least 3 peaks and 3 troughs to form a channel
        if len(peaks) < 3 or len(troughs) < 3:
            return result
        
        # Check for channels in the last min_points peaks and troughs
        last_peaks = peaks.iloc[-min_points:] if len(peaks) >= min_points else peaks
        last_troughs = troughs.iloc[-min_points:] if len(troughs) >= min_points else troughs
        
        # Calculate trend lines
        if len(last_peaks) >= 2:
            # Fit a line to the peaks
            peak_indices = last_peaks.index.astype(int)
            peak_values = last_peaks[column].values
            peak_slope, peak_intercept = np.polyfit(peak_indices, peak_values, 1)
        else:
            peak_slope = 0
        
        if len(last_troughs) >= 2:
            # Fit a line to the troughs
            trough_indices = last_troughs.index.astype(int)
            trough_values = last_troughs[column].values
            trough_slope, trough_intercept = np.polyfit(trough_indices, trough_values, 1)
        else:
            trough_slope = 0
        
        # Check if slopes are similar (parallel lines)
        if abs(peak_slope - trough_slope) <= tolerance:
            # Identify channel type based on slope
            if peak_slope > tolerance:
                # Ascending channel
                result.iloc[-1, result.columns.get_loc("ascending_channel")] = True
            elif peak_slope < -tolerance:
                # Descending channel
                result.iloc[-1, result.columns.get_loc("descending_channel")] = True
            else:
                # Horizontal channel
                result.iloc[-1, result.columns.get_loc("horizontal_channel")] = True
        
        return result
