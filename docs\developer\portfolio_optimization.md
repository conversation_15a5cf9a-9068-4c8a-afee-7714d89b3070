# Portfolio Optimization Module

The portfolio optimization module provides tools for optimizing investment portfolios using various strategies and techniques.

## Architecture

The portfolio optimization module follows a modular architecture:

1. **OptimizationStrategy**: Abstract base class that defines the interface for all optimization strategies
2. **Specific Strategies**: Implementations for different optimization techniques
3. **Portfolio Optimization Manager**: Unified interface for all portfolio optimization functionality
4. **Performance Metrics**: Tools for calculating and analyzing portfolio performance

## Available Optimization Strategies

### Mean-Variance Optimization

Mean-Variance Optimization is the classic approach to portfolio optimization, based on the work of <PERSON>. It aims to maximize the expected return for a given level of risk, or minimize risk for a given level of expected return.

**Features:**
- Maximum Sharpe ratio optimization
- Minimum volatility optimization
- Target return/risk optimization
- Efficient frontier generation

### Black-Litterman Optimization

The Black-Litterman model extends mean-variance optimization by incorporating investor views and market equilibrium. It addresses the sensitivity of mean-variance optimization to input parameters.

**Features:**
- Incorporation of investor views
- Market equilibrium as a starting point
- Confidence levels for views
- Reduced sensitivity to estimation errors

### Hierarchical Risk Parity (HRP)

Hierarchical Risk Parity is a modern portfolio optimization technique that uses machine learning to build diversified portfolios. It addresses the limitations of mean-variance optimization by using hierarchical clustering.

**Features:**
- No need for expected returns
- Robust to estimation errors
- Hierarchical clustering of assets
- Recursive bisection for weight allocation

### Robust Optimization

Robust optimization techniques are designed to be less sensitive to estimation errors in the inputs. They account for uncertainty in the optimization process.

**Features:**
- Bayes-Stein shrinkage of expected returns
- Resampled efficient frontier
- Worst-case optimization
- Uncertainty sets for parameters

### Factor Optimization

Factor-based optimization uses factor models to estimate expected returns and risk. It can incorporate both statistical and fundamental factors.

**Features:**
- PCA-based factor extraction
- Statistical factor models
- Fundamental factor models
- Reduced dimensionality

## Using the Portfolio Optimization Manager

The `PortfolioOptimizationManager` provides a unified interface for all portfolio optimization functionality.

### Optimizing a Portfolio

```python
from app.core.portfolio.optimization.manager import portfolio_optimization_manager

# Optimize a portfolio
weights = await portfolio_optimization_manager.optimize_portfolio(
    symbols=["AAPL", "MSFT", "AMZN", "GOOGL"],
    strategy="mean_variance",
    start_date=start_date,
    end_date=end_date,
    objective="sharpe"
)
```

### Comparing Strategies

```python
# Compare different optimization strategies
results = await portfolio_optimization_manager.compare_strategies(
    symbols=["AAPL", "MSFT", "AMZN", "GOOGL"],
    strategies=["mean_variance", "hierarchical_risk_parity", "black_litterman"],
    start_date=start_date,
    end_date=end_date
)
```

### Backtesting a Portfolio

```python
# Backtest a portfolio
backtest = await portfolio_optimization_manager.backtest_portfolio(
    symbols=["AAPL", "MSFT", "AMZN", "GOOGL"],
    weights=weights,
    start_date=start_date,
    end_date=end_date
)
```

### Comparing Backtests

```python
# Compare backtests for different strategies
backtest_results = await portfolio_optimization_manager.compare_backtests(
    symbols=["AAPL", "MSFT", "AMZN", "GOOGL"],
    strategies=["mean_variance", "hierarchical_risk_parity", "black_litterman"],
    start_date=start_date,
    end_date=end_date
)
```

## Performance Metrics

The portfolio optimization module includes tools for calculating and analyzing portfolio performance.

### Basic Metrics

- **Total Return**: The total return of the portfolio over the period
- **Annualized Return**: The annualized return of the portfolio
- **Volatility**: The annualized standard deviation of returns
- **Maximum Drawdown**: The maximum peak-to-trough decline in portfolio value

### Risk-Adjusted Metrics

- **Sharpe Ratio**: Return per unit of risk (volatility)
- **Sortino Ratio**: Return per unit of downside risk
- **Calmar Ratio**: Return per unit of maximum drawdown
- **Information Ratio**: Active return per unit of tracking error

### Risk Metrics

- **Value at Risk (VaR)**: The maximum loss at a given confidence level
- **Conditional Value at Risk (CVaR)**: The expected loss given that the loss exceeds the VaR
- **Downside Deviation**: The standard deviation of negative returns
- **Beta**: The sensitivity of the portfolio to market movements

### Other Metrics

- **Alpha**: The excess return of the portfolio over the benchmark
- **Tracking Error**: The standard deviation of the difference between portfolio and benchmark returns
- **Up/Down Capture**: The performance of the portfolio in up and down markets
- **Win Rate**: The percentage of periods with positive returns

## Adding a New Optimization Strategy

To add a new optimization strategy, follow these steps:

1. Create a new class that inherits from `OptimizationStrategy`
2. Implement the required methods:
   - `optimize`: Optimize portfolio weights
3. Register the strategy in the `PortfolioOptimizationManager`

**Example:**
```python
from app.core.portfolio.optimization.base import OptimizationStrategy

class MyOptimizationStrategy(OptimizationStrategy):
    def __init__(self):
        super().__init__("My Strategy")
    
    def optimize(self, returns, **kwargs):
        # Implementation
        pass
```

Then register it in the `PortfolioOptimizationManager`:

```python
# Register the strategy
portfolio_optimization_manager.register_strategy("my_strategy", MyOptimizationStrategy())
```

## Advanced Features

### Custom Constraints

The portfolio optimization module supports custom constraints for optimization:

```python
# Define constraints
constraints = {
    "weight_bounds": (0, 0.2),  # No short selling, maximum 20% per asset
    "sector_constraints": {
        "Technology": (0.1, 0.5),  # At least 10%, at most 50% in Technology
        "Financials": (0.05, 0.3)  # At least 5%, at most 30% in Financials
    },
    "sector_mapper": {
        "Technology": ["AAPL", "MSFT", "GOOGL"],
        "Financials": ["JPM", "BAC", "GS"]
    }
}

# Optimize with constraints
weights = await portfolio_optimization_manager.optimize_portfolio(
    symbols=symbols,
    strategy="mean_variance",
    constraints=constraints
)
```

### Custom Objectives

The portfolio optimization module supports various optimization objectives:

- `max_sharpe`: Maximize the Sharpe ratio
- `min_volatility`: Minimize portfolio volatility
- `max_return`: Maximize expected return
- `efficient_risk`: Maximize return for a given level of risk
- `efficient_return`: Minimize risk for a given level of return
- `max_diversification`: Maximize portfolio diversification
- `min_cvar`: Minimize Conditional Value at Risk

```python
# Optimize with a custom objective
weights = await portfolio_optimization_manager.optimize_portfolio(
    symbols=symbols,
    strategy="mean_variance",
    objective="efficient_risk",
    target_risk=0.15  # Target 15% volatility
)
```

### Factor Models

The portfolio optimization module supports factor models for estimating expected returns and risk:

```python
# Optimize using a factor model
weights = await portfolio_optimization_manager.optimize_portfolio(
    symbols=symbols,
    strategy="factor",
    factor_model="pca",
    n_factors=3
)
```

### Investor Views

The Black-Litterman model allows incorporating investor views:

```python
# Define investor views
views = {
    "view1": {"AAPL": 1.0, "MSFT": -1.0},  # AAPL will outperform MSFT
    "view2": {"AMZN": 1.0}  # AMZN will have a positive return
}

# Define view confidences (0-1)
view_confidences = {
    "view1": 0.7,  # 70% confidence
    "view2": 0.5   # 50% confidence
}

# Optimize with investor views
weights = await portfolio_optimization_manager.optimize_portfolio(
    symbols=symbols,
    strategy="black_litterman",
    views=views,
    view_confidences=view_confidences
)
```
