"""
Utility functions to kill processes using specific ports.
"""
import socket
import subprocess
import sys
import time
from typing import List, Optional

def is_port_in_use(port: int) -> bool:
    """
    Check if a port is in use.
    
    Args:
        port: Port number to check
        
    Returns:
        True if port is in use, False otherwise
    """
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) == 0

def kill_port(port: int) -> bool:
    """
    Kill the process using a specific port.
    
    Args:
        port: Port number to kill
        
    Returns:
        True if process was killed, False otherwise
    """
    if not is_port_in_use(port):
        return True
    
    try:
        # Find process ID using the port
        if sys.platform == 'win32':
            # Windows
            result = subprocess.run(
                ['netstat', '-ano', '|', 'findstr', f':{port}'],
                capture_output=True,
                text=True,
                shell=True
            )
            
            if result.returncode != 0:
                return False
            
            # Parse output to get PID
            for line in result.stdout.splitlines():
                if f':{port}' in line:
                    parts = line.strip().split()
                    if len(parts) >= 5:
                        pid = parts[-1]
                        # Kill process
                        subprocess.run(['taskkill', '/F', '/PID', pid], capture_output=True)
                        return True
        else:
            # Unix-like
            result = subprocess.run(
                ['lsof', '-i', f':{port}', '-t'],
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                return False
            
            # Parse output to get PID
            for pid in result.stdout.splitlines():
                # Kill process
                subprocess.run(['kill', '-9', pid], capture_output=True)
                return True
    except Exception as e:
        print(f"Error killing process on port {port}: {e}")
    
    return False

def wait_for_port(port: int, timeout: int = 5) -> bool:
    """
    Wait for a port to be available.
    
    Args:
        port: Port number to wait for
        timeout: Timeout in seconds
        
    Returns:
        True if port is available, False otherwise
    """
    start_time = time.time()
    while time.time() - start_time < timeout:
        if not is_port_in_use(port):
            return True
        time.sleep(0.1)
    
    return False
