app:
  name: Portfolio Optimizer (Production)
  environment: production
  debug: false

api:
  debug: false
  host: 0.0.0.0
  port: 8000
  cors_origins: ["https://portfolio-optimizer.example.com"]
  timeout: 30  # seconds
  max_connections: 500

streamlit:
  host: 0.0.0.0
  port: 8501

database:
  url: ${DATABASE_URL:postgresql://user:password@localhost:5432/portfolio_optimizer}
  echo: false
  pool_size: 20
  max_overflow: 20
  pool_timeout: 60
  pool_recycle: 3600

cache:
  type: redis
  redis_url: ${REDIS_URL:redis://localhost:6379/0}
  expiry: 86400  # 24 hours

data_sources:
  yahoo_finance:
    cache_expiry: 604800  # 7 days in seconds
  alpha_vantage:
    api_key: ${ALPHA_VANTAGE_API_KEY}
    cache_expiry: 604800  # 7 days in seconds
  financial_modeling_prep:
    api_key: ${FINANCIAL_MODELING_PREP_API_KEY}
    cache_expiry: 604800  # 7 days in seconds

logging:
  level: WARNING
  console: false
  file: /var/log/portfolio-optimizer/app.log
  max_size: 104857600  # 100 MB
  backup_count: 30

security:
  jwt_secret: ${JWT_SECRET}
  password_min_length: 12

performance:
  enable_profiling: false
