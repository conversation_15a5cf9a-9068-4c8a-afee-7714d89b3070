"""
Analysis service.
"""
from datetime import date
from typing import Dict, List, Optional

from app.core.data_collection.data_manager import data_manager
from app.core.technical_analysis.indicators.momentum import RSI, Stochastic
from app.core.technical_analysis.indicators.trend import MACD, MovingAverage
from app.core.technical_analysis.indicators.volatility import ATR, BollingerBands
from app.core.technical_analysis.indicators.volume import OBV, VWAP
from app.core.technical_analysis.patterns.candlestick import CandlestickPatterns
from app.core.technical_analysis.patterns.chart_patterns import ChartPatterns
from app.core.technical_analysis.screener.screener import Screener
from app.utils.logging import logger


class AnalysisService:
    """
    Analysis service.
    
    This service provides methods for technical analysis.
    """
    
    def __init__(self):
        """Initialize the analysis service."""
        # Initialize indicators
        self.indicators = {
            "sma": MovingAverage("sma"),
            "ema": MovingAverage("ema"),
            "wma": MovingAverage("wma"),
            "macd": MACD(),
            "rsi": RSI(),
            "stoch": Stochastic(),
            "bbands": BollingerBands(),
            "atr": ATR(),
            "obv": OBV(),
            "vwap": VWAP()
        }
        
        # Initialize pattern recognizers
        self.candlestick_patterns = CandlestickPatterns()
        self.chart_patterns = ChartPatterns()
        
        # Initialize screener
        self.screener = Screener()
    
    async def calculate_indicator(
        self,
        symbol: str,
        indicator: str,
        start_date: date,
        end_date: date,
        interval: str = "1d",
        params: Optional[Dict] = None
    ) -> Dict:
        """
        Calculate a technical indicator.
        
        Args:
            symbol: Stock symbol
            indicator: Indicator type
            start_date: Start date
            end_date: End date
            interval: Data interval
            params: Additional parameters for the indicator
            
        Returns:
            Dictionary with indicator data
        """
        try:
            # Check if indicator is supported
            if indicator not in self.indicators:
                return {
                    "symbol": symbol,
                    "indicator": indicator,
                    "data": [],
                    "error": f"Unsupported indicator: {indicator}"
                }
            
            # Get stock data
            df = await data_manager.get_stock_data(
                symbol,
                start_date,
                end_date,
                interval
            )
            
            # Check if data was retrieved
            if df.empty:
                return {
                    "symbol": symbol,
                    "indicator": indicator,
                    "data": [],
                    "error": f"No data found for {symbol}"
                }
            
            # Calculate indicator
            indicator_obj = self.indicators[indicator]
            params = params or {}
            df = indicator_obj.calculate(df, **params)
            
            # Convert DataFrame to dictionary
            data = df.to_dict(orient="records")
            
            return {
                "symbol": symbol,
                "indicator": indicator,
                "data": data
            }
        
        except Exception as e:
            logger.error(f"Error calculating indicator: {e}")
            return {
                "symbol": symbol,
                "indicator": indicator,
                "data": [],
                "error": str(e)
            }
    
    async def recognize_patterns(
        self,
        symbol: str,
        pattern_type: str,
        start_date: date,
        end_date: date,
        interval: str = "1d",
        patterns: Optional[List[str]] = None
    ) -> Dict:
        """
        Recognize patterns in stock data.
        
        Args:
            symbol: Stock symbol
            pattern_type: Pattern type ('candlestick' or 'chart')
            start_date: Start date
            end_date: End date
            interval: Data interval
            patterns: List of patterns to recognize
            
        Returns:
            Dictionary with pattern data
        """
        try:
            # Get stock data
            df = await data_manager.get_stock_data(
                symbol,
                start_date,
                end_date,
                interval
            )
            
            # Check if data was retrieved
            if df.empty:
                return {
                    "symbol": symbol,
                    "pattern_type": pattern_type,
                    "patterns": [],
                    "data": [],
                    "error": f"No data found for {symbol}"
                }
            
            # Recognize patterns
            if pattern_type == "candlestick":
                df = self.candlestick_patterns.recognize_patterns(df, patterns)
                recognized_patterns = self.candlestick_patterns.get_recognized_patterns(df)
            elif pattern_type == "chart":
                df = self.chart_patterns.recognize_patterns(df, patterns)
                recognized_patterns = self.chart_patterns.get_recognized_patterns(df)
            else:
                return {
                    "symbol": symbol,
                    "pattern_type": pattern_type,
                    "patterns": [],
                    "data": [],
                    "error": f"Unsupported pattern type: {pattern_type}"
                }
            
            # Convert DataFrame to dictionary
            data = df.to_dict(orient="records")
            
            return {
                "symbol": symbol,
                "pattern_type": pattern_type,
                "patterns": recognized_patterns,
                "data": data
            }
        
        except Exception as e:
            logger.error(f"Error recognizing patterns: {e}")
            return {
                "symbol": symbol,
                "pattern_type": pattern_type,
                "patterns": [],
                "data": [],
                "error": str(e)
            }
    
    async def screen_stocks(
        self,
        symbols: List[str],
        filters: Dict,
        start_date: date,
        end_date: date,
        interval: str = "1d",
        indicators: Optional[List[Dict]] = None
    ) -> Dict:
        """
        Screen stocks based on filters.
        
        Args:
            symbols: List of stock symbols
            filters: Filter conditions
            start_date: Start date
            end_date: End date
            interval: Data interval
            indicators: List of indicators to calculate
            
        Returns:
            Dictionary with screening results
        """
        try:
            # Get stock data
            data_dict = await data_manager.get_multiple_stock_data(
                symbols,
                start_date,
                end_date,
                interval
            )
            
            # Screen stocks
            results = self.screener.screen(
                data_dict,
                filters,
                indicators
            )
            
            # Convert DataFrames to dictionaries
            result_dict = {}
            for symbol, df in results.items():
                if not df.empty:
                    result_dict[symbol] = df.to_dict(orient="records")
                else:
                    result_dict[symbol] = []
            
            return {
                "symbols": symbols,
                "results": result_dict
            }
        
        except Exception as e:
            logger.error(f"Error screening stocks: {e}")
            return {
                "symbols": symbols,
                "results": {},
                "error": str(e)
            }


# Create a singleton instance
analysis_service = AnalysisService()
