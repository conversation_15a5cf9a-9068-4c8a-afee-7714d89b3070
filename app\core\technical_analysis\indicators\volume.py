"""
Volume indicators implementation.
"""
from typing import Dict, List, Optional, Union

import numpy as np
import pandas as pd
import pandas_ta as ta

from app.core.technical_analysis.indicators.base import BaseIndicator
from app.utils.logging import logger


class OBV(BaseIndicator):
    """
    On-Balance Volume (OBV) indicator.
    """

    def __init__(self):
        """Initialize the OBV indicator."""
        super().__init__("OBV")

    def calculate(
        self,
        data: pd.DataFrame,
        **kwargs
    ) -> pd.DataFrame:
        """
        Calculate the OBV indicator.

        Args:
            data: DataFrame with OHLCV data
            **kwargs: Additional parameters

        Returns:
            DataFrame with the indicator values
        """
        # Validate data
        if not self.validate_data(data, ["close", "volume"]):
            logger.warning(f"Invalid data for {self.name} calculation")
            return data

        # Make a copy of the data
        result = data.copy()

        # Calculate OBV
        result["obv"] = ta.obv(result["close"], result["volume"])

        # Calculate OBV EMA
        result["obv_ema"] = ta.ema(result["obv"], length=20)

        return result

    def get_signal(
        self,
        data: pd.DataFrame,
        **kwargs
    ) -> pd.DataFrame:
        """
        Get trading signals from the OBV indicator.

        Args:
            data: DataFrame with indicator values
            **kwargs: Additional parameters

        Returns:
            DataFrame with trading signals
        """
        # Calculate the indicator if not already calculated
        if "obv" not in data.columns or "obv_ema" not in data.columns:
            data = self.calculate(data, **kwargs)

        # Make a copy of the data
        result = data.copy()

        # Generate signals
        # 1 = buy, -1 = sell, 0 = neutral
        result["obv_signal"] = 0

        # OBV crosses above its EMA = buy signal
        result.loc[
            (result["obv"] > result["obv_ema"]) &
            (result["obv"].shift(1) <= result["obv_ema"].shift(1)),
            "obv_signal"
        ] = 1

        # OBV crosses below its EMA = sell signal
        result.loc[
            (result["obv"] < result["obv_ema"]) &
            (result["obv"].shift(1) >= result["obv_ema"].shift(1)),
            "obv_signal"
        ] = -1

        return result


class VWAP(BaseIndicator):
    """
    Volume-Weighted Average Price (VWAP) indicator.
    """

    def __init__(self):
        """Initialize the VWAP indicator."""
        super().__init__("VWAP")

    def calculate(
        self,
        data: pd.DataFrame,
        **kwargs
    ) -> pd.DataFrame:
        """
        Calculate the VWAP indicator.

        Args:
            data: DataFrame with OHLCV data
            **kwargs: Additional parameters

        Returns:
            DataFrame with the indicator values
        """
        # Validate data
        if not self.validate_data(data, ["high", "low", "close", "volume"]):
            logger.warning(f"Invalid data for {self.name} calculation")
            return data

        # Make a copy of the data
        result = data.copy()

        # Calculate typical price
        result["typical_price"] = (result["high"] + result["low"] + result["close"]) / 3

        # Calculate VWAP
        # VWAP requires a DatetimeIndex, so we need to set the date column as the index if it's not already
        has_date_index = isinstance(result.index, pd.DatetimeIndex)

        if not has_date_index and 'date' in result.columns:
            # Save the original index
            original_index = result.index.copy()

            # Set date as index for VWAP calculation
            result = result.set_index('date')

            # Ensure the index is DatetimeIndex
            if not isinstance(result.index, pd.DatetimeIndex):
                result.index = pd.to_datetime(result.index)

            # Calculate VWAP
            try:
                result["vwap"] = ta.vwap(
                    result["high"],
                    result["low"],
                    result["close"],
                    result["volume"]
                )
            except Exception as e:
                logger.warning(f"Error calculating VWAP: {e}")
                result["vwap"] = result["close"]  # Fallback to close price

            # Reset the index to restore the original structure
            result = result.reset_index()
        else:
            # If we already have a DatetimeIndex, calculate normally
            try:
                result["vwap"] = ta.vwap(
                    result["high"],
                    result["low"],
                    result["close"],
                    result["volume"]
                )
            except Exception as e:
                logger.warning(f"Error calculating VWAP: {e}")
                result["vwap"] = result["close"]  # Fallback to close price

        return result

    def get_signal(
        self,
        data: pd.DataFrame,
        column: str = "close",
        **kwargs
    ) -> pd.DataFrame:
        """
        Get trading signals from the VWAP indicator.

        Args:
            data: DataFrame with indicator values
            column: Column to use for comparison
            **kwargs: Additional parameters

        Returns:
            DataFrame with trading signals
        """
        # Calculate the indicator if not already calculated
        if "vwap" not in data.columns:
            data = self.calculate(data, **kwargs)

        # Make a copy of the data
        result = data.copy()

        # Generate signals
        # 1 = buy, -1 = sell, 0 = neutral
        result["vwap_signal"] = 0

        # Price crosses above VWAP = buy signal
        result.loc[
            (result[column] > result["vwap"]) &
            (result[column].shift(1) <= result["vwap"].shift(1)),
            "vwap_signal"
        ] = 1

        # Price crosses below VWAP = sell signal
        result.loc[
            (result[column] < result["vwap"]) &
            (result[column].shift(1) >= result["vwap"].shift(1)),
            "vwap_signal"
        ] = -1

        return result


class AD(BaseIndicator):
    """
    Accumulation/Distribution (A/D) indicator.

    The A/D indicator measures the cumulative flow of money into and out of a security.
    It uses price and volume to determine whether a stock is being accumulated (bought) or distributed (sold).
    """

    def __init__(self):
        """Initialize the A/D indicator."""
        super().__init__("AD")

    def calculate(
        self,
        data: pd.DataFrame,
        **kwargs
    ) -> pd.DataFrame:
        """
        Calculate the A/D indicator.

        Args:
            data: DataFrame with OHLCV data
            **kwargs: Additional parameters

        Returns:
            DataFrame with the indicator values
        """
        # Validate data
        if not self.validate_data(data, ["high", "low", "close", "volume"]):
            logger.warning(f"Invalid data for {self.name} calculation")
            return data

        # Make a copy of the data
        result = data.copy()

        # Calculate A/D
        result["ad"] = ta.ad(
            result["high"],
            result["low"],
            result["close"],
            result["volume"]
        )

        # Calculate A/D EMA
        result["ad_ema"] = ta.ema(result["ad"], length=20)

        return result

    def get_signal(
        self,
        data: pd.DataFrame,
        **kwargs
    ) -> pd.DataFrame:
        """
        Get trading signals from the A/D indicator.

        Args:
            data: DataFrame with indicator values
            **kwargs: Additional parameters

        Returns:
            DataFrame with trading signals
        """
        # Calculate the indicator if not already calculated
        if "ad" not in data.columns or "ad_ema" not in data.columns:
            data = self.calculate(data, **kwargs)

        # Make a copy of the data
        result = data.copy()

        # Generate signals
        # 1 = buy, -1 = sell, 0 = neutral
        result["ad_signal"] = 0

        # A/D crosses above its EMA = buy signal
        result.loc[
            (result["ad"] > result["ad_ema"]) &
            (result["ad"].shift(1) <= result["ad_ema"].shift(1)),
            "ad_signal"
        ] = 1

        # A/D crosses below its EMA = sell signal
        result.loc[
            (result["ad"] < result["ad_ema"]) &
            (result["ad"].shift(1) >= result["ad_ema"].shift(1)),
            "ad_signal"
        ] = -1

        # Check for divergence
        # Bullish divergence: price makes a lower low but A/D makes a higher low
        for i in range(5, len(result)):
            if (
                result["close"].iloc[i] < result["close"].iloc[i-5] and  # Price made a lower low
                result["ad"].iloc[i] > result["ad"].iloc[i-5]  # A/D made a higher low
            ):
                result["ad_signal"].iloc[i] = 2  # Strong buy signal

        # Bearish divergence: price makes a higher high but A/D makes a lower high
        for i in range(5, len(result)):
            if (
                result["close"].iloc[i] > result["close"].iloc[i-5] and  # Price made a higher high
                result["ad"].iloc[i] < result["ad"].iloc[i-5]  # A/D made a lower high
            ):
                result["ad_signal"].iloc[i] = -2  # Strong sell signal

        return result


class ADOSC(BaseIndicator):
    """
    Chaikin Oscillator (ADOSC) indicator.

    The Chaikin Oscillator measures the momentum of the Accumulation/Distribution (A/D) line
    using the MACD formula. It is calculated by subtracting a longer period EMA of the A/D line
    from a shorter period EMA of the A/D line.
    """

    def __init__(self):
        """Initialize the ADOSC indicator."""
        super().__init__("ADOSC")

    def calculate(
        self,
        data: pd.DataFrame,
        fast_period: int = 3,
        slow_period: int = 10,
        **kwargs
    ) -> pd.DataFrame:
        """
        Calculate the ADOSC indicator.

        Args:
            data: DataFrame with OHLCV data
            fast_period: Fast period for the EMA calculation
            slow_period: Slow period for the EMA calculation
            **kwargs: Additional parameters

        Returns:
            DataFrame with the indicator values
        """
        # Validate data
        if not self.validate_data(data, ["high", "low", "close", "volume"]):
            logger.warning(f"Invalid data for {self.name} calculation")
            return data

        # Make a copy of the data
        result = data.copy()

        # Calculate ADOSC
        result[f"adosc_{fast_period}_{slow_period}"] = ta.adosc(
            result["high"],
            result["low"],
            result["close"],
            result["volume"],
            fast=fast_period,
            slow=slow_period
        )

        return result

    def get_signal(
        self,
        data: pd.DataFrame,
        fast_period: int = 3,
        slow_period: int = 10,
        **kwargs
    ) -> pd.DataFrame:
        """
        Get trading signals from the ADOSC indicator.

        Args:
            data: DataFrame with indicator values
            fast_period: Fast period for the EMA calculation
            slow_period: Slow period for the EMA calculation
            **kwargs: Additional parameters

        Returns:
            DataFrame with trading signals
        """
        # Calculate the indicator if not already calculated
        adosc_column = f"adosc_{fast_period}_{slow_period}"
        if adosc_column not in data.columns:
            data = self.calculate(data, fast_period=fast_period, slow_period=slow_period, **kwargs)

        # Make a copy of the data
        result = data.copy()

        # Generate signals
        # 1 = buy, -1 = sell, 0 = neutral
        result[f"{adosc_column}_signal"] = 0

        # ADOSC crosses above zero = buy signal
        result.loc[
            (result[adosc_column] > 0) &
            (result[adosc_column].shift(1) <= 0),
            f"{adosc_column}_signal"
        ] = 1

        # ADOSC crosses below zero = sell signal
        result.loc[
            (result[adosc_column] < 0) &
            (result[adosc_column].shift(1) >= 0),
            f"{adosc_column}_signal"
        ] = -1

        return result
