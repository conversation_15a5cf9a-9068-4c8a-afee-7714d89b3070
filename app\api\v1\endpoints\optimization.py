"""
Portfolio Optimization API endpoints.
"""
from datetime import date
from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.core.portfolio.optimization.manager import portfolio_optimization_manager
from app.db.database import get_db
from app.db.models import User
from app.middleware.auth import get_current_user

router = APIRouter()


@router.post("/optimize")
async def optimize_portfolio(
    symbols: List[str],
    strategy: str = Query("mean_variance", description="Optimization strategy"),
    start_date: Optional[date] = Query(None, description="Start date for historical data"),
    end_date: Optional[date] = Query(None, description="End date for historical data"),
    interval: str = Query("1d", description="Data interval (e.g., '1d', '1wk', '1mo')"),
    objective: str = Query("sharpe", description="Optimization objective"),
    risk_free_rate: float = Query(0.02, description="Risk-free rate"),
    target_return: Optional[float] = Query(None, description="Target return for efficient_return objective"),
    target_risk: Optional[float] = Query(None, description="Target risk for efficient_risk objective"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Optimize a portfolio.
    """
    try:
        # Optimize portfolio
        weights = await portfolio_optimization_manager.optimize_portfolio(
            symbols=symbols,
            strategy=strategy,
            start_date=start_date,
            end_date=end_date,
            interval=interval,
            objective=objective,
            risk_free_rate=risk_free_rate,
            target_return=target_return,
            target_risk=target_risk
        )
        
        return {
            "strategy": strategy,
            "weights": weights
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.post("/compare-strategies")
async def compare_strategies(
    symbols: List[str],
    strategies: List[str] = Query(..., description="List of optimization strategies"),
    start_date: Optional[date] = Query(None, description="Start date for historical data"),
    end_date: Optional[date] = Query(None, description="End date for historical data"),
    interval: str = Query("1d", description="Data interval (e.g., '1d', '1wk', '1mo')"),
    objective: str = Query("sharpe", description="Optimization objective"),
    risk_free_rate: float = Query(0.02, description="Risk-free rate"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Compare different optimization strategies.
    """
    try:
        # Compare strategies
        results = await portfolio_optimization_manager.compare_strategies(
            symbols=symbols,
            strategies=strategies,
            start_date=start_date,
            end_date=end_date,
            interval=interval,
            objective=objective,
            risk_free_rate=risk_free_rate
        )
        
        return results
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.post("/backtest")
async def backtest_portfolio(
    symbols: List[str],
    weights: Dict[str, float],
    start_date: Optional[date] = Query(None, description="Start date for historical data"),
    end_date: Optional[date] = Query(None, description="End date for historical data"),
    interval: str = Query("1d", description="Data interval (e.g., '1d', '1wk', '1mo')"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Backtest a portfolio.
    """
    try:
        # Backtest portfolio
        results = await portfolio_optimization_manager.backtest_portfolio(
            symbols=symbols,
            weights=weights,
            start_date=start_date,
            end_date=end_date,
            interval=interval
        )
        
        # Convert to JSON-serializable format
        metrics = results["metrics"]
        returns = results["returns"]
        
        return {
            "metrics": metrics,
            "returns": returns.to_dict(),
            "weights": results["weights"]
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.post("/compare-backtests")
async def compare_backtests(
    symbols: List[str],
    strategies: List[str] = Query(..., description="List of optimization strategies"),
    start_date: Optional[date] = Query(None, description="Start date for historical data"),
    end_date: Optional[date] = Query(None, description="End date for historical data"),
    interval: str = Query("1d", description="Data interval (e.g., '1d', '1wk', '1mo')"),
    objective: str = Query("sharpe", description="Optimization objective"),
    risk_free_rate: float = Query(0.02, description="Risk-free rate"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Compare backtests for different optimization strategies.
    """
    try:
        # Compare backtests
        results = await portfolio_optimization_manager.compare_backtests(
            symbols=symbols,
            strategies=strategies,
            start_date=start_date,
            end_date=end_date,
            interval=interval,
            objective=objective,
            risk_free_rate=risk_free_rate
        )
        
        # Convert to JSON-serializable format
        json_results = {}
        
        for strategy, backtest in results.items():
            metrics = backtest["metrics"]
            returns = backtest["returns"]
            
            json_results[strategy] = {
                "metrics": metrics,
                "returns": returns.to_dict(),
                "weights": backtest["weights"]
            }
        
        return json_results
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
