# Portfolio Optimization Examples

This document provides practical examples of how to use the Portfolio Optimization application for various investment scenarios.

## Example 1: Building a Diversified Stock Portfolio

In this example, we'll build a diversified portfolio of large-cap US stocks using the Mean-Variance Optimization method.

### Step 1: Data Collection

1. Go to the "Data" page
2. Enter the following stock symbols: AAPL, MSFT, GOOGL, AMZN, META, JNJ, PG, V, JPM, XOM
3. Select a date range of the last 3 years
4. Choose "1wk" for the interval
5. Click "Fetch Data"

### Step 2: Portfolio Optimization

1. Go to the "Portfolio" page
2. In the "Portfolio Optimizer" tab, enter the same stock symbols
3. Keep the same date range and interval
4. For optimization criterion, select "max_sharpe"
5. For expected returns method, select "mean_historical_return"
6. For risk model method, select "ledoit_wolf" (this provides a more robust covariance estimate)
7. Set minimum weight per asset to 0.05 (5%)
8. Set maximum weight per asset to 0.25 (25%)
9. Enter your total portfolio value (e.g., $10,000)
10. Click "Optimize Portfolio"

### Step 3: Analyze Results

The application will display:
- The optimal portfolio weights
- Expected annual return, volatility, and Sharpe ratio
- A pie chart showing the allocation
- The number of shares to buy for each stock
- The efficient frontier with your optimal portfolio highlighted

### Step 4: Implementation

1. Use the discrete allocation to determine how many shares of each stock to buy
2. Place the trades with your broker
3. Save the portfolio for future reference and rebalancing

## Example 2: Risk Parity Portfolio with ETFs

In this example, we'll create a risk parity portfolio using ETFs that represent different asset classes.

### Step 1: Data Collection

1. Go to the "Data" page
2. Enter the following ETF symbols: SPY (US Stocks), EFA (International Stocks), AGG (US Bonds), TIP (Inflation-Protected Bonds), GLD (Gold), VNQ (Real Estate)
3. Select a date range of the last 5 years
4. Choose "1wk" for the interval
5. Click "Fetch Data"

### Step 2: Asset Allocation

1. Go to the "Portfolio" page
2. In the "Asset Allocation" tab, enter the same ETF symbols
3. Keep the same date range and interval
4. For allocation strategy, select "risk_parity"
5. For risk profile, select "moderate"
6. Enter your total portfolio value (e.g., $50,000)
7. Click "Allocate Assets"

### Step 3: Analyze Results

The application will display:
- The asset weights based on risk parity
- A pie chart showing the allocation
- The number of shares to buy for each ETF
- Information about the risk contribution of each asset

### Step 4: Risk Analysis

1. Go to the "Risk Analysis" tab
2. Enter the same ETF symbols
3. Enter the weights from the risk parity allocation
4. Keep the same date range and interval
5. For benchmark, enter "SPY"
6. Click "Analyze Risk"

The application will display:
- Risk metrics (volatility, VaR, CVaR)
- Performance metrics (Sharpe ratio, Sortino ratio)
- Correlation matrix
- Returns distribution
- Drawdown chart

## Example 3: Tactical Asset Allocation

In this example, we'll implement a tactical asset allocation strategy that adjusts weights based on market conditions.

### Step 1: Data Collection

1. Go to the "Data" page
2. Enter the following ETF symbols: SPY (US Stocks), IEF (Intermediate-Term Bonds), GLD (Gold)
3. Select a date range of the last 10 years
4. Choose "1mo" for the interval
5. Click "Fetch Data"

### Step 2: Technical Analysis

1. Go to the "Analysis" page
2. Select SPY from the dropdown
3. Add the following indicators:
   - 200-day Simple Moving Average
   - 50-day Simple Moving Average
   - RSI (14)
4. Click "Analyze"

Repeat the analysis for IEF and GLD.

### Step 3: Define Tactical Rules

Based on the technical analysis, we'll define the following tactical rules:
- If SPY is above its 200-day SMA, allocate 60% to SPY, 30% to IEF, 10% to GLD
- If SPY is below its 200-day SMA, allocate 30% to SPY, 60% to IEF, 10% to GLD
- If GLD's RSI is above 70, reduce GLD allocation by 5% and add to IEF
- If GLD's RSI is below 30, increase GLD allocation by 5% from IEF

### Step 4: Portfolio Optimization

1. Go to the "Portfolio" page
2. In the "Portfolio Optimizer" tab, enter the ETF symbols
3. Keep the same date range and interval
4. For optimization criterion, select "efficient_risk"
5. Set target volatility based on your risk tolerance
6. Set constraints based on your tactical rules
7. Click "Optimize Portfolio"

### Step 5: Backtest

1. Use the optimized weights
2. Set rebalancing frequency to "monthly"
3. Click "Backtest"

The application will display:
- Performance metrics
- Comparison to benchmark
- Drawdown analysis
- Monthly returns heatmap

## Example 4: Income-Focused Portfolio

In this example, we'll create a portfolio focused on generating income using dividend stocks and bonds.

### Step 1: Data Collection

1. Go to the "Data" page
2. Enter the following symbols: VYM (High Dividend ETF), SCHD (Dividend ETF), PFF (Preferred Stock ETF), HYG (High Yield Bond ETF), LQD (Corporate Bond ETF), MUB (Municipal Bond ETF)
3. Select a date range of the last 3 years
4. Choose "1wk" for the interval
5. Click "Fetch Data"

### Step 2: Portfolio Optimization

1. Go to the "Portfolio" page
2. In the "Portfolio Optimizer" tab, enter the same symbols
3. Keep the same date range and interval
4. For optimization criterion, select "efficient_return"
5. Set target return based on your income needs
6. Set minimum weight per asset to 0.05 (5%)
7. Set maximum weight per asset to 0.30 (30%)
8. Click "Optimize Portfolio"

### Step 3: Income Analysis

The application will display:
- The optimal portfolio weights
- Expected annual return (income)
- Volatility and Sharpe ratio
- A pie chart showing the allocation

### Step 4: Implementation and Monitoring

1. Use the discrete allocation to determine how many shares/units to buy
2. Place the trades with your broker
3. Monitor the income generated by the portfolio
4. Rebalance periodically to maintain the target allocation

## Example 5: Factor-Based Portfolio

In this example, we'll create a portfolio based on factor investing principles.

### Step 1: Data Collection

1. Go to the "Data" page
2. Enter the following factor ETF symbols: MTUM (Momentum), VLUE (Value), SIZE (Size), QUAL (Quality), USMV (Minimum Volatility)
3. Select a date range of the last 5 years
4. Choose "1wk" for the interval
5. Click "Fetch Data"

### Step 2: Factor Analysis

1. Go to the "Analysis" page
2. Select each factor ETF and analyze its performance
3. Look for factors that have low correlation with each other
4. Identify factors that have performed well in different market environments

### Step 3: Portfolio Optimization

1. Go to the "Portfolio" page
2. In the "Portfolio Optimizer" tab, enter the factor ETF symbols
3. Keep the same date range and interval
4. For optimization criterion, select "max_sharpe"
5. Set minimum weight per asset to 0.10 (10%)
6. Set maximum weight per asset to 0.40 (40%)
7. Click "Optimize Portfolio"

### Step 4: Risk Analysis

1. Go to the "Risk Analysis" tab
2. Enter the same factor ETF symbols
3. Enter the weights from the optimization
4. For benchmark, enter "SPY"
5. Click "Analyze Risk"

The application will display:
- Risk metrics
- Performance metrics
- Correlation matrix
- Factor exposures

### Step 5: Implementation and Monitoring

1. Use the discrete allocation to determine how many shares to buy
2. Place the trades with your broker
3. Monitor the factor exposures of the portfolio
4. Rebalance periodically to maintain the target factor exposures

## Advanced Usage: API Integration

For advanced users, the Portfolio Optimization application provides a REST API that can be used to integrate with other systems or automate portfolio management tasks.

### Example: Automating Portfolio Rebalancing

```python
import requests
import pandas as pd
import json

# API base URL
base_url = "https://api.portfolio-optimization.com/api/v1"

# Authentication
auth_response = requests.post(
    f"{base_url}/auth/login",
    json={"username": "your_username", "password": "your_password"}
)
token = auth_response.json()["access_token"]
headers = {"Authorization": f"Bearer {token}"}

# Get current portfolio
portfolio_response = requests.get(
    f"{base_url}/portfolio/1",  # Portfolio ID
    headers=headers
)
portfolio = portfolio_response.json()

# Get current market data
symbols = [asset["symbol"] for asset in portfolio["assets"]]
data_response = requests.get(
    f"{base_url}/data/stock",
    params={
        "symbols": ",".join(symbols),
        "start_date": "2023-01-01",
        "end_date": "2023-12-31",
        "interval": "1wk"
    },
    headers=headers
)
market_data = data_response.json()

# Optimize portfolio
optimize_response = requests.post(
    f"{base_url}/portfolio/optimize",
    json={
        "symbols": symbols,
        "start_date": "2023-01-01",
        "end_date": "2023-12-31",
        "method": "mean_variance",
        "objective": "sharpe",
        "constraints": {
            "min_weight": 0.05,
            "max_weight": 0.3
        }
    },
    headers=headers
)
optimization_result = optimize_response.json()

# Get rebalancing trades
current_weights = {asset["symbol"]: asset["weight"] for asset in portfolio["allocations"]}
target_weights = optimization_result["weights"]

# Calculate trades
trades = []
for symbol in symbols:
    current = current_weights.get(symbol, 0)
    target = target_weights.get(symbol, 0)
    if abs(target - current) > 0.01:  # 1% threshold for rebalancing
        trades.append({
            "symbol": symbol,
            "current_weight": current,
            "target_weight": target,
            "action": "buy" if target > current else "sell"
        })

print(json.dumps(trades, indent=2))
```

This script:
1. Authenticates with the API
2. Retrieves the current portfolio
3. Gets the latest market data
4. Runs portfolio optimization
5. Calculates the trades needed to rebalance the portfolio

You can schedule this script to run periodically (e.g., monthly) to automate your portfolio rebalancing process.
