"""
Portfolio repository for database operations.
"""
from typing import Dict, List, Optional

from sqlalchemy.orm import Session

from app.db.models import Asset, Portfolio, portfolio_assets
from app.db.repositories.base import BaseRepository


class PortfolioRepository(BaseRepository[Portfolio, Dict, Dict]):
    """
    Portfolio repository for database operations.
    """
    
    def __init__(self):
        """Initialize the repository."""
        super().__init__(Portfolio)
    
    def get_by_user_id(self, db: Session, user_id: int) -> List[Portfolio]:
        """
        Get portfolios by user ID.
        
        Args:
            db: Database session
            user_id: User ID
            
        Returns:
            List of portfolios
        """
        return db.query(self.model).filter(self.model.user_id == user_id).all()
    
    def get_by_name(self, db: Session, user_id: int, name: str) -> Optional[Portfolio]:
        """
        Get a portfolio by name and user ID.
        
        Args:
            db: Database session
            user_id: User ID
            name: Portfolio name
            
        Returns:
            Portfolio if found, None otherwise
        """
        return db.query(self.model).filter(
            self.model.user_id == user_id,
            self.model.name == name
        ).first()
    
    def add_asset(
        self,
        db: Session,
        portfolio_id: int,
        asset_id: int,
        weight: float,
        shares: Optional[int] = None
    ) -> None:
        """
        Add an asset to a portfolio.
        
        Args:
            db: Database session
            portfolio_id: Portfolio ID
            asset_id: Asset ID
            weight: Asset weight in the portfolio
            shares: Number of shares (optional)
        """
        # Check if the asset is already in the portfolio
        stmt = portfolio_assets.select().where(
            portfolio_assets.c.portfolio_id == portfolio_id,
            portfolio_assets.c.asset_id == asset_id
        )
        result = db.execute(stmt).first()
        
        if result:
            # Update existing asset
            stmt = portfolio_assets.update().where(
                portfolio_assets.c.portfolio_id == portfolio_id,
                portfolio_assets.c.asset_id == asset_id
            ).values(weight=weight, shares=shares)
            db.execute(stmt)
        else:
            # Add new asset
            stmt = portfolio_assets.insert().values(
                portfolio_id=portfolio_id,
                asset_id=asset_id,
                weight=weight,
                shares=shares
            )
            db.execute(stmt)
        
        db.commit()
    
    def remove_asset(self, db: Session, portfolio_id: int, asset_id: int) -> None:
        """
        Remove an asset from a portfolio.
        
        Args:
            db: Database session
            portfolio_id: Portfolio ID
            asset_id: Asset ID
        """
        stmt = portfolio_assets.delete().where(
            portfolio_assets.c.portfolio_id == portfolio_id,
            portfolio_assets.c.asset_id == asset_id
        )
        db.execute(stmt)
        db.commit()
    
    def get_assets(self, db: Session, portfolio_id: int) -> List[Dict]:
        """
        Get assets in a portfolio.
        
        Args:
            db: Database session
            portfolio_id: Portfolio ID
            
        Returns:
            List of assets with weights and shares
        """
        # Query assets in the portfolio
        query = db.query(
            Asset,
            portfolio_assets.c.weight,
            portfolio_assets.c.shares
        ).join(
            portfolio_assets,
            Asset.id == portfolio_assets.c.asset_id
        ).filter(
            portfolio_assets.c.portfolio_id == portfolio_id
        )
        
        # Convert to list of dictionaries
        result = []
        for asset, weight, shares in query:
            result.append({
                "id": asset.id,
                "symbol": asset.symbol,
                "name": asset.name,
                "weight": weight,
                "shares": shares
            })
        
        return result


# Create a singleton instance
portfolio_repository = PortfolioRepository()
