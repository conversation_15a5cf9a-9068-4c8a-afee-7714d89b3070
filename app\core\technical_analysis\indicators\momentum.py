"""
Momentum indicators implementation.
"""
from typing import Dict, List, Optional, Union

import numpy as np
import pandas as pd
import pandas_ta as ta

from app.core.technical_analysis.indicators.base import BaseIndicator
from app.utils.logging import logger


class RSI(BaseIndicator):
    """
    Relative Strength Index (RSI) indicator.
    """

    def __init__(self):
        """Initialize the RSI indicator."""
        super().__init__("RSI")

    def calculate(
        self,
        data: pd.DataFrame,
        period: int = 14,
        column: str = "close",
        **kwargs
    ) -> pd.DataFrame:
        """
        Calculate the RSI indicator.

        Args:
            data: DataFrame with OHLCV data
            period: Period for the RSI calculation
            column: Column to use for calculation
            **kwargs: Additional parameters

        Returns:
            DataFrame with the indicator values
        """
        # Validate data
        if not self.validate_data(data, [column]):
            logger.warning(f"Invalid data for {self.name} calculation")
            return data

        # Make a copy of the data
        result = data.copy()

        # Calculate RSI
        result[f"rsi_{period}"] = ta.rsi(result[column], length=period)

        return result

    def get_signal(
        self,
        data: pd.DataFrame,
        period: int = 14,
        overbought: float = 70.0,
        oversold: float = 30.0,
        **kwargs
    ) -> pd.DataFrame:
        """
        Get trading signals from the RSI indicator.

        Args:
            data: DataFrame with indicator values
            period: Period for the RSI calculation
            overbought: Overbought threshold
            oversold: Oversold threshold
            **kwargs: Additional parameters

        Returns:
            DataFrame with trading signals
        """
        # Calculate the indicator if not already calculated
        rsi_column = f"rsi_{period}"
        if rsi_column not in data.columns:
            data = self.calculate(data, period=period, **kwargs)

        # Make a copy of the data
        result = data.copy()

        # Generate signals
        # 1 = buy, -1 = sell, 0 = neutral
        result[f"{rsi_column}_signal"] = 0

        # RSI crosses below overbought threshold = sell signal
        result.loc[
            (result[rsi_column] < overbought) &
            (result[rsi_column].shift(1) >= overbought),
            f"{rsi_column}_signal"
        ] = -1

        # RSI crosses above oversold threshold = buy signal
        result.loc[
            (result[rsi_column] > oversold) &
            (result[rsi_column].shift(1) <= oversold),
            f"{rsi_column}_signal"
        ] = 1

        return result


class Stochastic(BaseIndicator):
    """
    Stochastic Oscillator indicator.
    """

    def __init__(self):
        """Initialize the Stochastic Oscillator indicator."""
        super().__init__("Stochastic")

    def calculate(
        self,
        data: pd.DataFrame,
        k_period: int = 14,
        d_period: int = 3,
        smooth_k: int = 3,
        **kwargs
    ) -> pd.DataFrame:
        """
        Calculate the Stochastic Oscillator indicator.

        Args:
            data: DataFrame with OHLCV data
            k_period: Period for the %K line
            d_period: Period for the %D line
            smooth_k: Smoothing for the %K line
            **kwargs: Additional parameters

        Returns:
            DataFrame with the indicator values
        """
        # Validate data
        if not self.validate_data(data, ["high", "low", "close"]):
            logger.warning(f"Invalid data for {self.name} calculation")
            return data

        # Make a copy of the data
        result = data.copy()

        # Calculate Stochastic Oscillator
        stoch = ta.stoch(
            result["high"],
            result["low"],
            result["close"],
            k=k_period,
            d=d_period,
            smooth_k=smooth_k
        )

        # Add Stochastic columns to the result
        result["stoch_k"] = stoch[f"STOCHk_{k_period}_{d_period}_{smooth_k}"]
        result["stoch_d"] = stoch[f"STOCHd_{k_period}_{d_period}_{smooth_k}"]

        return result

    def get_signal(
        self,
        data: pd.DataFrame,
        overbought: float = 80.0,
        oversold: float = 20.0,
        **kwargs
    ) -> pd.DataFrame:
        """
        Get trading signals from the Stochastic Oscillator indicator.

        Args:
            data: DataFrame with indicator values
            overbought: Overbought threshold
            oversold: Oversold threshold
            **kwargs: Additional parameters

        Returns:
            DataFrame with trading signals
        """
        # Calculate the indicator if not already calculated
        if "stoch_k" not in data.columns or "stoch_d" not in data.columns:
            data = self.calculate(data, **kwargs)

        # Make a copy of the data
        result = data.copy()

        # Generate signals
        # 1 = buy, -1 = sell, 0 = neutral
        result["stoch_signal"] = 0

        # %K crosses above %D in oversold region = buy signal
        result.loc[
            (result["stoch_k"] > result["stoch_d"]) &
            (result["stoch_k"].shift(1) <= result["stoch_d"].shift(1)) &
            (result["stoch_k"] < oversold),
            "stoch_signal"
        ] = 1

        # %K crosses below %D in overbought region = sell signal
        result.loc[
            (result["stoch_k"] < result["stoch_d"]) &
            (result["stoch_k"].shift(1) >= result["stoch_d"].shift(1)) &
            (result["stoch_k"] > overbought),
            "stoch_signal"
        ] = -1

        return result


class CCI(BaseIndicator):
    """
    Commodity Channel Index (CCI) indicator.

    The CCI is a versatile indicator that can be used to identify a new trend or warn of extreme conditions.
    It measures the current price level relative to an average price level over a given period of time.
    """

    def __init__(self):
        """Initialize the CCI indicator."""
        super().__init__("CCI")

    def calculate(
        self,
        data: pd.DataFrame,
        period: int = 20,
        constant: float = 0.015,
        **kwargs
    ) -> pd.DataFrame:
        """
        Calculate the CCI indicator.

        Args:
            data: DataFrame with OHLCV data
            period: Period for the CCI calculation
            constant: Constant multiplier (typically 0.015)
            **kwargs: Additional parameters

        Returns:
            DataFrame with the indicator values
        """
        # Validate data
        if not self.validate_data(data, ["high", "low", "close"]):
            logger.warning(f"Invalid data for {self.name} calculation")
            return data

        # Make a copy of the data
        result = data.copy()

        # Calculate CCI
        result[f"cci_{period}"] = ta.cci(
            result["high"],
            result["low"],
            result["close"],
            length=period,
            c=constant
        )

        return result

    def get_signal(
        self,
        data: pd.DataFrame,
        period: int = 20,
        overbought: float = 100.0,
        oversold: float = -100.0,
        **kwargs
    ) -> pd.DataFrame:
        """
        Get trading signals from the CCI indicator.

        Args:
            data: DataFrame with indicator values
            period: Period for the CCI calculation
            overbought: Overbought threshold
            oversold: Oversold threshold
            **kwargs: Additional parameters

        Returns:
            DataFrame with trading signals
        """
        # Calculate the indicator if not already calculated
        cci_column = f"cci_{period}"
        if cci_column not in data.columns:
            data = self.calculate(data, period=period, **kwargs)

        # Make a copy of the data
        result = data.copy()

        # Generate signals
        # 1 = buy, -1 = sell, 0 = neutral
        result[f"{cci_column}_signal"] = 0

        # CCI crosses below overbought threshold = sell signal
        result.loc[
            (result[cci_column] < overbought) &
            (result[cci_column].shift(1) >= overbought),
            f"{cci_column}_signal"
        ] = -1

        # CCI crosses above oversold threshold = buy signal
        result.loc[
            (result[cci_column] > oversold) &
            (result[cci_column].shift(1) <= oversold),
            f"{cci_column}_signal"
        ] = 1

        return result


class MFI(BaseIndicator):
    """
    Money Flow Index (MFI) indicator.

    The MFI is a momentum indicator that measures the flow of money into and out of a security over a specified period.
    It is related to the Relative Strength Index (RSI) but incorporates volume, whereas the RSI only considers price.
    """

    def __init__(self):
        """Initialize the MFI indicator."""
        super().__init__("MFI")

    def calculate(
        self,
        data: pd.DataFrame,
        period: int = 14,
        **kwargs
    ) -> pd.DataFrame:
        """
        Calculate the MFI indicator.

        Args:
            data: DataFrame with OHLCV data
            period: Period for the MFI calculation
            **kwargs: Additional parameters

        Returns:
            DataFrame with the indicator values
        """
        # Validate data
        if not self.validate_data(data, ["high", "low", "close", "volume"]):
            logger.warning(f"Invalid data for {self.name} calculation")
            return data

        # Make a copy of the data
        result = data.copy()

        # Calculate MFI
        result[f"mfi_{period}"] = ta.mfi(
            result["high"],
            result["low"],
            result["close"],
            result["volume"],
            length=period
        )

        return result

    def get_signal(
        self,
        data: pd.DataFrame,
        period: int = 14,
        overbought: float = 80.0,
        oversold: float = 20.0,
        **kwargs
    ) -> pd.DataFrame:
        """
        Get trading signals from the MFI indicator.

        Args:
            data: DataFrame with indicator values
            period: Period for the MFI calculation
            overbought: Overbought threshold
            oversold: Oversold threshold
            **kwargs: Additional parameters

        Returns:
            DataFrame with trading signals
        """
        # Calculate the indicator if not already calculated
        mfi_column = f"mfi_{period}"
        if mfi_column not in data.columns:
            data = self.calculate(data, period=period, **kwargs)

        # Make a copy of the data
        result = data.copy()

        # Generate signals
        # 1 = buy, -1 = sell, 0 = neutral
        result[f"{mfi_column}_signal"] = 0

        # MFI crosses below overbought threshold = sell signal
        result.loc[
            (result[mfi_column] < overbought) &
            (result[mfi_column].shift(1) >= overbought),
            f"{mfi_column}_signal"
        ] = -1

        # MFI crosses above oversold threshold = buy signal
        result.loc[
            (result[mfi_column] > oversold) &
            (result[mfi_column].shift(1) <= oversold),
            f"{mfi_column}_signal"
        ] = 1

        # Bullish divergence: price makes a lower low but MFI makes a higher low = strong buy signal
        for i in range(5, len(result)):
            if (
                result["close"].iloc[i] < result["close"].iloc[i-5] and  # Price made a lower low
                result[mfi_column].iloc[i] > result[mfi_column].iloc[i-5]  # MFI made a higher low
            ):
                result[f"{mfi_column}_signal"].iloc[i] = 2  # Strong buy signal

        # Bearish divergence: price makes a higher high but MFI makes a lower high = strong sell signal
        for i in range(5, len(result)):
            if (
                result["close"].iloc[i] > result["close"].iloc[i-5] and  # Price made a higher high
                result[mfi_column].iloc[i] < result[mfi_column].iloc[i-5]  # MFI made a lower high
            ):
                result[f"{mfi_column}_signal"].iloc[i] = -2  # Strong sell signal

        return result
