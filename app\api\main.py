"""
FastAPI application entry point.
"""
from datetime import date
from typing import Dict, List, Optional, Any

import pandas as pd
import numpy as np
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware

from app.api.v1.router import api_router
from app.db.database import init_db
from app.middleware.error_handler import add_error_handlers
from app.middleware.logging import LoggingMiddleware
from app.middleware.zero_division_handler import ZeroDivisionErrorMiddleware
from app.patches import apply_all_patches
from app.settings import get_settings
from app.utils.logging import logger

# Apply patches first, before any other initialization
apply_all_patches()

# Get settings
settings = get_settings()

# Initialize FastAPI app
app = FastAPI(
    title=settings.app["name"],  # Access as dictionary
    description=settings.app["description"],
    version=settings.app["version"],
    debug=settings.app["debug"]
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.api['cors_origins'],  # Changed to dictionary access
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add custom middleware
app.add_middleware(LoggingMiddleware)
app.add_middleware(ZeroDivisionErrorMiddleware)

# Add error handlers
add_error_handlers(app)

# Include routers
app.include_router(
    api_router,
    prefix=settings.api["prefix"]
)  # Fix: use settings.api.prefix instead of settings.api.prefix


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "name": settings.name,
        "version": settings.version,
        "description": settings.description,
        "api_docs": "/api/docs"
    }


@app.get("/test")
async def test():
    """Test endpoint."""
    return {"status": "ok", "message": "Test endpoint is working"}


@app.get("/api/v1/test")
async def api_test():
    """API test endpoint."""
    return {"status": "ok", "message": "API test endpoint is working"}


# Risk metrics functionality has been moved to a standalone server (risk_api.py)

# Direct rebalancing endpoint
from pydantic import BaseModel

# Define request model
class DirectRebalancingRequest(BaseModel):
    """Request model for direct portfolio rebalancing."""
    symbols: List[str]
    initial_weights: Dict[str, float]
    start_date: date
    end_date: date
    strategy: str
    strategy_params: Optional[Dict[str, Any]] = None
    total_portfolio_value: Optional[float] = None

# Define response model
class DirectRebalancingResponse(BaseModel):
    """Response model for direct portfolio rebalancing."""
    strategy: str
    strategy_params: Optional[Dict[str, Any]] = None
    performance_metrics: Dict[str, float]
    rebalancing_events: List[Dict[str, Any]]
    rebalancing_dates: List[str]
    weights_over_time: Dict[str, Dict[str, float]]
    portfolio_value: Dict[str, Any]
    drawdown: Dict[str, Any]
    cumulative_returns: Dict[str, Any]

@app.post("/direct_rebalance", response_model=DirectRebalancingResponse)
async def direct_rebalance_portfolio(request: DirectRebalancingRequest):
    """Direct portfolio rebalancing endpoint."""
    try:
        logger.info(f"Direct rebalancing endpoint called with symbols: {request.symbols}")

        # Generate sample data for demonstration
        dates = pd.date_range(start=request.start_date, end=request.end_date, freq='D')

        # Create sample price data for each symbol
        data = {}
        for symbol in request.symbols:
            # Generate random prices with an upward trend
            np.random.seed(hash(symbol) % 10000)  # Use symbol as seed for reproducibility
            prices = 100 + np.cumsum(np.random.normal(0.001, 0.02, len(dates)))
            df = pd.DataFrame({'close': prices}, index=dates)
            data[symbol] = df

        # Process and combine stock data
        prices_df = pd.DataFrame()
        for symbol, df in data.items():
            prices_df[symbol] = df['close']

        # Calculate returns
        returns_df = prices_df.pct_change().dropna()

        # Initialize portfolio
        initial_portfolio_value = request.total_portfolio_value or 10000
        current_weights = request.initial_weights.copy()
        portfolio_values = []
        weights_over_time = {}
        rebalancing_events = []
        rebalancing_dates = []

        # Simulate portfolio over time
        dates = returns_df.index.tolist()
        current_portfolio_value = initial_portfolio_value

        # Determine rebalancing frequency based on strategy
        if request.strategy == "periodic":
            frequency = request.strategy_params.get("frequency", "quarterly") if request.strategy_params else "quarterly"
            if frequency == "monthly":
                rebalance_every = 30
            elif frequency == "quarterly":
                rebalance_every = 90
            elif frequency == "annually":
                rebalance_every = 365
            else:
                rebalance_every = 90  # Default to quarterly
        else:
            rebalance_every = 90  # Default to quarterly for other strategies

        # Simulate portfolio over time
        for i, date_idx in enumerate(dates):
            date_str = date_idx.strftime('%Y-%m-%d')

            # Store current weights
            weights_over_time[date_str] = current_weights.copy()

            # Calculate portfolio return for the day
            daily_return = sum(current_weights.get(symbol, 0) * returns_df.loc[date_idx, symbol]
                              for symbol in request.symbols if symbol in returns_df.columns)

            # Update portfolio value
            current_portfolio_value *= (1 + daily_return)
            portfolio_values.append(current_portfolio_value)

            # Check if rebalancing is needed
            should_rebalance = False

            # Periodic rebalancing
            if i > 0 and i % rebalance_every == 0:
                should_rebalance = True

            # Threshold rebalancing (if strategy is threshold or hybrid)
            if request.strategy in ["threshold", "hybrid"] and i > 0:
                threshold = request.strategy_params.get("threshold", 0.05) if request.strategy_params else 0.05
                # Check if any weight deviates from target by more than threshold
                for symbol, target_weight in request.initial_weights.items():
                    current_weight = current_weights.get(symbol, 0)
                    if abs(current_weight - target_weight) > threshold:
                        should_rebalance = True
                        break

            if should_rebalance:
                # Rebalance portfolio
                current_weights = request.initial_weights.copy()
                rebalancing_events.append({
                    "date": date_str,
                    "action": "Rebalanced to target weights",
                    "transaction_cost": "$0.00"  # Simplified for now
                })
                rebalancing_dates.append(i)
            else:
                # Update weights based on performance
                total_value = sum(current_weights.get(symbol, 0) * (1 + returns_df.loc[date_idx, symbol])
                                 for symbol in request.symbols if symbol in returns_df.columns)

                if total_value > 0:
                    for symbol in request.symbols:
                        if symbol in returns_df.columns:
                            current_weights[symbol] = (current_weights.get(symbol, 0) *
                                                     (1 + returns_df.loc[date_idx, symbol])) / total_value

        # Calculate drawdown
        peak = [portfolio_values[0]]
        for i in range(1, len(portfolio_values)):
            peak.append(max(peak[i-1], portfolio_values[i]))

        drawdown = [(portfolio_values[i] - peak[i]) / peak[i] for i in range(len(portfolio_values))]

        # Calculate performance metrics
        total_return = (portfolio_values[-1] / portfolio_values[0]) - 1
        trading_days_per_year = 252
        years = len(portfolio_values) / trading_days_per_year
        annualized_return = (1 + total_return) ** (1 / years) - 1 if years > 0 else 0

        # Calculate volatility
        returns = [(portfolio_values[i] / portfolio_values[i-1]) - 1 for i in range(1, len(portfolio_values))]
        volatility = np.std(returns) * np.sqrt(trading_days_per_year)

        # Calculate Sharpe ratio (assuming risk-free rate of 0.02)
        risk_free_rate = 0.02
        sharpe_ratio = (annualized_return - risk_free_rate) / volatility if volatility > 0 else 0

        # Calculate Sortino ratio (downside deviation)
        negative_returns = [r for r in returns if r < 0]
        downside_deviation = np.std(negative_returns) * np.sqrt(trading_days_per_year) if negative_returns else 0.01
        sortino_ratio = (annualized_return - risk_free_rate) / downside_deviation if downside_deviation > 0 else 0

        # Prepare metrics
        metrics = {
            "total_return": total_return,
            "annualized_return": annualized_return,
            "volatility": volatility,
            "sharpe_ratio": sharpe_ratio,
            "sortino_ratio": sortino_ratio,
            "max_drawdown": min(drawdown)
        }

        # Return results
        return {
            "strategy": request.strategy,
            "strategy_params": request.strategy_params,
            "performance_metrics": metrics,
            "rebalancing_events": rebalancing_events,
            "rebalancing_dates": [dates[idx].strftime('%Y-%m-%d') for idx in rebalancing_dates],
            "weights_over_time": weights_over_time,
            "portfolio_value": {
                "dates": [date_idx.strftime('%Y-%m-%d') for date_idx in dates],
                "values": portfolio_values
            },
            "drawdown": {
                "dates": [date_idx.strftime('%Y-%m-%d') for date_idx in dates],
                "values": drawdown
            },
            "cumulative_returns": {
                "dates": [date_idx.strftime('%Y-%m-%d') for date_idx in dates],
                "values": [(portfolio_values[i] / portfolio_values[0]) - 1 for i in range(len(portfolio_values))]
            }
        }
    except Exception as e:
        logger.error(f"Error in direct portfolio rebalancing: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error in direct portfolio rebalancing: {str(e)}")




