{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Portfolio Optimization - Backtesting\n", "\n", "This notebook demonstrates how to backtest different portfolio optimization strategies using historical data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import sys\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime, timedelta\n", "import plotly.graph_objects as go\n", "import plotly.express as px\n", "from plotly.subplots import make_subplots\n", "\n", "# Add the project root to the path\n", "sys.path.append(os.path.abspath('..'))\n", "\n", "# Set plotting style\n", "plt.style.use('fivethirtyeight')\n", "sns.set_theme(style=\"whitegrid\")\n", "\n", "# Configure pandas display options\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', 1000)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. <PERSON><PERSON>\n", "\n", "First, we'll load historical stock data for our backtest."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import data manager\n", "from app.core.data_collection.data_manager import data_manager\n", "import asyncio\n", "\n", "# Define symbols and date range\n", "symbols = ['AAPL', 'MSFT', 'AMZN', 'GOOGL', 'META', 'TSLA', 'NVDA', 'JPM', 'V', 'JN<PERSON>']\n", "start_date = datetime.now() - <PERSON><PERSON><PERSON>(days=365*3)  # 3 years of data\n", "end_date = datetime.now()\n", "\n", "# Create an async function to get data\n", "async def get_data():\n", "    return await data_manager.get_multiple_stock_data(\n", "        symbols=symbols,\n", "        start_date=start_date.date(),\n", "        end_date=end_date.date(),\n", "        interval='1d'\n", "    )\n", "\n", "# Run the async function\n", "stock_data = asyncio.run(get_data())\n", "\n", "# Display the first few rows of the first stock\n", "stock_data[symbols[0]].head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Create Price DataFrame\n", "\n", "We'll create a DataFrame with adjusted close prices for all symbols."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extract adjusted close prices\n", "prices_df = pd.DataFrame()\n", "\n", "for symbol, data in stock_data.items():\n", "    prices_df[symbol] = data['Adj Close']\n", "\n", "# Set index to datetime\n", "prices_df.index = pd.to_datetime(prices_df.index)\n", "\n", "# Sort by date\n", "prices_df = prices_df.sort_index()\n", "\n", "# Display the first few rows\n", "prices_df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Define Backtesting Framework\n", "\n", "We'll create a simple backtesting framework to test different portfolio optimization strategies."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from app.core.portfolio.optimization import PortfolioOptimizer\n", "\n", "class BacktestFramework:\n", "    \"\"\"\n", "    A simple backtesting framework for portfolio optimization strategies.\n", "    \"\"\"\n", "    \n", "    def __init__(self, prices_df, initial_capital=10000):\n", "        \"\"\"\n", "        Initialize the backtesting framework.\n", "        \n", "        Args:\n", "            prices_df: DataFrame with adjusted close prices\n", "            initial_capital: Initial capital for the backtest\n", "        \"\"\"\n", "        self.prices_df = prices_df\n", "        self.initial_capital = initial_capital\n", "        self.optimizer = PortfolioOptimizer()\n", "        \n", "    def run_backtest(self, strategy, window=252, rebalance_freq=63):\n", "        \"\"\"\n", "        Run a backtest for a given strategy.\n", "        \n", "        Args:\n", "            strategy: Strategy function that returns weights\n", "            window: Lookback window for optimization (in trading days)\n", "            rebalance_freq: Rebalancing frequency (in trading days)\n", "            \n", "        Returns:\n", "            DataFrame with portfolio values and metrics\n", "        \"\"\"\n", "        # Initialize portfolio\n", "        portfolio_values = []\n", "        weights_history = []\n", "        current_weights = None\n", "        current_shares = None\n", "        \n", "        # Get all dates\n", "        dates = self.prices_df.index\n", "        \n", "        # Start with cash\n", "        portfolio_value = self.initial_capital\n", "        \n", "        # Loop through dates\n", "        for i in range(window, len(dates)):\n", "            current_date = dates[i]\n", "            \n", "            # Check if we need to rebalance\n", "            if i == window or (i - window) % rebalance_freq == 0:\n", "                # Get historical data for optimization\n", "                hist_data = self.prices_df.iloc[i-window:i]\n", "                \n", "                # Get new weights based on strategy\n", "                new_weights = strategy(hist_data)\n", "                \n", "                # Calculate shares to buy/sell\n", "                current_prices = self.prices_df.iloc[i]\n", "                current_shares = {}\n", "                \n", "                for symbol, weight in new_weights.items():\n", "                    # Calculate number of shares\n", "                    shares = (portfolio_value * weight) / current_prices[symbol]\n", "                    current_shares[symbol] = int(shares)  # Whole shares only\n", "                \n", "                # Update current weights\n", "                current_weights = new_weights\n", "                weights_history.append((current_date, current_weights))\n", "            \n", "            # Calculate portfolio value\n", "            if current_shares:\n", "                current_prices = self.prices_df.iloc[i]\n", "                portfolio_value = sum(current_shares[symbol] * current_prices[symbol] \n", "                                     for symbol in current_shares)\n", "            \n", "            # Record portfolio value\n", "            portfolio_values.append((current_date, portfolio_value))\n", "        \n", "        # Create results DataFrame\n", "        results_df = pd.DataFrame(portfolio_values, columns=['Date', 'Value'])\n", "        results_df.set_index('Date', inplace=True)\n", "        \n", "        # Calculate returns\n", "        results_df['Returns'] = results_df['Value'].pct_change()\n", "        \n", "        # Calculate metrics\n", "        results_df['Cumulative Returns'] = (1 + results_df['Returns']).cumprod() - 1\n", "        \n", "        # Create weights DataFrame\n", "        weights_df = pd.DataFrame([{**{'Date': date}, **weights} \n", "                                  for date, weights in weights_history])\n", "        weights_df.set_index('Date', inplace=True)\n", "        \n", "        return results_df, weights_df\n", "    \n", "    def calculate_metrics(self, results_df):\n", "        \"\"\"\n", "        Calculate performance metrics for a backtest.\n", "        \n", "        Args:\n", "            results_df: DataFrame with backtest results\n", "            \n", "        Returns:\n", "            Dictionary with performance metrics\n", "        \"\"\"\n", "        # Calculate metrics\n", "        returns = results_df['Returns'].dropna()\n", "        total_return = results_df['Value'].iloc[-1] / results_df['Value'].iloc[0] - 1\n", "        annual_return = (1 + total_return) ** (252 / len(returns)) - 1\n", "        annual_volatility = returns.std() * np.sqrt(252)\n", "        sharpe_ratio = annual_return / annual_volatility\n", "        max_drawdown = (results_df['Value'] / results_df['Value'].cummax() - 1).min()\n", "        \n", "        # Create metrics dictionary\n", "        metrics = {\n", "            'Total Return': total_return,\n", "            'Annual Return': annual_return,\n", "            'Annual Volatility': annual_volatility,\n", "            'Sharpe Ratio': sharpe_ratio,\n", "            'Max Drawdown': max_drawdown\n", "        }\n", "        \n", "        return metrics"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Define Portfolio Optimization Strategies\n", "\n", "We'll define several portfolio optimization strategies to test."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pypfopt import EfficientFrontier, expected_returns, risk_models\n", "\n", "def equal_weight_strategy(hist_data):\n", "    \"\"\"\n", "    Equal weight strategy.\n", "    \n", "    Args:\n", "        hist_data: Historical price data\n", "        \n", "    Returns:\n", "        Dictionary with weights\n", "    \"\"\"\n", "    n_assets = len(hist_data.columns)\n", "    weight = 1.0 / n_assets\n", "    return {symbol: weight for symbol in hist_data.columns}\n", "\n", "def min_volatility_strategy(hist_data):\n", "    \"\"\"\n", "    Minimum volatility strategy.\n", "    \n", "    Args:\n", "        hist_data: Historical price data\n", "        \n", "    Returns:\n", "        Dictionary with weights\n", "    \"\"\"\n", "    # Calculate expected returns and covariance matrix\n", "    mu = expected_returns.mean_historical_return(hist_data)\n", "    S = risk_models.sample_cov(hist_data)\n", "    \n", "    # Optimize for minimum volatility\n", "    ef = EfficientFrontier(mu, S)\n", "    weights = ef.min_volatility()\n", "    \n", "    # Clean weights\n", "    cleaned_weights = ef.clean_weights()\n", "    \n", "    return cleaned_weights\n", "\n", "def max_sharpe_strategy(hist_data):\n", "    \"\"\"\n", "    Maximum Sharpe ratio strategy.\n", "    \n", "    Args:\n", "        hist_data: Historical price data\n", "        \n", "    Returns:\n", "        Dictionary with weights\n", "    \"\"\"\n", "    # Calculate expected returns and covariance matrix\n", "    mu = expected_returns.mean_historical_return(hist_data)\n", "    S = risk_models.sample_cov(hist_data)\n", "    \n", "    # Optimize for maximum Sharpe ratio\n", "    ef = EfficientFrontier(mu, S)\n", "    weights = ef.max_sharpe()\n", "    \n", "    # Clean weights\n", "    cleaned_weights = ef.clean_weights()\n", "    \n", "    return cleaned_weights\n", "\n", "def risk_parity_strategy(hist_data):\n", "    \"\"\"\n", "    Risk parity strategy.\n", "    \n", "    Args:\n", "        hist_data: Historical price data\n", "        \n", "    Returns:\n", "        Dictionary with weights\n", "    \"\"\"\n", "    # Calculate covariance matrix\n", "    S = risk_models.sample_cov(hist_data)\n", "    \n", "    # Calculate asset volatilities\n", "    vols = np.sqrt(np.diag(S))\n", "    \n", "    # Calculate inverse volatility weights\n", "    inv_vols = 1 / vols\n", "    weights = inv_vols / np.sum(inv_vols)\n", "    \n", "    # Create weights dictionary\n", "    return {symbol: weight for symbol, weight in zip(hist_data.columns, weights)}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. <PERSON> Backtests\n", "\n", "Now we'll run backtests for each strategy."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize backtesting framework\n", "backtest = BacktestFramework(prices_df)\n", "\n", "# Define strategies to test\n", "strategies = {\n", "    'Equal Weight': equal_weight_strategy,\n", "    'Minimum Volatility': min_volatility_strategy,\n", "    'Maximum Sharpe': max_sharpe_strategy,\n", "    'Risk Parity': risk_parity_strategy\n", "}\n", "\n", "# Run backtests\n", "results = {}\n", "weights = {}\n", "metrics = {}\n", "\n", "for name, strategy in strategies.items():\n", "    print(f\"Running backtest for {name} strategy...\")\n", "    results[name], weights[name] = backtest.run_backtest(strategy)\n", "    metrics[name] = backtest.calculate_metrics(results[name])\n", "    print(f\"Completed backtest for {name} strategy.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. <PERSON><PERSON><PERSON> Results\n", "\n", "Let's analyze the results of our backtests."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a DataFrame with metrics\n", "metrics_df = pd.DataFrame(metrics).T\n", "metrics_df = metrics_df.round(4)\n", "\n", "# Display metrics\n", "metrics_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot portfolio values\n", "fig = go.Figure()\n", "\n", "for name, result in results.items():\n", "    fig.add_trace(go.<PERSON>(\n", "        x=result.index,\n", "        y=result['Value'],\n", "        mode='lines',\n", "        name=name\n", "    ))\n", "\n", "fig.update_layout(\n", "    title='Portfolio Value Over Time',\n", "    xaxis_title='Date',\n", "    yaxis_title='Portfolio Value ($)',\n", "    legend_title='Strategy',\n", "    height=600,\n", "    width=1000\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot cumulative returns\n", "fig = go.Figure()\n", "\n", "for name, result in results.items():\n", "    fig.add_trace(go.<PERSON>(\n", "        x=result.index,\n", "        y=result['Cumulative Returns'],\n", "        mode='lines',\n", "        name=name\n", "    ))\n", "\n", "fig.update_layout(\n", "    title='Cumulative Returns Over Time',\n", "    xaxis_title='Date',\n", "    yaxis_title='Cumulative Returns',\n", "    legend_title='Strategy',\n", "    height=600,\n", "    width=1000\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. <PERSON><PERSON><PERSON> Portfolio Weights\n", "\n", "Let's analyze how portfolio weights change over time for each strategy."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot weights for each strategy\n", "for name, weight_df in weights.items():\n", "    fig = px.area(\n", "        weight_df,\n", "        title=f\"{name} Strategy - Portfolio Weights Over Time\",\n", "        height=500,\n", "        width=1000\n", "    )\n", "    \n", "    fig.update_layout(\n", "        xaxis_title='Date',\n", "        yaxis_title='Weight',\n", "        legend_title='Asset'\n", "    )\n", "    \n", "    fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Drawdown Analysis\n", "\n", "Let's analyze drawdowns for each strategy."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate drawdowns for each strategy\n", "drawdowns = {}\n", "\n", "for name, result in results.items():\n", "    # Calculate drawdown\n", "    drawdown = result['Value'] / result['Value'].cummax() - 1\n", "    drawdowns[name] = drawdown\n", "\n", "# Plot drawdowns\n", "fig = go.Figure()\n", "\n", "for name, drawdown in drawdowns.items():\n", "    fig.add_trace(go.<PERSON>(\n", "        x=drawdown.index,\n", "        y=drawdown,\n", "        mode='lines',\n", "        name=name\n", "    ))\n", "\n", "fig.update_layout(\n", "    title='Portfolio Drawdowns Over Time',\n", "    xaxis_title='Date',\n", "    yaxis_title='Drawdown',\n", "    legend_title='Strategy',\n", "    height=600,\n", "    width=1000\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Rolling Performance Analysis\n", "\n", "Let's analyze rolling performance metrics for each strategy."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate rolling metrics\n", "window = 63  # ~3 months\n", "\n", "rolling_returns = {}\n", "rolling_volatility = {}\n", "rolling_sharpe = {}\n", "\n", "for name, result in results.items():\n", "    # Calculate rolling returns (annualized)\n", "    rolling_ret = result['Returns'].rolling(window).mean() * 252\n", "    rolling_returns[name] = rolling_ret\n", "    \n", "    # Calculate rolling volatility (annualized)\n", "    rolling_vol = result['Returns'].rolling(window).std() * np.sqrt(252)\n", "    rolling_volatility[name] = rolling_vol\n", "    \n", "    # Calculate rolling Sharpe ratio\n", "    rolling_sharpe[name] = rolling_ret / rolling_vol\n", "\n", "# Plot rolling returns\n", "fig = go.Figure()\n", "\n", "for name, rolling_ret in rolling_returns.items():\n", "    fig.add_trace(go.<PERSON>(\n", "        x=rolling_ret.index,\n", "        y=rolling_ret,\n", "        mode='lines',\n", "        name=name\n", "    ))\n", "\n", "fig.update_layout(\n", "    title=f'Rolling {window}-Day Annualized Returns',\n", "    xaxis_title='Date',\n", "    yaxis_title='Annualized Return',\n", "    legend_title='Strategy',\n", "    height=600,\n", "    width=1000\n", ")\n", "\n", "fig.show()\n", "\n", "# Plot rolling volatility\n", "fig = go.Figure()\n", "\n", "for name, rolling_vol in rolling_volatility.items():\n", "    fig.add_trace(go.<PERSON>(\n", "        x=rolling_vol.index,\n", "        y=rolling_vol,\n", "        mode='lines',\n", "        name=name\n", "    ))\n", "\n", "fig.update_layout(\n", "    title=f'Rolling {window}-Day Annualized Volatility',\n", "    xaxis_title='Date',\n", "    yaxis_title='Annualized Volatility',\n", "    legend_title='Strategy',\n", "    height=600,\n", "    width=1000\n", ")\n", "\n", "fig.show()\n", "\n", "# Plot rolling Sharpe ratio\n", "fig = go.Figure()\n", "\n", "for name, rolling_sr in rolling_sharpe.items():\n", "    fig.add_trace(go.<PERSON>(\n", "        x=rolling_sr.index,\n", "        y=rolling_sr,\n", "        mode='lines',\n", "        name=name\n", "    ))\n", "\n", "fig.update_layout(\n", "    title=f'Rolling {window}-Day <PERSON>',\n", "    xaxis_title='Date',\n", "    yaxis_title='<PERSON>',\n", "    legend_title='Strategy',\n", "    height=600,\n", "    width=1000\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. Conc<PERSON>\n", "\n", "In this notebook, we've backtested several portfolio optimization strategies:\n", "\n", "1. Equal Weight\n", "2. Minimum Volatility\n", "3. <PERSON>\n", "4. Risk Parity\n", "\n", "Based on the results, we can draw the following conclusions:\n", "\n", "- The Maximum Sharpe Ratio strategy typically provides the highest risk-adjusted returns but may have higher turnover.\n", "- The Minimum Volatility strategy offers lower returns but with significantly reduced risk.\n", "- The Equal Weight strategy is simple but can be effective, especially in bull markets.\n", "- The Risk Parity strategy provides a good balance between risk and return.\n", "\n", "The choice of strategy depends on the investor's risk tolerance, investment horizon, and market conditions. It's also important to consider transaction costs and taxes when implementing these strategies in practice."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}