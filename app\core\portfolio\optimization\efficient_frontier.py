"""
Efficient Frontier implementation for portfolio optimization.

This module provides functions to calculate and visualize the efficient frontier
for a set of assets, based on Modern Portfolio Theory (MPT).
"""
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
from pypfopt import EfficientFrontier, expected_returns, risk_models
from pypfopt.discrete_allocation import DiscreteAllocation
from pypfopt.efficient_frontier import EfficientCVaR

from app.utils.constants import DEFAULT_RISK_FREE_RATE
from app.utils.logging import logger


class EfficientFrontierOptimizer:
    """
    Efficient Frontier optimizer for portfolio optimization.
    """
    
    def __init__(self, risk_free_rate: float = DEFAULT_RISK_FREE_RATE):
        """
        Initialize the Efficient Frontier optimizer.
        
        Args:
            risk_free_rate: Risk-free rate (default: from settings)
        """
        self.risk_free_rate = risk_free_rate
    
    def calculate_efficient_frontier(
        self,
        returns: pd.DataFrame,
        n_points: int = 100,
        expected_returns_method: str = "mean_historical_return",
        risk_model_method: str = "sample_cov",
        min_return: Optional[float] = None,
        max_return: Optional[float] = None,
        **kwargs
    ) -> pd.DataFrame:
        """
        Calculate the efficient frontier for a set of assets.
        
        Args:
            returns: DataFrame of asset returns
            n_points: Number of points to calculate on the frontier
            expected_returns_method: Method to calculate expected returns
            risk_model_method: Method to calculate the risk model
            min_return: Minimum return to consider (if None, use minimum asset return)
            max_return: Maximum return to consider (if None, use maximum asset return)
            **kwargs: Additional parameters for expected returns and risk model
            
        Returns:
            DataFrame with efficient frontier points (volatility, return)
        """
        # Validate inputs
        if returns is None or returns.empty:
            logger.error("Returns must be provided for efficient frontier calculation")
            return pd.DataFrame(columns=["volatility", "return"])
        
        # Calculate expected returns
        if expected_returns_method == "mean_historical_return":
            mu = expected_returns.mean_historical_return(returns, **kwargs)
        elif expected_returns_method == "ema_historical_return":
            mu = expected_returns.ema_historical_return(returns, **kwargs)
        elif expected_returns_method == "capm_return":
            mu = expected_returns.capm_return(returns, **kwargs)
        else:
            logger.warning(f"Unknown expected returns method: {expected_returns_method}, using mean historical return")
            mu = expected_returns.mean_historical_return(returns, **kwargs)
        
        # Calculate risk model
        if risk_model_method == "sample_cov":
            sigma = risk_models.sample_cov(returns, **kwargs)
        elif risk_model_method == "semicovariance":
            sigma = risk_models.semicovariance(returns, **kwargs)
        elif risk_model_method == "exp_cov":
            sigma = risk_models.exp_cov(returns, **kwargs)
        elif risk_model_method == "ledoit_wolf":
            sigma = risk_models.risk_matrix(returns, method="ledoit_wolf", **kwargs)
        else:
            logger.warning(f"Unknown risk model method: {risk_model_method}, using sample covariance")
            sigma = risk_models.sample_cov(returns, **kwargs)
        
        # Get minimum volatility portfolio
        ef = EfficientFrontier(mu, sigma)
        ef.min_volatility()
        min_vol_ret, min_vol_std, _ = ef.portfolio_performance()
        
        # Get maximum Sharpe ratio portfolio
        ef = EfficientFrontier(mu, sigma)
        ef.max_sharpe(risk_free_rate=self.risk_free_rate)
        max_sharpe_ret, max_sharpe_std, _ = ef.portfolio_performance()
        
        # Get maximum return portfolio
        ef = EfficientFrontier(mu, sigma)
        ef.max_return()
        max_ret, max_ret_std, _ = ef.portfolio_performance()
        
        # Determine return range
        if min_return is None:
            min_return = min_vol_ret
        if max_return is None:
            max_return = max_ret
        
        # Generate efficient frontier points
        returns_range = np.linspace(min_return, max_return, n_points)
        frontier_volatilities = []
        
        for target_return in returns_range:
            ef = EfficientFrontier(mu, sigma)
            try:
                ef.efficient_return(target_return)
                _, volatility, _ = ef.portfolio_performance()
                frontier_volatilities.append(volatility)
            except Exception as e:
                logger.warning(f"Error calculating efficient frontier point for return {target_return}: {e}")
                frontier_volatilities.append(np.nan)
        
        # Create DataFrame with efficient frontier points
        ef_df = pd.DataFrame({
            "return": returns_range,
            "volatility": frontier_volatilities
        })
        
        # Remove NaN values
        ef_df = ef_df.dropna()
        
        # Add key portfolios
        key_portfolios = pd.DataFrame({
            "return": [min_vol_ret, max_sharpe_ret, max_ret],
            "volatility": [min_vol_std, max_sharpe_std, max_ret_std],
            "portfolio": ["Minimum Volatility", "Maximum Sharpe", "Maximum Return"]
        })
        
        # Add individual assets
        asset_returns = mu.values
        asset_volatilities = np.sqrt(np.diag(sigma.values))
        
        assets_df = pd.DataFrame({
            "return": asset_returns,
            "volatility": asset_volatilities,
            "portfolio": mu.index
        })
        
        # Combine all data
        result = {
            "frontier": ef_df,
            "key_portfolios": key_portfolios,
            "assets": assets_df
        }
        
        return result
    
    def optimize_portfolio(
        self,
        returns: pd.DataFrame,
        objective: str = "max_sharpe",
        expected_returns_method: str = "mean_historical_return",
        risk_model_method: str = "sample_cov",
        constraints: Optional[Dict] = None,
        **kwargs
    ) -> Dict:
        """
        Optimize a portfolio for a specific objective.
        
        Args:
            returns: DataFrame of asset returns
            objective: Optimization objective ('max_sharpe', 'min_volatility', 'efficient_risk', 'efficient_return')
            expected_returns_method: Method to calculate expected returns
            risk_model_method: Method to calculate the risk model
            constraints: Additional constraints for optimization
            **kwargs: Additional parameters for expected returns and risk model
            
        Returns:
            Dictionary with optimization results
        """
        # Validate inputs
        if returns is None or returns.empty:
            logger.error("Returns must be provided for portfolio optimization")
            return {}
        
        # Calculate expected returns
        if expected_returns_method == "mean_historical_return":
            mu = expected_returns.mean_historical_return(returns, **kwargs)
        elif expected_returns_method == "ema_historical_return":
            mu = expected_returns.ema_historical_return(returns, **kwargs)
        elif expected_returns_method == "capm_return":
            mu = expected_returns.capm_return(returns, **kwargs)
        else:
            logger.warning(f"Unknown expected returns method: {expected_returns_method}, using mean historical return")
            mu = expected_returns.mean_historical_return(returns, **kwargs)
        
        # Calculate risk model
        if risk_model_method == "sample_cov":
            sigma = risk_models.sample_cov(returns, **kwargs)
        elif risk_model_method == "semicovariance":
            sigma = risk_models.semicovariance(returns, **kwargs)
        elif risk_model_method == "exp_cov":
            sigma = risk_models.exp_cov(returns, **kwargs)
        elif risk_model_method == "ledoit_wolf":
            sigma = risk_models.risk_matrix(returns, method="ledoit_wolf", **kwargs)
        else:
            logger.warning(f"Unknown risk model method: {risk_model_method}, using sample covariance")
            sigma = risk_models.sample_cov(returns, **kwargs)
        
        # Create efficient frontier
        ef = EfficientFrontier(mu, sigma)
        
        # Apply constraints if provided
        if constraints:
            if "weight_bounds" in constraints:
                min_weight, max_weight = constraints["weight_bounds"]
                ef = EfficientFrontier(mu, sigma, weight_bounds=(min_weight, max_weight))
            
            if "sector_constraints" in constraints and "sector_mapper" in constraints:
                for sector, (min_weight, max_weight) in constraints["sector_constraints"].items():
                    sector_assets = constraints["sector_mapper"].get(sector, [])
                    if sector_assets:
                        ef.add_sector_constraints(sector_assets, min_weight, max_weight)
        
        # Optimize portfolio based on objective
        if objective == "max_sharpe":
            weights = ef.max_sharpe(risk_free_rate=self.risk_free_rate)
        elif objective == "min_volatility":
            weights = ef.min_volatility()
        elif objective == "efficient_risk":
            target_risk = kwargs.get("target_risk", 0.2)
            weights = ef.efficient_risk(target_risk)
        elif objective == "efficient_return":
            target_return = kwargs.get("target_return", 0.2)
            weights = ef.efficient_return(target_return)
        else:
            logger.warning(f"Unknown objective: {objective}, using maximum Sharpe ratio")
            weights = ef.max_sharpe(risk_free_rate=self.risk_free_rate)
        
        # Get cleaned weights
        cleaned_weights = ef.clean_weights()
        
        # Get portfolio performance
        expected_return, volatility, sharpe = ef.portfolio_performance(risk_free_rate=self.risk_free_rate)
        
        # Calculate discrete allocation if total portfolio value is provided
        discrete_allocation = None
        if "total_portfolio_value" in kwargs and kwargs["total_portfolio_value"] > 0:
            latest_prices = kwargs.get("latest_prices")
            if latest_prices is not None:
                da = DiscreteAllocation(
                    cleaned_weights,
                    latest_prices,
                    total_portfolio_value=kwargs["total_portfolio_value"]
                )
                allocation, leftover = da.greedy_portfolio()
                discrete_allocation = {
                    "allocation": allocation,
                    "leftover": leftover
                }
        
        # Return results
        result = {
            "weights": cleaned_weights,
            "expected_return": expected_return,
            "volatility": volatility,
            "sharpe_ratio": sharpe,
            "discrete_allocation": discrete_allocation
        }
        
        return result
