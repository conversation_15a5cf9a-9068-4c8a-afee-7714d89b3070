"""
Pytest configuration file.
"""
import os
import sys
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

# Add the project root to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.api.main import app
from app.db.base import Base
from app.db.session import get_db
from app.settings import get_settings

# Override settings for testing
os.environ["APP_ENV"] = "testing"


@pytest.fixture(scope="session")
def settings():
    """Return application settings for testing."""
    return get_settings()


@pytest.fixture(scope="session")
def db_engine():
    """Create a SQLAlchemy engine for testing."""
    # Use in-memory SQLite for testing
    engine = create_engine(
        "sqlite:///:memory:",
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
    # Create all tables
    Base.metadata.create_all(bind=engine)
    yield engine
    # Drop all tables
    Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def db_session(db_engine):
    """Create a SQLAlchemy session for testing."""
    # Create a new session for each test
    Session = sessionmaker(autocommit=False, autoflush=False, bind=db_engine)
    session = Session()
    try:
        yield session
    finally:
        session.rollback()
        session.close()


@pytest.fixture(scope="function")
def client(db_session):
    """Create a FastAPI test client."""
    # Override the get_db dependency
    def override_get_db():
        try:
            yield db_session
        finally:
            pass

    app.dependency_overrides[get_db] = override_get_db
    with TestClient(app) as test_client:
        yield test_client
    # Remove the override
    app.dependency_overrides.clear()


@pytest.fixture(scope="session")
def test_data():
    """Return test data for testing."""
    return {
        "symbols": ["AAPL", "MSFT", "AMZN", "GOOGL", "META"],
        "start_date": "2020-01-01",
        "end_date": "2020-12-31",
        "risk_free_rate": 0.02,
    }
