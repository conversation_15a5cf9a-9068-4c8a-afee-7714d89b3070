"""
Risk visualization components for Streamlit.
"""
import numpy as np
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import streamlit as st
from typing import Dict, List, Optional, Tuple, Union

from app.utils.logging import logger


def plot_efficient_frontier(
    expected_returns: pd.Series,
    cov_matrix: pd.DataFrame,
    weights: Dict[str, float],
    n_portfolios: int = 1000,
    risk_free_rate: float = 0.02,
    height: int = 600,
    width: int = 800
) -> go.Figure:
    """
    Plot the efficient frontier with the current portfolio.
    
    Args:
        expected_returns: Series of expected returns for each asset
        cov_matrix: Covariance matrix of returns
        weights: Dictionary mapping assets to weights
        n_portfolios: Number of random portfolios to generate
        risk_free_rate: Risk-free rate for Sharpe ratio calculation
        height: Plot height
        width: Plot width
        
    Returns:
        Plotly figure with the efficient frontier
    """
    try:
        # Convert weights to array
        assets = expected_returns.index.tolist()
        weights_array = np.array([weights.get(asset, 0) for asset in assets])
        
        # Calculate portfolio expected return and volatility
        portfolio_return = expected_returns.dot(weights_array)
        portfolio_volatility = np.sqrt(weights_array.T @ cov_matrix.values @ weights_array)
        
        # Calculate portfolio Sharpe ratio
        portfolio_sharpe = (portfolio_return - risk_free_rate) / portfolio_volatility
        
        # Generate random portfolios
        results = np.zeros((n_portfolios, 3))
        for i in range(n_portfolios):
            # Generate random weights
            random_weights = np.random.random(len(assets))
            random_weights = random_weights / np.sum(random_weights)
            
            # Calculate portfolio return and volatility
            portfolio_return = expected_returns.dot(random_weights)
            portfolio_volatility = np.sqrt(random_weights.T @ cov_matrix.values @ random_weights)
            
            # Calculate Sharpe ratio
            sharpe_ratio = (portfolio_return - risk_free_rate) / portfolio_volatility
            
            # Store results
            results[i, 0] = portfolio_volatility
            results[i, 1] = portfolio_return
            results[i, 2] = sharpe_ratio
        
        # Create DataFrame with results
        columns = ['volatility', 'return', 'sharpe']
        df = pd.DataFrame(results, columns=columns)
        
        # Create figure
        fig = px.scatter(
            df, x='volatility', y='return', color='sharpe',
            color_continuous_scale='viridis',
            title='Efficient Frontier',
            labels={'volatility': 'Volatility (Standard Deviation)',
                   'return': 'Expected Return',
                   'sharpe': 'Sharpe Ratio'},
            height=height, width=width
        )
        
        # Add current portfolio
        fig.add_trace(
            go.Scatter(
                x=[portfolio_volatility],
                y=[portfolio_return],
                mode='markers',
                marker=dict(
                    color='red',
                    size=15,
                    symbol='star'
                ),
                name='Current Portfolio'
            )
        )
        
        # Add Capital Market Line
        max_sharpe_idx = df['sharpe'].idxmax()
        max_sharpe_volatility = df.loc[max_sharpe_idx, 'volatility']
        max_sharpe_return = df.loc[max_sharpe_idx, 'return']
        
        fig.add_trace(
            go.Scatter(
                x=[0, max_sharpe_volatility * 1.5],
                y=[risk_free_rate, risk_free_rate + (max_sharpe_return - risk_free_rate) * 1.5],
                mode='lines',
                line=dict(color='black', dash='dash'),
                name='Capital Market Line'
            )
        )
        
        # Update layout
        fig.update_layout(
            xaxis_title='Volatility (Standard Deviation)',
            yaxis_title='Expected Return',
            legend=dict(
                yanchor="top",
                y=0.99,
                xanchor="left",
                x=0.01
            ),
            margin=dict(l=40, r=40, t=40, b=40)
        )
        
        return fig
    except Exception as e:
        logger.error(f"Error plotting efficient frontier: {str(e)}")
        # Return empty figure
        return go.Figure()


def plot_monte_carlo_simulation(
    simulation_results: pd.DataFrame,
    initial_value: float = 10000,
    confidence_level: float = 0.05,
    height: int = 600,
    width: int = 800
) -> go.Figure:
    """
    Plot Monte Carlo simulation results.
    
    Args:
        simulation_results: DataFrame with simulation results
        initial_value: Initial portfolio value
        confidence_level: Confidence level for VaR and CVaR
        height: Plot height
        width: Plot width
        
    Returns:
        Plotly figure with Monte Carlo simulation results
    """
    try:
        # Create figure
        fig = go.Figure()
        
        # Add simulation paths
        for i in range(min(100, len(simulation_results))):
            fig.add_trace(
                go.Scatter(
                    y=simulation_results.iloc[i],
                    mode='lines',
                    line=dict(width=0.5, color='rgba(0, 100, 80, 0.2)'),
                    showlegend=False
                )
            )
        
        # Calculate percentiles
        percentiles = [5, 50, 95]
        percentile_values = np.percentile(simulation_results.iloc[:, -1], percentiles)
        
        # Add percentile lines
        colors = ['red', 'blue', 'green']
        for i, percentile in enumerate(percentiles):
            # Get percentile path
            percentile_idx = np.abs(simulation_results.iloc[:, -1] - percentile_values[i]).idxmin()
            percentile_path = simulation_results.iloc[percentile_idx]
            
            fig.add_trace(
                go.Scatter(
                    y=percentile_path,
                    mode='lines',
                    line=dict(width=2, color=colors[i]),
                    name=f'{percentile}th Percentile'
                )
            )
        
        # Update layout
        fig.update_layout(
            title='Monte Carlo Simulation',
            xaxis_title='Time Period',
            yaxis_title='Portfolio Value',
            height=height,
            width=width,
            legend=dict(
                yanchor="top",
                y=0.99,
                xanchor="left",
                x=0.01
            ),
            margin=dict(l=40, r=40, t=40, b=40)
        )
        
        return fig
    except Exception as e:
        logger.error(f"Error plotting Monte Carlo simulation: {str(e)}")
        # Return empty figure
        return go.Figure()


def plot_risk_contribution(
    risk_contributions: Dict[str, float],
    risk_type: str = 'Volatility',
    height: int = 500,
    width: int = 700
) -> go.Figure:
    """
    Plot risk contribution by asset.
    
    Args:
        risk_contributions: Dictionary mapping assets to risk contributions
        risk_type: Type of risk (Volatility, VaR, CVaR, etc.)
        height: Plot height
        width: Plot width
        
    Returns:
        Plotly figure with risk contribution by asset
    """
    try:
        # Create DataFrame from risk contributions
        df = pd.DataFrame({
            'Asset': list(risk_contributions.keys()),
            'Contribution': list(risk_contributions.values())
        })
        
        # Sort by contribution
        df = df.sort_values('Contribution', ascending=False)
        
        # Create figure
        fig = px.bar(
            df,
            x='Asset',
            y='Contribution',
            title=f'{risk_type} Contribution by Asset',
            height=height,
            width=width,
            color='Contribution',
            color_continuous_scale='viridis'
        )
        
        # Update layout
        fig.update_layout(
            xaxis_title='Asset',
            yaxis_title=f'{risk_type} Contribution (%)',
            coloraxis_showscale=False,
            margin=dict(l=40, r=40, t=40, b=40)
        )
        
        return fig
    except Exception as e:
        logger.error(f"Error plotting risk contribution: {str(e)}")
        # Return empty figure
        return go.Figure()


def plot_correlation_matrix(
    returns: pd.DataFrame,
    height: int = 600,
    width: int = 800
) -> go.Figure:
    """
    Plot correlation matrix of asset returns.
    
    Args:
        returns: DataFrame of asset returns
        height: Plot height
        width: Plot width
        
    Returns:
        Plotly figure with correlation matrix
    """
    try:
        # Calculate correlation matrix
        corr_matrix = returns.corr()
        
        # Create figure
        fig = px.imshow(
            corr_matrix,
            title='Correlation Matrix',
            height=height,
            width=width,
            color_continuous_scale='RdBu_r',
            zmin=-1,
            zmax=1
        )
        
        # Add correlation values as text
        for i in range(len(corr_matrix.columns)):
            for j in range(len(corr_matrix.index)):
                fig.add_annotation(
                    x=i,
                    y=j,
                    text=f"{corr_matrix.iloc[j, i]:.2f}",
                    showarrow=False,
                    font=dict(
                        color="white" if abs(corr_matrix.iloc[j, i]) > 0.5 else "black"
                    )
                )
        
        # Update layout
        fig.update_layout(
            margin=dict(l=40, r=40, t=40, b=40)
        )
        
        return fig
    except Exception as e:
        logger.error(f"Error plotting correlation matrix: {str(e)}")
        # Return empty figure
        return go.Figure()


def plot_rolling_metrics(
    returns: pd.DataFrame,
    weights: Dict[str, float],
    window: int = 252,
    height: int = 800,
    width: int = 1000
) -> go.Figure:
    """
    Plot rolling risk metrics.
    
    Args:
        returns: DataFrame of asset returns
        weights: Dictionary mapping assets to weights
        window: Rolling window size
        height: Plot height
        width: Plot width
        
    Returns:
        Plotly figure with rolling risk metrics
    """
    try:
        # Convert weights to Series
        weight_series = pd.Series(weights)
        
        # Calculate portfolio returns
        portfolio_returns = returns[weight_series.index].dot(weight_series)
        
        # Calculate rolling metrics
        rolling_volatility = portfolio_returns.rolling(window=window).std() * np.sqrt(252)
        rolling_sharpe = portfolio_returns.rolling(window=window).mean() / portfolio_returns.rolling(window=window).std() * np.sqrt(252)
        rolling_sortino = portfolio_returns.rolling(window=window).mean() / portfolio_returns[portfolio_returns < 0].rolling(window=window).std() * np.sqrt(252)
        rolling_max_drawdown = (portfolio_returns.rolling(window=window).apply(
            lambda x: (1 + x).cumprod().max() - (1 + x).cumprod().iloc[-1]
        ) / (1 + portfolio_returns).rolling(window=window).apply(
            lambda x: x.cumprod().max()
        ))
        
        # Create subplots
        fig = make_subplots(
            rows=4, cols=1,
            subplot_titles=(
                'Rolling Volatility (Annualized)',
                'Rolling Sharpe Ratio',
                'Rolling Sortino Ratio',
                'Rolling Maximum Drawdown'
            ),
            shared_xaxes=True,
            vertical_spacing=0.05
        )
        
        # Add traces
        fig.add_trace(
            go.Scatter(
                x=rolling_volatility.index,
                y=rolling_volatility.values,
                mode='lines',
                name='Volatility'
            ),
            row=1, col=1
        )
        
        fig.add_trace(
            go.Scatter(
                x=rolling_sharpe.index,
                y=rolling_sharpe.values,
                mode='lines',
                name='Sharpe Ratio'
            ),
            row=2, col=1
        )
        
        fig.add_trace(
            go.Scatter(
                x=rolling_sortino.index,
                y=rolling_sortino.values,
                mode='lines',
                name='Sortino Ratio'
            ),
            row=3, col=1
        )
        
        fig.add_trace(
            go.Scatter(
                x=rolling_max_drawdown.index,
                y=rolling_max_drawdown.values,
                mode='lines',
                name='Max Drawdown'
            ),
            row=4, col=1
        )
        
        # Update layout
        fig.update_layout(
            title='Rolling Risk Metrics',
            height=height,
            width=width,
            showlegend=False,
            margin=dict(l=40, r=40, t=40, b=40)
        )
        
        return fig
    except Exception as e:
        logger.error(f"Error plotting rolling metrics: {str(e)}")
        # Return empty figure
        return go.Figure()


def plot_drawdown_chart(
    returns: pd.Series,
    height: int = 600,
    width: int = 800
) -> go.Figure:
    """
    Plot drawdown chart.
    
    Args:
        returns: Series of portfolio returns
        height: Plot height
        width: Plot width
        
    Returns:
        Plotly figure with drawdown chart
    """
    try:
        # Calculate cumulative returns
        cumulative_returns = (1 + returns).cumprod()
        
        # Calculate running maximum
        running_max = cumulative_returns.cummax()
        
        # Calculate drawdown
        drawdown = (cumulative_returns / running_max) - 1
        
        # Create subplots
        fig = make_subplots(
            rows=2, cols=1,
            subplot_titles=('Cumulative Returns', 'Drawdown'),
            shared_xaxes=True,
            vertical_spacing=0.05,
            row_heights=[0.7, 0.3]
        )
        
        # Add cumulative returns trace
        fig.add_trace(
            go.Scatter(
                x=cumulative_returns.index,
                y=cumulative_returns.values,
                mode='lines',
                name='Cumulative Returns',
                line=dict(color='blue')
            ),
            row=1, col=1
        )
        
        # Add running maximum trace
        fig.add_trace(
            go.Scatter(
                x=running_max.index,
                y=running_max.values,
                mode='lines',
                name='Running Maximum',
                line=dict(color='green', dash='dash')
            ),
            row=1, col=1
        )
        
        # Add drawdown trace
        fig.add_trace(
            go.Scatter(
                x=drawdown.index,
                y=drawdown.values,
                mode='lines',
                name='Drawdown',
                line=dict(color='red')
            ),
            row=2, col=1
        )
        
        # Add zero line to drawdown chart
        fig.add_trace(
            go.Scatter(
                x=[drawdown.index[0], drawdown.index[-1]],
                y=[0, 0],
                mode='lines',
                line=dict(color='black', dash='dash'),
                showlegend=False
            ),
            row=2, col=1
        )
        
        # Update layout
        fig.update_layout(
            title='Portfolio Drawdown Analysis',
            height=height,
            width=width,
            legend=dict(
                yanchor="top",
                y=0.99,
                xanchor="left",
                x=0.01
            ),
            margin=dict(l=40, r=40, t=40, b=40)
        )
        
        # Update y-axis labels
        fig.update_yaxes(title_text='Cumulative Returns', row=1, col=1)
        fig.update_yaxes(title_text='Drawdown', row=2, col=1)
        
        return fig
    except Exception as e:
        logger.error(f"Error plotting drawdown chart: {str(e)}")
        # Return empty figure
        return go.Figure()
