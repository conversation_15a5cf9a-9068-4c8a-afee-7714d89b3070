"""
Ichimoku Cloud indicator implementation.
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union

from app.core.technical_analysis.indicators.base import BaseIndicator
from app.utils.logging import logger


class IchimokuCloud(BaseIndicator):
    """
    Ichimoku Cloud (Ichimoku Kinko Hyo) indicator.
    
    The Ichimoku Cloud is a comprehensive indicator that defines support and resistance,
    identifies trend direction, gauges momentum, and provides trading signals.
    """
    
    def __init__(self):
        """Initialize the Ichimoku Cloud indicator."""
        super().__init__("Ichimoku Cloud")
    
    def calculate(
        self,
        data: pd.DataFrame,
        tenkan_period: int = 9,
        kijun_period: int = 26,
        senkou_span_b_period: int = 52,
        displacement: int = 26,
        **kwargs
    ) -> pd.DataFrame:
        """
        Calculate the Ichimoku Cloud indicator.
        
        Args:
            data: DataFrame with OHLC data
            tenkan_period: Period for Tenkan-sen (Conversion Line)
            kijun_period: Period for Kijun-sen (Base Line)
            senkou_span_b_period: Period for Senkou Span B (Leading Span B)
            displacement: Displacement period for Senkou Span
            **kwargs: Additional parameters
            
        Returns:
            DataFrame with Ichimoku Cloud indicator values
        """
        try:
            # Validate input data
            required_columns = ['high', 'low', 'close']
            if not all(col in data.columns for col in required_columns):
                # Try with capitalized column names
                capitalized_columns = ['High', 'Low', 'Close']
                if all(col in data.columns for col in capitalized_columns):
                    # Use capitalized column names
                    high = data['High']
                    low = data['Low']
                    close = data['Close']
                else:
                    logger.error("Required columns not found in data")
                    return data
            else:
                # Use lowercase column names
                high = data['high']
                low = data['low']
                close = data['close']
            
            # Create a copy of the input data
            result = data.copy()
            
            # Calculate Tenkan-sen (Conversion Line)
            # (highest high + lowest low) / 2 for the past tenkan_period
            tenkan_sen = (
                high.rolling(window=tenkan_period).max() +
                low.rolling(window=tenkan_period).min()
            ) / 2
            
            # Calculate Kijun-sen (Base Line)
            # (highest high + lowest low) / 2 for the past kijun_period
            kijun_sen = (
                high.rolling(window=kijun_period).max() +
                low.rolling(window=kijun_period).min()
            ) / 2
            
            # Calculate Senkou Span A (Leading Span A)
            # (Tenkan-sen + Kijun-sen) / 2, displaced forward by displacement periods
            senkou_span_a = ((tenkan_sen + kijun_sen) / 2).shift(displacement)
            
            # Calculate Senkou Span B (Leading Span B)
            # (highest high + lowest low) / 2 for the past senkou_span_b_period, displaced forward by displacement periods
            senkou_span_b = (
                (high.rolling(window=senkou_span_b_period).max() +
                 low.rolling(window=senkou_span_b_period).min()) / 2
            ).shift(displacement)
            
            # Calculate Chikou Span (Lagging Span)
            # Close price, displaced backwards by displacement periods
            chikou_span = close.shift(-displacement)
            
            # Add indicator values to the result DataFrame
            result['ichimoku_tenkan_sen'] = tenkan_sen
            result['ichimoku_kijun_sen'] = kijun_sen
            result['ichimoku_senkou_span_a'] = senkou_span_a
            result['ichimoku_senkou_span_b'] = senkou_span_b
            result['ichimoku_chikou_span'] = chikou_span
            
            # Calculate the cloud (Kumo)
            # The cloud is the area between Senkou Span A and Senkou Span B
            result['ichimoku_cloud_top'] = result[['ichimoku_senkou_span_a', 'ichimoku_senkou_span_b']].max(axis=1)
            result['ichimoku_cloud_bottom'] = result[['ichimoku_senkou_span_a', 'ichimoku_senkou_span_b']].min(axis=1)
            result['ichimoku_cloud_color'] = np.where(
                result['ichimoku_senkou_span_a'] >= result['ichimoku_senkou_span_b'],
                'green',  # Bullish cloud
                'red'     # Bearish cloud
            )
            
            return result
        except Exception as e:
            logger.error(f"Error calculating Ichimoku Cloud: {str(e)}")
            return data
    
    def get_signal(
        self,
        data: pd.DataFrame,
        tenkan_period: int = 9,
        kijun_period: int = 26,
        senkou_span_b_period: int = 52,
        displacement: int = 26,
        **kwargs
    ) -> pd.DataFrame:
        """
        Generate trading signals based on the Ichimoku Cloud indicator.
        
        Args:
            data: DataFrame with OHLC data
            tenkan_period: Period for Tenkan-sen (Conversion Line)
            kijun_period: Period for Kijun-sen (Base Line)
            senkou_span_b_period: Period for Senkou Span B (Leading Span B)
            displacement: Displacement period for Senkou Span
            **kwargs: Additional parameters
            
        Returns:
            DataFrame with Ichimoku Cloud signals
        """
        try:
            # Calculate Ichimoku Cloud indicator
            ichimoku_data = self.calculate(
                data,
                tenkan_period=tenkan_period,
                kijun_period=kijun_period,
                senkou_span_b_period=senkou_span_b_period,
                displacement=displacement,
                **kwargs
            )
            
            # Get close prices
            if 'close' in ichimoku_data.columns:
                close = ichimoku_data['close']
            elif 'Close' in ichimoku_data.columns:
                close = ichimoku_data['Close']
            else:
                logger.error("Close price column not found in data")
                return ichimoku_data
            
            # Generate signals
            
            # TK Cross (Tenkan-sen crosses Kijun-sen)
            ichimoku_data['ichimoku_tk_cross'] = np.where(
                ichimoku_data['ichimoku_tenkan_sen'] > ichimoku_data['ichimoku_kijun_sen'],
                1,  # Bullish TK Cross
                np.where(
                    ichimoku_data['ichimoku_tenkan_sen'] < ichimoku_data['ichimoku_kijun_sen'],
                    -1,  # Bearish TK Cross
                    0    # No cross
                )
            )
            
            # Price-Kumo relationship (Price relative to the cloud)
            ichimoku_data['ichimoku_price_kumo'] = np.where(
                close > ichimoku_data['ichimoku_cloud_top'],
                1,  # Price above the cloud (bullish)
                np.where(
                    close < ichimoku_data['ichimoku_cloud_bottom'],
                    -1,  # Price below the cloud (bearish)
                    0    # Price inside the cloud (neutral)
                )
            )
            
            # Kumo breakout (Price crosses the cloud)
            ichimoku_data['ichimoku_kumo_breakout'] = np.where(
                (close > ichimoku_data['ichimoku_cloud_top']) &
                (close.shift(1) <= ichimoku_data['ichimoku_cloud_top'].shift(1)),
                1,  # Bullish breakout
                np.where(
                    (close < ichimoku_data['ichimoku_cloud_bottom']) &
                    (close.shift(1) >= ichimoku_data['ichimoku_cloud_bottom'].shift(1)),
                    -1,  # Bearish breakout
                    0    # No breakout
                )
            )
            
            # Chikou Span position (Chikou Span relative to price)
            ichimoku_data['ichimoku_chikou_position'] = np.where(
                ichimoku_data['ichimoku_chikou_span'] > close.shift(displacement),
                1,  # Bullish (Chikou Span above price)
                np.where(
                    ichimoku_data['ichimoku_chikou_span'] < close.shift(displacement),
                    -1,  # Bearish (Chikou Span below price)
                    0    # Neutral
                )
            )
            
            # Aggregate signal
            ichimoku_data['ichimoku_signal'] = (
                ichimoku_data['ichimoku_tk_cross'] +
                ichimoku_data['ichimoku_price_kumo'] +
                ichimoku_data['ichimoku_kumo_breakout'] +
                ichimoku_data['ichimoku_chikou_position']
            )
            
            # Classify signal strength
            ichimoku_data['ichimoku_signal_strength'] = np.where(
                ichimoku_data['ichimoku_signal'] >= 3,
                'strong_buy',
                np.where(
                    ichimoku_data['ichimoku_signal'] >= 1,
                    'buy',
                    np.where(
                        ichimoku_data['ichimoku_signal'] <= -3,
                        'strong_sell',
                        np.where(
                            ichimoku_data['ichimoku_signal'] <= -1,
                            'sell',
                            'neutral'
                        )
                    )
                )
            )
            
            return ichimoku_data
        except Exception as e:
            logger.error(f"Error generating Ichimoku Cloud signals: {str(e)}")
            return data
