"""
Hierarchical Risk Parity (HRP) portfolio optimization implementation.

HRP is a portfolio optimization method that uses hierarchical clustering
to allocate assets based on their risk characteristics. It was introduced by
<PERSON> in "Building Diversified Portfolios that Outperform Out of Sample".

This approach addresses some of the limitations of traditional mean-variance optimization,
particularly the sensitivity to estimation errors in expected returns and covariance.
"""
import numpy as np
import pandas as pd
from scipy.cluster.hierarchy import linkage, dendrogram, fcluster
from scipy.spatial.distance import squareform
from typing import Dict, List, Optional, Tuple, Union

from app.utils.logging import logger


def get_correlation_distance_matrix(returns: pd.DataFrame) -> pd.DataFrame:
    """
    Calculate the correlation-based distance matrix.
    
    Args:
        returns: DataFrame of asset returns
        
    Returns:
        Distance matrix based on correlations
    """
    # Calculate correlation matrix
    corr = returns.corr()
    
    # Convert correlations to distances: distance = sqrt(0.5 * (1 - correlation))
    distance = np.sqrt(0.5 * (1 - corr))
    
    return distance


def get_quasi_diag(link: np.ndarray) -> List[int]:
    """
    Sort assets based on hierarchical clustering.
    
    Args:
        link: Linkage matrix from hierarchical clustering
        
    Returns:
        List of sorted asset indices
    """
    # Number of assets
    n_assets = link.shape[0] + 1
    
    # Initialize sorted list with first two assets
    sorted_assets = [link[0, 0], link[0, 1]]
    
    # Initialize clusters with first two assets
    clusters = {link[0, 0]: [link[0, 0]], link[0, 1]: [link[0, 1]]}
    
    # Iterate through remaining links
    for i in range(1, link.shape[0]):
        # Get the two clusters being merged
        cluster1 = link[i, 0]
        cluster2 = link[i, 1]
        
        # If these are original assets (not clusters)
        if cluster1 < n_assets:
            cluster1_assets = [cluster1]
        else:
            # Get assets in this cluster
            cluster1_assets = clusters[cluster1]
        
        if cluster2 < n_assets:
            cluster2_assets = [cluster2]
        else:
            # Get assets in this cluster
            cluster2_assets = clusters[cluster2]
        
        # Create new cluster
        new_cluster = cluster1_assets + cluster2_assets
        clusters[n_assets + i - 1] = new_cluster
        
        # Update sorted assets
        # Remove the old clusters
        for asset in cluster1_assets:
            if asset in sorted_assets:
                sorted_assets.remove(asset)
        for asset in cluster2_assets:
            if asset in sorted_assets:
                sorted_assets.remove(asset)
        
        # Add the new cluster
        sorted_assets = sorted_assets + new_cluster
    
    return sorted_assets


def get_recursive_bisection(cov: pd.DataFrame, sorted_assets: List[int]) -> np.ndarray:
    """
    Perform recursive bisection to allocate weights.
    
    Args:
        cov: Covariance matrix
        sorted_assets: List of sorted asset indices
        
    Returns:
        Array of weights
    """
    # Initialize weights
    weights = np.ones(len(sorted_assets))
    
    # Recursive bisection
    def _recursive_bisection(weights: np.ndarray, indices: List[int]) -> None:
        # If only one asset, return
        if len(indices) <= 1:
            return
        
        # Split indices into two
        mid = len(indices) // 2
        left_indices = indices[:mid]
        right_indices = indices[mid:]
        
        # Get covariance for these subsets
        left_cov = cov.iloc[left_indices, left_indices].values
        right_cov = cov.iloc[right_indices, right_indices].values
        
        # Calculate variance of subsets
        left_var = np.sum(np.diag(left_cov))
        right_var = np.sum(np.diag(right_cov))
        
        # Adjust weights based on variance
        alpha = 1 - left_var / (left_var + right_var)
        
        # Update weights
        weights[left_indices] *= alpha
        weights[right_indices] *= (1 - alpha)
        
        # Recursively apply to subsets
        _recursive_bisection(weights, left_indices)
        _recursive_bisection(weights, right_indices)
    
    # Apply recursive bisection
    _recursive_bisection(weights, list(range(len(sorted_assets))))
    
    return weights


def optimize_hrp(returns: pd.DataFrame) -> Dict[str, float]:
    """
    Optimize portfolio using Hierarchical Risk Parity.
    
    Args:
        returns: DataFrame of asset returns
        
    Returns:
        Dictionary of asset weights
    """
    try:
        # Get correlation-based distance matrix
        distance = get_correlation_distance_matrix(returns)
        
        # Convert to condensed distance matrix (required by linkage)
        condensed_distance = squareform(distance)
        
        # Perform hierarchical clustering
        link = linkage(condensed_distance, method='single')
        
        # Get quasi-diagonal ordering of assets
        sorted_assets = get_quasi_diag(link)
        
        # Get covariance matrix
        cov = returns.cov()
        
        # Get weights through recursive bisection
        weights = get_recursive_bisection(cov, sorted_assets)
        
        # Normalize weights to sum to 1
        weights = weights / np.sum(weights)
        
        # Create dictionary of weights
        asset_weights = {returns.columns[i]: weights[i] for i in range(len(weights))}
        
        return asset_weights
    
    except Exception as e:
        logger.error(f"Error in HRP optimization: {str(e)}")
        # Return equal weights as fallback
        equal_weight = 1.0 / len(returns.columns)
        return {asset: equal_weight for asset in returns.columns}


def optimize_portfolio_hrp(
    returns: pd.DataFrame,
    risk_free_rate: float = 0.0,
    **kwargs
) -> Dict:
    """
    Optimize portfolio using Hierarchical Risk Parity.
    
    Args:
        returns: DataFrame of asset returns
        risk_free_rate: Risk-free rate
        **kwargs: Additional parameters (not used)
        
    Returns:
        Dictionary with optimization results
    """
    try:
        # Check if returns data is valid
        if returns.empty:
            raise ValueError("Returns data is empty")
        
        # Optimize using HRP
        weights = optimize_hrp(returns)
        
        # Calculate expected return and risk
        expected_returns = returns.mean()
        cov_matrix = returns.cov()
        
        portfolio_return = sum(weights[asset] * expected_returns[asset] for asset in weights)
        portfolio_volatility = np.sqrt(
            sum(weights[asset1] * weights[asset2] * cov_matrix.loc[asset1, asset2]
                for asset1 in weights for asset2 in weights)
        )
        
        # Calculate Sharpe ratio
        sharpe_ratio = (portfolio_return - risk_free_rate) / portfolio_volatility if portfolio_volatility > 0 else 0
        
        # Prepare result
        result = {
            "weights": weights,
            "expected_annual_return": portfolio_return * 252,  # Annualized
            "annual_volatility": portfolio_volatility * np.sqrt(252),  # Annualized
            "sharpe_ratio": sharpe_ratio * np.sqrt(252),  # Annualized
            "method": "hierarchical_risk_parity"
        }
        
        return result
    
    except Exception as e:
        logger.error(f"Error in HRP portfolio optimization: {str(e)}")
        # Return equal weights as fallback
        equal_weight = 1.0 / len(returns.columns)
        weights = {asset: equal_weight for asset in returns.columns}
        
        return {
            "weights": weights,
            "expected_annual_return": 0.0,
            "annual_volatility": 0.0,
            "sharpe_ratio": 0.0,
            "method": "hierarchical_risk_parity",
            "error": str(e)
        }
