"""
Portfolio Optimization Manager implementation.

This module provides a unified interface for all portfolio optimization functionality.
"""
from datetime import date, timedelta
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd

from app.core.data_collection.data_manager import data_manager
from app.core.portfolio.optimization.base import OptimizationStrategy
from app.core.portfolio.optimization.mean_variance import MeanVarianceOptimization
from app.core.portfolio.optimization.hrp_strategy import HierarchicalRiskParityStrategy
from app.core.portfolio.optimization.black_litterman import BlackLittermanOptimization
from app.core.portfolio.optimization.robust_optimization import RobustOptimization
from app.core.portfolio.optimization.factor_optimization import FactorOptimization
from app.core.portfolio.performance import calculate_performance_metrics
from app.settings import get_settings
from app.utils.constants import DEFAULT_RISK_FREE_RATE
from app.utils.logging import logger


class PortfolioOptimizationManager:
    """
    Portfolio Optimization Manager.

    This class provides a unified interface for all portfolio optimization functionality.
    """

    def __init__(self):
        """Initialize the Portfolio Optimization Manager."""
        logger.info("Initializing Portfolio Optimization Manager")

        try:
            # Get settings
            self.settings = get_settings()

            # Set risk-free rate with fallback
            self.risk_free_rate = getattr(
                self.settings,
                "risk_free_rate",
                DEFAULT_RISK_FREE_RATE
            )

            # Initialize optimization strategies with error handling
            self.strategies = {}
            strategy_classes = {
                "mean_variance": MeanVarianceOptimization,
                "hierarchical_risk_parity": HierarchicalRiskParityStrategy,
                "black_litterman": BlackLittermanOptimization,
                "robust_optimization": RobustOptimization,
                "factor_optimization": FactorOptimization
            }

            for name, strategy_class in strategy_classes.items():
                try:
                    # Set appropriate kwargs for each strategy
                    if name == "hierarchical_risk_parity":
                        kwargs = {}
                    elif name == "robust_optimization":
                        kwargs = {"risk_free_rate": self.risk_free_rate, "uncertainty": 0.1}
                    elif name == "factor_optimization":
                        kwargs = {"risk_free_rate": self.risk_free_rate}
                    else:
                        kwargs = {"risk_free_rate": self.risk_free_rate}

                    self.strategies[name] = strategy_class(**kwargs)
                    logger.debug(f"Initialized {name} strategy")
                except Exception as e:
                    logger.error(f"Failed to initialize {name} strategy: {e}")

            # Initialize cache
            self.cache = {}

        except Exception as e:
            logger.error(f"Error initializing Portfolio Optimization Manager: {e}")
            raise

    def get_strategy(self, strategy_name: str) -> OptimizationStrategy:
        """
        Get an optimization strategy by name.

        Args:
            strategy_name: Name of the optimization strategy

        Returns:
            The optimization strategy

        Raises:
            ValueError: If the strategy is not found
        """
        if strategy_name not in self.strategies:
            raise ValueError(f"Strategy '{strategy_name}' not found")

        return self.strategies[strategy_name]

    def register_strategy(self, strategy_name: str, strategy: OptimizationStrategy) -> None:
        """
        Register a new optimization strategy.

        Args:
            strategy_name: Name of the optimization strategy
            strategy: The optimization strategy
        """
        self.strategies[strategy_name] = strategy
        logger.info(f"Registered optimization strategy: {strategy_name}")

    async def get_returns(
        self,
        symbols: List[str],
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        interval: str = "1d",
        source: Optional[str] = None
    ) -> pd.DataFrame:
        """
        Get returns for a list of symbols.

        Args:
            symbols: List of symbols
            start_date: Start date for historical data
            end_date: End date for historical data
            interval: Data interval (e.g., '1d', '1wk', '1mo')
            source: Data source

        Returns:
            DataFrame with returns for each symbol
        """
        # Set default dates if not provided
        if end_date is None:
            end_date = date.today()

        if start_date is None:
            # Default to 1 year of data
            start_date = end_date - timedelta(days=365)

        # Get historical data
        data = await data_manager.get_multiple_stock_data(
            symbols, start_date, end_date, interval, source
        )

        # Extract close prices
        prices = {}
        for symbol, df in data.items():
            if not df.empty:
                # Set date as index
                df = df.set_index("date")
                # Get close prices
                prices[symbol] = df["close"]

        # Create DataFrame with prices
        price_df = pd.DataFrame(prices)

        # Calculate returns
        returns = price_df.pct_change().dropna()

        return returns

    async def optimize_portfolio(
        self,
        symbols: List[str],
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        interval: str = "1d",
        optimization_criterion: str = "max_sharpe",
        expected_returns_method: str = "mean_historical_return",
        risk_model_method: str = "sample_cov",
        constraints: Optional[Dict] = None,
        total_portfolio_value: Optional[float] = None,
        source: Optional[str] = None,
        **kwargs
    ) -> Dict:
        """
        Optimize a portfolio for a list of symbols.

        Args:
            symbols: List of symbols
            start_date: Start date for historical data
            end_date: End date for historical data
            interval: Data interval (e.g., '1d', '1wk', '1mo')
            optimization_criterion: Optimization criterion (e.g., 'max_sharpe', 'min_volatility')
            expected_returns_method: Method for calculating expected returns
            risk_model_method: Method for calculating the risk model
            constraints: Additional constraints for the optimization
            total_portfolio_value: Total portfolio value for discrete allocation
            source: Data source
            **kwargs: Additional parameters for the optimization

        Returns:
            Dictionary with optimization results including weights and performance metrics
        """
        # Get returns
        returns = await self.get_returns(symbols, start_date, end_date, interval, source)

        # Map optimization criterion to strategy
        strategy_map = {
            "max_sharpe": "mean_variance",
            "min_volatility": "mean_variance",
            "efficient_risk": "mean_variance",
            "efficient_return": "mean_variance",
            "max_quadratic_utility": "mean_variance",
            "hierarchical_risk_parity": "hierarchical_risk_parity",
            "black_litterman": "black_litterman",
            "robust_sharpe": "robust_optimization",
            "min_cvar": "robust_optimization",
            "max_return": "robust_optimization",
            "factor_sharpe": "factor_optimization",
            "min_factor_risk": "factor_optimization",
            "target_factor": "factor_optimization"
        }

        strategy = strategy_map.get(optimization_criterion, "mean_variance")

        # Get optimization strategy
        optimizer = self.get_strategy(strategy)

        # Prepare optimization parameters
        optimization_params = {
            "objective": optimization_criterion,
            "expected_returns_method": expected_returns_method,
            "risk_model_method": risk_model_method,
            "constraints": constraints
        }

        # Add any additional parameters
        optimization_params.update(kwargs)

        # Optimize portfolio
        weights = optimizer.optimize(returns, **optimization_params)

        # Calculate portfolio metrics
        expected_returns = returns.mean()
        cov_matrix = returns.cov()

        # Calculate portfolio return
        port_return = sum(expected_returns[asset] * weight for asset, weight in weights.items())

        # Calculate portfolio risk
        try:
            port_risk = np.sqrt(
                sum(weights[asset_i] * weights[asset_j] * cov_matrix.loc[asset_i, asset_j]
                    for asset_i in symbols for asset_j in symbols)
            )

            # Ensure risk is not zero to avoid division by zero
            if port_risk <= 1e-10:
                logger.warning("Portfolio risk is too close to zero, using small positive value")
                port_risk = 1e-6  # Use a small positive value
        except Exception as e:
            logger.error(f"Error calculating portfolio risk: {e}")
            port_risk = 0.1  # Default risk

        # Calculate Sharpe ratio (used for annualized calculation below)
        # port_sharpe = (port_return - self.risk_free_rate) / port_risk

        # Annualize metrics
        # Assuming 252 trading days per year for daily data, 52 for weekly, 12 for monthly
        if interval == "1d":
            trading_periods = 252
        elif interval == "1wk":
            trading_periods = 52
        elif interval == "1mo":
            trading_periods = 12
        else:
            trading_periods = 252  # Default to daily

        annual_return = port_return * trading_periods
        annual_risk = port_risk * np.sqrt(trading_periods)
        annual_sharpe = (annual_return - self.risk_free_rate) / annual_risk if annual_risk > 0 else 0

        # Get latest prices for allocation
        latest_prices = {}
        try:
            # Get historical data for the last day
            if end_date is None:
                end_date = date.today()

            start_date_prices = end_date - timedelta(days=7)  # Get a week of data to ensure we have the latest prices

            data = await data_manager.get_multiple_stock_data(
                symbols, start_date_prices, end_date, "1d", source
            )

            for symbol, df in data.items():
                if not df.empty and "close" in df.columns:
                    latest_prices[symbol] = float(df["close"].iloc[-1])
        except Exception as e:
            logger.error(f"Error getting latest prices: {e}")
            # Use default prices
            latest_prices = {symbol: 100.0 for symbol in symbols}

        # Calculate discrete allocation if total_portfolio_value is provided
        allocation = {}
        leftover = 0
        if total_portfolio_value:
            try:
                from pypfopt.discrete_allocation import DiscreteAllocation
                import pandas as pd

                # Convert latest_prices to pandas Series and ensure no NaNs
                prices_series = pd.Series(latest_prices)

                # Check for missing prices and fill with reasonable defaults
                for symbol in symbols:
                    if symbol not in prices_series or pd.isna(prices_series[symbol]) or prices_series[symbol] <= 0:
                        # Use a default price of $100 if no valid price is available
                        prices_series[symbol] = 100.0
                        logger.warning(f"Using default price of $100 for {symbol}")

                # Create a discrete allocation object
                da = DiscreteAllocation(weights, prices_series, total_portfolio_value)

                # Allocate
                allocation, leftover = da.greedy_portfolio()
            except Exception as e:
                logger.error(f"Error in discrete allocation: {e}")
                # Fallback to simple allocation
                for symbol in symbols:
                    if symbol in weights:
                        # Get price with fallback
                        price = latest_prices.get(symbol, 100.0)
                        if price <= 0:
                            price = 100.0

                        shares = int((total_portfolio_value * weights[symbol]) / price)
                        allocation[symbol] = shares

                # Calculate leftover
                allocated_value = sum(allocation[symbol] * latest_prices.get(symbol, 100.0) for symbol in allocation)
                leftover = total_portfolio_value - allocated_value

        # Create comprehensive result dictionary
        result = {
            "weights": weights,
            "expected_annual_return": annual_return,
            "annual_volatility": annual_risk,
            "sharpe_ratio": annual_sharpe,
            "latest_prices": latest_prices,
            "allocation": allocation,
            "leftover": leftover
        }

        return result

    async def optimize_portfolio_with_returns(
        self,
        mu: pd.Series,
        sigma: pd.DataFrame,
        optimization_criterion: str = "max_sharpe",
        constraints: Optional[Dict] = None,
        **kwargs
    ) -> Dict[str, float]:
        """
        Optimize a portfolio using pre-calculated expected returns and covariance matrix.

        Args:
            mu: Series with expected returns for each asset
            sigma: DataFrame with the covariance matrix
            optimization_criterion: Optimization criterion
            constraints: Additional constraints for the optimization
            **kwargs: Additional parameters for the optimization

        Returns:
            Dictionary mapping symbols to weights
        """
        # Map optimization criterion to strategy
        strategy_map = {
            "max_sharpe": "mean_variance",
            "min_volatility": "mean_variance",
            "efficient_risk": "mean_variance",
            "efficient_return": "mean_variance",
            "max_quadratic_utility": "mean_variance",
            "hierarchical_risk_parity": "hierarchical_risk_parity",
            "black_litterman": "black_litterman",
            "robust_sharpe": "robust_optimization",
            "min_cvar": "robust_optimization",
            "max_return": "robust_optimization",
            "factor_sharpe": "factor_optimization",
            "min_factor_risk": "factor_optimization",
            "target_factor": "factor_optimization"
        }

        strategy = strategy_map.get(optimization_criterion, "mean_variance")

        # Get optimization strategy
        optimizer = self.get_strategy(strategy)

        # Create a temporary returns DataFrame
        # This is a workaround since our optimizers expect returns, not mu and sigma directly
        assets = mu.index.tolist()
        temp_returns = pd.DataFrame(index=[0], columns=assets)
        for asset in assets:
            temp_returns.loc[0, asset] = mu[asset]

        # Set the covariance matrix as an attribute of the returns DataFrame
        # This is a hack, but it allows us to pass both mu and sigma to the optimizer
        temp_returns.cov = lambda: sigma

        # Optimize portfolio
        objective_map = {
            "max_sharpe": "sharpe",
            "min_volatility": "min_risk",
            "efficient_risk": "target_risk",
            "efficient_return": "target_return",
            "max_quadratic_utility": "max_return",
            "black_litterman": "sharpe",
            "robust_sharpe": "robust_sharpe",
            "min_cvar": "min_cvar",
            "max_return": "max_return",
            "factor_sharpe": "factor_sharpe",
            "min_factor_risk": "min_factor_risk",
            "target_factor": "target_factor"
        }

        objective = objective_map.get(optimization_criterion, "sharpe")

        try:
            weights = optimizer.optimize(temp_returns, objective=objective, constraints=constraints, **kwargs)
            return weights
        except Exception as e:
            logger.error(f"Error in optimize_portfolio_with_returns: {e}")
            # Fallback to equal weights
            equal_weight = 1.0 / len(assets)
            return {asset: equal_weight for asset in assets}

    async def compare_strategies(
        self,
        symbols: List[str],
        strategies: List[str],
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        interval: str = "1d",
        source: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Dict[str, float]]:
        """
        Compare different optimization strategies.

        Args:
            symbols: List of symbols
            strategies: List of optimization strategies
            start_date: Start date for historical data
            end_date: End date for historical data
            interval: Data interval (e.g., '1d', '1wk', '1mo')
            source: Data source
            **kwargs: Additional parameters for the optimization

        Returns:
            Dictionary mapping strategies to weights
        """
        # Get returns
        returns = await self.get_returns(symbols, start_date, end_date, interval, source)

        # Optimize portfolio for each strategy
        results = {}
        for strategy_name in strategies:
            try:
                # Get optimization strategy
                optimizer = self.get_strategy(strategy_name)

                # Optimize portfolio
                weights = optimizer.optimize(returns, **kwargs)

                # Store results
                results[strategy_name] = weights
            except Exception as e:
                logger.error(f"Error optimizing portfolio with strategy '{strategy_name}': {str(e)}")
                results[strategy_name] = {}

        return results

    async def backtest_portfolio(
        self,
        symbols: List[str],
        weights: Dict[str, float],
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        interval: str = "1d",
        source: Optional[str] = None
    ) -> Dict:
        """
        Backtest a portfolio with fixed weights.

        Args:
            symbols: List of symbols
            weights: Dictionary mapping symbols to weights
            start_date: Start date for historical data
            end_date: End date for historical data
            interval: Data interval (e.g., '1d', '1wk', '1mo')
            source: Data source

        Returns:
            Dictionary with backtest results
        """
        # Set default dates if not provided
        if end_date is None:
            end_date = date.today()

        if start_date is None:
            # Default to 1 year of data
            start_date = end_date - timedelta(days=365)

        # Get historical data
        data = await data_manager.get_multiple_stock_data(
            symbols, start_date, end_date, interval, source
        )

        # Extract close prices
        prices = {}
        for symbol, df in data.items():
            if not df.empty:
                # Set date as index
                df = df.set_index("date")
                # Get close prices
                prices[symbol] = df["close"]

        # Create DataFrame with prices
        price_df = pd.DataFrame(prices)

        # Calculate returns
        returns = price_df.pct_change().dropna()

        # Calculate portfolio returns
        portfolio_returns = pd.Series(0.0, index=returns.index)
        for symbol, weight in weights.items():
            if symbol in returns.columns:
                portfolio_returns += returns[symbol] * weight

        # Calculate performance metrics
        metrics = calculate_performance_metrics(portfolio_returns, self.risk_free_rate)

        # Create backtest results
        results = {
            "returns": portfolio_returns,
            "metrics": metrics,
            "weights": weights
        }

        return results

    async def compare_backtests(
        self,
        symbols: List[str],
        strategies: List[str],
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        interval: str = "1d",
        source: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Dict]:
        """
        Compare backtests for different optimization strategies.

        Args:
            symbols: List of symbols
            strategies: List of optimization strategies
            start_date: Start date for historical data
            end_date: End date for historical data
            interval: Data interval (e.g., '1d', '1wk', '1mo')
            source: Data source
            **kwargs: Additional parameters for the optimization

        Returns:
            Dictionary mapping strategies to backtest results
        """
        # Get optimization results for each strategy
        optimization_results = await self.compare_strategies(
            symbols, strategies, start_date, end_date, interval, source, **kwargs
        )

        # Backtest each strategy
        backtest_results = {}
        for strategy_name, weights in optimization_results.items():
            if weights:
                # Backtest portfolio
                results = await self.backtest_portfolio(
                    symbols, weights, start_date, end_date, interval, source
                )

                # Store results
                backtest_results[strategy_name] = results
            else:
                backtest_results[strategy_name] = {
                    "returns": pd.Series(),
                    "metrics": {},
                    "weights": {}
                }

        return backtest_results


# Create a singleton instance
portfolio_optimization_manager = PortfolioOptimizationManager()

