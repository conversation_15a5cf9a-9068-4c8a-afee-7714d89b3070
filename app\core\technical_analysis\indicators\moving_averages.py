"""
Moving average indicators for technical analysis.
"""
from typing import Optional, Union

import numpy as np
import pandas as pd


def simple_moving_average(
    data: Union[pd.Series, pd.DataFrame],
    window: int,
    column: Optional[str] = None
) -> Union[pd.Series, pd.DataFrame]:
    """
    Calculate Simple Moving Average (SMA).
    
    Args:
        data: Price data as Series or DataFrame
        window: Window size for moving average
        column: Column name to use if data is a DataFrame
        
    Returns:
        Series or DataFrame with SMA values
    """
    if isinstance(data, pd.DataFrame) and column:
        return data[column].rolling(window=window).mean()
    return data.rolling(window=window).mean()


def exponential_moving_average(
    data: Union[pd.Series, pd.DataFrame],
    window: int,
    column: Optional[str] = None
) -> Union[pd.Series, pd.DataFrame]:
    """
    Calculate Exponential Moving Average (EMA).
    
    Args:
        data: Price data as Series or DataFrame
        window: Window size for moving average
        column: Column name to use if data is a DataFrame
        
    Returns:
        Series or DataFrame with EMA values
    """
    if isinstance(data, pd.DataFrame) and column:
        return data[column].ewm(span=window, adjust=False).mean()
    return data.ewm(span=window, adjust=False).mean()


def weighted_moving_average(
    data: Union[pd.Series, pd.DataFrame],
    window: int,
    column: Optional[str] = None
) -> Union[pd.Series, pd.DataFrame]:
    """
    Calculate Weighted Moving Average (WMA).
    
    Args:
        data: Price data as Series or DataFrame
        window: Window size for moving average
        column: Column name to use if data is a DataFrame
        
    Returns:
        Series or DataFrame with WMA values
    """
    weights = np.arange(1, window + 1)
    
    if isinstance(data, pd.DataFrame) and column:
        return data[column].rolling(window=window).apply(
            lambda x: np.sum(weights * x) / weights.sum(), raw=True
        )
    
    return data.rolling(window=window).apply(
        lambda x: np.sum(weights * x) / weights.sum(), raw=True
    )


def hull_moving_average(
    data: Union[pd.Series, pd.DataFrame],
    window: int,
    column: Optional[str] = None
) -> Union[pd.Series, pd.DataFrame]:
    """
    Calculate Hull Moving Average (HMA).
    
    Args:
        data: Price data as Series or DataFrame
        window: Window size for moving average
        column: Column name to use if data is a DataFrame
        
    Returns:
        Series or DataFrame with HMA values
    """
    if isinstance(data, pd.DataFrame) and column:
        series = data[column]
    else:
        series = data
    
    # Calculate WMA with period window/2
    half_window = int(window / 2)
    wma_half = weighted_moving_average(series, half_window)
    
    # Calculate WMA for full window
    wma_full = weighted_moving_average(series, window)
    
    # Calculate 2 * WMA(n/2) - WMA(n)
    diff = 2 * wma_half - wma_full
    
    # Calculate WMA with period sqrt(n)
    sqrt_window = int(np.sqrt(window))
    hma = weighted_moving_average(diff, sqrt_window)
    
    return hma


def double_exponential_moving_average(
    data: Union[pd.Series, pd.DataFrame],
    window: int,
    column: Optional[str] = None
) -> Union[pd.Series, pd.DataFrame]:
    """
    Calculate Double Exponential Moving Average (DEMA).
    
    Args:
        data: Price data as Series or DataFrame
        window: Window size for moving average
        column: Column name to use if data is a DataFrame
        
    Returns:
        Series or DataFrame with DEMA values
    """
    if isinstance(data, pd.DataFrame) and column:
        series = data[column]
    else:
        series = data
    
    # Calculate EMA
    ema = exponential_moving_average(series, window)
    
    # Calculate EMA of EMA
    ema_of_ema = exponential_moving_average(ema, window)
    
    # Calculate DEMA: 2 * EMA - EMA of EMA
    dema = 2 * ema - ema_of_ema
    
    return dema


def triple_exponential_moving_average(
    data: Union[pd.Series, pd.DataFrame],
    window: int,
    column: Optional[str] = None
) -> Union[pd.Series, pd.DataFrame]:
    """
    Calculate Triple Exponential Moving Average (TEMA).
    
    Args:
        data: Price data as Series or DataFrame
        window: Window size for moving average
        column: Column name to use if data is a DataFrame
        
    Returns:
        Series or DataFrame with TEMA values
    """
    if isinstance(data, pd.DataFrame) and column:
        series = data[column]
    else:
        series = data
    
    # Calculate EMA
    ema1 = exponential_moving_average(series, window)
    
    # Calculate EMA of EMA
    ema2 = exponential_moving_average(ema1, window)
    
    # Calculate EMA of EMA of EMA
    ema3 = exponential_moving_average(ema2, window)
    
    # Calculate TEMA: 3 * EMA - 3 * EMA of EMA + EMA of EMA of EMA
    tema = 3 * ema1 - 3 * ema2 + ema3
    
    return tema


def moving_average_convergence_divergence(
    data: Union[pd.Series, pd.DataFrame],
    fast_period: int = 12,
    slow_period: int = 26,
    signal_period: int = 9,
    column: Optional[str] = None
) -> pd.DataFrame:
    """
    Calculate Moving Average Convergence Divergence (MACD).
    
    Args:
        data: Price data as Series or DataFrame
        fast_period: Fast EMA period
        slow_period: Slow EMA period
        signal_period: Signal line period
        column: Column name to use if data is a DataFrame
        
    Returns:
        DataFrame with MACD line, signal line, and histogram
    """
    if isinstance(data, pd.DataFrame) and column:
        series = data[column]
    else:
        series = data
    
    # Calculate fast and slow EMAs
    fast_ema = exponential_moving_average(series, fast_period)
    slow_ema = exponential_moving_average(series, slow_period)
    
    # Calculate MACD line
    macd_line = fast_ema - slow_ema
    
    # Calculate signal line
    signal_line = exponential_moving_average(macd_line, signal_period)
    
    # Calculate histogram
    histogram = macd_line - signal_line
    
    # Create result DataFrame
    result = pd.DataFrame({
        'macd_line': macd_line,
        'signal_line': signal_line,
        'histogram': histogram
    }, index=data.index)
    
    return result


def bollinger_bands(
    data: Union[pd.Series, pd.DataFrame],
    window: int = 20,
    num_std: float = 2.0,
    column: Optional[str] = None
) -> pd.DataFrame:
    """
    Calculate Bollinger Bands.
    
    Args:
        data: Price data as Series or DataFrame
        window: Window size for moving average
        num_std: Number of standard deviations for bands
        column: Column name to use if data is a DataFrame
        
    Returns:
        DataFrame with middle band, upper band, and lower band
    """
    if isinstance(data, pd.DataFrame) and column:
        series = data[column]
    else:
        series = data
    
    # Calculate middle band (SMA)
    middle_band = simple_moving_average(series, window)
    
    # Calculate standard deviation
    std = series.rolling(window=window).std()
    
    # Calculate upper and lower bands
    upper_band = middle_band + (std * num_std)
    lower_band = middle_band - (std * num_std)
    
    # Create result DataFrame
    result = pd.DataFrame({
        'middle_band': middle_band,
        'upper_band': upper_band,
        'lower_band': lower_band
    }, index=data.index)
    
    return result


def keltner_channel(
    data: pd.DataFrame,
    window: int = 20,
    atr_window: int = 10,
    atr_multiplier: float = 2.0,
    price_column: str = 'close',
    high_column: str = 'high',
    low_column: str = 'low'
) -> pd.DataFrame:
    """
    Calculate Keltner Channel.
    
    Args:
        data: Price data as DataFrame with high, low, close columns
        window: Window size for moving average
        atr_window: Window size for ATR calculation
        atr_multiplier: Multiplier for ATR
        price_column: Column name for price data
        high_column: Column name for high price data
        low_column: Column name for low price data
        
    Returns:
        DataFrame with middle line, upper line, and lower line
    """
    # Calculate middle line (EMA)
    middle_line = exponential_moving_average(data, window, price_column)
    
    # Calculate Average True Range (ATR)
    tr1 = data[high_column] - data[low_column]
    tr2 = abs(data[high_column] - data[price_column].shift(1))
    tr3 = abs(data[low_column] - data[price_column].shift(1))
    
    tr = pd.DataFrame({
        'tr1': tr1,
        'tr2': tr2,
        'tr3': tr3
    }).max(axis=1)
    
    atr = tr.rolling(window=atr_window).mean()
    
    # Calculate upper and lower lines
    upper_line = middle_line + (atr * atr_multiplier)
    lower_line = middle_line - (atr * atr_multiplier)
    
    # Create result DataFrame
    result = pd.DataFrame({
        'middle_line': middle_line,
        'upper_line': upper_line,
        'lower_line': lower_line
    }, index=data.index)
    
    return result
