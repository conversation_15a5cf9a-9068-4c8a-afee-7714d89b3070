"""
Factor-based portfolio optimization strategy.

This module implements factor-based optimization techniques for portfolio optimization,
which use factor models to estimate expected returns and risk.
"""
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
from scipy import optimize, stats

from app.core.portfolio.optimization.base import OptimizationStrategy
from app.core.portfolio.optimization.mean_variance import MeanVarianceOptimization
from app.utils.constants import DEFAULT_RISK_FREE_RATE
from app.utils.logging import logger


class FactorOptimization(OptimizationStrategy):
    """
    Factor-based portfolio optimization strategy.
    
    This class implements factor-based optimization techniques for portfolio optimization,
    which use factor models to estimate expected returns and risk.
    """
    
    def __init__(self, risk_free_rate: float = DEFAULT_RISK_FREE_RATE):
        """
        Initialize the factor-based optimization strategy.
        
        Args:
            risk_free_rate: Risk-free rate (default: 2%)
        """
        super().__init__("Factor Optimization")
        self.risk_free_rate = risk_free_rate
        self.mv_optimizer = MeanVarianceOptimization(risk_free_rate=risk_free_rate)
    
    def optimize(
        self,
        returns: pd.DataFrame,
        factor_returns: Optional[pd.DataFrame] = None,
        factor_model: str = "pca",
        n_factors: int = 3,
        objective: str = "sharpe",
        **kwargs
    ) -> Dict[str, float]:
        """
        Optimize portfolio weights using factor-based optimization.
        
        Args:
            returns: DataFrame of asset returns
            factor_returns: DataFrame of factor returns (optional)
            factor_model: Factor model to use ('pca', 'statistical', 'fundamental')
            n_factors: Number of factors to use
            objective: Optimization objective for the final portfolio
            **kwargs: Additional parameters
            
        Returns:
            Dictionary mapping asset names to weights
        """
        # Validate inputs
        if returns is None or returns.empty:
            logger.error("Returns must be provided for factor optimization")
            return {}
        
        # Get assets
        assets = returns.columns.tolist()
        n_assets = len(assets)
        
        # Estimate factor model
        if factor_returns is not None:
            # Use provided factor returns
            factor_loadings, specific_risk = self._estimate_factor_model_with_factors(
                returns, factor_returns
            )
        elif factor_model == "pca":
            # Use PCA to extract factors
            factor_returns, factor_loadings, specific_risk = self._estimate_pca_factor_model(
                returns, n_factors
            )
        elif factor_model == "statistical":
            # Use statistical factor model
            factor_returns, factor_loadings, specific_risk = self._estimate_statistical_factor_model(
                returns, n_factors
            )
        else:
            logger.warning(f"Unknown factor model: {factor_model}, using PCA")
            factor_returns, factor_loadings, specific_risk = self._estimate_pca_factor_model(
                returns, n_factors
            )
        
        # Estimate expected returns and covariance matrix using the factor model
        expected_returns, cov_matrix = self._estimate_factor_model_parameters(
            returns, factor_returns, factor_loadings, specific_risk
        )
        
        # Create temporary returns DataFrame for mean-variance optimization
        temp_returns = pd.DataFrame({asset: [ret] for asset, ret in zip(assets, expected_returns)})
        
        # Use mean-variance optimization with the factor model estimates
        return self.mv_optimizer.optimize(
            temp_returns,
            objective=objective,
            cov_matrix=cov_matrix,
            **kwargs
        )
    
    def _estimate_factor_model_with_factors(
        self,
        returns: pd.DataFrame,
        factor_returns: pd.DataFrame
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        Estimate factor loadings and specific risk using provided factor returns.
        
        Args:
            returns: DataFrame of asset returns
            factor_returns: DataFrame of factor returns
            
        Returns:
            Tuple of (factor loadings, specific risk)
        """
        # Align factor returns with asset returns
        common_index = returns.index.intersection(factor_returns.index)
        asset_returns = returns.loc[common_index]
        factors = factor_returns.loc[common_index]
        
        # Get dimensions
        n_assets = len(returns.columns)
        n_factors = len(factors.columns)
        
        # Initialize factor loadings and specific risk
        factor_loadings = np.zeros((n_assets, n_factors))
        specific_risk = np.zeros(n_assets)
        
        # Estimate factor loadings for each asset using linear regression
        for i, asset in enumerate(returns.columns):
            # Perform linear regression
            X = factors.values
            y = asset_returns[asset].values
            
            # Add constant term
            X_with_const = np.column_stack([np.ones(len(X)), X])
            
            # Estimate coefficients
            try:
                # Use OLS regression
                coeffs, residuals, _, _ = np.linalg.lstsq(X_with_const, y, rcond=None)
                
                # Extract factor loadings (skip the constant term)
                factor_loadings[i, :] = coeffs[1:]
                
                # Calculate specific risk (variance of residuals)
                specific_risk[i] = np.var(y - X_with_const @ coeffs)
            except:
                logger.warning(f"Error estimating factor loadings for {asset}")
                # Use zeros for factor loadings and sample variance for specific risk
                factor_loadings[i, :] = 0
                specific_risk[i] = np.var(y)
        
        return factor_loadings, specific_risk
    
    def _estimate_pca_factor_model(
        self,
        returns: pd.DataFrame,
        n_factors: int
    ) -> Tuple[pd.DataFrame, np.ndarray, np.ndarray]:
        """
        Estimate a PCA factor model.
        
        Args:
            returns: DataFrame of asset returns
            n_factors: Number of factors to extract
            
        Returns:
            Tuple of (factor returns, factor loadings, specific risk)
        """
        # Get dimensions
        n_obs = len(returns)
        n_assets = len(returns.columns)
        
        # Ensure n_factors is valid
        n_factors = min(n_factors, n_assets, n_obs)
        
        # Standardize returns
        standardized_returns = (returns - returns.mean()) / returns.std()
        
        # Perform PCA
        try:
            # Calculate covariance matrix
            cov_matrix = standardized_returns.cov().values
            
            # Perform eigendecomposition
            eigenvalues, eigenvectors = np.linalg.eigh(cov_matrix)
            
            # Sort eigenvalues and eigenvectors in descending order
            idx = eigenvalues.argsort()[::-1]
            eigenvalues = eigenvalues[idx]
            eigenvectors = eigenvectors[:, idx]
            
            # Select top n_factors eigenvectors
            factor_loadings = eigenvectors[:, :n_factors]
            
            # Calculate factor returns
            factor_returns = standardized_returns.values @ factor_loadings
            
            # Convert to DataFrame
            factor_returns_df = pd.DataFrame(
                factor_returns,
                index=returns.index,
                columns=[f"Factor_{i+1}" for i in range(n_factors)]
            )
            
            # Calculate specific risk
            reconstructed_returns = factor_returns @ factor_loadings.T
            residuals = standardized_returns.values - reconstructed_returns
            specific_risk = np.var(residuals, axis=0)
            
            return factor_returns_df, factor_loadings, specific_risk
        except:
            logger.error("Error performing PCA")
            # Return empty factor returns and identity factor loadings
            factor_returns_df = pd.DataFrame(
                np.zeros((n_obs, n_factors)),
                index=returns.index,
                columns=[f"Factor_{i+1}" for i in range(n_factors)]
            )
            factor_loadings = np.eye(n_assets, n_factors)
            specific_risk = np.var(returns.values, axis=0)
            
            return factor_returns_df, factor_loadings, specific_risk
    
    def _estimate_statistical_factor_model(
        self,
        returns: pd.DataFrame,
        n_factors: int
    ) -> Tuple[pd.DataFrame, np.ndarray, np.ndarray]:
        """
        Estimate a statistical factor model using maximum likelihood.
        
        Args:
            returns: DataFrame of asset returns
            n_factors: Number of factors to extract
            
        Returns:
            Tuple of (factor returns, factor loadings, specific risk)
        """
        # For simplicity, we'll use PCA as an approximation to the statistical factor model
        # In practice, a more sophisticated approach like maximum likelihood estimation would be used
        return self._estimate_pca_factor_model(returns, n_factors)
    
    def _estimate_factor_model_parameters(
        self,
        returns: pd.DataFrame,
        factor_returns: pd.DataFrame,
        factor_loadings: np.ndarray,
        specific_risk: np.ndarray
    ) -> Tuple[np.ndarray, pd.DataFrame]:
        """
        Estimate expected returns and covariance matrix using a factor model.
        
        Args:
            returns: DataFrame of asset returns
            factor_returns: DataFrame of factor returns
            factor_loadings: Factor loadings matrix
            specific_risk: Specific risk vector
            
        Returns:
            Tuple of (expected returns, covariance matrix)
        """
        # Get assets
        assets = returns.columns.tolist()
        n_assets = len(assets)
        
        # Estimate factor expected returns
        factor_expected_returns = factor_returns.mean().values
        
        # Estimate asset expected returns using the factor model
        expected_returns = factor_loadings @ factor_expected_returns
        
        # Estimate factor covariance matrix
        factor_cov = factor_returns.cov().values
        
        # Estimate asset covariance matrix using the factor model
        # Cov = B * F * B' + D
        # where B is factor loadings, F is factor covariance, D is specific risk diagonal matrix
        common_risk = factor_loadings @ factor_cov @ factor_loadings.T
        specific_risk_matrix = np.diag(specific_risk)
        cov_matrix = common_risk + specific_risk_matrix
        
        # Convert to DataFrame
        cov_df = pd.DataFrame(cov_matrix, index=assets, columns=assets)
        
        return expected_returns, cov_df
