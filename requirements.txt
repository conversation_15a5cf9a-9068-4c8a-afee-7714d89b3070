# Data & Analysis
pandas>=1.3.0
numpy>=1.20.0
yfinance>=0.1.70
pandas-ta>=0.3.14b0
scipy>=1.7.0
aiohttp>=3.8.0
aiolimiter>=1.0.0

# Portfolio Optimization
PyPortfolioOpt>=1.5.0
cvxpy>=1.2.0
scikit-learn>=1.0.0

# Web/API
fastapi>=0.68.0
uvicorn>=0.15.0
pydantic>=2.0.0
pydantic-settings>=2.0.0  # Required for BaseSettings
python-multipart>=0.0.5  # Required for form data
streamlit>=1.10.0

# Authentication
python-jose>=3.3.0  # JWT token handling
passlib>=1.7.4  # Password hashing
bcrypt>=3.2.0  # Password hashing

# Visualization
plotly>=5.3.0
matplotlib>=3.4.0
mplfinance>=0.12.7b0
seaborn>=0.11.0

# Database & Caching
SQLAlchemy>=1.4.0
aiosqlite>=0.17.0
redis>=4.0.0

# Testing
pytest>=6.2.0
pytest-asyncio>=0.15.0
pytest-cov>=2.12.0

# Utilities
python-dotenv>=0.19.0
pyyaml>=6.0
requests>=2.26.0
jupyter>=1.0.0
notebook>=6.4.0
websocket-client>=1.3.0  # Required for real-time data

# HTTP Client
httpx>=0.23.0  # Required for FastAPI TestClient
