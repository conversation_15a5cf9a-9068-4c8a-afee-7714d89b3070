# Documentation

This directory contains documentation for the Portfolio Optimization project.

## Documentation Structure

- **api.md**: API documentation for backend endpoints
- **user_guide.md**: User guide for the Streamlit frontend
- **developer_guide.md**: Developer guide for contributing to the project
- **architecture.md**: Architecture overview of the project
- **deployment.md**: Deployment instructions
- **faq.md**: Frequently asked questions

## API Documentation

The API documentation provides detailed information about the backend API endpoints, including:

- Endpoint URLs
- Request parameters
- Request bodies
- Response formats
- Authentication requirements
- Error handling

## User Guide

The user guide provides instructions for using the Streamlit frontend, including:

- Getting started
- Data exploration
- Technical analysis
- Portfolio optimization
- Backtesting
- Performance analysis
- User portfolios
- Settings
- Troubleshooting

## Developer Guide

The developer guide provides information for developers who want to contribute to the project, including:

- Setting up the development environment
- Project structure
- Coding standards
- Testing
- Pull request process
- Release process

## Architecture Overview

The architecture overview provides a high-level overview of the project architecture, including:

- Component diagram
- Data flow diagram
- Technology stack
- Design patterns
- Security considerations

## Deployment Instructions

The deployment instructions provide step-by-step instructions for deploying the project, including:

- Prerequisites
- Installation
- Configuration
- Database setup
- Web server setup
- Docker deployment
- Cloud deployment

## Frequently Asked Questions

The FAQ provides answers to frequently asked questions about the project, including:

- General questions
- Data questions
- Technical analysis questions
- Portfolio optimization questions
- Backtesting questions
- Performance analysis questions
- User portfolio questions
- Settings questions
- Troubleshooting questions

## Generating Documentation

The API documentation can be automatically generated from the FastAPI application using the following command:

```bash
python -m app.utils.docs_generator
```

This will generate an updated `api.md` file based on the current API endpoints.

## Viewing Documentation

The documentation can be viewed in any Markdown viewer or on GitHub.

For a more interactive experience, you can use a documentation generator like MkDocs:

```bash
# Install MkDocs
pip install mkdocs

# Build and serve the documentation
mkdocs serve
```

Then open your browser and navigate to `http://localhost:8000` to view the documentation.
