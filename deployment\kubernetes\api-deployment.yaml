apiVersion: apps/v1
kind: Deployment
metadata:
  name: portfolio-api
  labels:
    app: portfolio-api
spec:
  replicas: 2
  selector:
    matchLabels:
      app: portfolio-api
  template:
    metadata:
      labels:
        app: portfolio-api
    spec:
      containers:
      - name: portfolio-api
        image: portfolio-optimization/api:latest
        imagePullPolicy: IfNotPresent
        command: ["/app/entrypoint.sh", "api"]
        ports:
        - containerPort: 8000
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: portfolio-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: portfolio-secrets
              key: redis-url
        resources:
          limits:
            cpu: "1"
            memory: "1Gi"
          requests:
            cpu: "500m"
            memory: "512Mi"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: portfolio-api
spec:
  selector:
    app: portfolio-api
  ports:
  - port: 80
    targetPort: 8000
  type: ClusterIP
