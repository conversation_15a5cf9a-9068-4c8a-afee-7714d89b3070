"""
Portfolio rebalancing strategies.

This module provides various portfolio rebalancing strategies, including
periodic rebalancing, threshold-based rebalancing, and others.
"""
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd

from app.utils.logging import logger


class RebalancingStrategy:
    """Base class for portfolio rebalancing strategies."""

    def __init__(self, name: str):
        """
        Initialize the rebalancing strategy.

        Args:
            name: Name of the strategy
        """
        self.name = name

    def should_rebalance(
        self,
        current_weights: Dict[str, float],
        target_weights: Dict[str, float],
        last_rebalance_date: Optional[Union[date, pd.Timestamp]] = None,
        current_date: Optional[Union[date, pd.Timestamp]] = None,
        **kwargs
    ) -> bool:
        """
        Determine if the portfolio should be rebalanced.

        Args:
            current_weights: Current portfolio weights
            target_weights: Target portfolio weights
            last_rebalance_date: Date of the last rebalance
            current_date: Current date
            **kwargs: Additional parameters

        Returns:
            True if the portfolio should be rebalanced, False otherwise
        """
        raise NotImplementedError("Subclasses must implement should_rebalance")

    def get_rebalanced_weights(
        self,
        current_weights: Dict[str, float],
        target_weights: Dict[str, float],
        **kwargs
    ) -> Dict[str, float]:
        """
        Get the rebalanced portfolio weights.

        Args:
            current_weights: Current portfolio weights
            target_weights: Target portfolio weights
            **kwargs: Additional parameters

        Returns:
            Rebalanced portfolio weights
        """
        # By default, return the target weights
        return target_weights


class PeriodicRebalancing(RebalancingStrategy):
    """Periodic rebalancing strategy."""

    def __init__(self, frequency: str = "quarterly"):
        """
        Initialize the periodic rebalancing strategy.

        Args:
            frequency: Rebalancing frequency ('daily', 'weekly', 'monthly', 'quarterly', 'annually')
        """
        super().__init__(f"Periodic Rebalancing ({frequency})")
        self.frequency = frequency

    def should_rebalance(
        self,
        current_weights: Dict[str, float],
        target_weights: Dict[str, float],
        last_rebalance_date: Optional[Union[date, pd.Timestamp]] = None,
        current_date: Optional[Union[date, pd.Timestamp]] = None,
        **kwargs
    ) -> bool:
        """
        Determine if the portfolio should be rebalanced based on the time since the last rebalance.

        Args:
            current_weights: Current portfolio weights
            target_weights: Target portfolio weights
            last_rebalance_date: Date of the last rebalance
            current_date: Current date
            **kwargs: Additional parameters

        Returns:
            True if the portfolio should be rebalanced, False otherwise
        """
        logger.debug(f"PeriodicRebalancing.should_rebalance called with frequency: {self.frequency}")
        logger.debug(f"last_rebalance_date: {last_rebalance_date}, type: {type(last_rebalance_date)}")
        logger.debug(f"current_date: {current_date}, type: {type(current_date)}")

        # If no last rebalance date or current date, rebalance
        if last_rebalance_date is None or current_date is None:
            logger.debug("No last_rebalance_date or current_date, returning True")
            return True

        # Convert to pandas Timestamp objects if they aren't already
        if not isinstance(last_rebalance_date, pd.Timestamp):
            logger.debug(f"Converting last_rebalance_date from {type(last_rebalance_date)} to pd.Timestamp")
            last_rebalance_date = pd.Timestamp(last_rebalance_date)
        if not isinstance(current_date, pd.Timestamp):
            logger.debug(f"Converting current_date from {type(current_date)} to pd.Timestamp")
            current_date = pd.Timestamp(current_date)

        # Ensure both dates have the same timezone or are both timezone-naive
        if last_rebalance_date.tzinfo is not None and current_date.tzinfo is None:
            current_date = current_date.tz_localize(last_rebalance_date.tzinfo)
        elif last_rebalance_date.tzinfo is None and current_date.tzinfo is not None:
            last_rebalance_date = last_rebalance_date.tz_localize(current_date.tzinfo)

        # Calculate time since last rebalance using pandas date_range
        try:
            logger.debug(f"Calculating time difference between {current_date} and {last_rebalance_date}")
            # Use pandas date_range which is more reliable than direct subtraction
            date_range = pd.date_range(start=last_rebalance_date, end=current_date, freq='D')
            days_since_last_rebalance = len(date_range) - 1
            logger.debug(f"Time difference calculated: {days_since_last_rebalance} days")
        except Exception as e:
            logger.warning(f"Error calculating time difference: {e}")
            # Fallback: use direct subtraction with pd.Timedelta to avoid Timestamp + int issues
            try:
                delta = current_date - last_rebalance_date
                days_since_last_rebalance = delta / pd.Timedelta(days=1)
                days_since_last_rebalance = int(days_since_last_rebalance)
                logger.debug(f"Fallback time difference result: {days_since_last_rebalance} days")
            except Exception as e2:
                logger.error(f"Fallback approach also failed: {e2}")
                days_since_last_rebalance = 0

        # Determine if rebalance is needed based on frequency
        if self.frequency == "daily":
            return days_since_last_rebalance >= 1
        elif self.frequency == "weekly":
            return days_since_last_rebalance >= 7
        elif self.frequency == "monthly":
            # Approximate a month as 30 days
            return days_since_last_rebalance >= 30
        elif self.frequency == "quarterly":
            # Approximate a quarter as 90 days
            return days_since_last_rebalance >= 90
        elif self.frequency == "annually":
            # Approximate a year as 365 days
            return days_since_last_rebalance >= 365
        else:
            logger.warning(f"Unknown frequency: {self.frequency}, defaulting to quarterly")
            return days_since_last_rebalance >= 90


class ThresholdRebalancing(RebalancingStrategy):
    """Threshold-based rebalancing strategy."""

    def __init__(self, threshold: float = 0.05):
        """
        Initialize the threshold-based rebalancing strategy.

        Args:
            threshold: Rebalancing threshold (as a decimal, e.g., 0.05 for 5%)
        """
        super().__init__(f"Threshold Rebalancing ({threshold:.1%})")
        self.threshold = threshold

    def should_rebalance(
        self,
        current_weights: Dict[str, float],
        target_weights: Dict[str, float],
        last_rebalance_date: Optional[Union[date, pd.Timestamp]] = None,
        current_date: Optional[Union[date, pd.Timestamp]] = None,
        **kwargs
    ) -> bool:
        """
        Determine if the portfolio should be rebalanced based on the deviation from target weights.

        Args:
            current_weights: Current portfolio weights
            target_weights: Target portfolio weights
            last_rebalance_date: Date of the last rebalance (not used)
            current_date: Current date (not used)
            **kwargs: Additional parameters

        Returns:
            True if the portfolio should be rebalanced, False otherwise
        """
        # Check if any asset's weight deviates from its target by more than the threshold
        for asset, target_weight in target_weights.items():
            current_weight = current_weights.get(asset, 0)
            if abs(current_weight - target_weight) > self.threshold:
                return True

        return False


class HybridRebalancing(RebalancingStrategy):
    """Hybrid rebalancing strategy combining periodic and threshold-based approaches."""

    def __init__(self, frequency: str = "quarterly", threshold: float = 0.05):
        """
        Initialize the hybrid rebalancing strategy.

        Args:
            frequency: Rebalancing frequency ('daily', 'weekly', 'monthly', 'quarterly', 'annually')
            threshold: Rebalancing threshold (as a decimal, e.g., 0.05 for 5%)
        """
        super().__init__(f"Hybrid Rebalancing ({frequency}, {threshold:.1%})")
        self.periodic_strategy = PeriodicRebalancing(frequency)
        self.threshold_strategy = ThresholdRebalancing(threshold)

    def should_rebalance(
        self,
        current_weights: Dict[str, float],
        target_weights: Dict[str, float],
        last_rebalance_date: Optional[Union[date, pd.Timestamp]] = None,
        current_date: Optional[Union[date, pd.Timestamp]] = None,
        **kwargs
    ) -> bool:
        """
        Determine if the portfolio should be rebalanced based on either time or weight deviation.

        Args:
            current_weights: Current portfolio weights
            target_weights: Target portfolio weights
            last_rebalance_date: Date of the last rebalance
            current_date: Current date
            **kwargs: Additional parameters

        Returns:
            True if the portfolio should be rebalanced, False otherwise
        """
        # Rebalance if either strategy says to rebalance
        return (
            self.periodic_strategy.should_rebalance(
                current_weights, target_weights, last_rebalance_date, current_date, **kwargs
            ) or
            self.threshold_strategy.should_rebalance(
                current_weights, target_weights, last_rebalance_date, current_date, **kwargs
            )
        )


class OptimalRebalancing(RebalancingStrategy):
    """
    Optimal rebalancing strategy that considers transaction costs.

    This strategy rebalances only when the expected benefit of rebalancing
    (in terms of risk-adjusted return) exceeds the transaction costs.
    """

    def __init__(self, transaction_cost: float = 0.001):
        """
        Initialize the optimal rebalancing strategy.

        Args:
            transaction_cost: Transaction cost as a fraction of the transaction amount
        """
        super().__init__(f"Optimal Rebalancing (TC={transaction_cost:.2%})")
        self.transaction_cost = transaction_cost

    def should_rebalance(
        self,
        current_weights: Dict[str, float],
        target_weights: Dict[str, float],
        last_rebalance_date: Optional[Union[date, pd.Timestamp]] = None,
        current_date: Optional[Union[date, pd.Timestamp]] = None,
        expected_returns: Optional[Dict[str, float]] = None,
        covariance_matrix: Optional[pd.DataFrame] = None,
        **kwargs
    ) -> bool:
        """
        Determine if the portfolio should be rebalanced based on a cost-benefit analysis.

        Args:
            current_weights: Current portfolio weights
            target_weights: Target portfolio weights
            last_rebalance_date: Date of the last rebalance (not used)
            current_date: Current date (not used)
            expected_returns: Expected returns for each asset
            covariance_matrix: Covariance matrix for the assets
            **kwargs: Additional parameters

        Returns:
            True if the portfolio should be rebalanced, False otherwise
        """
        # If expected returns or covariance matrix are not provided, use a threshold-based approach
        if expected_returns is None or covariance_matrix is None:
            threshold_strategy = ThresholdRebalancing(0.05)
            return threshold_strategy.should_rebalance(
                current_weights, target_weights, last_rebalance_date, current_date, **kwargs
            )

        # Calculate the expected return and risk of the current portfolio
        # Ensure expected_returns values are floats
        expected_returns_float = {k: float(v) for k, v in expected_returns.items()}
        current_return = sum(current_weights.get(asset, 0) * expected_returns_float.get(asset, 0.0) for asset in expected_returns_float)

        # Calculate the expected return of the target portfolio
        target_return = sum(target_weights.get(asset, 0) * expected_returns_float.get(asset, 0.0) for asset in expected_returns_float)

        # Calculate the transaction cost of rebalancing
        transaction_amount = sum(
            abs(current_weights.get(asset, 0) - target_weights.get(asset, 0))
            for asset in set(current_weights) | set(target_weights)
        )
        rebalancing_cost = transaction_amount * self.transaction_cost

        # Rebalance if the expected benefit exceeds the cost
        return (target_return - current_return) > rebalancing_cost


def get_rebalancing_strategy(strategy_name: str, **kwargs) -> RebalancingStrategy:
    """
    Get a rebalancing strategy by name.

    Args:
        strategy_name: Name of the strategy
        **kwargs: Additional parameters for the strategy

    Returns:
        Rebalancing strategy
    """
    if strategy_name == "periodic":
        frequency = kwargs.get("frequency", "quarterly")
        return PeriodicRebalancing(frequency)
    elif strategy_name == "threshold":
        threshold = kwargs.get("threshold", 0.05)
        return ThresholdRebalancing(threshold)
    elif strategy_name == "hybrid":
        frequency = kwargs.get("frequency", "quarterly")
        threshold = kwargs.get("threshold", 0.05)
        return HybridRebalancing(frequency, threshold)
    elif strategy_name == "optimal":
        transaction_cost = kwargs.get("transaction_cost", 0.001)
        return OptimalRebalancing(transaction_cost)
    else:
        logger.warning(f"Unknown rebalancing strategy: {strategy_name}, using periodic")
        return PeriodicRebalancing("quarterly")
