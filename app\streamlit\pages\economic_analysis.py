"""
Economic analysis page for Streamlit app.
"""
import streamlit as st
import pandas as pd
import numpy as np
from datetime import date, datetime, timedelta
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots

from app.core.data_collection.economic import economic_data_collector
from app.utils.logging import logger


async def show_economic_analysis_page():
    """Show the economic analysis page."""
    st.title("Economic Analysis")
    
    # Create tabs for different analysis types
    tabs = st.tabs([
        "Economic Indicators",
        "Yield Curve",
        "Correlation Analysis"
    ])
    
    # Economic Indicators tab
    with tabs[0]:
        await show_economic_indicators()
    
    # Yield Curve tab
    with tabs[1]:
        await show_yield_curve()
    
    # Correlation Analysis tab
    with tabs[2]:
        await show_correlation_analysis()


async def show_economic_indicators():
    """Show economic indicators."""
    st.header("Economic Indicators")
    
    # Get user inputs
    with st.sidebar:
        st.subheader("Economic Indicators Settings")
        
        # Select indicators
        available_indicators = [
            "GDP",
            "INFLATION",
            "CPI",
            "UNEMPLOYMENT",
            "RETAIL_SALES",
            "NONFARM_PAYROLL",
            "INTEREST_RATE",
            "CONSUMER_SENTIMENT"
        ]
        
        selected_indicators = st.multiselect(
            "Select Indicators",
            options=available_indicators,
            default=["GDP", "INFLATION", "UNEMPLOYMENT"]
        )
        
        # Select interval
        interval = st.selectbox(
            "Interval",
            options=["monthly", "quarterly", "annual"],
            index=0
        )
        
        # Select date range
        col1, col2 = st.columns(2)
        with col1:
            start_date = st.date_input(
                "Start Date",
                value=date.today() - timedelta(days=365*5)
            )
        with col2:
            end_date = st.date_input(
                "End Date",
                value=date.today()
            )
    
    # Check if indicators are selected
    if not selected_indicators:
        st.warning("Please select at least one economic indicator.")
        return
    
    # Get economic indicators data
    with st.spinner("Loading economic indicators..."):
        try:
            # Get data for selected indicators
            indicators_data = await economic_data_collector.get_multiple_economic_indicators(
                selected_indicators,
                interval,
                start_date,
                end_date
            )
            
            if not indicators_data:
                st.error("No economic indicator data found.")
                return
            
            # Display indicators
            for indicator, df in indicators_data.items():
                st.subheader(f"{indicator}")
                
                # Display data
                st.dataframe(df)
                
                # Plot indicator
                fig = px.line(
                    df,
                    x=df.index,
                    y=df.columns[0],
                    title=f"{indicator} ({interval})",
                    labels={
                        df.columns[0]: indicator,
                        "date": "Date"
                    }
                )
                
                st.plotly_chart(fig, use_container_width=True)
            
            # Plot all indicators in one chart
            if len(indicators_data) > 1:
                st.subheader("Combined Indicators")
                
                # Create figure
                fig = go.Figure()
                
                # Add trace for each indicator
                for indicator, df in indicators_data.items():
                    # Normalize data for comparison
                    normalized_data = (df[df.columns[0]] - df[df.columns[0]].mean()) / df[df.columns[0]].std()
                    
                    fig.add_trace(
                        go.Scatter(
                            x=df.index,
                            y=normalized_data,
                            mode="lines",
                            name=indicator
                        )
                    )
                
                # Update layout
                fig.update_layout(
                    title="Normalized Economic Indicators",
                    xaxis_title="Date",
                    yaxis_title="Normalized Value (Z-Score)",
                    legend=dict(
                        orientation="h",
                        yanchor="bottom",
                        y=1.02,
                        xanchor="right",
                        x=1
                    )
                )
                
                st.plotly_chart(fig, use_container_width=True)
            
        except Exception as e:
            st.error(f"Error loading economic indicators: {str(e)}")
            logger.error(f"Error loading economic indicators: {str(e)}")


async def show_yield_curve():
    """Show yield curve analysis."""
    st.header("Yield Curve Analysis")
    
    # Get user inputs
    with st.sidebar:
        st.subheader("Yield Curve Settings")
        
        # Select date
        selected_date = st.date_input(
            "Select Date",
            value=date.today() - timedelta(days=1)
        )
    
    # Get yield curve data
    with st.spinner("Loading yield curve data..."):
        try:
            # Format date
            date_str = selected_date.strftime("%Y-%m-%d")
            
            # Get yield curve data
            df = await economic_data_collector.get_yield_curve(date_str)
            
            if df.empty:
                st.error("No yield curve data found for the selected date.")
                return
            
            # Display data
            st.dataframe(df)
            
            # Plot yield curve
            st.subheader("Yield Curve")
            
            # Extract maturities and yields
            maturities = []
            yields = []
            
            for col in df.columns:
                if col.endswith("yr"):
                    maturities.append(col)
                    yields.append(df[col].iloc[0])
            
            # Convert maturities to numeric values
            maturity_values = []
            for maturity in maturities:
                if maturity == "3mo":
                    maturity_values.append(0.25)
                elif maturity == "6mo":
                    maturity_values.append(0.5)
                elif maturity == "1yr":
                    maturity_values.append(1)
                elif maturity == "2yr":
                    maturity_values.append(2)
                elif maturity == "3yr":
                    maturity_values.append(3)
                elif maturity == "5yr":
                    maturity_values.append(5)
                elif maturity == "7yr":
                    maturity_values.append(7)
                elif maturity == "10yr":
                    maturity_values.append(10)
                elif maturity == "20yr":
                    maturity_values.append(20)
                elif maturity == "30yr":
                    maturity_values.append(30)
            
            # Create DataFrame for plotting
            yield_curve_df = pd.DataFrame({
                "Maturity": maturity_values,
                "Yield": yields,
                "Maturity_Label": maturities
            })
            
            # Sort by maturity
            yield_curve_df = yield_curve_df.sort_values("Maturity")
            
            # Plot yield curve
            fig = px.line(
                yield_curve_df,
                x="Maturity",
                y="Yield",
                title=f"Yield Curve ({date_str})",
                labels={
                    "Maturity": "Maturity (Years)",
                    "Yield": "Yield (%)"
                },
                markers=True
            )
            
            # Add hover text with maturity labels
            fig.update_traces(
                hovertemplate="Maturity: %{x} years<br>Yield: %{y:.2f}%"
            )
            
            # Check if yield curve is inverted
            is_inverted = yield_curve_df["Yield"].iloc[0] > yield_curve_df["Yield"].iloc[-1]
            
            if is_inverted:
                st.warning("The yield curve is inverted, which may indicate a potential recession.")
            
            st.plotly_chart(fig, use_container_width=True)
            
            # Add explanation
            st.subheader("Yield Curve Interpretation")
            st.write("""
            The yield curve shows the relationship between interest rates and the time to maturity of government bonds.
            
            - **Normal Yield Curve**: Upward sloping, indicating that longer-term bonds have higher yields than shorter-term bonds. This is the most common shape and suggests that the economy is expected to grow.
            
            - **Inverted Yield Curve**: Downward sloping, indicating that shorter-term bonds have higher yields than longer-term bonds. This is often seen as a predictor of economic recession.
            
            - **Flat Yield Curve**: Little difference between short-term and long-term yields, suggesting uncertainty about economic growth.
            """)
            
        except Exception as e:
            st.error(f"Error loading yield curve data: {str(e)}")
            logger.error(f"Error loading yield curve data: {str(e)}")


async def show_correlation_analysis():
    """Show correlation analysis between economic indicators and stock market."""
    st.header("Correlation Analysis")
    
    # Get user inputs
    with st.sidebar:
        st.subheader("Correlation Analysis Settings")
        
        # Select indicators
        available_indicators = [
            "GDP",
            "INFLATION",
            "CPI",
            "UNEMPLOYMENT",
            "RETAIL_SALES",
            "NONFARM_PAYROLL",
            "INTEREST_RATE",
            "CONSUMER_SENTIMENT"
        ]
        
        selected_indicators = st.multiselect(
            "Select Indicators",
            options=available_indicators,
            default=["GDP", "INFLATION", "UNEMPLOYMENT"]
        )
        
        # Select market index
        market_index = st.selectbox(
            "Market Index",
            options=["S&P 500", "Dow Jones", "NASDAQ"],
            index=0
        )
        
        # Map market index to symbol
        market_symbol_map = {
            "S&P 500": "SPY",
            "Dow Jones": "DIA",
            "NASDAQ": "QQQ"
        }
        
        market_symbol = market_symbol_map[market_index]
        
        # Select interval
        interval = st.selectbox(
            "Interval",
            options=["monthly", "quarterly"],
            index=0
        )
        
        # Select date range
        col1, col2 = st.columns(2)
        with col1:
            start_date = st.date_input(
                "Start Date",
                value=date.today() - timedelta(days=365*5),
                key="corr_start_date"
            )
        with col2:
            end_date = st.date_input(
                "End Date",
                value=date.today(),
                key="corr_end_date"
            )
    
    # Check if indicators are selected
    if not selected_indicators:
        st.warning("Please select at least one economic indicator.")
        return
    
    st.info("""
    This analysis shows the correlation between economic indicators and the stock market.
    Due to API limitations, we're using simulated data for demonstration purposes.
    In a production environment, this would use real market data.
    """)
    
    # Generate simulated market data
    with st.spinner("Analyzing correlations..."):
        try:
            # Get data for selected indicators
            indicators_data = await economic_data_collector.get_multiple_economic_indicators(
                selected_indicators,
                interval,
                start_date,
                end_date
            )
            
            if not indicators_data:
                st.error("No economic indicator data found.")
                return
            
            # Generate simulated market data
            # In a real implementation, this would fetch actual market data
            market_data = pd.DataFrame(index=pd.date_range(start=start_date, end=end_date, freq='M'))
            market_data["price"] = np.random.normal(loc=0.01, scale=0.05, size=len(market_data))
            market_data["price"] = (1 + market_data["price"]).cumprod() * 100
            
            # Calculate market returns
            market_data["return"] = market_data["price"].pct_change()
            
            # Create correlation DataFrame
            correlation_data = pd.DataFrame(index=market_data.index)
            correlation_data["market_return"] = market_data["return"]
            
            # Add indicator data
            for indicator, df in indicators_data.items():
                # Resample to match market data frequency if needed
                if interval == "quarterly" and market_data.index.freq == "M":
                    df = df.resample("M").ffill()
                
                # Merge with correlation data
                for col in df.columns:
                    correlation_data[f"{indicator}_{col}"] = df[col]
            
            # Drop NaN values
            correlation_data = correlation_data.dropna()
            
            # Calculate correlation matrix
            correlation_matrix = correlation_data.corr()
            
            # Display correlation matrix
            st.subheader("Correlation Matrix")
            st.dataframe(correlation_matrix)
            
            # Plot correlation heatmap
            st.subheader("Correlation Heatmap")
            
            fig = px.imshow(
                correlation_matrix,
                text_auto=True,
                aspect="auto",
                color_continuous_scale="RdBu_r",
                title="Correlation Matrix",
                labels=dict(x="Variable", y="Variable", color="Correlation")
            )
            
            st.plotly_chart(fig, use_container_width=True)
            
            # Plot scatter plots for each indicator vs market returns
            st.subheader("Scatter Plots")
            
            for indicator, df in indicators_data.items():
                for col in df.columns:
                    indicator_col = f"{indicator}_{col}"
                    if indicator_col in correlation_data.columns:
                        # Create scatter plot
                        fig = px.scatter(
                            correlation_data,
                            x=indicator_col,
                            y="market_return",
                            trendline="ols",
                            title=f"{indicator} vs {market_index} Returns",
                            labels={
                                indicator_col: indicator,
                                "market_return": f"{market_index} Returns"
                            }
                        )
                        
                        st.plotly_chart(fig, use_container_width=True)
                        
                        # Calculate and display correlation coefficient
                        corr = correlation_data[[indicator_col, "market_return"]].corr().iloc[0, 1]
                        st.write(f"Correlation coefficient: {corr:.2f}")
                        
                        # Add interpretation
                        if abs(corr) < 0.3:
                            st.write("Weak correlation")
                        elif abs(corr) < 0.7:
                            st.write("Moderate correlation")
                        else:
                            st.write("Strong correlation")
                        
                        st.write("---")
            
        except Exception as e:
            st.error(f"Error analyzing correlations: {str(e)}")
            logger.error(f"Error analyzing correlations: {str(e)}")
