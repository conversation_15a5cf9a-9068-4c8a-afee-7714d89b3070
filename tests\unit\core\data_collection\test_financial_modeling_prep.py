"""
Unit tests for Financial Modeling Prep data collector.
"""
import os
from datetime import date, timedelta
from unittest.mock import AsyncMock, MagicMock, patch

import pandas as pd
import pytest

from app.core.data_collection.financial_modeling_prep import FinancialModelingPrepCollector


@pytest.fixture
def fmp_collector():
    """Create a Financial Modeling Prep collector for testing."""
    with patch.dict(os.environ, {"FINANCIAL_MODELING_PREP_API_KEY": "test_api_key"}):
        collector = FinancialModelingPrepCollector()
        return collector


@pytest.fixture
def mock_daily_response():
    """Create a mock daily response for testing."""
    mock = AsyncMock()
    mock.status = 200
    mock.json = AsyncMock(return_value={
        "symbol": "AAPL",
        "historical": [
            {
                "date": "2023-01-05",
                "open": 126.01,
                "high": 127.33,
                "low": 124.76,
                "close": 125.02,
                "adjClose": 124.56,
                "volume": *********,
                "unadjustedVolume": *********,
                "change": -1.34,
                "changePercent": -1.06,
                "vwap": 125.7,
                "label": "January 05, 23",
                "changeOverTime": -0.0106
            },
            {
                "date": "2023-01-04",
                "open": 130.28,
                "high": 131.03,
                "low": 126.76,
                "close": 126.36,
                "adjClose": 125.89,
                "volume": 97457142,
                "unadjustedVolume": 97457142,
                "change": -3.92,
                "changePercent": -3.01,
                "vwap": 128.05,
                "label": "January 04, 23",
                "changeOverTime": -0.0301
            }
        ]
    })
    return mock


@pytest.fixture
def mock_intraday_response():
    """Create a mock intraday response for testing."""
    mock = AsyncMock()
    mock.status = 200
    mock.json = AsyncMock(return_value=[
        {
            "date": "2023-01-05 16:00:00",
            "open": 126.01,
            "high": 127.33,
            "low": 124.76,
            "close": 125.02,
            "volume": *********
        },
        {
            "date": "2023-01-05 15:00:00",
            "open": 125.50,
            "high": 126.25,
            "low": 125.10,
            "close": 126.01,
            "volume": 8765432
        }
    ])
    return mock


@pytest.fixture
def mock_company_info_response():
    """Create a mock company info response for testing."""
    mock = AsyncMock()
    mock.status = 200
    mock.json = AsyncMock(return_value=[
        {
            "symbol": "AAPL",
            "companyName": "Apple Inc",
            "exchange": "NASDAQ",
            "industry": "Consumer Electronics",
            "website": "https://www.apple.com",
            "description": "Apple Inc. designs, manufactures, and markets smartphones, personal computers, tablets, wearables, and accessories worldwide.",
            "ceo": "Tim Cook",
            "sector": "Technology",
            "country": "US",
            "fullTimeEmployees": 154000,
            "phone": "14089961010",
            "address": "One Apple Park Way",
            "city": "Cupertino",
            "state": "CA",
            "zip": "95014",
            "dcfDiff": 1.3157,
            "dcf": 148.19,
            "image": "https://financialmodelingprep.com/image-stock/AAPL.png",
            "ipoDate": "1980-12-12",
            "defaultImage": False,
            "isEtf": False,
            "isActivelyTrading": True,
            "isAdr": False,
            "isFund": False,
            "mktCap": 2100000000000,
            "price": 125.02,
            "beta": 1.2,
            "lastDiv": 0.92,
            "range": "124.17-150.25",
            "changes": -1.34,
            "volAvg": 90000000,
            "currency": "USD"
        }
    ])
    return mock


@pytest.fixture
def mock_search_response():
    """Create a mock search response for testing."""
    mock = AsyncMock()
    mock.status = 200
    mock.json = AsyncMock(return_value=[
        {
            "symbol": "AAPL",
            "name": "Apple Inc",
            "currency": "USD",
            "stockExchange": "NASDAQ",
            "exchangeShortName": "NASDAQ"
        },
        {
            "symbol": "AAPL.LON",
            "name": "Apple Inc",
            "currency": "GBP",
            "stockExchange": "London Stock Exchange",
            "exchangeShortName": "LSE"
        }
    ])
    return mock


@pytest.mark.asyncio
async def test_get_stock_data_daily(fmp_collector, mock_daily_response):
    """Test getting daily stock data from Financial Modeling Prep."""
    # Mock the aiohttp ClientSession
    with patch("aiohttp.ClientSession") as mock_session:
        # Set up the mock
        mock_context = MagicMock()
        mock_context.__aenter__.return_value = mock_daily_response
        mock_session.return_value.get.return_value = mock_context
        
        # Call the method
        start_date = date(2023, 1, 1)
        end_date = date(2023, 1, 5)
        df = await fmp_collector.get_stock_data("AAPL", start_date, end_date)
        
        # Check the result
        assert not df.empty
        assert len(df) == 2
        assert "open" in df.columns
        assert "high" in df.columns
        assert "low" in df.columns
        assert "close" in df.columns
        assert "volume" in df.columns
        assert df["close"].iloc[0] == 126.36
        assert df["close"].iloc[1] == 125.02


@pytest.mark.asyncio
async def test_get_stock_data_intraday(fmp_collector, mock_intraday_response):
    """Test getting intraday stock data from Financial Modeling Prep."""
    # Mock the aiohttp ClientSession
    with patch("aiohttp.ClientSession") as mock_session:
        # Set up the mock
        mock_context = MagicMock()
        mock_context.__aenter__.return_value = mock_intraday_response
        mock_session.return_value.get.return_value = mock_context
        
        # Call the method
        start_date = date(2023, 1, 5)
        end_date = date(2023, 1, 5)
        df = await fmp_collector.get_stock_data("AAPL", start_date, end_date, interval="1h")
        
        # Check the result
        assert not df.empty
        assert len(df) == 2
        assert "open" in df.columns
        assert "high" in df.columns
        assert "low" in df.columns
        assert "close" in df.columns
        assert "volume" in df.columns
        assert df["close"].iloc[0] == 126.01
        assert df["close"].iloc[1] == 125.02


@pytest.mark.asyncio
async def test_get_company_info(fmp_collector, mock_company_info_response):
    """Test getting company info from Financial Modeling Prep."""
    # Mock the aiohttp ClientSession
    with patch("aiohttp.ClientSession") as mock_session:
        # Set up the mock
        mock_context = MagicMock()
        mock_context.__aenter__.return_value = mock_company_info_response
        mock_session.return_value.get.return_value = mock_context
        
        # Call the method
        info = await fmp_collector.get_company_info("AAPL")
        
        # Check the result
        assert info["symbol"] == "AAPL"
        assert info["name"] == "Apple Inc"
        assert info["sector"] == "Technology"
        assert info["marketCap"] == 2100000000000
        assert info["beta"] == 1.2
        assert info["dividendYield"] == 92.0  # Converted to percentage
        assert info["website"] == "https://www.apple.com"
        assert info["ceo"] == "Tim Cook"
        assert info["employees"] == 154000
        assert info["ipoDate"] == "1980-12-12"


@pytest.mark.asyncio
async def test_search_symbols(fmp_collector, mock_search_response):
    """Test searching symbols from Financial Modeling Prep."""
    # Mock the aiohttp ClientSession
    with patch("aiohttp.ClientSession") as mock_session:
        # Set up the mock
        mock_context = MagicMock()
        mock_context.__aenter__.return_value = mock_search_response
        mock_session.return_value.get.return_value = mock_context
        
        # Call the method
        results = await fmp_collector.search_symbols("Apple")
        
        # Check the result
        assert len(results) == 2
        assert results[0]["symbol"] == "AAPL"
        assert results[0]["name"] == "Apple Inc"
        assert results[0]["type"] == "NASDAQ"
        assert results[0]["exchange"] == "NASDAQ"
        assert results[0]["currency"] == "USD"
        assert results[1]["symbol"] == "AAPL.LON"
        assert results[1]["currency"] == "GBP"
