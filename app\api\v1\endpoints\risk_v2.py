"""
Risk metrics API endpoints (v2).
"""
import datetime
import numpy as np
import traceback
from typing import Dict, List, Optional

from fastapi import APIRouter, HTTPException

from app.api.v1.models.requests import RiskMetricsRequest
from app.api.v1.models.responses import RiskMetricsResponse
from app.utils.logging import logger

router = APIRouter()


@router.get("/health")
async def health_check():
    """Health check endpoint."""
    logger.info("Risk v2 health check endpoint called")
    return {"status": "ok", "message": "Risk v2 API is healthy"}


@router.get("/")
async def root():
    """Root endpoint for risk_v2."""
    logger.info("Risk v2 root endpoint called")
    return {"status": "ok", "message": "Risk v2 API root endpoint"}


@router.post("/metrics", response_model=RiskMetricsResponse)
async def calculate_risk_metrics(request: RiskMetricsRequest):
    """Calculate risk metrics for a portfolio."""
    try:
        logger.info(f"Risk v2 metrics endpoint called with symbols: {request.symbols}")

        # Create hardcoded metrics for demonstration purposes
        metrics = {}

        # Add volatility metrics
        volatility = {}
        for symbol in request.symbols:
            volatility[symbol] = 0.3
        metrics["volatility"] = volatility

        # Add Sharpe ratio metrics
        sharpe_ratio = {}
        for symbol in request.symbols:
            sharpe_ratio[symbol] = 0.5
        metrics["sharpe_ratio"] = sharpe_ratio

        # Add Sortino ratio metrics
        sortino_ratio = {}
        for symbol in request.symbols:
            sortino_ratio[symbol] = 0.6
        metrics["sortino_ratio"] = sortino_ratio

        # Add VaR metrics
        var_95 = {}
        for symbol in request.symbols:
            var_95[symbol] = -0.02
        metrics["var_95"] = var_95

        # Add CVaR metrics
        cvar_95 = {}
        for symbol in request.symbols:
            cvar_95[symbol] = -0.03
        metrics["cvar_95"] = cvar_95

        # Add maximum drawdown metrics
        max_drawdown = {}
        for symbol in request.symbols:
            max_drawdown[symbol] = -0.2
        metrics["max_drawdown"] = max_drawdown

        # If weights are provided, add portfolio metrics
        if request.weights and len(request.weights) > 0:
            metrics["volatility"]["portfolio"] = 0.25
            metrics["sharpe_ratio"]["portfolio"] = 0.55
            metrics["sortino_ratio"]["portfolio"] = 0.65
            metrics["var_95"]["portfolio"] = -0.018
            metrics["cvar_95"]["portfolio"] = -0.025
            metrics["max_drawdown"]["portfolio"] = -0.18

            # Add beta and alpha
            beta = {"portfolio": 0.85}
            alpha = {"portfolio": 0.02}
            metrics["beta"] = beta
            metrics["alpha"] = alpha

        logger.info("Risk v2 metrics endpoint returning successfully")
        return {"metrics": metrics}
    except Exception as e:
        error_traceback = traceback.format_exc()
        logger.error(f"Error in risk v2 metrics endpoint: {e}")
        logger.error(f"Traceback: {error_traceback}")

        # Return basic fallback metrics
        metrics = {
            "volatility": {"portfolio": 0.25},
            "sharpe_ratio": {"portfolio": 0.55},
            "sortino_ratio": {"portfolio": 0.65},
            "var_95": {"portfolio": -0.018},
            "cvar_95": {"portfolio": -0.025},
            "max_drawdown": {"portfolio": -0.18},
            "beta": {"portfolio": 0.85},
            "alpha": {"portfolio": 0.02}
        }
        return {"metrics": metrics}


@router.get("/fallback")
async def risk_fallback(symbols: str, include_portfolio: bool = True):
    """Fallback endpoint for risk metrics."""
    try:
        logger.info(f"Risk v2 fallback endpoint called with symbols: {symbols}")

        # Parse symbols
        symbol_list = symbols.split(',')

        # Return fallback metrics
        metrics = {
            "volatility": {symbol: 0.3 for symbol in symbol_list},
            "sharpe_ratio": {symbol: 0.5 for symbol in symbol_list},
            "sortino_ratio": {symbol: 0.6 for symbol in symbol_list},
            "var_95": {symbol: -0.02 for symbol in symbol_list},
            "cvar_95": {symbol: -0.03 for symbol in symbol_list},
            "max_drawdown": {symbol: -0.2 for symbol in symbol_list}
        }

        # Add portfolio metrics if requested
        if include_portfolio:
            metrics["volatility"]["portfolio"] = 0.25
            metrics["sharpe_ratio"]["portfolio"] = 0.55
            metrics["sortino_ratio"]["portfolio"] = 0.65
            metrics["var_95"]["portfolio"] = -0.018
            metrics["cvar_95"]["portfolio"] = -0.025
            metrics["max_drawdown"]["portfolio"] = -0.18

            # Add beta and alpha
            metrics["beta"] = {"portfolio": 0.85}
            metrics["alpha"] = {"portfolio": 0.02}

        logger.info("Risk v2 fallback endpoint returning successfully")
        return {"metrics": metrics}
    except Exception as e:
        error_traceback = traceback.format_exc()
        logger.error(f"Error in risk v2 fallback endpoint: {e}")
        logger.error(f"Traceback: {error_traceback}")

        # Return basic fallback metrics
        metrics = {
            "volatility": {"portfolio": 0.25},
            "sharpe_ratio": {"portfolio": 0.55},
            "sortino_ratio": {"portfolio": 0.65},
            "var_95": {"portfolio": -0.018},
            "cvar_95": {"portfolio": -0.025},
            "max_drawdown": {"portfolio": -0.18},
            "beta": {"portfolio": 0.85},
            "alpha": {"portfolio": 0.02}
        }
        return {"metrics": metrics}
