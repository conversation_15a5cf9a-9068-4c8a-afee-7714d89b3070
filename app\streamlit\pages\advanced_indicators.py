"""
Advanced technical indicators page for Streamlit app.
"""
import streamlit as st
import pandas as pd
import numpy as np
from datetime import date, datetime, timedelta
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import asyncio

from app.core.data_collection.data_manager import data_manager
from app.core.technical_analysis.ichimoku import calculate_ichimoku, get_ichimoku_signals, get_ichimoku_support_resistance
from app.core.technical_analysis.fibonacci import identify_fibonacci_levels
from app.core.technical_analysis.elliott_wave import identify_elliott_wave_pattern, get_elliott_wave_projection
from app.utils.logging import logger


async def show_advanced_indicators_page():
    """Show the advanced technical indicators page."""
    st.title("Advanced Technical Indicators")

    # Create tabs for different indicators
    tabs = st.tabs([
        "Ichimoku Cloud",
        "Fibonacci",
        "Elliott Wave",
        "Combined Analysis"
    ])

    # Ichimoku Cloud tab
    with tabs[0]:
        await show_ichimoku_cloud()

    # Fibonacci tab
    with tabs[1]:
        await show_fibonacci()

    # Elliott Wave tab
    with tabs[2]:
        await show_elliott_wave()

    # Combined Analysis tab
    with tabs[3]:
        await show_combined_analysis()




            # Add a horizontal line at y=0 for the histogram
            fig.add_trace(
                go.Scatter(
                    x=[macd_df.index[0], macd_df.index[-1]],
                    y=[0, 0],
                    mode='lines',
                    line=dict(color='black', width=0.5, dash='dash'),
                    showlegend=False
                ),
                row=2,
                col=1
            )

            # Update layout
            fig.update_layout(
                title=f"MACD for {symbol}",
                xaxis_title="Date",
                yaxis_title="Price",
                xaxis2_title="Date",
                yaxis2_title="MACD",
                height=800,
                legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.02,
                    xanchor="right",
                    x=1
                )
            )

            # Display chart
            st.plotly_chart(fig, use_container_width=True)

            # Display signals
            st.subheader("MACD Signals")

            # Get the latest signal
            latest_signal = signals_df.iloc[-1]['macd_signal'] if not signals_df.empty else 0
            latest_trend = signals_df.iloc[-1]['macd_trend'] if not signals_df.empty else 'neutral'

            # Create columns for signal display
            col1, col2 = st.columns(2)

            with col1:
                # Display trend
                trend_color = "green" if latest_trend == "bullish" else "red" if latest_trend == "bearish" else "gray"
                st.markdown(
                    f"<h3 style='text-align: center; color: {trend_color};'>Trend: {latest_trend.capitalize()}</h3>",
                    unsafe_allow_html=True
                )

            with col2:
                # Display signal
                signal_text = "Buy" if latest_signal > 0 else "Sell" if latest_signal < 0 else "Neutral"
                signal_color = "green" if latest_signal > 0 else "red" if latest_signal < 0 else "gray"
                signal_strength = abs(latest_signal)

                st.markdown(
                    f"<h3 style='text-align: center; color: {signal_color};'>Signal: {signal_text} ({signal_strength:.1f})</h3>",
                    unsafe_allow_html=True
                )

            # Display MACD strength
            st.subheader("MACD Strength Indicators")

            # Create columns for strength indicators
            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric(
                    label="MACD Value",
                    value=f"{macd_strength['macd_value']:.4f}"
                )

                st.metric(
                    label="Signal Value",
                    value=f"{macd_strength['signal_value']:.4f}"
                )

            with col2:
                st.metric(
                    label="Histogram Value",
                    value=f"{macd_strength['histogram_value']:.4f}"
                )

                st.metric(
                    label="Trend Strength",
                    value=f"{macd_strength['trend_strength']:.4f}"
                )

            with col3:
                st.metric(
                    label="MACD Momentum",
                    value=f"{macd_strength['macd_momentum']:.4f}",
                    delta=f"{macd_strength['macd_momentum']:.4f}"
                )

                st.metric(
                    label="Histogram Momentum",
                    value=f"{macd_strength['histogram_momentum']:.4f}",
                    delta=f"{macd_strength['histogram_momentum']:.4f}"
                )

            # Display overbought/oversold status
            if macd_strength['is_overbought']:
                st.warning("MACD is potentially overbought")

            if macd_strength['is_oversold']:
                st.warning("MACD is potentially oversold")

            # Display interpretation
            st.subheader("MACD Interpretation")

            # Generate interpretation text
            interpretation = ""

            # MACD line position relative to zero
            if macd_strength['macd_value'] > 0:
                interpretation += "- MACD line is **above zero**, indicating a **bullish trend**.\n"
            else:
                interpretation += "- MACD line is **below zero**, indicating a **bearish trend**.\n"

            # MACD line position relative to signal line
            if macd_strength['macd_value'] > macd_strength['signal_value']:
                interpretation += "- MACD line is **above** the signal line, indicating **bullish momentum**.\n"
            else:
                interpretation += "- MACD line is **below** the signal line, indicating **bearish momentum**.\n"

            # Histogram direction
            if macd_strength['histogram_momentum'] > 0:
                interpretation += "- Histogram is **increasing**, suggesting **strengthening momentum**.\n"
            else:
                interpretation += "- Histogram is **decreasing**, suggesting **weakening momentum**.\n"

            # Overbought/oversold
            if macd_strength['is_overbought']:
                interpretation += "- MACD is potentially **overbought**, suggesting a possible **reversal or pullback**.\n"

            if macd_strength['is_oversold']:
                interpretation += "- MACD is potentially **oversold**, suggesting a possible **reversal or bounce**.\n"

            # Overall assessment
            interpretation += f"\n**Overall Assessment:** {macd_strength['trend']}\n"

            if macd_strength['trend'] == "Strong Bullish":
                interpretation += "Consider buying or holding positions."
            elif macd_strength['trend'] == "Bullish":
                interpretation += "Watch for confirmation signals before entering new positions."
            elif macd_strength['trend'] == "Strong Bearish":
                interpretation += "Consider selling or reducing positions."
            elif macd_strength['trend'] == "Bearish":
                interpretation += "Watch for confirmation signals before entering new positions."
            else:
                interpretation += "The market is showing mixed signals, consider waiting for clearer direction."

            st.markdown(interpretation)

        except Exception as e:
            st.error(f"Error calculating MACD: {str(e)}")
            logger.error(f"Error calculating MACD for {symbol}: {str(e)}")


async def show_elliott_wave():
    """Show Elliott Wave pattern analysis."""
    st.header("Elliott Wave Analysis")

    # Introduction
    st.markdown("""
    Elliott Wave Theory is a technical analysis approach that suggests that market prices unfold in specific patterns called waves.

    The theory identifies two types of waves:
    - **Impulse waves**: Five-wave structures that move in the direction of the main trend
    - **Corrective waves**: Three-wave structures that move against the trend

    This analysis helps identify the current market position within these wave patterns and project potential future price movements.
    """)

    # Settings section
    st.subheader("Elliott Wave Analysis Settings")

    # Create columns for settings
    col1, col2 = st.columns(2)

    with col1:
        # Get symbol
        symbol = st.text_input(
            "Enter stock symbol",
            value="AAPL",
            key="ew_symbol"
        ).upper()

        # Get interval
        interval = st.selectbox(
            "Interval",
            options=["1d", "1wk", "1mo"],
            index=0,
            key="ew_interval"
        )

    with col2:
        # Get date range
        start_date = st.date_input(
            "Start Date",
            value=date.today() - timedelta(days=365*2),  # 2 years of data for better pattern recognition
            key="ew_start_date"
        )

        end_date = st.date_input(
            "End Date",
            value=date.today(),
            key="ew_end_date"
        )

    # Create expander for advanced parameters
    with st.expander("Advanced Parameters", expanded=False):
        # Get window size for swing points
        window = st.slider(
            "Window Size for Swing Points",
            min_value=3,
            max_value=10,
            value=5,
            key="ew_window"
        )

    # Get historical data and identify Elliott Wave patterns
    with st.spinner(f"Analyzing Elliott Wave patterns for {symbol}..."):
        try:
            # Get historical data
            df = await data_manager.get_stock_data(symbol, start_date, end_date, interval)

            if df.empty:
                st.error(f"No historical data found for {symbol}")
                return

            # Ensure column names are standardized
            if 'open' in df.columns: df.rename(columns={'open': 'Open'}, inplace=True)
            if 'high' in df.columns: df.rename(columns={'high': 'High'}, inplace=True)
            if 'low' in df.columns: df.rename(columns={'low': 'Low'}, inplace=True)
            if 'close' in df.columns: df.rename(columns={'close': 'Close'}, inplace=True)
            if 'volume' in df.columns: df.rename(columns={'volume': 'Volume'}, inplace=True)

            # Get Elliott Wave projection
            elliott_wave_projection = get_elliott_wave_projection(df, window=window)

            # Display price chart with Elliott Wave patterns
            st.subheader(f"Price Chart with Elliott Wave Patterns for {symbol}")

            # Create figure
            fig = go.Figure()

            # Add price candlesticks
            fig.add_trace(
                go.Candlestick(
                    x=df.index,
                    open=df['Open'],
                    high=df['High'],
                    low=df['Low'],
                    close=df['Close'],
                    name="Price"
                )
            )

            # Add wave annotations if available
            if 'waves' in elliott_wave_projection and elliott_wave_projection['waves']:
                waves = elliott_wave_projection['waves']

                # Add wave markers
                for wave in waves:
                    if 'date' in wave and 'price' in wave and 'label' in wave:
                        fig.add_annotation(
                            x=wave['date'],
                            y=wave['price'],
                            text=wave['label'],
                            showarrow=True,
                            arrowhead=1,
                            arrowsize=1,
                            arrowwidth=2,
                            arrowcolor="#636363",
                            font=dict(size=12, color="#ffffff"),
                            bgcolor=wave.get('color', "#ff7f0e"),
                            bordercolor="#c7c7c7",
                            borderwidth=1,
                            borderpad=4,
                            opacity=0.8
                        )

            # Update layout
            fig.update_layout(
                title=f"Elliott Wave Analysis for {symbol}",
                xaxis_title="Date",
                yaxis_title="Price",
                height=600,
                legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.02,
                    xanchor="right",
                    x=1
                )
            )

            # Display chart
            st.plotly_chart(fig, use_container_width=True)

            # Display Elliott Wave analysis
            st.subheader("Elliott Wave Pattern Analysis")

            # Create columns for pattern details
            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric(
                    label="Pattern Type",
                    value=elliott_wave_projection.get('pattern', 'Unknown')
                )

            with col2:
                st.metric(
                    label="Current Wave",
                    value=elliott_wave_projection.get('current_wave', 'Unknown')
                )

            with col3:
                confidence = elliott_wave_projection.get('confidence', 0)
                st.metric(
                    label="Pattern Confidence",
                    value=f"{confidence:.0%}" if isinstance(confidence, (int, float)) else confidence
                )

            # Display price projection
            st.subheader("Price Projection")

            # Create columns for current price and target
            col1, col2 = st.columns(2)

            with col1:
                current_price = elliott_wave_projection.get('current_price', df.iloc[-1]['Close'])
                st.metric(
                    label="Current Price",
                    value=f"${current_price:.2f}"
                )

            with col2:
                target_price = elliott_wave_projection.get('target_price')
                if target_price:
                    price_change = target_price - current_price
                    percentage_change = (price_change / current_price) * 100

                    st.metric(
                        label="Target Price",
                        value=f"${target_price:.2f}",
                        delta=f"{percentage_change:.2f}%"
                    )
                else:
                    st.metric(
                        label="Target Price",
                        value="N/A"
                    )

            # Display analysis text
            st.subheader("Analysis")

            analysis_text = elliott_wave_projection.get('analysis', 'No analysis available')
            st.markdown(analysis_text)

            # Display trading strategy
            st.subheader("Trading Strategy")

            # Determine strategy based on pattern and current wave
            pattern = elliott_wave_projection.get('pattern', '').lower()
            current_wave = elliott_wave_projection.get('current_wave', '').lower()

            strategy_text = ""

            if 'impulse' in pattern:
                if 'wave_1' in current_wave or 'wave_2' in current_wave:
                    strategy_text += """
                    **Current Position**: Early in impulse pattern (Wave 1-2)

                    **Strategy**:
                    - Consider entering long positions if Wave 2 retracement is complete
                    - Look for confirmation with other technical indicators
                    - Set stop loss below the start of Wave 1
                    - Target: Potential completion of Wave 3 (typically the strongest wave)
                    """
                elif 'wave_3' in current_wave:
                    strategy_text += """
                    **Current Position**: Middle of impulse pattern (Wave 3)

                    **Strategy**:
                    - Wave 3 is typically the strongest and longest wave
                    - Consider adding to long positions if early in Wave 3
                    - Avoid entering new positions if late in Wave 3
                    - Adjust stop loss to below Wave 2 low
                    - Target: Completion of Wave 3
                    """
                elif 'wave_4' in current_wave:
                    strategy_text += """
                    **Current Position**: Corrective phase in impulse pattern (Wave 4)

                    **Strategy**:
                    - Wave 4 is typically a sideways correction
                    - Consider adding to long positions near the end of Wave 4
                    - Ensure Wave 4 doesn't retrace below the top of Wave 1
                    - Adjust stop loss to below Wave 4 low
                    - Target: Completion of Wave 5
                    """
                elif 'wave_5' in current_wave:
                    strategy_text += """
                    **Current Position**: Final phase of impulse pattern (Wave 5)

                    **Strategy**:
                    - Wave 5 completes the impulse pattern
                    - Consider taking profits as Wave 5 develops
                    - Watch for signs of exhaustion (divergence in indicators)
                    - Prepare for a potential reversal after Wave 5 completes
                    - Consider trailing stop loss to protect profits
                    """
                else:
                    strategy_text += """
                    **Current Position**: Impulse pattern identified but specific wave unclear

                    **Strategy**:
                    - Monitor price action for clearer wave identification
                    - Use other technical indicators for confirmation
                    - Wait for better entry opportunity with clearer wave structure
                    """
            elif 'correction' in pattern or 'corrective' in pattern:
                if 'wave_a' in current_wave:
                    strategy_text += """
                    **Current Position**: Initial corrective move (Wave A)

                    **Strategy**:
                    - Wave A is the first leg down in a correction
                    - Consider short positions if in a larger downtrend
                    - Avoid catching the falling knife if Wave A is still developing
                    - Target: Completion of Wave A and potential Wave B bounce
                    """
                elif 'wave_b' in current_wave:
                    strategy_text += """
                    **Current Position**: Counter-trend move in correction (Wave B)

                    **Strategy**:
                    - Wave B is typically a counter-trend move (upward in a downtrend)
                    - Be cautious of false breakouts during Wave B
                    - Consider preparing for short positions near the end of Wave B
                    - Target: Completion of Wave B and start of Wave C
                    """
                elif 'wave_c' in current_wave:
                    strategy_text += """
                    **Current Position**: Final corrective move (Wave C)

                    **Strategy**:
                    - Wave C typically completes the correction
                    - Consider taking profits on short positions as Wave C develops
                    - Watch for signs of exhaustion for potential trend reversal
                    - Prepare for a new impulse wave in the opposite direction
                    """
                else:
                    strategy_text += """
                    **Current Position**: Corrective pattern identified but specific wave unclear

                    **Strategy**:
                    - Monitor price action for clearer wave identification
                    - Use other technical indicators for confirmation
                    - Wait for better entry opportunity with clearer wave structure
                    """
            else:
                strategy_text += """
                **Current Position**: No clear Elliott Wave pattern identified

                **Strategy**:
                - Wait for clearer pattern development
                - Use other technical analysis methods for trading decisions
                - Monitor price action for potential wave formations
                """

            st.markdown(strategy_text)

            # Risk management advice
            st.subheader("Risk Management")

            st.markdown("""
            **Always follow these risk management principles**:

            1. **Position Sizing**: Limit each trade to 1-2% of your total portfolio
            2. **Stop Loss**: Always use stop loss orders to protect your capital
            3. **Confirmation**: Use multiple technical indicators to confirm Elliott Wave signals
            4. **Patience**: Wait for clear wave patterns rather than forcing trades
            5. **Adaptability**: Be prepared to revise your wave count as new price data emerges
            """)

        except Exception as e:
            st.error(f"Error analyzing Elliott Wave patterns: {str(e)}")
            logger.error(f"Error analyzing Elliott Wave patterns for {symbol}: {str(e)}")


async def show_fibonacci():
    """Show Fibonacci retracement and extension analysis."""
    st.header("Fibonacci Retracement and Extension")

    # Introduction
    st.markdown("""
    Fibonacci retracement and extension levels are horizontal lines that indicate potential support and resistance levels where price could potentially reverse.

    These levels are derived from the Fibonacci sequence and are used to identify:
    - **Retracement levels**: Potential reversal points during a pullback
    - **Extension levels**: Potential price targets for the next move

    Common Fibonacci retracement levels are 23.6%, 38.2%, 50%, 61.8%, and 78.6%.
    Common Fibonacci extension levels are 61.8%, 100%, 161.8%, 261.8%, and 423.6%.
    """)

    # Settings section
    st.subheader("Fibonacci Analysis Settings")

    # Create columns for settings
    col1, col2 = st.columns(2)

    with col1:
        # Get symbol
        symbol = st.text_input(
            "Enter stock symbol",
            value="AAPL",
            key="fib_symbol"
        ).upper()

        # Get interval
        interval = st.selectbox(
            "Interval",
            options=["1d", "1wk", "1mo"],
            index=0,
            key="fib_interval"
        )

    with col2:
        # Get date range
        start_date = st.date_input(
            "Start Date",
            value=date.today() - timedelta(days=365),
            key="fib_start_date"
        )

        end_date = st.date_input(
            "End Date",
            value=date.today(),
            key="fib_end_date"
        )

    # Create expander for advanced parameters
    with st.expander("Advanced Parameters", expanded=False):
        # Get window size for swing points
        window = st.slider(
            "Window Size for Swing Points",
            min_value=3,
            max_value=10,
            value=5,
            key="fib_window"
        )

    # Get historical data and identify Fibonacci levels
    with st.spinner(f"Analyzing Fibonacci levels for {symbol}..."):
        try:
            # Get historical data
            df = await data_manager.get_stock_data(symbol, start_date, end_date, interval)

            if df.empty:
                st.error(f"No historical data found for {symbol}")
                return

            # Identify Fibonacci levels
            fibonacci_levels = identify_fibonacci_levels(df, window=window)

            # Display price chart with Fibonacci levels
            st.subheader(f"Price Chart with Fibonacci Levels for {symbol}")

            # Create figure
            fig = go.Figure()

            # Add price candlesticks
            fig.add_trace(
                go.Candlestick(
                    x=df.index,
                    open=df['Open'],
                    high=df['High'],
                    low=df['Low'],
                    close=df['Close'],
                    name="Price"
                )
            )

            # Add Fibonacci retracement levels
            if 'retracement' in fibonacci_levels and 'levels' in fibonacci_levels['retracement']:
                retracement_levels = fibonacci_levels['retracement']['levels']
                level_names = fibonacci_levels['retracement'].get('level_names', {})

                for level_key, level_price in retracement_levels.items():
                    level_name = level_names.get(level_key, level_key)

                    # Determine color based on level
                    if '0.0' in level_key:
                        color = 'green'
                    elif '0.236' in level_key:
                        color = 'blue'
                    elif '0.382' in level_key:
                        color = 'purple'
                    elif '0.5' in level_key:
                        color = 'orange'
                    elif '0.618' in level_key:
                        color = 'red'
                    elif '0.786' in level_key:
                        color = 'brown'
                    elif '1.0' in level_key:
                        color = 'black'
                    else:
                        color = 'gray'

                    # Add horizontal line
                    fig.add_trace(
                        go.Scatter(
                            x=[df.index[0], df.index[-1]],
                            y=[level_price, level_price],
                            mode='lines',
                            name=f"Retracement: {level_name}",
                            line=dict(color=color, width=1, dash='dash')
                        )
                    )

            # Update layout
            fig.update_layout(
                title=f"Fibonacci Analysis for {symbol}",
                xaxis_title="Date",
                yaxis_title="Price",
                height=600,
                legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.02,
                    xanchor="right",
                    x=1
                )
            )

            # Display chart
            st.plotly_chart(fig, use_container_width=True)

            # Display Fibonacci levels
            st.subheader("Fibonacci Levels")

            # Create columns for retracement and extension
            col1, col2 = st.columns(2)

            with col1:
                st.subheader("Retracement Levels")

                if 'retracement' in fibonacci_levels and 'levels' in fibonacci_levels['retracement']:
                    retracement_levels = fibonacci_levels['retracement']['levels']
                    level_names = fibonacci_levels['retracement'].get('level_names', {})

                    # Create a DataFrame for display
                    data = []
                    for level_key, level_price in retracement_levels.items():
                        level_name = level_names.get(level_key, level_key)
                        data.append({
                            "Level": level_name,
                            "Price": f"{level_price:.2f}"
                        })

                    # Convert to DataFrame
                    df_retracement = pd.DataFrame(data)

                    # Display as table
                    st.dataframe(df_retracement)
                else:
                    st.write("No retracement levels identified")

            with col2:
                st.subheader("Extension Levels")

                if 'extension' in fibonacci_levels and 'levels' in fibonacci_levels['extension']:
                    extension_levels = fibonacci_levels['extension']['levels']
                    level_names = fibonacci_levels['extension'].get('level_names', {})

                    # Create a DataFrame for display
                    data = []
                    for level_key, level_price in extension_levels.items():
                        level_name = level_names.get(level_key, level_key)
                        data.append({
                            "Level": level_name,
                            "Price": f"{level_price:.2f}"
                        })

                    # Convert to DataFrame
                    df_extension = pd.DataFrame(data)

                    # Display as table
                    st.dataframe(df_extension)
                else:
                    st.write("No extension levels identified")

            # Display current price in relation to Fibonacci levels
            st.subheader("Current Price Analysis")

            # Get current price
            current_price = fibonacci_levels.get('current_price', df.iloc[-1]['Close'])

            # Display current price
            st.metric(
                label="Current Price",
                value=f"{current_price:.2f}"
            )

            # Display analysis
            if 'analysis' in fibonacci_levels:
                st.markdown(fibonacci_levels['analysis'])

            # Display trend direction
            if 'is_uptrend' in fibonacci_levels:
                is_uptrend = fibonacci_levels['is_uptrend']
                trend_text = "Uptrend" if is_uptrend else "Downtrend"
                trend_color = "green" if is_uptrend else "red"

                st.markdown(
                    f"<h3 style='text-align: center; color: {trend_color};'>Current Trend: {trend_text}</h3>",
                    unsafe_allow_html=True
                )

            # Display trading strategy based on Fibonacci levels
            st.subheader("Trading Strategy")

            strategy_text = ""

            if 'is_uptrend' in fibonacci_levels and 'retracement' in fibonacci_levels and 'levels' in fibonacci_levels['retracement']:
                is_uptrend = fibonacci_levels['is_uptrend']
                retracement_levels = fibonacci_levels['retracement']['levels']

                if is_uptrend:
                    # Uptrend strategy
                    strategy_text += "**Uptrend Strategy:**\n\n"
                    strategy_text += "1. **Buy opportunities**: Look for price pullbacks to key Fibonacci retracement levels (38.2%, 50%, 61.8%).\n"
                    strategy_text += "2. **Stop loss**: Place stop loss just below the retracement level you're trading from.\n"
                    strategy_text += "3. **Take profit**: Use Fibonacci extension levels (161.8%, 261.8%) as potential profit targets.\n\n"

                    # Find closest retracement level below current price
                    closest_level = None
                    closest_diff = float('inf')

                    for level_key, level_price in retracement_levels.items():
                        if level_price < current_price:
                            diff = current_price - level_price
                            if diff < closest_diff:
                                closest_diff = diff
                                closest_level = (level_key, level_price)

                    if closest_level:
                        level_key, level_price = closest_level
                        strategy_text += f"**Current situation**: Price is above the {level_key.replace('level_', '')} retracement level ({level_price:.2f}).\n"
                        strategy_text += f"Consider using this level as potential support for stop loss placement.\n"
                else:
                    # Downtrend strategy
                    strategy_text += "**Downtrend Strategy:**\n\n"
                    strategy_text += "1. **Sell opportunities**: Look for price bounces to key Fibonacci retracement levels (38.2%, 50%, 61.8%).\n"
                    strategy_text += "2. **Stop loss**: Place stop loss just above the retracement level you're trading from.\n"
                    strategy_text += "3. **Take profit**: Use Fibonacci extension levels (161.8%, 261.8%) as potential profit targets.\n\n"

                    # Find closest retracement level above current price
                    closest_level = None
                    closest_diff = float('inf')

                    for level_key, level_price in retracement_levels.items():
                        if level_price > current_price:
                            diff = level_price - current_price
                            if diff < closest_diff:
                                closest_diff = diff
                                closest_level = (level_key, level_price)

                    if closest_level:
                        level_key, level_price = closest_level
                        strategy_text += f"**Current situation**: Price is below the {level_key.replace('level_', '')} retracement level ({level_price:.2f}).\n"
                        strategy_text += f"Consider using this level as potential resistance for stop loss placement.\n"

            # Add risk management advice
            strategy_text += "\n**Risk Management:**\n"
            strategy_text += "- Always use proper position sizing (1-2% risk per trade).\n"
            strategy_text += "- Confirm signals with other technical indicators or price action.\n"
            strategy_text += "- Be aware that Fibonacci levels are most effective in trending markets and may not work well in ranging markets.\n"

            st.markdown(strategy_text)

        except Exception as e:
            st.error(f"Error analyzing Fibonacci levels: {str(e)}")
            logger.error(f"Error analyzing Fibonacci levels for {symbol}: {str(e)}")


async def show_combined_analysis():
    """Show combined technical analysis with multiple indicators."""
    st.header("Combined Technical Analysis")

    # Introduction
    st.markdown("""
    This analysis combines multiple advanced technical indicators to provide a comprehensive market view:

    - **Ichimoku Cloud**: For trend direction, support/resistance, and momentum
    - **MACD**: For momentum and potential reversals
    - **Fibonacci Levels**: For key support/resistance areas
    - **Elliott Wave**: For market structure and cycle position

    By combining these indicators, we can develop a more robust trading strategy with higher confidence signals.
    """)

    # Settings section
    st.subheader("Combined Analysis Settings")

    # Create columns for settings
    col1, col2 = st.columns(2)

    with col1:
        # Get symbol
        symbol = st.text_input(
            "Enter stock symbol",
            value="AAPL",
            key="combined_symbol"
        ).upper()

        # Get interval
        interval = st.selectbox(
            "Interval",
            options=["1d", "1wk", "1mo"],
            index=0,
            key="combined_interval"
        )

    with col2:
        # Get date range
        start_date = st.date_input(
            "Start Date",
            value=date.today() - timedelta(days=365*2),  # 2 years of data
            key="combined_start_date"
        )

        end_date = st.date_input(
            "End Date",
            value=date.today(),
            key="combined_end_date"
        )

    # Get historical data and perform combined analysis
    with st.spinner(f"Performing combined analysis for {symbol}..."):
        try:
            # Get historical data
            df = await data_manager.get_stock_data(symbol, start_date, end_date, interval)

            if df.empty:
                st.error(f"No historical data found for {symbol}")
                return

            # Ensure column names are standardized
            if 'open' in df.columns: df.rename(columns={'open': 'Open'}, inplace=True)
            if 'high' in df.columns: df.rename(columns={'high': 'High'}, inplace=True)
            if 'low' in df.columns: df.rename(columns={'low': 'Low'}, inplace=True)
            if 'close' in df.columns: df.rename(columns={'close': 'Close'}, inplace=True)
            if 'volume' in df.columns: df.rename(columns={'volume': 'Volume'}, inplace=True)

            # Calculate Ichimoku Cloud
            ichimoku_df = calculate_ichimoku(df)
            ichimoku_signals = get_ichimoku_signals(ichimoku_df)
            ichimoku_support_resistance = get_ichimoku_support_resistance(ichimoku_df)

            # Calculate MACD
            macd_df = calculate_macd(df)
            macd_signals = get_macd_signals(macd_df)
            macd_strength = get_macd_strength(macd_df)

            # Identify Fibonacci levels
            fibonacci_levels = identify_fibonacci_levels(df)

            # Get Elliott Wave projection
            elliott_wave_projection = get_elliott_wave_projection(df)

            # Create combined visualization
            st.subheader(f"Combined Technical Analysis for {symbol}")

            # Create figure with subplots
            fig = make_subplots(
                rows=4,
                cols=1,
                shared_xaxes=True,
                vertical_spacing=0.05,
                row_heights=[0.5, 0.2, 0.15, 0.15],
                subplot_titles=(
                    "Price with Ichimoku Cloud & Fibonacci Levels",
                    "MACD",
                    "Volume",
                    "RSI"
                )
            )

            # Add price candlesticks
            fig.add_trace(
                go.Candlestick(
                    x=df.index,
                    open=df['Open'],
                    high=df['High'],
                    low=df['Low'],
                    close=df['Close'],
                    name="Price"
                ),
                row=1, col=1
            )

            # Add Ichimoku Cloud components
            fig.add_trace(
                go.Scatter(
                    x=ichimoku_df.index,
                    y=ichimoku_df['tenkan_sen'],
                    name="Tenkan-sen (Conversion Line)",
                    line=dict(color='blue', width=1)
                ),
                row=1, col=1
            )

            fig.add_trace(
                go.Scatter(
                    x=ichimoku_df.index,
                    y=ichimoku_df['kijun_sen'],
                    name="Kijun-sen (Base Line)",
                    line=dict(color='red', width=1)
                ),
                row=1, col=1
            )

            # Add Ichimoku Cloud
            fig.add_trace(
                go.Scatter(
                    x=ichimoku_df.index,
                    y=ichimoku_df['senkou_span_a'],
                    name="Senkou Span A",
                    line=dict(color='green', width=0.5)
                ),
                row=1, col=1
            )

            fig.add_trace(
                go.Scatter(
                    x=ichimoku_df.index,
                    y=ichimoku_df['senkou_span_b'],
                    name="Senkou Span B",
                    line=dict(color='red', width=0.5),
                    fill='tonexty',
                    fillcolor='rgba(0, 250, 0, 0.1)'
                ),
                row=1, col=1
            )

            # Add Fibonacci retracement levels if available
            if 'retracement' in fibonacci_levels and 'levels' in fibonacci_levels['retracement']:
                retracement_levels = fibonacci_levels['retracement']['levels']

                for level_key, level_price in retracement_levels.items():
                    # Determine color based on level
                    if '0.0' in level_key:
                        color = 'green'
                    elif '0.236' in level_key:
                        color = 'blue'
                    elif '0.382' in level_key:
                        color = 'purple'
                    elif '0.5' in level_key:
                        color = 'orange'
                    elif '0.618' in level_key:
                        color = 'red'
                    elif '0.786' in level_key:
                        color = 'brown'
                    elif '1.0' in level_key:
                        color = 'black'
                    else:
                        color = 'gray'

                    # Add horizontal line
                    fig.add_trace(
                        go.Scatter(
                            x=[df.index[0], df.index[-1]],
                            y=[level_price, level_price],
                            mode='lines',
                            name=f"Fib: {level_key}",
                            line=dict(color=color, width=1, dash='dash')
                        ),
                        row=1, col=1
                    )

            # Add Elliott Wave annotations if available
            if 'waves' in elliott_wave_projection and elliott_wave_projection['waves']:
                waves = elliott_wave_projection['waves']

                for wave in waves:
                    if 'date' in wave and 'price' in wave and 'label' in wave:
                        fig.add_annotation(
                            x=wave['date'],
                            y=wave['price'],
                            text=wave['label'],
                            showarrow=True,
                            arrowhead=1,
                            arrowsize=1,
                            arrowwidth=2,
                            arrowcolor="#636363",
                            font=dict(size=10, color="#ffffff"),
                            bgcolor=wave.get('color', "#ff7f0e"),
                            bordercolor="#c7c7c7",
                            borderwidth=1,
                            borderpad=4,
                            opacity=0.8,
                            row=1, col=1
                        )

            # Add MACD
            fig.add_trace(
                go.Scatter(
                    x=macd_df.index,
                    y=macd_df['macd'],
                    name="MACD",
                    line=dict(color='blue', width=1.5)
                ),
                row=2, col=1
            )

            fig.add_trace(
                go.Scatter(
                    x=macd_df.index,
                    y=macd_df['signal'],
                    name="Signal",
                    line=dict(color='red', width=1)
                ),
                row=2, col=1
            )

            fig.add_trace(
                go.Bar(
                    x=macd_df.index,
                    y=macd_df['histogram'],
                    name="Histogram",
                    marker=dict(
                        color=macd_df['histogram'].apply(
                            lambda x: 'green' if x > 0 else 'red'
                        )
                    )
                ),
                row=2, col=1
            )

            # Add Volume
            fig.add_trace(
                go.Bar(
                    x=df.index,
                    y=df['Volume'],
                    name="Volume",
                    marker=dict(
                        color=df['Close'].diff().apply(
                            lambda x: 'green' if x > 0 else 'red'
                        )
                    )
                ),
                row=3, col=1
            )

            # Calculate and add RSI
            # Simple RSI calculation
            delta = df['Close'].diff()
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)
            avg_gain = gain.rolling(window=14).mean()
            avg_loss = loss.rolling(window=14).mean()
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))

            fig.add_trace(
                go.Scatter(
                    x=df.index,
                    y=rsi,
                    name="RSI",
                    line=dict(color='purple', width=1)
                ),
                row=4, col=1
            )

            # Add RSI reference lines
            fig.add_trace(
                go.Scatter(
                    x=[df.index[0], df.index[-1]],
                    y=[70, 70],
                    name="Overbought",
                    line=dict(color='red', width=1, dash='dash')
                ),
                row=4, col=1
            )

            fig.add_trace(
                go.Scatter(
                    x=[df.index[0], df.index[-1]],
                    y=[30, 30],
                    name="Oversold",
                    line=dict(color='green', width=1, dash='dash')
                ),
                row=4, col=1
            )

            # Update layout
            fig.update_layout(
                title=f"Combined Technical Analysis for {symbol}",
                height=900,
                legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.02,
                    xanchor="right",
                    x=1
                ),
                xaxis4_title="Date",
                yaxis_title="Price",
                yaxis2_title="MACD",
                yaxis3_title="Volume",
                yaxis4_title="RSI"
            )

            # Update y-axis for RSI
            fig.update_yaxes(range=[0, 100], row=4, col=1)

            # Display chart
            st.plotly_chart(fig, use_container_width=True)

            # Display combined signals and analysis
            st.subheader("Combined Signals and Analysis")

            # Get latest signals
            latest_ichimoku_signal = ichimoku_signals.iloc[-1]['signal'] if not ichimoku_signals.empty else "neutral"
            latest_ichimoku_trend = ichimoku_signals.iloc[-1]['trend'] if not ichimoku_signals.empty else "neutral"

            latest_macd_signal = macd_signals.iloc[-1]['signal'] if not macd_signals.empty else "neutral"
            latest_macd_trend = macd_signals.iloc[-1]['trend'] if not macd_signals.empty else "neutral"

            # Determine combined signal
            signals = [latest_ichimoku_signal, latest_macd_signal]
            buy_count = signals.count("buy")
            sell_count = signals.count("sell")

            if buy_count > sell_count:
                combined_signal = "buy"
            elif sell_count > buy_count:
                combined_signal = "sell"
            else:
                combined_signal = "neutral"

            # Determine combined trend
            trends = [latest_ichimoku_trend, latest_macd_trend]
            bullish_count = trends.count("bullish")
            bearish_count = trends.count("bearish")

            if bullish_count > bearish_count:
                combined_trend = "bullish"
            elif bearish_count > bullish_count:
                combined_trend = "bearish"
            else:
                combined_trend = "neutral"

            # Display signal metrics
            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric(
                    label="Combined Signal",
                    value=combined_signal.capitalize(),
                    delta="Bullish" if combined_signal == "buy" else ("Bearish" if combined_signal == "sell" else "Neutral")
                )

            with col2:
                st.metric(
                    label="Combined Trend",
                    value=combined_trend.capitalize()
                )

            with col3:
                current_price = df.iloc[-1]['Close']
                st.metric(
                    label="Current Price",
                    value=f"${current_price:.2f}"
                )

            # Display individual indicator signals
            st.subheader("Individual Indicator Signals")

            col1, col2 = st.columns(2)

            with col1:
                st.markdown("**Ichimoku Cloud**")
                st.markdown(f"- Signal: {latest_ichimoku_signal.capitalize()}")
                st.markdown(f"- Trend: {latest_ichimoku_trend.capitalize()}")

                # Support and resistance
                if ichimoku_support_resistance:
                    st.markdown("- Support levels:")
                    for level in ichimoku_support_resistance.get('support', []):
                        st.markdown(f"  - ${level:.2f}")

                    st.markdown("- Resistance levels:")
                    for level in ichimoku_support_resistance.get('resistance', []):
                        st.markdown(f"  - ${level:.2f}")

            with col2:
                st.markdown("**MACD**")
                st.markdown(f"- Signal: {latest_macd_signal.capitalize()}")
                st.markdown(f"- Trend: {latest_macd_trend.capitalize()}")
                st.markdown(f"- Strength: {macd_strength.get('trend_strength', 0):.2f}")

                if macd_strength.get('is_overbought'):
                    st.markdown("- **Potentially overbought**")
                if macd_strength.get('is_oversold'):
                    st.markdown("- **Potentially oversold**")

            # Display Fibonacci and Elliott Wave analysis
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("**Fibonacci Analysis**")

                fib_analysis = fibonacci_levels.get('analysis', 'No analysis available')
                st.markdown(fib_analysis)

                # Display key levels
                if 'retracement' in fibonacci_levels and 'levels' in fibonacci_levels['retracement']:
                    st.markdown("Key retracement levels:")
                    levels_df = pd.DataFrame({
                        "Level": fibonacci_levels['retracement']['levels'].keys(),
                        "Price": fibonacci_levels['retracement']['levels'].values()
                    })
                    st.dataframe(levels_df.style.format({"Price": "${:.2f}"}))

            with col2:
                st.markdown("**Elliott Wave Analysis**")

                pattern = elliott_wave_projection.get('pattern', 'Unknown')
                current_wave = elliott_wave_projection.get('current_wave', 'Unknown')

                st.markdown(f"- Pattern: {pattern}")
                st.markdown(f"- Current Wave: {current_wave}")

                ew_analysis = elliott_wave_projection.get('analysis', 'No analysis available')
                st.markdown(ew_analysis)

                # Display target price if available
                target_price = elliott_wave_projection.get('target_price')
                if target_price:
                    price_change = target_price - current_price
                    percentage_change = (price_change / current_price) * 100
                    st.markdown(f"- Target Price: ${target_price:.2f} ({percentage_change:.2f}%)")

            # Combined analysis and trading strategy
            st.subheader("Combined Analysis and Trading Strategy")

            # Create analysis text
            analysis_text = f"""
            ### Market Analysis for {symbol}

            **Current Price**: ${current_price:.2f}

            **Overall Market Trend**: {combined_trend.capitalize()}

            **Current Signal**: {combined_signal.capitalize()}

            **Ichimoku Cloud Analysis**: {latest_ichimoku_signal.capitalize()} signal in a {latest_ichimoku_trend.capitalize()} trend

            **MACD Analysis**: {latest_macd_signal.capitalize()} signal with strength {macd_strength.get('trend_strength', 0):.2f}

            **Fibonacci Analysis**: {fibonacci_levels.get('analysis', 'No analysis available')}

            **Elliott Wave Analysis**: {pattern} pattern, currently in {current_wave}
            """

            st.markdown(analysis_text)

            # Trading strategy based on combined signals
            strategy_text = "### Trading Strategy\n\n"

            if combined_signal == "buy" and combined_trend == "bullish":
                strategy_text += """
                **Strong Buy Signal**

                - Consider entering long positions with proper risk management
                - Set stop loss below recent support or Fibonacci retracement levels
                - Target profit at next resistance or Fibonacci extension levels
                - Consider scaling in rather than taking a full position immediately
                """
            elif combined_signal == "buy" and combined_trend == "neutral":
                strategy_text += """
                **Moderate Buy Signal**

                - Consider small long positions with tight stop losses
                - Wait for confirmation of trend change before adding to position
                - Be cautious as the market lacks a clear trend
                - Consider shorter-term trades rather than long-term holds
                """
            elif combined_signal == "sell" and combined_trend == "bearish":
                strategy_text += """
                **Strong Sell Signal**

                - Consider entering short positions with proper risk management
                - Set stop loss above recent resistance or Fibonacci retracement levels
                - Target profit at next support or Fibonacci extension levels
                - Consider scaling in rather than taking a full position immediately
                """
            elif combined_signal == "sell" and combined_trend == "neutral":
                strategy_text += """
                **Moderate Sell Signal**

                - Consider small short positions with tight stop losses
                - Wait for confirmation of trend change before adding to position
                - Be cautious as the market lacks a clear trend
                - Consider shorter-term trades rather than long-term holds
                """
            else:
                strategy_text += """
                **Neutral Signal**

                - Consider staying on the sidelines until clearer signals emerge
                - Focus on other assets with stronger signals
                - If already in a position, consider tightening stop losses
                - Use this time to plan entries for when stronger signals appear
                """

            # Add Elliott Wave specific strategy
            if 'impulse' in pattern.lower():
                if 'wave_3' in current_wave.lower():
                    strategy_text += """

                    **Elliott Wave Strategy (Wave 3)**

                    - Wave 3 is typically the strongest and longest wave
                    - Consider adding to long positions if in a bullish trend
                    - Adjust stop loss to below Wave 2 low
                    - Target: Completion of Wave 3
                    """
                elif 'wave_5' in current_wave.lower():
                    strategy_text += """

                    **Elliott Wave Strategy (Wave 5)**

                    - Wave 5 completes the impulse pattern
                    - Consider taking profits as Wave 5 develops
                    - Watch for signs of exhaustion (divergence in indicators)
                    - Prepare for a potential reversal after Wave 5 completes
                    """

            # Add risk management advice
            strategy_text += """

            ### Risk Management

            1. **Position Sizing**: Limit each trade to 1-2% of your total portfolio
            2. **Stop Loss**: Always use stop loss orders to protect your capital
            3. **Confirmation**: Use multiple technical indicators to confirm signals
            4. **Patience**: Wait for high-probability setups rather than forcing trades
            5. **Adaptability**: Be prepared to exit if the market conditions change
            """

            st.markdown(strategy_text)

        except Exception as e:
            st.error(f"Error performing combined analysis: {str(e)}")
            logger.error(f"Error performing combined analysis for {symbol}: {str(e)}")


async def show_ichimoku_cloud():
    """Show Ichimoku Cloud analysis."""
    st.header("Ichimoku Cloud")

    # Introduction
    st.markdown("""
    The Ichimoku Cloud is a comprehensive indicator that provides information about support/resistance, trend direction, and momentum.

    Components:
    - **Tenkan-sen (Conversion Line)**: Short-term trend indicator
    - **Kijun-sen (Base Line)**: Medium-term trend indicator
    - **Senkou Span A (Leading Span A)**: First component of the cloud
    - **Senkou Span B (Leading Span B)**: Second component of the cloud
    - **Chikou Span (Lagging Span)**: Closing price shifted backward

    The space between Senkou Span A and B is called the "cloud" (Kumo).
    """)

    # Settings section
    st.subheader("Ichimoku Cloud Settings")

    # Create columns for settings
    col1, col2 = st.columns(2)

    with col1:
        # Get symbol
        symbol = st.text_input(
            "Enter stock symbol",
            value="AAPL"
        ).upper()

        # Get interval
        interval = st.selectbox(
            "Interval",
            options=["1d", "1wk", "1mo"],
            index=0
        )

    with col2:
        # Get date range
        start_date = st.date_input(
            "Start Date",
            value=date.today() - timedelta(days=365)
        )

        end_date = st.date_input(
            "End Date",
            value=date.today()
        )

    # Create expander for advanced parameters
    with st.expander("Ichimoku Parameters", expanded=False):
        col1, col2 = st.columns(2)

        with col1:
            tenkan_period = st.slider(
                "Tenkan-sen Period",
                min_value=1,
                max_value=30,
                value=9
            )

            kijun_period = st.slider(
                "Kijun-sen Period",
                min_value=10,
                max_value=60,
                value=26
            )

        with col2:
            senkou_span_b_period = st.slider(
                "Senkou Span B Period",
                min_value=30,
                max_value=120,
                value=52
            )

            displacement = st.slider(
                "Displacement",
                min_value=10,
                max_value=60,
                value=26
            )

    # Get historical data and calculate Ichimoku Cloud
    with st.spinner(f"Calculating Ichimoku Cloud for {symbol}..."):
        try:
            # Get historical data
            df = await data_manager.get_stock_data(symbol, start_date, end_date, interval)

            if df.empty:
                st.error(f"No historical data found for {symbol}")
                return

            # Calculate Ichimoku Cloud
            try:
                # Check if all required columns exist
                required_cols = ['Open', 'High', 'Low', 'Close']
                missing_cols = [col for col in required_cols if col not in df.columns]

                # Check if we need to convert DatetimeIndex to a regular column
                if isinstance(df.index, pd.DatetimeIndex):
                    logger.debug("Converting DatetimeIndex to date column")
                    df = df.reset_index()
                    if 'index' in df.columns:
                        df.rename(columns={'index': 'date'}, inplace=True)

                # Ensure we're working with numeric data by checking column types
                for col in df.columns:
                    if pd.api.types.is_datetime64_any_dtype(df[col]):
                        # If it's a date column, keep it as is
                        logger.debug(f"Column {col} is a datetime type, skipping numeric conversion")
                        continue

                    # Try to convert to numeric, coerce errors to NaN
                    try:
                        df[col] = pd.to_numeric(df[col], errors='coerce')
                    except Exception as e:
                        logger.warning(f"Could not convert column {col} to numeric: {str(e)}")

                if missing_cols:
                    logger.warning(f"Missing columns for Ichimoku Cloud: {missing_cols}")
                    # Add missing columns with default values
                    for col in missing_cols:
                        if col == 'Open':
                            # Find a numeric column to use as a base
                            base_col = None
                            for potential_col in ['Close', 'close']:
                                if potential_col in df.columns and pd.api.types.is_numeric_dtype(df[potential_col]):
                                    base_col = potential_col
                                    break

                            if base_col is None:
                                # Find the first numeric column
                                for potential_col in df.columns:
                                    if pd.api.types.is_numeric_dtype(df[potential_col]):
                                        base_col = potential_col
                                        break

                            if base_col is not None:
                                df['Open'] = df[base_col]
                            else:
                                # If no numeric column found, create a dummy column
                                df['Open'] = 100.0
                                logger.warning("No numeric column found for Open, using dummy values")

                        elif col == 'High':
                            # Find a numeric column to use as a base
                            if 'Open' in df.columns and pd.api.types.is_numeric_dtype(df['Open']):
                                df['High'] = df['Open'] * 1.001
                            elif 'Close' in df.columns and pd.api.types.is_numeric_dtype(df['Close']):
                                df['High'] = df['Close'] * 1.001
                            else:
                                # Find the first numeric column
                                base_col = None
                                for potential_col in df.columns:
                                    if pd.api.types.is_numeric_dtype(df[potential_col]):
                                        base_col = potential_col
                                        break

                                if base_col is not None:
                                    df['High'] = df[base_col] * 1.001
                                else:
                                    # If no numeric column found, create a dummy column
                                    df['High'] = 101.0
                                    logger.warning("No numeric column found for High, using dummy values")

                        elif col == 'Low':
                            # Find a numeric column to use as a base
                            if 'Open' in df.columns and pd.api.types.is_numeric_dtype(df['Open']):
                                df['Low'] = df['Open'] * 0.999
                            elif 'Close' in df.columns and pd.api.types.is_numeric_dtype(df['Close']):
                                df['Low'] = df['Close'] * 0.999
                            else:
                                # Find the first numeric column
                                base_col = None
                                for potential_col in df.columns:
                                    if pd.api.types.is_numeric_dtype(df[potential_col]):
                                        base_col = potential_col
                                        break

                                if base_col is not None:
                                    df['Low'] = df[base_col] * 0.999
                                else:
                                    # If no numeric column found, create a dummy column
                                    df['Low'] = 99.0
                                    logger.warning("No numeric column found for Low, using dummy values")

                        elif col == 'Close':
                            # Find a numeric column to use as a base
                            if 'Open' in df.columns and pd.api.types.is_numeric_dtype(df['Open']):
                                df['Close'] = df['Open']
                            else:
                                # Find the first numeric column
                                base_col = None
                                for potential_col in df.columns:
                                    if pd.api.types.is_numeric_dtype(df[potential_col]):
                                        base_col = potential_col
                                        break

                                if base_col is not None:
                                    df['Close'] = df[base_col]
                                else:
                                    # If no numeric column found, create a dummy column
                                    df['Close'] = 100.0
                                    logger.warning("No numeric column found for Close, using dummy values")

                # Check if we have enough data for Ichimoku Cloud calculation
                min_required_data = max(tenkan_period, kijun_period, senkou_span_b_period) + displacement

                if len(df) < min_required_data:
                    st.warning(f"Not enough data points for reliable Ichimoku Cloud calculation. Need at least {min_required_data} data points, but only have {len(df)}.")
                    logger.warning(f"Not enough data for Ichimoku Cloud: have {len(df)} points, need {min_required_data}")

                # Now calculate Ichimoku Cloud
                ichimoku_df = calculate_ichimoku(
                    df,
                    tenkan_period=tenkan_period,
                    kijun_period=kijun_period,
                    senkou_span_b_period=senkou_span_b_period,
                    displacement=displacement
                )
            except Exception as e:
                logger.error(f"Error preparing data for Ichimoku Cloud: {str(e)}")
                st.error(f"Error preparing data for Ichimoku Cloud: {str(e)}")
                return

            # Generate signals
            signals_df = get_ichimoku_signals(ichimoku_df)

            # Get support and resistance levels
            support_resistance = get_ichimoku_support_resistance(ichimoku_df)

            # Display Ichimoku Cloud chart
            st.subheader(f"Ichimoku Cloud Chart for {symbol}")

            # Create figure
            fig = go.Figure()

            # Add price candlesticks
            fig.add_trace(
                go.Candlestick(
                    x=df.index,
                    open=df['Open'],
                    high=df['High'],
                    low=df['Low'],
                    close=df['Close'],
                    name="Price"
                )
            )

            # Add Tenkan-sen
            fig.add_trace(
                go.Scatter(
                    x=ichimoku_df.index,
                    y=ichimoku_df['tenkan_sen'],
                    name="Tenkan-sen (Conversion Line)",
                    line=dict(color='blue', width=1)
                )
            )

            # Add Kijun-sen
            fig.add_trace(
                go.Scatter(
                    x=ichimoku_df.index,
                    y=ichimoku_df['kijun_sen'],
                    name="Kijun-sen (Base Line)",
                    line=dict(color='red', width=1)
                )
            )

            # Add Senkou Span A
            fig.add_trace(
                go.Scatter(
                    x=ichimoku_df.index,
                    y=ichimoku_df['senkou_span_a'],
                    name="Senkou Span A",
                    line=dict(color='green', width=1)
                )
            )

            # Add Senkou Span B
            fig.add_trace(
                go.Scatter(
                    x=ichimoku_df.index,
                    y=ichimoku_df['senkou_span_b'],
                    name="Senkou Span B",
                    line=dict(color='purple', width=1)
                )
            )

            # Add Chikou Span
            fig.add_trace(
                go.Scatter(
                    x=ichimoku_df.index,
                    y=ichimoku_df['chikou_span'],
                    name="Chikou Span (Lagging Span)",
                    line=dict(color='brown', width=1)
                )
            )

            # Fill the cloud
            # Green cloud when Senkou Span A > Senkou Span B (bullish)
            # Red cloud when Senkou Span A < Senkou Span B (bearish)
            fig.add_trace(
                go.Scatter(
                    x=ichimoku_df.index,
                    y=ichimoku_df['senkou_span_a'],
                    name="Cloud",
                    line=dict(width=0),
                    showlegend=False
                )
            )

            fig.add_trace(
                go.Scatter(
                    x=ichimoku_df.index,
                    y=ichimoku_df['senkou_span_b'],
                    name="Cloud",
                    line=dict(width=0),
                    fill='tonexty',
                    fillcolor='rgba(0, 250, 0, 0.1)',
                    showlegend=False
                )
            )

            # Update layout
            fig.update_layout(
                title=f"Ichimoku Cloud for {symbol}",
                xaxis_title="Date",
                yaxis_title="Price",
                height=600,
                legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.02,
                    xanchor="right",
                    x=1
                )
            )

            # Display chart
            st.plotly_chart(fig, use_container_width=True)

            # Display signals with improved UI
            st.markdown("""
            <style>
            .signal-container {
                background-color: #1E1E1E;
                border-radius: 10px;
                padding: 20px;
                margin-bottom: 20px;
            }
            .signal-header {
                font-size: 1.5rem;
                font-weight: bold;
                margin-bottom: 20px;
                text-align: center;
            }
            .signal-box {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
            }
            .signal-label {
                font-size: 1.2rem;
                font-weight: bold;
            }
            .signal-value {
                font-size: 1.2rem;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 5px;
            }
            .level-container {
                background-color: #1E1E1E;
                border-radius: 10px;
                padding: 20px;
            }
            .level-header {
                font-size: 1.3rem;
                font-weight: bold;
                margin-bottom: 15px;
                text-align: center;
            }
            .level-item {
                display: flex;
                justify-content: space-between;
                padding: 8px 0;
                border-bottom: 1px solid #333;
            }
            .level-name {
                font-weight: bold;
            }
            .level-price {
                font-family: monospace;
            }
            </style>
            """, unsafe_allow_html=True)

            # Get the latest signal
            latest_signal = signals_df.iloc[-1]['ichimoku_signal'] if not signals_df.empty else 0
            latest_trend = signals_df.iloc[-1]['ichimoku_trend'] if not signals_df.empty else 'neutral'

            # Signal text and colors
            signal_text = "Buy" if latest_signal > 0 else "Sell" if latest_signal < 0 else "Neutral"
            signal_color = "green" if latest_signal > 0 else "red" if latest_signal < 0 else "gray"
            trend_color = "green" if latest_trend == "bullish" else "red" if latest_trend == "bearish" else "gray"
            signal_strength = abs(latest_signal)

            # Display signals in a styled container - using a cleaner approach
            trend_bg_color = "0, 128, 0" if trend_color == "green" else "255, 0, 0" if trend_color == "red" else "128, 128, 128"
            signal_bg_color = "0, 128, 0" if signal_color == "green" else "255, 0, 0" if signal_color == "red" else "128, 128, 128"

            signal_html = '<div class="signal-container">'
            signal_html += '<div class="signal-header">Ichimoku Cloud Signals</div>'

            # Trend box
            signal_html += '<div class="signal-box">'
            signal_html += '<div class="signal-label">Trend:</div>'
            signal_html += f'<div class="signal-value" style="background-color: rgba({trend_bg_color}, 0.2); color: {trend_color};">'
            signal_html += f'{latest_trend.capitalize()}'
            signal_html += '</div>'
            signal_html += '</div>'

            # Signal box
            signal_html += '<div class="signal-box">'
            signal_html += '<div class="signal-label">Signal:</div>'
            signal_html += f'<div class="signal-value" style="background-color: rgba({signal_bg_color}, 0.2); color: {signal_color};">'
            signal_html += f'{signal_text} ({signal_strength:.1f})'
            signal_html += '</div>'
            signal_html += '</div>'

            signal_html += '</div>'

            st.markdown(signal_html, unsafe_allow_html=True)

            # Display support and resistance levels with improved UI
            st.markdown('<div class="signal-header">Support and Resistance Levels</div>', unsafe_allow_html=True)

            # Create columns for support and resistance
            col1, col2 = st.columns(2)

            with col1:
                # Support levels with improved styling - using a cleaner approach
                support_html = '<div class="level-container"><div class="level-header">Support Levels</div>'

                if support_resistance.get('support_levels'):
                    for level_name, level_price in support_resistance['support_levels']:
                        # Use a simpler string format to avoid multi-line string issues
                        support_html += '<div class="level-item">'
                        support_html += f'<span class="level-name">{level_name}</span>'
                        support_html += f'<span class="level-price">{level_price:.2f}</span>'
                        support_html += '</div>'
                else:
                    support_html += '<div class="level-item">No support levels identified</div>'

                support_html += '</div>'
                st.markdown(support_html, unsafe_allow_html=True)

            with col2:
                # Resistance levels with improved styling - using a cleaner approach
                resistance_html = '<div class="level-container"><div class="level-header">Resistance Levels</div>'

                if support_resistance.get('resistance_levels'):
                    for level_name, level_price in support_resistance['resistance_levels']:
                        # Use a simpler string format to avoid multi-line string issues
                        resistance_html += '<div class="level-item">'
                        resistance_html += f'<span class="level-name">{level_name}</span>'
                        resistance_html += f'<span class="level-price">{level_price:.2f}</span>'
                        resistance_html += '</div>'
                else:
                    resistance_html += '<div class="level-item">No resistance levels identified</div>'

                resistance_html += '</div>'
                st.markdown(resistance_html, unsafe_allow_html=True)

            # Add more CSS for interpretation section
            st.markdown("""
            <style>
            .interpretation-container {
                background-color: #1E1E1E;
                border-radius: 10px;
                padding: 20px;
                margin-top: 20px;
                margin-bottom: 20px;
            }
            .interpretation-header {
                font-size: 1.5rem;
                font-weight: bold;
                margin-bottom: 20px;
                text-align: center;
            }
            .interpretation-item {
                display: flex;
                align-items: flex-start;
                margin-bottom: 12px;
                padding-bottom: 12px;
                border-bottom: 1px solid #333;
            }
            .interpretation-icon {
                margin-right: 10px;
                font-size: 1.2rem;
                min-width: 24px;
                text-align: center;
            }
            .interpretation-text {
                flex: 1;
            }
            .overall-assessment {
                margin-top: 20px;
                padding: 15px;
                border-radius: 5px;
                font-weight: bold;
                text-align: center;
            }
            </style>
            """, unsafe_allow_html=True)

            # Current price - with error handling
            try:
                current_price = df.iloc[-1]['Close']
            except (IndexError, KeyError) as e:
                logger.warning(f"Error getting current price: {str(e)}")
                # Set a default value
                current_price = 0

            # Initialize default values
            above_cloud = False
            below_cloud = False
            in_cloud = True  # Default to in cloud if we can't determine
            cloud_bullish = False
            tenkan_above_kijun = False

            # Check if we have data
            if not ichimoku_df.empty:
                try:
                    # Determine position relative to cloud
                    latest_senkou_span_a = ichimoku_df.iloc[-1]['senkou_span_a']
                    latest_senkou_span_b = ichimoku_df.iloc[-1]['senkou_span_b']

                    # Check for NaN values
                    if not pd.isna(latest_senkou_span_a) and not pd.isna(latest_senkou_span_b):
                        above_cloud = current_price > max(latest_senkou_span_a, latest_senkou_span_b)
                        below_cloud = current_price < min(latest_senkou_span_a, latest_senkou_span_b)
                        in_cloud = not above_cloud and not below_cloud

                        # Determine cloud direction
                        cloud_bullish = latest_senkou_span_a > latest_senkou_span_b

                    # Tenkan-sen and Kijun-sen relationship
                    latest_tenkan_sen = ichimoku_df.iloc[-1]['tenkan_sen']
                    latest_kijun_sen = ichimoku_df.iloc[-1]['kijun_sen']

                    if not pd.isna(latest_tenkan_sen) and not pd.isna(latest_kijun_sen):
                        tenkan_above_kijun = latest_tenkan_sen > latest_kijun_sen
                except (IndexError, KeyError) as e:
                    logger.warning(f"Error determining Ichimoku Cloud position: {str(e)}")
                    # Keep the default values

            # Chikou Span relationship with price
            chikou_above_price = False
            # Check if we have enough data points for the displacement
            if len(ichimoku_df) > displacement:
                # Make sure the indices exist and the values are not NaN
                try:
                    if not pd.isna(ichimoku_df.iloc[-displacement]['Close']) and not pd.isna(ichimoku_df.iloc[-1]['chikou_span']):
                        chikou_above_price = ichimoku_df.iloc[-1]['chikou_span'] > ichimoku_df.iloc[-displacement]['Close']
                except (IndexError, KeyError) as e:
                    logger.warning(f"Error checking Chikou Span relationship: {str(e)}")
                    # Keep the default value of False

            # Generate interpretation HTML - using a cleaner approach
            interpretation_html = '<div class="interpretation-container">'
            interpretation_html += '<div class="interpretation-header">Ichimoku Cloud Interpretation</div>'

            # Price position relative to cloud
            if above_cloud:
                interpretation_html += '<div class="interpretation-item">'
                interpretation_html += '<div class="interpretation-icon" style="color: green;">↑</div>'
                interpretation_html += '<div class="interpretation-text">Price is <strong>above the cloud</strong>, indicating a <strong>bullish trend</strong>.</div>'
                interpretation_html += '</div>'
            elif below_cloud:
                interpretation_html += '<div class="interpretation-item">'
                interpretation_html += '<div class="interpretation-icon" style="color: red;">↓</div>'
                interpretation_html += '<div class="interpretation-text">Price is <strong>below the cloud</strong>, indicating a <strong>bearish trend</strong>.</div>'
                interpretation_html += '</div>'
            else:
                interpretation_html += '<div class="interpretation-item">'
                interpretation_html += '<div class="interpretation-icon" style="color: gray;">↔</div>'
                interpretation_html += '<div class="interpretation-text">Price is <strong>inside the cloud</strong>, indicating <strong>consolidation or uncertainty</strong>.</div>'
                interpretation_html += '</div>'

            # Cloud direction
            if cloud_bullish:
                interpretation_html += '<div class="interpretation-item">'
                interpretation_html += '<div class="interpretation-icon" style="color: green;">☁</div>'
                interpretation_html += '<div class="interpretation-text">The cloud is <strong>bullish</strong> (Senkou Span A above Senkou Span B).</div>'
                interpretation_html += '</div>'
            else:
                interpretation_html += '<div class="interpretation-item">'
                interpretation_html += '<div class="interpretation-icon" style="color: red;">☁</div>'
                interpretation_html += '<div class="interpretation-text">The cloud is <strong>bearish</strong> (Senkou Span A below Senkou Span B).</div>'
                interpretation_html += '</div>'

            # Tenkan-sen and Kijun-sen relationship
            if tenkan_above_kijun:
                interpretation_html += '<div class="interpretation-item">'
                interpretation_html += '<div class="interpretation-icon" style="color: green;">✓</div>'
                interpretation_html += '<div class="interpretation-text">Tenkan-sen is <strong>above</strong> Kijun-sen, indicating <strong>bullish momentum</strong>.</div>'
                interpretation_html += '</div>'
            else:
                interpretation_html += '<div class="interpretation-item">'
                interpretation_html += '<div class="interpretation-icon" style="color: red;">✗</div>'
                interpretation_html += '<div class="interpretation-text">Tenkan-sen is <strong>below</strong> Kijun-sen, indicating <strong>bearish momentum</strong>.</div>'
                interpretation_html += '</div>'

            # Chikou Span relationship
            if chikou_above_price:
                interpretation_html += '<div class="interpretation-item">'
                interpretation_html += '<div class="interpretation-icon" style="color: green;">★</div>'
                interpretation_html += '<div class="interpretation-text">Chikou Span is <strong>above</strong> the price from 26 periods ago, indicating <strong>bullish sentiment</strong>.</div>'
                interpretation_html += '</div>'
            else:
                interpretation_html += '<div class="interpretation-item">'
                interpretation_html += '<div class="interpretation-icon" style="color: red;">★</div>'
                interpretation_html += '<div class="interpretation-text">Chikou Span is <strong>below</strong> the price from 26 periods ago, indicating <strong>bearish sentiment</strong>.</div>'
                interpretation_html += '</div>'

            # Overall assessment
            assessment = ""
            assessment_color = ""

            if above_cloud and tenkan_above_kijun and chikou_above_price:
                assessment = "Strong bullish trend. Consider buying or holding positions."
                assessment_color = "rgba(0, 128, 0, 0.2)"
                assessment_text_color = "green"
            elif below_cloud and not tenkan_above_kijun and not chikou_above_price:
                assessment = "Strong bearish trend. Consider selling or reducing positions."
                assessment_color = "rgba(255, 0, 0, 0.2)"
                assessment_text_color = "red"
            elif above_cloud or (in_cloud and tenkan_above_kijun):
                assessment = "Moderately bullish. Watch for confirmation signals before entering new positions."
                assessment_color = "rgba(0, 128, 0, 0.1)"
                assessment_text_color = "green"
            elif below_cloud or (in_cloud and not tenkan_above_kijun):
                assessment = "Moderately bearish. Watch for confirmation signals before entering new positions."
                assessment_color = "rgba(255, 0, 0, 0.1)"
                assessment_text_color = "red"
            else:
                assessment = "Neutral. The market is consolidating, consider waiting for clearer signals."
                assessment_color = "rgba(128, 128, 128, 0.2)"
                assessment_text_color = "gray"

            # Add overall assessment using the cleaner approach
            interpretation_html += f'<div class="overall-assessment" style="background-color: {assessment_color}; color: {assessment_text_color};">'
            interpretation_html += '<strong>Overall Assessment:</strong><br>'
            interpretation_html += f'{assessment}'
            interpretation_html += '</div>'

            interpretation_html += '</div>'

            # Display the interpretation
            st.markdown(interpretation_html, unsafe_allow_html=True)

        except Exception as e:
            st.error(f"Error calculating Ichimoku Cloud: {str(e)}")
            logger.error(f"Error calculating Ichimoku Cloud for {symbol}: {str(e)}")



