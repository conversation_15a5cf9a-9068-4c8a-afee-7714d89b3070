"""
Hierarchical Risk Parity portfolio optimization.
"""
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
from scipy.cluster import hierarchy
from scipy.spatial.distance import squareform

from app.core.portfolio.optimization.base import OptimizationStrategy
from app.utils.logging import logger


class HierarchicalRiskParity(OptimizationStrategy):
    """
    Hierarchical Risk Parity (HRP) portfolio optimization.

    This class implements the Hierarchical Risk Parity algorithm developed by
    <PERSON>, which uses hierarchical clustering to allocate
    portfolio weights.
    """

    def __init__(self):
        """Initialize the Hierarchical Risk Parity optimization strategy."""
        super().__init__("Hierarchical Risk Parity")

    def optimize(
        self,
        returns: pd.DataFrame,
        linkage_method: str = "single",
        distance_metric: str = "correlation",
        **kwargs
    ) -> Dict[str, float]:
        """
        Optimize portfolio weights using Hierarchical Risk Parity.

        Args:
            returns: DataFrame of asset returns
            linkage_method: Method for hierarchical clustering ('single', 'complete', 'average', 'ward')
            distance_metric: Metric for calculating distances ('correlation', 'euclidean')
            **kwargs: Additional parameters

        Returns:
            Dictionary mapping asset names to weights
        """
        # Validate inputs
        if returns is None or returns.empty:
            logger.error("Returns must be provided for HRP optimization")
            return {}

        # Get asset names
        assets = returns.columns.tolist()
        n_assets = len(assets)

        if n_assets == 0:
            logger.warning("No assets provided for optimization")
            return {}

        # Calculate covariance matrix
        cov_matrix = returns.cov()

        # Calculate correlation matrix
        corr_matrix = returns.corr()

        # Calculate distance matrix
        if distance_metric == "correlation":
            # Convert correlation to distance (1 - correlation)
            distance_matrix = 1 - corr_matrix.abs().values
        else:
            # Use Euclidean distance
            distance_matrix = np.zeros((n_assets, n_assets))
            for i in range(n_assets):
                for j in range(i+1, n_assets):
                    distance_matrix[i, j] = np.sqrt(np.sum((returns.iloc[:, i] - returns.iloc[:, j])**2))
                    distance_matrix[j, i] = distance_matrix[i, j]

        # Convert distance matrix to condensed form
        condensed_distance = squareform(distance_matrix)

        # Perform hierarchical clustering
        try:
            link = hierarchy.linkage(condensed_distance, method=linkage_method)

            # Get quasi-diagonalization order
            sorted_indices = self._get_quasi_diag(link)
            sorted_assets = [assets[i] for i in sorted_indices]

            # Compute HRP weights
            weights = self._get_hrp_weights(cov_matrix.iloc[sorted_indices, sorted_indices])

            # Map weights back to original assets
            result_weights = {asset: weight for asset, weight in zip(sorted_assets, weights)}

            logger.info(f"Allocated HRP weights to {n_assets} assets")

            return result_weights

        except Exception as e:
            logger.error(f"Error in HRP optimization: {str(e)}")
            # Fall back to equal weights
            weight = 1.0 / n_assets
            return {asset: weight for asset in assets}

    def _get_quasi_diag(self, link: np.ndarray) -> List[int]:
        """
        Get quasi-diagonalization of the hierarchical clustering.

        Args:
            link: Linkage matrix from hierarchical clustering

        Returns:
            List of indices in quasi-diagonalization order
        """
        # Number of assets
        n_assets = link.shape[0] + 1

        # Initialize with all assets as separate clusters
        clusters = {i: [i] for i in range(n_assets)}

        # Initialize result list
        result = []

        # Process linkage matrix
        for i in range(link.shape[0]):
            # Get clusters to merge
            cluster1 = int(link[i, 0])
            cluster2 = int(link[i, 1])

            # Merge clusters
            if cluster1 >= n_assets:
                # Cluster1 is a merged cluster
                cluster1_assets = clusters[cluster1]
            else:
                # Cluster1 is a single asset
                cluster1_assets = [cluster1]

            if cluster2 >= n_assets:
                # Cluster2 is a merged cluster
                cluster2_assets = clusters[cluster2]
            else:
                # Cluster2 is a single asset
                cluster2_assets = [cluster2]

            # Create new merged cluster
            new_cluster = cluster1_assets + cluster2_assets
            clusters[n_assets + i] = new_cluster

            # If this is the last merge, we have the final order
            if i == link.shape[0] - 1:
                result = new_cluster

        return result

    def _get_hrp_weights(self, cov_matrix: pd.DataFrame) -> np.ndarray:
        """
        Calculate HRP weights from the covariance matrix.

        Args:
            cov_matrix: Covariance matrix in quasi-diagonalized order

        Returns:
            Array of weights
        """
        # Number of assets
        n_assets = cov_matrix.shape[0]

        # Initialize weights
        weights = np.ones(n_assets)

        # Calculate inverse variance weights
        inv_var = 1 / np.diag(cov_matrix)
        weights = weights * inv_var

        # Normalize weights to sum to 1
        weights = weights / np.sum(weights)

        # Recursive bisection
        clusters = {0: np.arange(n_assets)}  # Initialize with all assets
        self._recursive_bisection(cov_matrix, clusters, weights, 0)

        return weights

    def _recursive_bisection(
        self,
        cov_matrix: pd.DataFrame,
        clusters: Dict[int, np.ndarray],
        weights: np.ndarray,
        cluster_id: int
    ) -> None:
        """
        Perform recursive bisection for HRP.

        Args:
            cov_matrix: Covariance matrix
            clusters: Dictionary of clusters
            weights: Array of weights to update
            cluster_id: Current cluster ID
        """
        # Get current cluster
        cluster = clusters[cluster_id]

        # If cluster has only one asset, return
        if len(cluster) <= 1:
            return

        # Split cluster into two
        # Calculate variance of the cluster
        var = np.diag(cov_matrix.iloc[cluster, cluster].values)

        # Calculate correlation matrix
        corr_matrix = cov_matrix.iloc[cluster, cluster].values / np.sqrt(np.outer(var, var))

        # Calculate distance matrix
        distance = 1 - corr_matrix

        # Perform linkage
        link = hierarchy.linkage(squareform(distance), method='single')

        # Get two clusters
        cluster_indices = hierarchy.cut_tree(link, n_clusters=2).flatten()

        # Create two new clusters
        cluster1 = cluster[cluster_indices == 0]
        cluster2 = cluster[cluster_indices == 1]

        # Store new clusters
        new_id1 = max(clusters.keys()) + 1
        new_id2 = max(clusters.keys()) + 2
        clusters[new_id1] = cluster1
        clusters[new_id2] = cluster2

        # Calculate cluster variances
        var1 = np.sum(weights[cluster1] * np.diag(cov_matrix.iloc[cluster1, cluster1].values))
        var2 = np.sum(weights[cluster2] * np.diag(cov_matrix.iloc[cluster2, cluster2].values))

        # Calculate alpha (weight scaler)
        alpha = 1 - var1 / (var1 + var2)

        # Update weights
        weights[cluster1] *= alpha
        weights[cluster2] *= (1 - alpha)

        # Recursive bisection on new clusters
        self._recursive_bisection(cov_matrix, clusters, weights, new_id1)
        self._recursive_bisection(cov_matrix, clusters, weights, new_id2)


# Create an alias for backward compatibility
class HierarchicalRiskParityOptimization(HierarchicalRiskParity):
    """
    Alias for HierarchicalRiskParity for backward compatibility.
    """
    pass
