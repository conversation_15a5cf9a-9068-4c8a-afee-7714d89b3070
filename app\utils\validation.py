"""
Input validation functions.
"""
import re
from typing import List, Optional, Union

import pandas as pd


def is_valid_ticker(ticker: str) -> bool:
    """
    Check if a ticker symbol is valid.

    Args:
        ticker: The ticker symbol to validate

    Returns:
        True if the ticker is valid, False otherwise
    """
    # Allow for common ticker formats including indices with ^ prefix
    # and tickers with dots or hyphens
    pattern = r'^[\^]?[A-Z0-9\.\-]{1,10}$'
    return bool(re.match(pattern, ticker))


def validate_tickers(tickers: Union[str, List[str]]) -> List[str]:
    """
    Validate a list of ticker symbols.

    Args:
        tickers: A single ticker or list of tickers to validate

    Returns:
        A list of valid ticker symbols

    Raises:
        ValueError: If no valid tickers are provided
    """
    if isinstance(tickers, str):
        tickers = [tickers]

    valid_tickers = [ticker for ticker in tickers if is_valid_ticker(ticker)]

    if not valid_tickers:
        raise ValueError("No valid ticker symbols provided")

    return valid_tickers


def validate_dataframe(
    df: pd.DataFrame,
    required_columns: Optional[List[str]] = None,
    min_rows: int = 1
) -> bool:
    """
    Validate a pandas DataFrame.

    Args:
        df: The DataFrame to validate
        required_columns: A list of columns that must be present
        min_rows: The minimum number of rows required

    Returns:
        True if the DataFrame is valid, False otherwise
    """
    # Check if DataFrame is empty
    if df is None or df.empty or len(df) < min_rows:
        return False

    # Check for required columns
    if required_columns:
        if not all(col in df.columns for col in required_columns):
            return False

    return True


def validate_weights(weights: List[float], tolerance: float = 1e-6) -> bool:
    """
    Validate portfolio weights.

    Args:
        weights: A list of portfolio weights
        tolerance: Tolerance for the sum of weights

    Returns:
        True if the weights are valid, False otherwise
    """
    # Check if weights are non-negative
    if any(w < 0 for w in weights):
        return False

    # Check if weights sum to 1
    if abs(sum(weights) - 1.0) > tolerance:
        return False

    return True
