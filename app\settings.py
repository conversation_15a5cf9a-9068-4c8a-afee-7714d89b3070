"""
Application settings module.

This module handles loading configuration from YAML files and environment variables.
"""
import os
from typing import Dict, Any
import yaml
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    app: Dict[str, Any]
    api: Dict[str, Any]
    streamlit: Dict[str, Any]
    database: Dict[str, Any]
    cache: Dict[str, Any]  # Added cache attribute
    data_sources: Dict[str, Any]  # Added data_sources attribute

    def __init__(self):
        # Determine the environment
        env = os.getenv("APP_ENV", "default")
        
        # Load the appropriate config file
        config_path = f"config/{env}.yaml"
        if not os.path.exists(config_path):
            config_path = "config/default.yaml"
        
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        super().__init__(**config)

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "allow"

_settings = None

def get_settings() -> Settings:
    """Get application settings."""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings
