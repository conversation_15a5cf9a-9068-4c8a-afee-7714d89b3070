"""
Technical Analysis API endpoints.
"""
from datetime import date
from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.core.data_collection.data_manager import data_manager
from app.core.technical_analysis.manager import technical_analysis_manager
from app.db.database import get_db
from app.db.models import User
from app.middleware.auth import get_current_user

router = APIRouter()


@router.get("/indicators/{symbol}")
async def calculate_indicators(
    symbol: str,
    indicators: List[str] = Query(..., description="List of indicators to calculate"),
    start_date: Optional[date] = Query(None, description="Start date for historical data"),
    end_date: Optional[date] = Query(None, description="End date for historical data"),
    interval: str = Query("1d", description="Data interval (e.g., '1d', '1wk', '1mo')"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Calculate technical indicators for a symbol.
    """
    try:
        # Get historical data
        df = await data_manager.get_stock_data(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            interval=interval
        )
        
        if df.empty:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No data found for symbol {symbol}"
            )
        
        # Prepare indicator parameters
        indicator_params = []
        for indicator in indicators:
            if indicator == "sma":
                indicator_params.extend([
                    {"indicator": "sma", "period": 20},
                    {"indicator": "sma", "period": 50},
                    {"indicator": "sma", "period": 200}
                ])
            elif indicator == "ema":
                indicator_params.extend([
                    {"indicator": "ema", "period": 20},
                    {"indicator": "ema", "period": 50}
                ])
            elif indicator == "macd":
                indicator_params.append({
                    "indicator": "macd",
                    "fast_period": 12,
                    "slow_period": 26,
                    "signal_period": 9
                })
            elif indicator == "rsi":
                indicator_params.append({
                    "indicator": "rsi",
                    "period": 14
                })
            elif indicator == "bollinger_bands":
                indicator_params.append({
                    "indicator": "bollinger_bands",
                    "period": 20,
                    "std_dev": 2
                })
            elif indicator == "stochastic":
                indicator_params.append({
                    "indicator": "stochastic",
                    "k_period": 14,
                    "d_period": 3,
                    "smooth_k": 3
                })
            elif indicator == "cci":
                indicator_params.append({
                    "indicator": "cci",
                    "period": 20
                })
            elif indicator == "mfi":
                indicator_params.append({
                    "indicator": "mfi",
                    "period": 14
                })
            elif indicator == "obv":
                indicator_params.append({
                    "indicator": "obv"
                })
            elif indicator == "vwap":
                indicator_params.append({
                    "indicator": "vwap"
                })
            elif indicator == "ad":
                indicator_params.append({
                    "indicator": "ad"
                })
            elif indicator == "adosc":
                indicator_params.append({
                    "indicator": "adosc",
                    "fast_period": 3,
                    "slow_period": 10
                })
        
        # Calculate indicators
        result = technical_analysis_manager.calculate_multiple_indicators(
            df,
            indicator_params
        )
        
        # Convert to JSON-serializable format
        result_dict = {
            "symbol": symbol,
            "data": []
        }
        
        for _, row in result.iterrows():
            data_point = {
                "date": row["date"].isoformat() if isinstance(row["date"], date) else row["date"],
                "open": row["open"],
                "high": row["high"],
                "low": row["low"],
                "close": row["close"],
                "volume": row["volume"]
            }
            
            # Add indicator values
            for col in row.index:
                if col not in ["date", "open", "high", "low", "close", "volume"]:
                    data_point[col] = row[col]
            
            result_dict["data"].append(data_point)
        
        return result_dict
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.get("/signals/{symbol}")
async def get_trading_signals(
    symbol: str,
    indicators: List[str] = Query(..., description="List of indicators to calculate"),
    start_date: Optional[date] = Query(None, description="Start date for historical data"),
    end_date: Optional[date] = Query(None, description="End date for historical data"),
    interval: str = Query("1d", description="Data interval (e.g., '1d', '1wk', '1mo')"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get trading signals for a symbol.
    """
    try:
        # Get historical data
        df = await data_manager.get_stock_data(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            interval=interval
        )
        
        if df.empty:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No data found for symbol {symbol}"
            )
        
        # Prepare indicator parameters
        indicator_params = []
        for indicator in indicators:
            if indicator == "sma":
                indicator_params.extend([
                    {"indicator": "sma", "period": 20},
                    {"indicator": "sma", "period": 50},
                    {"indicator": "sma", "period": 200}
                ])
            elif indicator == "ema":
                indicator_params.extend([
                    {"indicator": "ema", "period": 20},
                    {"indicator": "ema", "period": 50}
                ])
            elif indicator == "macd":
                indicator_params.append({
                    "indicator": "macd",
                    "fast_period": 12,
                    "slow_period": 26,
                    "signal_period": 9
                })
            elif indicator == "rsi":
                indicator_params.append({
                    "indicator": "rsi",
                    "period": 14
                })
            elif indicator == "bollinger_bands":
                indicator_params.append({
                    "indicator": "bollinger_bands",
                    "period": 20,
                    "std_dev": 2
                })
            elif indicator == "stochastic":
                indicator_params.append({
                    "indicator": "stochastic",
                    "k_period": 14,
                    "d_period": 3,
                    "smooth_k": 3
                })
            elif indicator == "cci":
                indicator_params.append({
                    "indicator": "cci",
                    "period": 20
                })
            elif indicator == "mfi":
                indicator_params.append({
                    "indicator": "mfi",
                    "period": 14
                })
            elif indicator == "obv":
                indicator_params.append({
                    "indicator": "obv"
                })
            elif indicator == "vwap":
                indicator_params.append({
                    "indicator": "vwap"
                })
            elif indicator == "ad":
                indicator_params.append({
                    "indicator": "ad"
                })
            elif indicator == "adosc":
                indicator_params.append({
                    "indicator": "adosc",
                    "fast_period": 3,
                    "slow_period": 10
                })
        
        # Get trading signals
        result = technical_analysis_manager.get_trading_signals(
            df,
            indicator_params
        )
        
        # Convert to JSON-serializable format
        result_dict = {
            "symbol": symbol,
            "data": []
        }
        
        for _, row in result.iterrows():
            data_point = {
                "date": row["date"].isoformat() if isinstance(row["date"], date) else row["date"],
                "close": row["close"]
            }
            
            # Add signal values
            signal_columns = [col for col in row.index if col.endswith("_signal")]
            for col in signal_columns:
                data_point[col] = row[col]
            
            # Add aggregated signal
            if "aggregated_signal" in row:
                data_point["aggregated_signal"] = row["aggregated_signal"]
                data_point["aggregated_signal_strength"] = row["aggregated_signal_strength"]
                data_point["signal_strength"] = row["signal_strength"]
            
            result_dict["data"].append(data_point)
        
        return result_dict
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.get("/patterns/{symbol}")
async def identify_patterns(
    symbol: str,
    pattern_type: str = Query(..., description="Pattern type ('candlestick' or 'chart')"),
    patterns: List[str] = Query(None, description="List of patterns to identify"),
    start_date: Optional[date] = Query(None, description="Start date for historical data"),
    end_date: Optional[date] = Query(None, description="End date for historical data"),
    interval: str = Query("1d", description="Data interval (e.g., '1d', '1wk', '1mo')"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Identify patterns for a symbol.
    """
    try:
        # Get historical data
        df = await data_manager.get_stock_data(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            interval=interval
        )
        
        if df.empty:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No data found for symbol {symbol}"
            )
        
        # Identify patterns
        if pattern_type == "candlestick":
            result = technical_analysis_manager.identify_candlestick_patterns(df, patterns)
        elif pattern_type == "chart":
            result = technical_analysis_manager.identify_chart_patterns(df, patterns)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid pattern type: {pattern_type}"
            )
        
        # Convert to JSON-serializable format
        result_dict = {
            "symbol": symbol,
            "data": []
        }
        
        for _, row in result.iterrows():
            data_point = {
                "date": row["date"].isoformat() if isinstance(row["date"], date) else row["date"],
                "open": row["open"],
                "high": row["high"],
                "low": row["low"],
                "close": row["close"]
            }
            
            # Add pattern values
            pattern_columns = [col for col in row.index if col.startswith(pattern_type)]
            for col in pattern_columns:
                data_point[col] = row[col]
            
            result_dict["data"].append(data_point)
        
        return result_dict
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
