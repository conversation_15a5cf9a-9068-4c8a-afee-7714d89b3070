# Configuration

This directory contains configuration files for different environments.

## Configuration Files

- **default.yaml**: Base configuration with default values for all environments
- **development.yaml**: Development environment overrides (local development)
- **production.yaml**: Production environment overrides (deployed application)
- **testing.yaml**: Testing environment overrides (automated tests)

## Configuration Hierarchy

The configuration system follows this hierarchy:

1. Default values from `default.yaml`
2. Environment-specific overrides from `development.yaml`, `production.yaml`, or `testing.yaml`
3. Environment variables (using the `${ENV_VAR:default_value}` syntax)
4. Command-line arguments

## Environment Variables

The following environment variables are used:

- `APP_ENV`: The application environment (`development`, `production`, or `testing`)
- `DATABASE_URL`: Database connection string
- `REDIS_URL`: Redis connection string
- `ALPHA_VANTAGE_API_KEY`: Alpha Vantage API key
- `FINANCIAL_MODELING_PREP_API_KEY`: Financial Modeling Prep API key
- `JWT_SECRET`: Secret key for JWT token generation

## Configuration Sections

- **app**: General application settings
- **api**: FastAPI backend settings
- **streamlit**: Streamlit frontend settings
- **database**: Database connection settings
- **cache**: Caching settings
- **data_sources**: Data source settings
- **portfolio**: Portfolio optimization settings
- **technical_analysis**: Technical analysis settings
- **logging**: Logging settings
- **security**: Security settings
- **performance**: Performance monitoring settings

## Usage

The configuration is loaded using the `app.utils.config` module:

```python
from app.utils.config import get_settings

settings = get_settings()
print(settings.app.name)
print(settings.api.port)
```

## Adding New Configuration

To add new configuration options:

1. Add the option to `default.yaml` with a sensible default value
2. Override the option in environment-specific files if needed
3. Update the configuration model in `app.utils.config` if necessary
