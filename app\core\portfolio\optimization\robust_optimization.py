"""
Robust portfolio optimization methods.

This module provides functions to implement robust portfolio optimization,
which accounts for uncertainty in the input parameters.
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union
import scipy.optimize as sco
import cvxpy as cp

from app.core.portfolio.optimization.base import OptimizationStrategy
from app.utils.constants import DEFAULT_RISK_FREE_RATE
from app.utils.logging import logger


class RobustOptimization(OptimizationStrategy):
    """
    Robust portfolio optimization.
    
    This class implements robust optimization methods that account for
    uncertainty in expected returns and covariance estimates.
    """
    
    def __init__(self, risk_free_rate: float = DEFAULT_RISK_FREE_RATE, uncertainty: float = 0.1):
        """
        Initialize the robust optimization model.
        
        Args:
            risk_free_rate: Risk-free rate
            uncertainty: Uncertainty parameter for robust optimization (0-1)
        """
        super().__init__("Robust Optimization")
        self.risk_free_rate = risk_free_rate
        self.uncertainty = uncertainty
    
    def optimize(
        self,
        returns: pd.DataFrame,
        objective: str = "robust_sharpe",
        uncertainty_returns: Optional[float] = None,
        uncertainty_cov: Optional[float] = None,
        **kwargs
    ) -> Dict[str, float]:
        """
        Optimize portfolio weights using robust optimization.
        
        Args:
            returns: DataFrame of asset returns
            objective: Optimization objective ('robust_sharpe', 'min_cvar', 'max_return')
            uncertainty_returns: Uncertainty in expected returns (0-1)
            uncertainty_cov: Uncertainty in covariance matrix (0-1)
            **kwargs: Additional parameters
            
        Returns:
            Dictionary mapping asset names to weights
        """
        # Validate inputs
        if returns is None or returns.empty:
            logger.error("Returns must be provided for robust optimization")
            return {}
        
        # Get asset names
        assets = returns.columns.tolist()
        n_assets = len(assets)
        
        if n_assets == 0:
            logger.warning("No assets provided for optimization")
            return {}
        
        # Set uncertainty parameters
        if uncertainty_returns is None:
            uncertainty_returns = self.uncertainty
        
        if uncertainty_cov is None:
            uncertainty_cov = self.uncertainty
        
        # Calculate expected returns and covariance matrix
        expected_returns = returns.mean()
        cov_matrix = returns.cov()
        
        # Get additional parameters
        target_return = kwargs.get('target_return', None)
        target_risk = kwargs.get('target_risk', None)
        min_weight = kwargs.get('min_weight', 0.0)
        max_weight = kwargs.get('max_weight', 1.0)
        
        # Choose optimization method based on objective
        if objective == "robust_sharpe":
            weights = self._optimize_robust_sharpe(
                expected_returns=expected_returns,
                cov_matrix=cov_matrix,
                uncertainty_returns=uncertainty_returns,
                uncertainty_cov=uncertainty_cov,
                min_weight=min_weight,
                max_weight=max_weight
            )
        elif objective == "min_cvar":
            weights = self._optimize_min_cvar(
                returns=returns,
                min_weight=min_weight,
                max_weight=max_weight,
                target_return=target_return
            )
        elif objective == "max_return":
            weights = self._optimize_max_return(
                expected_returns=expected_returns,
                cov_matrix=cov_matrix,
                uncertainty_returns=uncertainty_returns,
                min_weight=min_weight,
                max_weight=max_weight,
                target_risk=target_risk
            )
        else:
            logger.warning(f"Unknown objective '{objective}', using robust_sharpe")
            weights = self._optimize_robust_sharpe(
                expected_returns=expected_returns,
                cov_matrix=cov_matrix,
                uncertainty_returns=uncertainty_returns,
                uncertainty_cov=uncertainty_cov,
                min_weight=min_weight,
                max_weight=max_weight
            )
        
        # Convert weights to dictionary
        weights_dict = {asset: weight for asset, weight in zip(assets, weights)}
        
        return weights_dict
    
    def _optimize_robust_sharpe(
        self,
        expected_returns: pd.Series,
        cov_matrix: pd.DataFrame,
        uncertainty_returns: float,
        uncertainty_cov: float,
        min_weight: float = 0.0,
        max_weight: float = 1.0
    ) -> np.ndarray:
        """
        Optimize portfolio weights using robust Sharpe ratio.
        
        Args:
            expected_returns: Series of expected returns
            cov_matrix: Covariance matrix
            uncertainty_returns: Uncertainty in expected returns (0-1)
            uncertainty_cov: Uncertainty in covariance matrix (0-1)
            min_weight: Minimum weight for each asset
            max_weight: Maximum weight for each asset
            
        Returns:
            Array of optimal weights
        """
        try:
            n_assets = len(expected_returns)
            
            # Convert to numpy arrays
            mu = expected_returns.values
            sigma = cov_matrix.values
            
            # Calculate worst-case expected returns
            # μ_worst = μ - δ * σ, where δ is the uncertainty parameter
            worst_case_returns = mu - uncertainty_returns * np.sqrt(np.diag(sigma))
            
            # Calculate worst-case covariance matrix
            # Σ_worst = (1 + δ) * Σ
            worst_case_cov = (1 + uncertainty_cov) * sigma
            
            # Define the optimization problem
            w = cp.Variable(n_assets)
            
            # Objective: maximize worst-case Sharpe ratio
            # Since we can't directly maximize the ratio, we'll maximize the numerator
            # while constraining the denominator (risk) to be 1
            objective = cp.Maximize(w @ worst_case_returns - self.risk_free_rate)
            
            # Constraints
            constraints = [
                cp.sum(w) == 1,  # Weights sum to 1
                w >= min_weight,  # Minimum weight
                w <= max_weight,  # Maximum weight
                cp.quad_form(w, worst_case_cov) <= 1  # Risk constraint
            ]
            
            # Solve the problem
            problem = cp.Problem(objective, constraints)
            problem.solve()
            
            if problem.status == 'optimal':
                # Get optimal weights
                weights = w.value
                
                # Normalize weights to sum to 1 (in case of numerical issues)
                weights = weights / np.sum(weights)
                
                return weights
            else:
                logger.error(f"Robust Sharpe optimization failed: {problem.status}")
                # Fallback to equal weights
                return np.ones(n_assets) / n_assets
        
        except Exception as e:
            logger.error(f"Error in robust Sharpe optimization: {str(e)}")
            # Fallback to equal weights
            return np.ones(n_assets) / n_assets
    
    def _optimize_min_cvar(
        self,
        returns: pd.DataFrame,
        min_weight: float = 0.0,
        max_weight: float = 1.0,
        target_return: Optional[float] = None,
        alpha: float = 0.05
    ) -> np.ndarray:
        """
        Optimize portfolio weights by minimizing Conditional Value at Risk (CVaR).
        
        Args:
            returns: DataFrame of asset returns
            min_weight: Minimum weight for each asset
            max_weight: Maximum weight for each asset
            target_return: Target portfolio return (optional)
            alpha: Confidence level for CVaR (default: 0.05 for 95% confidence)
            
        Returns:
            Array of optimal weights
        """
        try:
            n_assets = returns.shape[1]
            n_samples = returns.shape[0]
            
            # Convert to numpy array
            returns_array = returns.values
            
            # Calculate expected returns
            expected_returns = returns.mean().values
            
            # Define the optimization problem
            w = cp.Variable(n_assets)
            var = cp.Variable()  # Value at Risk
            aux_vars = cp.Variable(n_samples)  # Auxiliary variables for CVaR
            
            # Calculate portfolio returns for each sample
            portfolio_returns = returns_array @ w
            
            # Objective: minimize CVaR
            objective = cp.Minimize(var + (1 / (alpha * n_samples)) * cp.sum(aux_vars))
            
            # Constraints
            constraints = [
                cp.sum(w) == 1,  # Weights sum to 1
                w >= min_weight,  # Minimum weight
                w <= max_weight,  # Maximum weight
                aux_vars >= 0,  # Auxiliary variables are non-negative
                aux_vars >= -portfolio_returns - var  # CVaR constraint
            ]
            
            # Add target return constraint if specified
            if target_return is not None:
                constraints.append(w @ expected_returns >= target_return)
            
            # Solve the problem
            problem = cp.Problem(objective, constraints)
            problem.solve()
            
            if problem.status == 'optimal':
                # Get optimal weights
                weights = w.value
                
                # Normalize weights to sum to 1 (in case of numerical issues)
                weights = weights / np.sum(weights)
                
                return weights
            else:
                logger.error(f"Min-CVaR optimization failed: {problem.status}")
                # Fallback to equal weights
                return np.ones(n_assets) / n_assets
        
        except Exception as e:
            logger.error(f"Error in Min-CVaR optimization: {str(e)}")
            # Fallback to equal weights
            return np.ones(n_assets) / n_assets
    
    def _optimize_max_return(
        self,
        expected_returns: pd.Series,
        cov_matrix: pd.DataFrame,
        uncertainty_returns: float,
        min_weight: float = 0.0,
        max_weight: float = 1.0,
        target_risk: Optional[float] = None
    ) -> np.ndarray:
        """
        Optimize portfolio weights by maximizing worst-case return.
        
        Args:
            expected_returns: Series of expected returns
            cov_matrix: Covariance matrix
            uncertainty_returns: Uncertainty in expected returns (0-1)
            min_weight: Minimum weight for each asset
            max_weight: Maximum weight for each asset
            target_risk: Target portfolio risk (optional)
            
        Returns:
            Array of optimal weights
        """
        try:
            n_assets = len(expected_returns)
            
            # Convert to numpy arrays
            mu = expected_returns.values
            sigma = cov_matrix.values
            
            # Calculate worst-case expected returns
            # μ_worst = μ - δ * σ, where δ is the uncertainty parameter
            worst_case_returns = mu - uncertainty_returns * np.sqrt(np.diag(sigma))
            
            # Define the optimization problem
            w = cp.Variable(n_assets)
            
            # Objective: maximize worst-case return
            objective = cp.Maximize(w @ worst_case_returns)
            
            # Constraints
            constraints = [
                cp.sum(w) == 1,  # Weights sum to 1
                w >= min_weight,  # Minimum weight
                w <= max_weight  # Maximum weight
            ]
            
            # Add target risk constraint if specified
            if target_risk is not None:
                constraints.append(cp.quad_form(w, sigma) <= target_risk**2)
            
            # Solve the problem
            problem = cp.Problem(objective, constraints)
            problem.solve()
            
            if problem.status == 'optimal':
                # Get optimal weights
                weights = w.value
                
                # Normalize weights to sum to 1 (in case of numerical issues)
                weights = weights / np.sum(weights)
                
                return weights
            else:
                logger.error(f"Max-return optimization failed: {problem.status}")
                # Fallback to equal weights
                return np.ones(n_assets) / n_assets
        
        except Exception as e:
            logger.error(f"Error in Max-return optimization: {str(e)}")
            # Fallback to equal weights
            return np.ones(n_assets) / n_assets
    
    def calculate_portfolio_metrics(
        self,
        weights: Dict[str, float],
        returns: pd.DataFrame
    ) -> Dict:
        """
        Calculate portfolio metrics including robust measures.
        
        Args:
            weights: Dictionary mapping asset names to weights
            returns: DataFrame of asset returns
            
        Returns:
            Dictionary of portfolio metrics
        """
        try:
            # Convert weights to Series
            weights_series = pd.Series(weights)
            
            # Filter returns to include only assets in weights
            common_assets = [asset for asset in weights_series.index if asset in returns.columns]
            filtered_returns = returns[common_assets]
            filtered_weights = weights_series[common_assets]
            
            # Normalize weights to sum to 1
            filtered_weights = filtered_weights / filtered_weights.sum()
            
            # Calculate portfolio returns
            portfolio_returns = filtered_returns.dot(filtered_weights)
            
            # Calculate basic metrics
            expected_return = portfolio_returns.mean()
            volatility = portfolio_returns.std()
            sharpe_ratio = (expected_return - self.risk_free_rate) / volatility
            
            # Calculate robust metrics
            # 1. Maximum Drawdown
            cumulative_returns = (1 + portfolio_returns).cumprod()
            max_drawdown = (cumulative_returns / cumulative_returns.cummax() - 1).min()
            
            # 2. Conditional Value at Risk (CVaR)
            alpha = 0.05  # 95% confidence level
            var_95 = portfolio_returns.quantile(alpha)
            cvar_95 = portfolio_returns[portfolio_returns <= var_95].mean()
            
            # 3. Downside Deviation
            target_return = self.risk_free_rate
            downside_returns = portfolio_returns[portfolio_returns < target_return]
            downside_deviation = np.sqrt(((downside_returns - target_return) ** 2).mean())
            
            # 4. Sortino Ratio
            sortino_ratio = (expected_return - self.risk_free_rate) / downside_deviation if downside_deviation > 0 else np.nan
            
            # Return metrics
            return {
                'expected_return': expected_return,
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'var_95': var_95,
                'cvar_95': cvar_95,
                'downside_deviation': downside_deviation,
                'sortino_ratio': sortino_ratio
            }
        
        except Exception as e:
            logger.error(f"Error calculating portfolio metrics: {str(e)}")
            return {
                'expected_return': None,
                'volatility': None,
                'sharpe_ratio': None,
                'max_drawdown': None,
                'var_95': None,
                'cvar_95': None,
                'downside_deviation': None,
                'sortino_ratio': None
            }
