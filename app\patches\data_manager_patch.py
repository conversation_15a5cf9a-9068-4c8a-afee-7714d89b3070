"""
Monkey patch for the DataManager class to add missing methods.
"""
import pandas as pd
from app.core.data_collection.data_manager import data_manager
from app.utils.logging import logger

def apply_patches():
    """Apply all patches to the data_manager instance."""
    logger.info("Applying patches to DataManager")

    if not hasattr(data_manager, 'combine_stock_data'):
        logger.error("combine_stock_data method is missing from DataManager class!")
        
        def combine_stock_data(self, data_dict):
            """Fallback implementation of combine_stock_data."""
            try:
                logger.debug(f"Using fallback combine_stock_data with {len(data_dict)} symbols")
                if not data_dict:
                    return pd.DataFrame()

                combined_df = pd.DataFrame()
                for symbol, df in data_dict.items():
                    if df is None or df.empty:
                        logger.warning(f"Empty or None DataFrame for {symbol}")
                        continue

                    price_col = next((col for col in ['close', 'Close'] if col in df.columns), None)
                    if price_col:
                        combined_df[symbol] = df[price_col]
                    else:
                        logger.warning(f"No close price column found for {symbol}")

                return combined_df.sort_index() if not combined_df.empty else pd.DataFrame()
            except Exception as e:
                logger.error(f"Error in combine_stock_data: {e}")
                return pd.DataFrame()

        # Add the method to the data_manager instance
        try:
            import types
            data_manager.combine_stock_data = types.MethodType(combine_stock_data, data_manager)
            logger.info("Added fallback combine_stock_data method to DataManager instance")
        except Exception as e:
            logger.error(f"Error adding fallback combine_stock_data method: {e}")

    logger.info("Successfully applied patches to DataManager")

