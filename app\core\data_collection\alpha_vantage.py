"""
Alpha Vantage data collector implementation.
"""
import asyncio
import os
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional, Union

import aiohttp
import pandas as pd
from aiolimiter import AsyncLimiter

from app.core.data_collection.base import BaseDataCollector
from app.utils.config import get_settings
from app.utils.constants import ALPHA_VANTAGE_RATE_LIMIT
from app.utils.logging import logger


class AlphaVantageCollector(BaseDataCollector):
    """
    Alpha Vantage data collector.
    
    This class provides methods to fetch financial data from Alpha Vantage API.
    """
    
    def __init__(self):
        """Initialize the Alpha Vantage collector."""
        logger.info("Initializing Alpha Vantage collector")
        
        # Get API key from environment variable or settings
        settings = get_settings()
        self.api_key = settings.data_sources["alpha_vantage"].get("api_key", "")
        if not self.api_key:
            self.api_key = os.getenv("ALPHA_VANTAGE_API_KEY", "")
        
        if not self.api_key:
            logger.warning("Alpha Vantage API key not found")
        
        # Set base URL
        self.base_url = "https://www.alphavantage.co/query"
        
        # Set rate limiter (Alpha Vantage free tier allows 5 requests per minute)
        settings = get_settings()
        # Debug print to check data_sources keys
        print(f"Data sources keys: {list(settings.data_sources.keys())}")
        rate_limit = settings.data_sources['alpha_vantage']['rate_limit'] or ALPHA_VANTAGE_RATE_LIMIT
        self.limiter = AsyncLimiter(rate_limit, 60)  # requests per minute
    
    async def get_stock_data(
        self,
        symbol: str,
        start_date: date,
        end_date: date,
        interval: str = "1d"
    ) -> pd.DataFrame:
        """
        Get historical stock data for a symbol.
        
        Args:
            symbol: The stock symbol
            start_date: Start date for historical data
            end_date: End date for historical data
            interval: Data interval (e.g., '1d', '1wk', '1mo')
            
        Returns:
            DataFrame with historical stock data
        """
        logger.debug(f"Getting stock data for {symbol} from {start_date} to {end_date}")
        
        if not self.api_key:
            logger.error("Alpha Vantage API key not set")
            return pd.DataFrame()
        
        # Map interval to Alpha Vantage format
        av_interval = self._map_interval(interval)
        
        # Determine function based on interval
        if av_interval in ["1min", "5min", "15min", "30min", "60min"]:
            function = "TIME_SERIES_INTRADAY"
            time_key = f"Time Series ({av_interval})"
        elif av_interval == "daily":
            function = "TIME_SERIES_DAILY_ADJUSTED"
            time_key = "Time Series (Daily)"
        elif av_interval == "weekly":
            function = "TIME_SERIES_WEEKLY_ADJUSTED"
            time_key = "Weekly Adjusted Time Series"
        elif av_interval == "monthly":
            function = "TIME_SERIES_MONTHLY_ADJUSTED"
            time_key = "Monthly Adjusted Time Series"
        else:
            logger.error(f"Unsupported interval: {interval}")
            return pd.DataFrame()
        
        # Prepare parameters
        params = {
            "function": function,
            "symbol": symbol,
            "apikey": self.api_key,
            "outputsize": "full"
        }
        
        # Add interval parameter for intraday data
        if function == "TIME_SERIES_INTRADAY":
            params["interval"] = av_interval
        
        try:
            # Respect rate limit
            async with self.limiter:
                # Make API request
                async with aiohttp.ClientSession() as session:
                    async with session.get(self.base_url, params=params) as response:
                        if response.status != 200:
                            logger.error(f"Error fetching data: {response.status}")
                            return pd.DataFrame()
                        
                        data = await response.json()
                
                # Check for error messages
                if "Error Message" in data:
                    logger.error(f"Alpha Vantage API error: {data['Error Message']}")
                    return pd.DataFrame()
                
                # Check if time series data exists
                if time_key not in data:
                    logger.error(f"No time series data found for {symbol}")
                    return pd.DataFrame()
                
                # Convert to DataFrame
                df = pd.DataFrame.from_dict(data[time_key], orient="index")
                
                # Convert index to datetime
                df.index = pd.to_datetime(df.index)
                
                # Sort by date
                df = df.sort_index()
                
                # Filter by date range
                df = df[(df.index.date >= start_date) & (df.index.date <= end_date)]
                
                # Rename columns
                column_map = {
                    "1. open": "open",
                    "2. high": "high",
                    "3. low": "low",
                    "4. close": "close",
                    "5. adjusted close": "adjusted_close",
                    "5. volume": "volume",
                    "6. volume": "volume",
                    "7. dividend amount": "dividend",
                    "8. split coefficient": "split"
                }
                
                df = df.rename(columns=column_map)
                
                # Convert numeric columns
                for col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors="coerce")
                
                # Reset index to make date a column
                df = df.reset_index()
                df = df.rename(columns={"index": "date"})
                
                return df
                
        except Exception as e:
            logger.error(f"Error fetching data for {symbol}: {str(e)}")
            return pd.DataFrame()
    
    async def get_multiple_stock_data(
        self,
        symbols: List[str],
        start_date: date,
        end_date: date,
        interval: str = "1d"
    ) -> Dict[str, pd.DataFrame]:
        """
        Get historical stock data for multiple symbols.
        
        Args:
            symbols: List of stock symbols
            start_date: Start date for historical data
            end_date: End date for historical data
            interval: Data interval (e.g., '1d', '1wk', '1mo')
            
        Returns:
            Dictionary mapping symbols to DataFrames with historical stock data
        """
        logger.debug(f"Getting stock data for {len(symbols)} symbols")
        
        result = {}
        
        # Alpha Vantage doesn't have a batch API, so we need to fetch each symbol individually
        for symbol in symbols:
            df = await self.get_stock_data(symbol, start_date, end_date, interval)
            result[symbol] = df
        
        return result
    
    async def get_company_info(self, symbol: str) -> Dict:
        """
        Get company information for a symbol.
        
        Args:
            symbol: The stock symbol
            
        Returns:
            Dictionary with company information
        """
        logger.debug(f"Getting company info for {symbol}")
        
        if not self.api_key:
            logger.error("Alpha Vantage API key not set")
            return {}
        
        # Prepare parameters
        params = {
            "function": "OVERVIEW",
            "symbol": symbol,
            "apikey": self.api_key
        }
        
        try:
            # Respect rate limit
            async with self.limiter:
                # Make API request
                async with aiohttp.ClientSession() as session:
                    async with session.get(self.base_url, params=params) as response:
                        if response.status != 200:
                            logger.error(f"Error fetching company info: {response.status}")
                            return {}
                        
                        data = await response.json()
                
                # Check for error messages
                if "Error Message" in data:
                    logger.error(f"Alpha Vantage API error: {data['Error Message']}")
                    return {}
                
                # Check if data is empty
                if not data or "Symbol" not in data:
                    logger.error(f"No company info found for {symbol}")
                    return {}
                
                # Map Alpha Vantage fields to our standard format
                info = {
                    "symbol": data.get("Symbol"),
                    "name": data.get("Name"),
                    "description": data.get("Description"),
                    "exchange": data.get("Exchange"),
                    "currency": data.get("Currency"),
                    "country": data.get("Country"),
                    "sector": data.get("Sector"),
                    "industry": data.get("Industry"),
                    "marketCap": float(data.get("MarketCapitalization", 0)),
                    "peRatio": float(data.get("PERatio", 0)),
                    "dividendYield": float(data.get("DividendYield", 0)) * 100,  # Convert to percentage
                    "52WeekHigh": float(data.get("52WeekHigh", 0)),
                    "52WeekLow": float(data.get("52WeekLow", 0)),
                    "50DayMA": float(data.get("50DayMovingAverage", 0)),
                    "200DayMA": float(data.get("200DayMovingAverage", 0)),
                    "beta": float(data.get("Beta", 0))
                }
                
                return info
                
        except Exception as e:
            logger.error(f"Error fetching company info for {symbol}: {str(e)}")
            return {}
    
    async def search_symbols(self, query: str) -> List[Dict]:
        """
        Search for symbols matching a query.
        
        Args:
            query: The search query
            
        Returns:
            List of dictionaries with symbol information
        """
        logger.debug(f"Searching for symbols matching '{query}'")
        
        if not self.api_key:
            logger.error("Alpha Vantage API key not set")
            return []
        
        # Prepare parameters
        params = {
            "function": "SYMBOL_SEARCH",
            "keywords": query,
            "apikey": self.api_key
        }
        
        try:
            # Respect rate limit
            async with self.limiter:
                # Make API request
                async with aiohttp.ClientSession() as session:
                    async with session.get(self.base_url, params=params) as response:
                        if response.status != 200:
                            logger.error(f"Error searching symbols: {response.status}")
                            return []
                        
                        data = await response.json()
                
                # Check for error messages
                if "Error Message" in data:
                    logger.error(f"Alpha Vantage API error: {data['Error Message']}")
                    return []
                
                # Check if data is empty
                if "bestMatches" not in data:
                    logger.error(f"No matches found for '{query}'")
                    return []
                
                # Process results
                results = []
                for match in data["bestMatches"]:
                    results.append({
                        "symbol": match.get("1. symbol"),
                        "name": match.get("2. name"),
                        "type": match.get("3. type"),
                        "region": match.get("4. region"),
                        "marketOpen": match.get("5. marketOpen"),
                        "marketClose": match.get("6. marketClose"),
                        "timezone": match.get("7. timezone"),
                        "currency": match.get("8. currency"),
                        "matchScore": float(match.get("9. matchScore", 0))
                    })
                
                return results
                
        except Exception as e:
            logger.error(f"Error searching symbols for '{query}': {str(e)}")
            return []
    
    def _map_interval(self, interval: str) -> str:
        """
        Map interval to Alpha Vantage format.
        
        Args:
            interval: Interval in our format ('1d', '1wk', '1mo')
            
        Returns:
            Interval in Alpha Vantage format
        """
        interval_map = {
            "1min": "1min",
            "5min": "5min",
            "15min": "15min",
            "30min": "30min",
            "60min": "60min",
            "1h": "60min",
            "1d": "daily",
            "daily": "daily",
            "1wk": "weekly",
            "weekly": "weekly",
            "1mo": "monthly",
            "monthly": "monthly"
        }
        
        return interval_map.get(interval, "daily")

