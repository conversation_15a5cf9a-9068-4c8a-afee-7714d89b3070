"""
Portfolio visualization components for Streamlit.

This module provides functions to create various visualizations for portfolio analysis,
including asset allocation charts, efficient frontier plots, and performance charts.
"""
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots


def create_asset_allocation_chart(
    weights: Dict[str, float],
    chart_type: str = "pie",
    title: str = "Portfolio Allocation"
) -> go.Figure:
    """
    Create an asset allocation chart.

    Args:
        weights: Dictionary mapping assets to weights
        chart_type: Type of chart ('pie', 'treemap', 'bar')
        title: Chart title

    Returns:
        Plotly figure
    """
    # Create a DataFrame for the weights
    weights_df = pd.DataFrame({
        "Asset": list(weights.keys()),
        "Weight": list(weights.values())
    })

    # Sort by weight (descending)
    weights_df = weights_df.sort_values("Weight", ascending=False)

    # Create the chart based on the specified type
    if chart_type == "pie":
        fig = px.pie(
            weights_df,
            values="Weight",
            names="Asset",
            title=title,
            hole=0.4
        )

        # Update layout
        fig.update_layout(
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=-0.2,
                xanchor="center",
                x=0.5
            )
        )
    elif chart_type == "treemap":
        fig = px.treemap(
            weights_df,
            path=["Asset"],
            values="Weight",
            title=title,
            color="Weight",
            color_continuous_scale="Viridis"
        )

        # Update layout
        fig.update_layout(
            coloraxis_showscale=True
        )
    elif chart_type == "bar":
        fig = px.bar(
            weights_df,
            x="Asset",
            y="Weight",
            title=title,
            color="Weight",
            color_continuous_scale="Viridis"
        )

        # Update layout
        fig.update_layout(
            xaxis_title="Asset",
            yaxis_title="Weight",
            coloraxis_showscale=True
        )
    else:
        # Default to pie chart
        fig = px.pie(
            weights_df,
            values="Weight",
            names="Asset",
            title=title,
            hole=0.4
        )

    return fig


def create_efficient_frontier_chart(
    frontier_df: pd.DataFrame,
    key_portfolios: Optional[pd.DataFrame] = None,
    assets_df: Optional[pd.DataFrame] = None,
    title: str = "Efficient Frontier"
) -> go.Figure:
    """
    Create an efficient frontier chart.

    Args:
        frontier_df: DataFrame with efficient frontier points (volatility, return)
        key_portfolios: DataFrame with key portfolios (volatility, return, portfolio)
        assets_df: DataFrame with individual assets (volatility, return, portfolio)
        title: Chart title

    Returns:
        Plotly figure
    """
    # Create figure
    fig = go.Figure()

    # Add efficient frontier line
    fig.add_trace(
        go.Scatter(
            x=frontier_df["volatility"],
            y=frontier_df["return"],
            mode="lines",
            name="Efficient Frontier",
            line=dict(color="blue", width=2)
        )
    )

    # Add key portfolios if provided
    if key_portfolios is not None:
        fig.add_trace(
            go.Scatter(
                x=key_portfolios["volatility"],
                y=key_portfolios["return"],
                mode="markers+text",
                marker=dict(size=10, color="red"),
                text=key_portfolios["portfolio"],
                textposition="top center",
                name="Key Portfolios"
            )
        )

    # Add individual assets if provided
    if assets_df is not None:
        fig.add_trace(
            go.Scatter(
                x=assets_df["volatility"],
                y=assets_df["return"],
                mode="markers+text",
                marker=dict(size=8, color="green"),
                text=assets_df["portfolio"],
                textposition="top center",
                name="Individual Assets"
            )
        )

    # Update layout
    fig.update_layout(
        title=title,
        xaxis_title="Annual Volatility",
        yaxis_title="Annual Return",
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )

    return fig


def create_risk_metrics_chart(
    metrics: Dict[str, float],
    title: str = "Portfolio Risk Metrics"
) -> go.Figure:
    """
    Create a risk metrics chart.

    Args:
        metrics: Dictionary with risk metrics
        title: Chart title

    Returns:
        Plotly figure
    """
    # Create a DataFrame for the metrics
    metrics_df = pd.DataFrame({
        "Metric": list(metrics.keys()),
        "Value": list(metrics.values())
    })

    # Create the chart
    fig = px.bar(
        metrics_df,
        x="Metric",
        y="Value",
        title=title,
        color="Value",
        color_continuous_scale="RdYlGn"
    )

    # Update layout
    fig.update_layout(
        xaxis_title="Metric",
        yaxis_title="Value",
        coloraxis_showscale=False
    )

    return fig


def create_drawdown_chart(
    drawdown_df: pd.DataFrame,
    title: str = "Portfolio Drawdown"
) -> go.Figure:
    """
    Create a drawdown chart.

    Args:
        drawdown_df: DataFrame with drawdown data
        title: Chart title

    Returns:
        Plotly figure
    """
    # Create figure with secondary y-axis
    fig = make_subplots(specs=[[{"secondary_y": True}]])

    # Add cumulative returns
    fig.add_trace(
        go.Scatter(
            x=drawdown_df.index,
            y=drawdown_df["cumulative_returns"],
            mode="lines",
            name="Cumulative Returns",
            line=dict(color="green")
        ),
        secondary_y=True
    )

    # Add drawdown
    fig.add_trace(
        go.Scatter(
            x=drawdown_df.index,
            y=drawdown_df["drawdown"],
            mode="lines",
            name="Drawdown",
            line=dict(color="red")
        ),
        secondary_y=False
    )

    # Add a horizontal line at 0
    fig.add_shape(
        type="line",
        x0=drawdown_df.index[0],
        y0=0,
        x1=drawdown_df.index[-1],
        y1=0,
        line=dict(color="black", width=1, dash="dash"),
        yref="y"
    )

    # Update layout
    fig.update_layout(
        title=title,
        xaxis_title="Date",
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )

    # Update y-axes
    fig.update_yaxes(title_text="Drawdown", secondary_y=False)
    fig.update_yaxes(title_text="Cumulative Returns", secondary_y=True)

    return fig


def create_rebalancing_chart(
    weights_over_time: pd.DataFrame,
    rebalance_dates: List[pd.Timestamp],
    title: str = "Portfolio Weights Over Time"
) -> go.Figure:
    """
    Create a chart showing portfolio weights over time with rebalancing events.

    Args:
        weights_over_time: DataFrame with weights over time (index=dates, columns=assets)
        rebalance_dates: List of dates when rebalancing occurred
        title: Chart title

    Returns:
        Plotly figure
    """
    # Create figure
    fig = go.Figure()

    # Add a trace for each asset
    for asset in weights_over_time.columns:
        fig.add_trace(
            go.Scatter(
                x=weights_over_time.index,
                y=weights_over_time[asset],
                mode="lines",
                name=asset,
                stackgroup="one"  # This creates a stacked area chart
            )
        )

    # Add vertical lines for rebalancing events
    for rebalance_date in rebalance_dates:
        fig.add_shape(
            type="line",
            x0=rebalance_date,
            y0=0,
            x1=rebalance_date,
            y1=1,
            line=dict(color="black", width=1, dash="dash")
        )

    # Update layout
    fig.update_layout(
        title=title,
        xaxis_title="Date",
        yaxis_title="Weight",
        yaxis=dict(
            tickformat=".0%",
            range=[0, 1]
        ),
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )

    return fig


def create_discrete_allocation_chart(
    allocation: Dict[str, int],
    prices: Dict[str, float],
    title: str = "Discrete Allocation"
) -> go.Figure:
    """
    Create a chart showing the discrete allocation of a portfolio.

    Args:
        allocation: Dictionary mapping assets to number of shares
        prices: Dictionary mapping assets to prices
        title: Chart title

    Returns:
        Plotly figure
    """
    # Calculate the value of each position
    values = {asset: allocation[asset] * prices.get(asset, 0) for asset in allocation}

    # Create a DataFrame for the allocation
    allocation_df = pd.DataFrame({
        "Asset": list(allocation.keys()),
        "Shares": list(allocation.values()),
        "Price": [prices.get(asset, 0) for asset in allocation],
        "Value": list(values.values())
    })

    # Sort by value (descending)
    allocation_df = allocation_df.sort_values("Value", ascending=False)

    # Create the chart
    fig = px.bar(
        allocation_df,
        x="Asset",
        y="Value",
        title=title,
        color="Asset",
        hover_data=["Shares", "Price"]
    )

    # Update layout
    fig.update_layout(
        xaxis_title="Asset",
        yaxis_title="Value ($)",
        showlegend=False
    )

    return fig
