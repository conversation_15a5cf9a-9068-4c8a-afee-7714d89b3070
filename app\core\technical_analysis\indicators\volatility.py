"""
Volatility indicators implementation.
"""
from typing import Dict, List, Optional, Union

import numpy as np
import pandas as pd
import pandas_ta as ta

from app.core.technical_analysis.indicators.base import BaseIndicator
from app.utils.logging import logger


class BollingerBands(BaseIndicator):
    """
    Bollinger Bands indicator.
    """
    
    def __init__(self):
        """Initialize the Bollinger Bands indicator."""
        super().__init__("Bollinger Bands")
    
    def calculate(
        self,
        data: pd.DataFrame,
        period: int = 20,
        std_dev: float = 2.0,
        column: str = "close",
        **kwargs
    ) -> pd.DataFrame:
        """
        Calculate the Bollinger Bands indicator.
        
        Args:
            data: DataFrame with OHLCV data
            period: Period for the moving average
            std_dev: Number of standard deviations
            column: Column to use for calculation
            **kwargs: Additional parameters
            
        Returns:
            DataFrame with the indicator values
        """
        # Validate data
        if not self.validate_data(data, [column]):
            logger.warning(f"Invalid data for {self.name} calculation")
            return data
        
        # Make a copy of the data
        result = data.copy()
        
        # Calculate Bollinger Bands
        bbands = ta.bbands(
            result[column],
            length=period,
            std=std_dev
        )
        
        # Add Bollinger Bands columns to the result
        result["bb_upper"] = bbands[f"BBU_{period}_{std_dev}"]
        result["bb_middle"] = bbands[f"BBM_{period}_{std_dev}"]
        result["bb_lower"] = bbands[f"BBL_{period}_{std_dev}"]
        
        # Calculate Bandwidth and %B
        result["bb_bandwidth"] = (result["bb_upper"] - result["bb_lower"]) / result["bb_middle"]
        result["bb_percent_b"] = (result[column] - result["bb_lower"]) / (result["bb_upper"] - result["bb_lower"])
        
        return result
    
    def get_signal(
        self,
        data: pd.DataFrame,
        column: str = "close",
        **kwargs
    ) -> pd.DataFrame:
        """
        Get trading signals from the Bollinger Bands indicator.
        
        Args:
            data: DataFrame with indicator values
            column: Column to use for comparison
            **kwargs: Additional parameters
            
        Returns:
            DataFrame with trading signals
        """
        # Calculate the indicator if not already calculated
        if "bb_upper" not in data.columns or "bb_lower" not in data.columns:
            data = self.calculate(data, column=column, **kwargs)
        
        # Make a copy of the data
        result = data.copy()
        
        # Generate signals
        # 1 = buy, -1 = sell, 0 = neutral
        result["bb_signal"] = 0
        
        # Price crosses below lower band = buy signal
        result.loc[
            (result[column] < result["bb_lower"]) &
            (result[column].shift(1) >= result["bb_lower"].shift(1)),
            "bb_signal"
        ] = 1
        
        # Price crosses above upper band = sell signal
        result.loc[
            (result[column] > result["bb_upper"]) &
            (result[column].shift(1) <= result["bb_upper"].shift(1)),
            "bb_signal"
        ] = -1
        
        return result


class ATR(BaseIndicator):
    """
    Average True Range (ATR) indicator.
    """
    
    def __init__(self):
        """Initialize the ATR indicator."""
        super().__init__("ATR")
    
    def calculate(
        self,
        data: pd.DataFrame,
        period: int = 14,
        **kwargs
    ) -> pd.DataFrame:
        """
        Calculate the ATR indicator.
        
        Args:
            data: DataFrame with OHLCV data
            period: Period for the ATR calculation
            **kwargs: Additional parameters
            
        Returns:
            DataFrame with the indicator values
        """
        # Validate data
        if not self.validate_data(data, ["high", "low", "close"]):
            logger.warning(f"Invalid data for {self.name} calculation")
            return data
        
        # Make a copy of the data
        result = data.copy()
        
        # Calculate ATR
        result[f"atr_{period}"] = ta.atr(
            result["high"],
            result["low"],
            result["close"],
            length=period
        )
        
        # Calculate Normalized ATR (NATR)
        result[f"natr_{period}"] = 100 * result[f"atr_{period}"] / result["close"]
        
        return result
    
    def get_signal(
        self,
        data: pd.DataFrame,
        period: int = 14,
        threshold: float = 0.5,
        **kwargs
    ) -> pd.DataFrame:
        """
        Get trading signals from the ATR indicator.
        
        Args:
            data: DataFrame with indicator values
            period: Period for the ATR calculation
            threshold: Threshold for volatility
            **kwargs: Additional parameters
            
        Returns:
            DataFrame with trading signals
        """
        # Calculate the indicator if not already calculated
        atr_column = f"atr_{period}"
        if atr_column not in data.columns:
            data = self.calculate(data, period=period, **kwargs)
        
        # Make a copy of the data
        result = data.copy()
        
        # Generate signals based on ATR changes
        # 1 = increasing volatility, -1 = decreasing volatility, 0 = stable
        result[f"{atr_column}_signal"] = 0
        
        # Calculate ATR percent change
        result[f"{atr_column}_pct_change"] = result[atr_column].pct_change()
        
        # Increasing volatility
        result.loc[result[f"{atr_column}_pct_change"] > threshold, f"{atr_column}_signal"] = 1
        
        # Decreasing volatility
        result.loc[result[f"{atr_column}_pct_change"] < -threshold, f"{atr_column}_signal"] = -1
        
        return result
