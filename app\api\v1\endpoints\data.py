"""
Data API endpoints.
"""
from datetime import date
from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query

from app.api.v1.models.requests import (CompanyInfoRequest, MultipleStockDataRequest,
                                      SearchSymbolsRequest, StockDataRequest)
from app.api.v1.models.responses import (CompanyInfoResponse, MultipleStockDataResponse,
                                       SearchSymbolsResponse, StockDataResponse)
from app.core.data_collection.data_manager import data_manager
from app.utils.logging import logger

# Create router
router = APIRouter()


@router.post("/stock", response_model=StockDataResponse)
async def get_stock_data(request: StockDataRequest):
    """
    Get historical stock data for a symbol.
    """
    try:
        # Get stock data
        df = await data_manager.get_stock_data(
            request.symbol,
            request.start_date,
            request.end_date,
            request.interval
        )
        
        # Check if data was retrieved
        if df.empty:
            raise HTTPException(status_code=404, detail=f"No data found for {request.symbol}")
        
        # Convert DataFrame to list of dictionaries
        data = df.to_dict(orient="records")
        
        # Return response
        return {
            "symbol": request.symbol,
            "data": data
        }
    except Exception as e:
        logger.error(f"Error getting stock data: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/stocks", response_model=MultipleStockDataResponse)
async def get_multiple_stock_data(request: MultipleStockDataRequest):
    """
    Get historical stock data for multiple symbols.
    """
    try:
        # Get stock data
        stock_data = await data_manager.get_multiple_stock_data(
            request.symbols,
            request.start_date,
            request.end_date,
            request.interval
        )
        
        # Check if data was retrieved
        if not stock_data:
            raise HTTPException(status_code=404, detail="No data found for any symbol")
        
        # Convert DataFrames to lists of dictionaries
        data = {}
        for symbol, df in stock_data.items():
            if not df.empty:
                data[symbol] = df.to_dict(orient="records")
        
        # Return response
        return {
            "data": data
        }
    except Exception as e:
        logger.error(f"Error getting multiple stock data: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/company", response_model=CompanyInfoResponse)
async def get_company_info(request: CompanyInfoRequest):
    """
    Get company information for a symbol.
    """
    try:
        # Get company info
        info = await data_manager.get_company_info(request.symbol)
        
        # Check if info was retrieved
        if not info:
            raise HTTPException(status_code=404, detail=f"No information found for {request.symbol}")
        
        # Return response
        return {
            "symbol": request.symbol,
            "info": info
        }
    except Exception as e:
        logger.error(f"Error getting company info: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/search", response_model=SearchSymbolsResponse)
async def search_symbols(request: SearchSymbolsRequest):
    """
    Search for symbols matching a query.
    """
    try:
        # Search for symbols
        results = await data_manager.search_symbols(request.query)
        
        # Check if results were found
        if not results:
            return {"results": []}
        
        # Return response
        return {
            "results": results
        }
    except Exception as e:
        logger.error(f"Error searching symbols: {e}")
        raise HTTPException(status_code=500, detail=str(e))
