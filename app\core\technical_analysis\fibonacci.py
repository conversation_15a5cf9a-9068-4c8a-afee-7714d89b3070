"""
Fibonacci retracement and extension technical analysis implementation.
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union, Tuple

from app.utils.logging import logger


def calculate_fibonacci_retracement(
    high_price: float,
    low_price: float,
    is_uptrend: bool = True
) -> Dict[str, float]:
    """
    Calculate Fibonacci retracement levels.

    Args:
        high_price: The highest price in the trend
        low_price: The lowest price in the trend
        is_uptrend: Whether the trend is up (True) or down (False)

    Returns:
        Dictionary with Fibonacci retracement levels
    """
    try:
        # Standard Fibonacci retracement levels
        levels = [0.0, 0.236, 0.382, 0.5, 0.618, 0.786, 1.0]

        # Calculate price difference
        price_diff = high_price - low_price

        # Calculate retracement levels
        if is_uptrend:
            # For uptrend, retracements are calculated from high to low
            retracement_levels = {
                f"level_{level}": high_price - (price_diff * level)
                for level in levels
            }
        else:
            # For downtrend, retracements are calculated from low to high
            retracement_levels = {
                f"level_{level}": low_price + (price_diff * level)
                for level in levels
            }

        # Add level names for clarity
        level_names = {
            "level_0.0": "0% (Trend High)" if is_uptrend else "0% (Trend Low)",
            "level_0.236": "23.6% Retracement",
            "level_0.382": "38.2% Retracement",
            "level_0.5": "50% Retracement",
            "level_0.618": "61.8% Retracement",
            "level_0.786": "78.6% Retracement",
            "level_1.0": "100% Retracement (Trend Low)" if is_uptrend else "100% Retracement (Trend High)"
        }

        # Add names to the result
        result = {
            "high_price": high_price,
            "low_price": low_price,
            "is_uptrend": is_uptrend,
            "levels": retracement_levels,
            "level_names": level_names
        }

        return result

    except Exception as e:
        logger.error(f"Error calculating Fibonacci retracement: {str(e)}")
        return {
            "high_price": high_price,
            "low_price": low_price,
            "is_uptrend": is_uptrend,
            "levels": {},
            "level_names": {}
        }


def calculate_fibonacci_extension(
    high_price: float,
    low_price: float,
    is_uptrend: bool = True
) -> Dict[str, float]:
    """
    Calculate Fibonacci extension levels.

    Args:
        high_price: The highest price in the trend
        low_price: The lowest price in the trend
        is_uptrend: Whether the trend is up (True) or down (False)

    Returns:
        Dictionary with Fibonacci extension levels
    """
    try:
        # Standard Fibonacci extension levels
        levels = [0.0, 0.618, 1.0, 1.618, 2.0, 2.618, 3.618, 4.236]

        # Calculate price difference
        price_diff = high_price - low_price

        # Calculate extension levels
        if is_uptrend:
            # For uptrend, extensions are calculated above the high
            extension_levels = {
                f"level_{level}": high_price + (price_diff * level)
                for level in levels
            }
            # Add the low price as the starting point
            extension_levels["level_-1.0"] = low_price
        else:
            # For downtrend, extensions are calculated below the low
            extension_levels = {
                f"level_{level}": low_price - (price_diff * level)
                for level in levels
            }
            # Add the high price as the starting point
            extension_levels["level_-1.0"] = high_price

        # Add level names for clarity
        level_names = {
            "level_-1.0": "Starting Point (Trend Low)" if is_uptrend else "Starting Point (Trend High)",
            "level_0.0": "0% (Trend High)" if is_uptrend else "0% (Trend Low)",
            "level_0.618": "61.8% Extension",
            "level_1.0": "100% Extension",
            "level_1.618": "161.8% Extension",
            "level_2.0": "200% Extension",
            "level_2.618": "261.8% Extension",
            "level_3.618": "361.8% Extension",
            "level_4.236": "423.6% Extension"
        }

        # Add names to the result
        result = {
            "high_price": high_price,
            "low_price": low_price,
            "is_uptrend": is_uptrend,
            "levels": extension_levels,
            "level_names": level_names
        }

        return result

    except Exception as e:
        logger.error(f"Error calculating Fibonacci extension: {str(e)}")
        return {
            "high_price": high_price,
            "low_price": low_price,
            "is_uptrend": is_uptrend,
            "levels": {},
            "level_names": {}
        }


def find_swing_points(
    df: pd.DataFrame,
    high_col: str = 'High',
    low_col: str = 'Low',
    window: int = 5
) -> Tuple[List[Dict], List[Dict]]:
    """
    Find swing high and swing low points in price data.

    Args:
        df: DataFrame with price data
        high_col: Name of the high price column
        low_col: Name of the low price column
        window: Window size for identifying swing points

    Returns:
        Tuple of (swing_highs, swing_lows) where each is a list of dictionaries
        with 'index', 'price', and 'date' keys
    """
    try:
        # Check if dataframe is empty
        if df.empty:
            logger.warning("Empty dataframe provided to find_swing_points")
            return [], []

        # Check if required columns exist
        if high_col not in df.columns or low_col not in df.columns:
            logger.error(f"Required columns not found in dataframe: {high_col} or {low_col}")
            return [], []

        # Make a copy of the dataframe to avoid modifying the original
        df_copy = df.copy()

        # Ensure high and low columns are numeric
        try:
            df_copy[high_col] = pd.to_numeric(df_copy[high_col], errors='coerce')
            df_copy[low_col] = pd.to_numeric(df_copy[low_col], errors='coerce')
        except Exception as e:
            logger.warning(f"Error converting columns to numeric: {str(e)}")
            return [], []

        # Check for NaN values
        if df_copy[high_col].isna().all() or df_copy[low_col].isna().all():
            logger.warning(f"Columns contain all NaN values")
            return [], []

        # Drop NaN values to avoid comparison issues
        df_copy = df_copy.dropna(subset=[high_col, low_col])

        # Check if we have enough data after dropping NaNs
        if len(df_copy) < 2 * window + 1:
            logger.warning(f"Not enough data points after dropping NaNs: {len(df_copy)} < {2 * window + 1}")
            return [], []

        # Initialize lists for swing points
        swing_highs = []
        swing_lows = []

        # Find swing highs
        for i in range(window, len(df_copy) - window):
            try:
                # Check if the current high is higher than all highs in the window before and after
                is_swing_high = True
                current_high = float(df_copy.iloc[i][high_col])

                # Skip NaN values
                if pd.isna(current_high):
                    continue

                for j in range(i - window, i):
                    compare_high = float(df_copy.iloc[j][high_col])
                    if not pd.isna(compare_high) and compare_high > current_high:
                        is_swing_high = False
                        break

                if is_swing_high:
                    for j in range(i + 1, i + window + 1):
                        compare_high = float(df_copy.iloc[j][high_col])
                        if not pd.isna(compare_high) and compare_high > current_high:
                            is_swing_high = False
                            break

                if is_swing_high:
                    # Get the date safely
                    try:
                        date_value = df_copy.index[i]
                    except Exception:
                        date_value = i  # Use index as fallback

                    swing_highs.append({
                        'index': i,
                        'price': current_high,
                        'date': date_value
                    })
            except Exception as e:
                logger.warning(f"Error processing swing high at index {i}: {str(e)}")
                continue

        # Find swing lows
        for i in range(window, len(df_copy) - window):
            try:
                # Check if the current low is lower than all lows in the window before and after
                is_swing_low = True
                current_low = float(df_copy.iloc[i][low_col])

                # Skip NaN values
                if pd.isna(current_low):
                    continue

                for j in range(i - window, i):
                    compare_low = float(df_copy.iloc[j][low_col])
                    if not pd.isna(compare_low) and compare_low < current_low:
                        is_swing_low = False
                        break

                if is_swing_low:
                    for j in range(i + 1, i + window + 1):
                        compare_low = float(df_copy.iloc[j][low_col])
                        if not pd.isna(compare_low) and compare_low < current_low:
                            is_swing_low = False
                            break

                if is_swing_low:
                    # Get the date safely
                    try:
                        date_value = df_copy.index[i]
                    except Exception:
                        date_value = i  # Use index as fallback

                    swing_lows.append({
                        'index': i,
                        'price': current_low,
                        'date': date_value
                    })
            except Exception as e:
                logger.warning(f"Error processing swing low at index {i}: {str(e)}")
                continue

        return swing_highs, swing_lows

    except Exception as e:
        logger.error(f"Error finding swing points: {str(e)}")
        return [], []


def identify_fibonacci_levels(
    df: pd.DataFrame,
    high_col: str = 'High',
    low_col: str = 'Low',
    close_col: str = 'Close',
    window: int = 5
) -> Dict:
    """
    Identify Fibonacci retracement and extension levels based on recent swing points.

    Args:
        df: DataFrame with price data
        high_col: Name of the high price column
        low_col: Name of the low price column
        close_col: Name of the close price column
        window: Window size for identifying swing points

    Returns:
        Dictionary with Fibonacci levels and analysis
    """
    try:
        # Find swing points
        swing_highs, swing_lows = find_swing_points(df, high_col, low_col, window)

        # If no swing points found, return empty result
        if not swing_highs or not swing_lows:
            return {
                'retracement': {},
                'extension': {},
                'current_price': df.iloc[-1][close_col],
                'analysis': 'Insufficient data to identify Fibonacci levels'
            }

        # Sort swing points by recency (most recent first)
        swing_highs.sort(key=lambda x: x['index'], reverse=True)
        swing_lows.sort(key=lambda x: x['index'], reverse=True)

        # Get the most recent swing high and swing low
        recent_high = swing_highs[0]
        recent_low = swing_lows[0]

        # Determine the trend direction
        is_uptrend = recent_high['index'] > recent_low['index']

        # Calculate Fibonacci levels
        if is_uptrend:
            # For uptrend, use the lowest low to the highest high
            # Find the lowest low before the highest high
            lowest_low = None
            for low in swing_lows:
                if low['index'] < recent_high['index']:
                    lowest_low = low
                    break

            if lowest_low is None:
                lowest_low = {'price': df[low_col].min(), 'index': 0}

            retracement = calculate_fibonacci_retracement(
                recent_high['price'],
                lowest_low['price'],
                is_uptrend=True
            )

            extension = calculate_fibonacci_extension(
                recent_high['price'],
                lowest_low['price'],
                is_uptrend=True
            )
        else:
            # For downtrend, use the highest high to the lowest low
            # Find the highest high before the lowest low
            highest_high = None
            for high in swing_highs:
                if high['index'] < recent_low['index']:
                    highest_high = high
                    break

            if highest_high is None:
                highest_high = {'price': df[high_col].max(), 'index': 0}

            retracement = calculate_fibonacci_retracement(
                highest_high['price'],
                recent_low['price'],
                is_uptrend=False
            )

            extension = calculate_fibonacci_extension(
                highest_high['price'],
                recent_low['price'],
                is_uptrend=False
            )

        # Get current price
        current_price = df.iloc[-1][close_col]

        # Analyze current price in relation to Fibonacci levels
        try:
            analysis = analyze_fibonacci_levels(current_price, retracement, extension, is_uptrend)
        except Exception as e:
            logger.error(f"Error analyzing Fibonacci levels: {str(e)}")
            analysis = f"Error analyzing Fibonacci levels: {str(e)}"

        return {
            'retracement': retracement,
            'extension': extension,
            'current_price': current_price,
            'is_uptrend': is_uptrend,
            'analysis': analysis
        }

    except Exception as e:
        logger.error(f"Error identifying Fibonacci levels: {str(e)}")
        return {
            'retracement': {},
            'extension': {},
            'current_price': df.iloc[-1][close_col] if not df.empty else 0,
            'analysis': f'Error: {str(e)}'
        }


def analyze_fibonacci_levels(
    current_price: float,
    retracement: Dict,
    extension: Dict,
    is_uptrend: bool
) -> str:
    """
    Analyze current price in relation to Fibonacci levels.

    Args:
        current_price: Current price
        retracement: Fibonacci retracement levels
        extension: Fibonacci extension levels
        is_uptrend: Whether the trend is up (True) or down (False)

    Returns:
        Analysis text
    """
    try:
        # Ensure current_price is a float
        try:
            current_price = float(current_price)
        except (TypeError, ValueError):
            logger.warning(f"Invalid current_price: {current_price}")
            return "Unable to analyze Fibonacci levels: invalid price."

        # Get retracement levels
        retracement_levels = retracement.get('levels', {})
        retracement_names = retracement.get('level_names', {})

        # Get extension levels
        extension_levels = extension.get('levels', {})
        extension_names = extension.get('level_names', {})

        # Check if we have any levels to analyze
        if not retracement_levels and not extension_levels:
            return "No Fibonacci levels available for analysis."

        # Find the closest retracement level
        closest_retracement = None
        closest_retracement_diff = float('inf')

        for level_key, level_price in retracement_levels.items():
            try:
                # Ensure level_price is a float
                level_price_float = float(level_price)
                diff = abs(current_price - level_price_float)
                if diff < closest_retracement_diff:
                    closest_retracement_diff = diff
                    closest_retracement = level_key
            except (TypeError, ValueError):
                logger.warning(f"Invalid retracement level price: {level_price}")
                continue

        # Find the closest extension level
        closest_extension = None
        closest_extension_diff = float('inf')

        for level_key, level_price in extension_levels.items():
            try:
                # Ensure level_price is a float
                level_price_float = float(level_price)
                diff = abs(current_price - level_price_float)
                if diff < closest_extension_diff:
                    closest_extension_diff = diff
                    closest_extension = level_key
            except (TypeError, ValueError):
                logger.warning(f"Invalid extension level price: {level_price}")
                continue

        # If no valid levels found, return early
        if closest_retracement is None and closest_extension is None:
            return "No valid Fibonacci levels found for analysis."

        # Determine if price is closer to a retracement or extension level
        if closest_retracement is not None and (closest_extension is None or closest_retracement_diff <= closest_extension_diff):
            closest_level = closest_retracement
            try:
                closest_price = float(retracement_levels.get(closest_level, 0))
            except (TypeError, ValueError):
                closest_price = 0
            closest_name = str(retracement_names.get(closest_level, closest_level))
            is_retracement = True
        else:
            closest_level = closest_extension
            try:
                closest_price = float(extension_levels.get(closest_level, 0))
            except (TypeError, ValueError):
                closest_price = 0
            closest_name = str(extension_names.get(closest_level, closest_level))
            is_retracement = False

        # Calculate percentage difference (safely)
        try:
            if closest_price != 0:
                percent_diff = abs(current_price - closest_price) / closest_price * 100
            else:
                percent_diff = 0
        except Exception:
            percent_diff = 0

        # Generate analysis text
        try:
            if is_retracement:
                if percent_diff < 1:
                    analysis = f"Price is at the {closest_name} level ({float(closest_price):.2f})."
                else:
                    if current_price > closest_price:
                        analysis = f"Price is {float(percent_diff):.2f}% above the {closest_name} level ({float(closest_price):.2f})."
                    else:
                        analysis = f"Price is {float(percent_diff):.2f}% below the {closest_name} level ({float(closest_price):.2f})."

                # Add trend context
                if is_uptrend:
                    if "0%" in closest_name or "23.6%" in closest_name:
                        analysis += " The uptrend remains strong."
                    elif "38.2%" in closest_name or "50%" in closest_name:
                        analysis += " This is a common retracement level in an uptrend."
                    elif "61.8%" in closest_name:
                        analysis += " This is a key retracement level. If it holds, the uptrend may continue."
                    elif "78.6%" in closest_name or "100%" in closest_name:
                        analysis += " The uptrend may be weakening or reversing."
                else:
                    if "0%" in closest_name or "23.6%" in closest_name:
                        analysis += " The downtrend remains strong."
                    elif "38.2%" in closest_name or "50%" in closest_name:
                        analysis += " This is a common retracement level in a downtrend."
                    elif "61.8%" in closest_name:
                        analysis += " This is a key retracement level. If it holds, the downtrend may continue."
                    elif "78.6%" in closest_name or "100%" in closest_name:
                        analysis += " The downtrend may be weakening or reversing."
            else:
                if percent_diff < 1:
                    analysis = f"Price is at the {closest_name} level ({float(closest_price):.2f})."
                else:
                    if current_price > closest_price:
                        analysis = f"Price is {float(percent_diff):.2f}% above the {closest_name} level ({float(closest_price):.2f})."
                    else:
                        analysis = f"Price is {float(percent_diff):.2f}% below the {closest_name} level ({float(closest_price):.2f})."

                # Add trend context
                if is_uptrend:
                    if "61.8%" in closest_name:
                        analysis += " This is a common target for an extended uptrend."
                    elif "100%" in closest_name:
                        analysis += " This is a measured move target."
                    elif "161.8%" in closest_name:
                        analysis += " This is a key extension target in an uptrend."
                    elif any(ext in closest_name for ext in ["200%", "261.8%", "361.8%", "423.6%"]):
                        analysis += " This is an extended target, suggesting a strong uptrend."
                else:
                    if "61.8%" in closest_name:
                        analysis += " This is a common target for an extended downtrend."
                    elif "100%" in closest_name:
                        analysis += " This is a measured move target."
                    elif "161.8%" in closest_name:
                        analysis += " This is a key extension target in a downtrend."
                    elif any(ext in closest_name for ext in ["200%", "261.8%", "361.8%", "423.6%"]):
                        analysis += " This is an extended target, suggesting a strong downtrend."
        except Exception as e:
            logger.warning(f"Error formatting analysis text: {str(e)}")
            analysis = f"Price is near a {closest_name} Fibonacci level."

        return analysis

    except Exception as e:
        logger.error(f"Error analyzing Fibonacci levels: {str(e)}")
        return "Unable to analyze Fibonacci levels."
