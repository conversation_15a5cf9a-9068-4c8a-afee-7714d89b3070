# Developer Guide

This guide provides information for developers who want to contribute to or extend the Portfolio Optimization application.

## Project Structure

```
portfolio-optimization/
├── app/                      # Main application code
│   ├── api/                  # FastAPI backend
│   │   ├── v1/               # API version 1
│   │   │   ├── endpoints/    # API endpoints
│   │   │   ├── models/       # Pydantic models
│   │   │   └── router.py     # API router
│   │   ├── main.py           # API entry point
│   │   └── __init__.py
│   ├── core/                 # Core business logic
│   │   ├── data_collection/  # Data collection module
│   │   ├── portfolio/        # Portfolio optimization module
│   │   ├── technical_analysis/ # Technical analysis module
│   │   └── __init__.py
│   ├── db/                   # Database module
│   │   ├── migrations/       # Database migrations
│   │   ├── repositories/     # Data access layer
│   │   ├── database.py       # Database connection
│   │   ├── models.py         # SQLAlchemy models
│   │   └── __init__.py
│   ├── middleware/           # Middleware components
│   ├── services/             # Service layer
│   ├── streamlit/            # Streamlit frontend
│   │   ├── components/       # Reusable UI components
│   │   ├── pages/            # Application pages
│   │   ├── app.py            # Streamlit entry point
│   │   └── __init__.py
│   ├── utils/                # Utility functions
│   ├── settings.py           # Application settings
│   └── __init__.py
├── config/                   # Configuration files
├── data/                     # Data storage
│   ├── cached/               # Cached data
│   ├── historical/           # Historical data
│   └── user/                 # User data
├── deployment/               # Deployment configurations
│   ├── docker/               # Docker configuration
│   ├── kubernetes/           # Kubernetes configuration
│   └── scripts/              # Deployment scripts
├── docs/                     # Documentation
│   ├── api/                  # API documentation
│   ├── developer/            # Developer documentation
│   └── user/                 # User documentation
├── logs/                     # Log files
├── notebooks/                # Jupyter notebooks
├── scripts/                  # Utility scripts
├── tests/                    # Test suite
│   ├── e2e/                  # End-to-end tests
│   ├── integration/          # Integration tests
│   └── unit/                 # Unit tests
├── .env.example              # Example environment variables
├── .gitignore                # Git ignore file
├── main.py                   # Application entry point
├── README.md                 # Project README
├── requirements-dev.txt      # Development dependencies
├── requirements.txt          # Production dependencies
└── setup.py                  # Package setup file
```

## Development Environment Setup

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/portfolio-optimization.git
   cd portfolio-optimization
   ```

2. Create a virtual environment:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install development dependencies:
   ```
   pip install -r requirements-dev.txt
   ```

4. Create a `.env` file from the example:
   ```
   cp .env.example .env
   ```

5. Run the tests to verify your setup:
   ```
   pytest
   ```

## Development Workflow

1. Create a new branch for your feature or bugfix:
   ```
   git checkout -b feature/your-feature-name
   ```

2. Make your changes and write tests

3. Run the tests:
   ```
   pytest
   ```

4. Run the linter:
   ```
   flake8
   ```

5. Format your code:
   ```
   black .
   ```

6. Commit your changes:
   ```
   git add .
   git commit -m "Add your feature description"
   ```

7. Push your branch:
   ```
   git push origin feature/your-feature-name
   ```

8. Create a pull request on GitHub

## Architecture Overview

The application follows a layered architecture:

1. **Presentation Layer**: Streamlit frontend and FastAPI endpoints
2. **Service Layer**: Business logic orchestration
3. **Core Layer**: Domain-specific logic (data collection, technical analysis, portfolio optimization)
4. **Data Access Layer**: Database repositories
5. **Infrastructure Layer**: Database, caching, logging

### Key Components

#### API Layer

The API is built with FastAPI and follows RESTful principles. The main components are:

- **Endpoints**: Handle HTTP requests and responses
- **Models**: Define request and response schemas using Pydantic
- **Router**: Route requests to appropriate endpoints

#### Core Layer

The core layer contains the main business logic:

- **Data Collection**: Fetch financial data from various sources
- **Technical Analysis**: Calculate technical indicators and patterns
- **Portfolio Optimization**: Implement portfolio optimization algorithms

#### Database Layer

The database layer uses SQLAlchemy for ORM:

- **Models**: Define database tables and relationships
- **Repositories**: Provide data access methods
- **Migrations**: Handle database schema changes

#### Frontend Layer

The frontend is built with Streamlit:

- **Pages**: Define application pages
- **Components**: Reusable UI components
- **App**: Main Streamlit application

## Extending the Application

### Adding a New Data Source

1. Create a new class in `app/core/data_collection/` that inherits from `BaseDataCollector`
2. Implement the required methods
3. Register the data source in `app/core/data_collection/data_sources.py`

Example:

```python
from app.core.data_collection.base import BaseDataCollector

class MyDataSource(BaseDataCollector):
    async def get_stock_data(self, symbol, start_date, end_date, interval="1d"):
        # Implementation
        pass
    
    async def get_multiple_stock_data(self, symbols, start_date, end_date, interval="1d"):
        # Implementation
        pass
    
    async def get_company_info(self, symbol):
        # Implementation
        pass
    
    async def search_symbols(self, query):
        # Implementation
        pass

# Register in data_sources.py
data_source_manager.register_data_source("my_source", MyDataSource())
```

### Adding a New Technical Indicator

1. Create a new class in the appropriate file in `app/core/technical_analysis/indicators/` that inherits from `BaseIndicator`
2. Implement the required methods

Example:

```python
from app.core.technical_analysis.indicators.base import BaseIndicator

class MyIndicator(BaseIndicator):
    def __init__(self):
        super().__init__("MyIndicator")
    
    def calculate(self, data, **kwargs):
        # Implementation
        return data
    
    def get_signal(self, data, **kwargs):
        # Implementation
        return data
```

### Adding a New Optimization Strategy

1. Create a new class in `app/core/portfolio/optimization/` that inherits from `OptimizationStrategy`
2. Implement the required methods

Example:

```python
from app.core.portfolio.optimization.base import OptimizationStrategy

class MyOptimizationStrategy(OptimizationStrategy):
    def __init__(self):
        super().__init__("My Strategy")
    
    def optimize(self, returns, **kwargs):
        # Implementation
        return weights
```

### Adding a New API Endpoint

1. Create a new file in `app/api/v1/endpoints/` or add to an existing one
2. Define request and response models in `app/api/v1/models/`
3. Add the endpoint to the router in `app/api/v1/router.py`

Example:

```python
from fastapi import APIRouter, Depends
from app.api.v1.models.requests import MyRequest
from app.api.v1.models.responses import MyResponse
from app.services.my_service import MyService

router = APIRouter()

@router.post("/my-endpoint", response_model=MyResponse)
async def my_endpoint(request: MyRequest, service: MyService = Depends()):
    result = await service.do_something(request.param)
    return MyResponse(result=result)
```

### Adding a New Streamlit Page

1. Create a new file in `app/streamlit/pages/`
2. Implement the page using Streamlit components
3. Add the page to the navigation in `app/streamlit/app.py`

Example:

```python
import streamlit as st
from app.streamlit.components.chart import create_chart

def render():
    st.title("My New Page")
    
    # Page content
    data = get_some_data()
    create_chart(data)
    
    # User interactions
    if st.button("Do Something"):
        result = process_data(data)
        st.success(f"Result: {result}")
```

## Testing

The project uses pytest for testing:

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test interactions between components
- **End-to-End Tests**: Test the entire application flow

### Running Tests

Run all tests:
```
pytest
```

Run specific test category:
```
pytest tests/unit/
pytest tests/integration/
pytest tests/e2e/
```

Run tests with coverage:
```
pytest --cov=app
```

### Writing Tests

Example unit test:

```python
import pytest
from app.core.technical_analysis.indicators.moving_averages import simple_moving_average
import pandas as pd
import numpy as np

def test_simple_moving_average():
    # Arrange
    data = pd.Series([1, 2, 3, 4, 5])
    
    # Act
    result = simple_moving_average(data, window=3)
    
    # Assert
    expected = pd.Series([np.nan, np.nan, 2.0, 3.0, 4.0])
    pd.testing.assert_series_equal(result, expected)
```

## Deployment

### Docker Deployment

Build and run with Docker Compose:
```
docker-compose -f deployment/docker/docker-compose.yml up -d
```

### Kubernetes Deployment

1. Build and push Docker images:
   ```
   docker build -t portfolio-optimization/api:latest -f deployment/docker/Dockerfile .
   docker build -t portfolio-optimization/streamlit:latest -f deployment/docker/Dockerfile .
   ```

2. Apply Kubernetes configurations:
   ```
   kubectl apply -f deployment/kubernetes/
   ```

## Continuous Integration

The project uses GitHub Actions for CI/CD:

- Run tests on pull requests
- Build and push Docker images on merge to main
- Deploy to staging/production environments

## Documentation

- API documentation is generated using OpenAPI/Swagger
- Code documentation follows Google style docstrings
- User documentation is written in Markdown

## Best Practices

1. Follow PEP 8 style guide
2. Write comprehensive docstrings
3. Write tests for new features
4. Keep functions small and focused
5. Use type hints
6. Handle errors gracefully
7. Log important events
8. Use dependency injection
9. Follow the SOLID principles

## Troubleshooting

### Common Issues

1. **Database connection errors**:
   - Check database credentials in `.env`
   - Ensure database server is running

2. **API errors**:
   - Check API logs in `logs/api.log`
   - Verify request format matches API specifications

3. **Streamlit UI issues**:
   - Check browser console for errors
   - Verify Streamlit is running on the correct port

### Debugging

1. Enable debug logging in `.env`:
   ```
   LOG_LEVEL=DEBUG
   ```

2. Use the debugger in your IDE

3. Add print statements for quick debugging

## Resources

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Streamlit Documentation](https://docs.streamlit.io/)
- [SQLAlchemy Documentation](https://docs.sqlalchemy.org/)
- [Pandas Documentation](https://pandas.pydata.org/docs/)
- [NumPy Documentation](https://numpy.org/doc/)
- [SciPy Documentation](https://docs.scipy.org/doc/scipy/)
