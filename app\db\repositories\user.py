"""
User repository for database operations.
"""
from typing import Dict, List, Optional

from sqlalchemy.orm import Session

from app.db.models import User
from app.db.repositories.base import BaseRepository


class UserRepository(BaseRepository[User, Dict, Dict]):
    """
    User repository for database operations.
    """
    
    def __init__(self):
        """Initialize the repository."""
        super().__init__(User)
    
    def get_by_username(self, db: Session, username: str) -> Optional[User]:
        """
        Get a user by username.
        
        Args:
            db: Database session
            username: Username
            
        Returns:
            User if found, None otherwise
        """
        return db.query(self.model).filter(self.model.username == username).first()
    
    def get_by_email(self, db: Session, email: str) -> Optional[User]:
        """
        Get a user by email.
        
        Args:
            db: Database session
            email: Email
            
        Returns:
            User if found, None otherwise
        """
        return db.query(self.model).filter(self.model.email == email).first()
    
    def create_user(
        self,
        db: Session,
        username: str,
        email: str,
        hashed_password: str
    ) -> User:
        """
        Create a new user.
        
        Args:
            db: Database session
            username: Username
            email: Email
            hashed_password: Hashed password
            
        Returns:
            Created user
        """
        user = User(
            username=username,
            email=email,
            hashed_password=hashed_password
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        return user
    
    def update_password(
        self,
        db: Session,
        user_id: int,
        hashed_password: str
    ) -> User:
        """
        Update a user's password.
        
        Args:
            db: Database session
            user_id: User ID
            hashed_password: New hashed password
            
        Returns:
            Updated user
        """
        user = self.get(db, user_id)
        if user:
            user.hashed_password = hashed_password
            db.commit()
            db.refresh(user)
        return user
    
    def deactivate_user(self, db: Session, user_id: int) -> User:
        """
        Deactivate a user.
        
        Args:
            db: Database session
            user_id: User ID
            
        Returns:
            Deactivated user
        """
        user = self.get(db, user_id)
        if user:
            user.is_active = 0
            db.commit()
            db.refresh(user)
        return user
    
    def activate_user(self, db: Session, user_id: int) -> User:
        """
        Activate a user.
        
        Args:
            db: Database session
            user_id: User ID
            
        Returns:
            Activated user
        """
        user = self.get(db, user_id)
        if user:
            user.is_active = 1
            db.commit()
            db.refresh(user)
        return user


# Create a singleton instance
user_repository = UserRepository()
