# Running the Portfolio Optimization Application

This document provides instructions on how to run the Portfolio Optimization application with proper logging.

## Prerequisites

- Python 3.9+
- Virtual environment (recommended)

## Setup

1. Create and activate a virtual environment:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

3. Make sure you have the required API keys in your `.env` file:
   ```
   ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key
   FINANCIAL_MODELING_PREP_API_KEY=your_financial_modeling_prep_api_key
   ```

## Running the Application

### Running the FastAPI Backend with Proper Logging

To run the FastAPI backend with proper logging, use the following command:

```
uvicorn app.api.main:app --reload --log-level debug
```

This will start the FastAPI server with debug-level logging, which will show more detailed information about any errors that occur.

You can also check the logs in the `logs/app.log` file, which will contain detailed error information.

### Running the Streamlit Frontend

To run the Streamlit frontend, use the following command:

```
streamlit run run_streamlit.py
```

or

```
python -m streamlit run app/streamlit/app.py
```

## Troubleshooting

If you encounter issues with the risk analysis section:

1. Check the FastAPI logs for detailed error information
2. Try using the debug endpoints:
   - `/api/v1/risk_metrics/debug` - Test if the risk metrics API is working
   - `/api/v1/risk_metrics/fallback?symbols=AAPL,MSFT,GOOGL&include_portfolio=true` - Get fallback metrics

## Logs

Logs are stored in the `logs/app.log` file. You can view them to diagnose issues:

```
type logs\app.log  # On Windows
cat logs/app.log   # On Linux/Mac
```

To filter logs for specific issues:

```
type logs\app.log | findstr "error"  # On Windows
cat logs/app.log | grep "error"      # On Linux/Mac
```
