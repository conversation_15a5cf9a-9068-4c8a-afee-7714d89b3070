# Getting Started with Portfolio Optimization

Welcome to the Portfolio Optimization application! This guide will help you get started with using the application to optimize your investment portfolio.

## Overview

The Portfolio Optimization application helps you:

1. Collect and analyze historical stock data
2. Apply technical analysis indicators
3. Optimize portfolio allocations using various strategies
4. Backtest portfolio performance
5. Visualize results and generate reports

## Installation

### Option 1: Using Docker (Recommended)

1. Make sure you have <PERSON><PERSON> and <PERSON><PERSON> Compose installed
2. Clone the repository:
   ```
   git clone https://github.com/yourusername/portfolio-optimization.git
   cd portfolio-optimization
   ```
3. Start the application:
   ```
   docker-compose -f deployment/docker/docker-compose.yml up -d
   ```
4. Access the web interface at http://localhost:8501

### Option 2: Manual Installation

1. Make sure you have Python 3.9+ installed
2. Clone the repository:
   ```
   git clone https://github.com/yourusername/portfolio-optimization.git
   cd portfolio-optimization
   ```
3. Create a virtual environment:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```
4. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
5. Start the API server:
   ```
   uvicorn app.api.main:app --reload
   ```
6. In a separate terminal, start the Streamlit interface:
   ```
   streamlit run app/streamlit/app.py
   ```
7. Access the web interface at http://localhost:8501

## Quick Start Guide

### 1. Data Collection

1. Go to the "Data" page
2. Enter stock symbols (e.g., AAPL, MSFT, GOOG)
3. Select date range
4. Click "Fetch Data"

### 2. Technical Analysis

1. Go to the "Analysis" page
2. Select a stock from your collected data
3. Choose indicators to apply (e.g., Moving Averages, RSI)
4. Adjust parameters if needed
5. Click "Analyze"

### 3. Portfolio Optimization

1. Go to the "Portfolio" page
2. Select stocks to include in your portfolio
3. Choose optimization method:
   - Mean-Variance Optimization
   - Black-Litterman Model
   - Hierarchical Risk Parity
   - Equal Weight
   - Risk Parity
4. Set constraints (optional)
5. Click "Optimize"

### 4. Backtesting

1. On the "Portfolio" page, after optimization
2. Set backtesting parameters:
   - Initial investment
   - Rebalancing frequency
3. Click "Backtest"
4. View performance metrics and charts

## Key Features

### Data Collection

- Multiple data sources (Yahoo Finance, etc.)
- Historical price data
- Fundamental data
- Economic indicators

### Technical Analysis

- Moving averages (SMA, EMA, WMA)
- Oscillators (RSI, MACD, Stochastic)
- Volume indicators
- Trend indicators
- Pattern recognition

### Portfolio Optimization

- Mean-Variance Optimization
- Black-Litterman Model
- Hierarchical Risk Parity
- Equal Weight
- Risk Parity
- Custom constraints

### Risk Analysis

- Volatility
- Sharpe Ratio
- Sortino Ratio
- Maximum Drawdown
- Value at Risk (VaR)
- Conditional Value at Risk (CVaR)

### Visualization

- Interactive charts
- Performance comparison
- Efficient frontier
- Correlation matrix
- Risk contribution

## Next Steps

- Check out the [API Documentation](../api/README.md) for programmatic access
- Explore the [Developer Guide](../developer/README.md) for extending the application
- Join our community forum for support and discussions

## Support

If you encounter any issues or have questions, please:

1. Check the [FAQ](faq.md)
2. Search existing issues on GitHub
3. Open a new issue if needed

Happy investing!
