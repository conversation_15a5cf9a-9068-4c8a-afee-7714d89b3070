# Portfolio Optimizer User Guide

This guide provides instructions for using the Portfolio Optimizer application.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Data Exploration](#data-exploration)
3. [Technical Analysis](#technical-analysis)
4. [Portfolio Optimization](#portfolio-optimization)
5. [Backtesting](#backtesting)
6. [Performance Analysis](#performance-analysis)
7. [User Portfolios](#user-portfolios)
8. [Settings](#settings)
9. [Troubleshooting](#troubleshooting)

## Getting Started

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/portfolio-optimization.git
   cd portfolio-optimization
   ```

2. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Set up environment variables:
   - Create a `.env` file in the project root
   - Add the following variables:
     ```
     ALPHA_VANTAGE_API_KEY=your_api_key
     FINANCIAL_MODELING_PREP_API_KEY=your_api_key
     ```

### Running the Application

1. Start the application:
   ```bash
   python run.py
   ```

2. Open your browser and navigate to:
   ```
   http://localhost:8501
   ```

## Data Exploration

The Data Exploration page allows you to explore historical stock data.

### Features

- **Stock Search**: Search for stocks by symbol or name
- **Date Range Selection**: Select a date range for historical data
- **Data Visualization**: View price charts, volume charts, and returns distribution
- **Data Statistics**: View summary statistics for the selected stocks
- **Data Download**: Download the data as CSV or Excel

### How to Use

1. Enter one or more stock symbols (e.g., AAPL, MSFT, GOOGL)
2. Select a date range
3. Click "Load Data"
4. Explore the visualizations and statistics
5. Download the data if needed

## Technical Analysis

The Technical Analysis page allows you to apply technical indicators to stock data.

### Features

- **Technical Indicators**: Apply various technical indicators:
  - Moving Averages (SMA, EMA, WMA)
  - Oscillators (RSI, MACD, Stochastic)
  - Volatility Indicators (Bollinger Bands, ATR)
  - Volume Indicators (OBV, Volume SMA)
- **Chart Patterns**: Identify chart patterns:
  - Support and Resistance
  - Trend Lines
  - Chart Patterns (Head and Shoulders, Double Top/Bottom)
- **Screening**: Screen stocks based on technical criteria

### How to Use

1. Enter a stock symbol
2. Select a date range
3. Choose technical indicators to apply
4. Adjust indicator parameters if needed
5. Click "Apply Indicators"
6. Explore the technical analysis charts
7. Use the screening tool to find stocks meeting specific criteria

## Portfolio Optimization

The Portfolio Optimization page allows you to optimize a portfolio based on historical data.

### Features

- **Optimization Methods**:
  - Maximum Sharpe Ratio
  - Minimum Volatility
  - Maximum Return
  - Risk Parity
  - Equal Weight
- **Constraints**:
  - Minimum and maximum weights
  - Sector constraints
  - Risk constraints
- **Visualization**:
  - Efficient Frontier
  - Risk-Return Scatter Plot
  - Weight Allocation
  - Risk Contribution

### How to Use

1. Enter stock symbols for your portfolio
2. Select a date range for historical data
3. Choose an optimization method
4. Set constraints if needed
5. Click "Optimize Portfolio"
6. Explore the optimization results
7. Save the optimized portfolio

## Backtesting

The Backtesting page allows you to backtest portfolio strategies.

### Features

- **Strategy Selection**:
  - Optimization-based strategies
  - Rebalancing strategies
  - Custom strategies
- **Performance Metrics**:
  - Total Return
  - Annualized Return
  - Volatility
  - Sharpe Ratio
  - Maximum Drawdown
- **Visualization**:
  - Portfolio Value
  - Drawdown
  - Rolling Returns
  - Rolling Volatility

### How to Use

1. Select a portfolio or enter stock symbols
2. Choose a strategy to backtest
3. Set strategy parameters
4. Select a date range
5. Click "Run Backtest"
6. Explore the backtest results
7. Compare with benchmarks

## Performance Analysis

The Performance Analysis page allows you to analyze portfolio performance.

### Features

- **Performance Metrics**:
  - Return Metrics
  - Risk Metrics
  - Risk-Adjusted Metrics
- **Risk Analysis**:
  - Value at Risk (VaR)
  - Conditional VaR (CVaR)
  - Drawdown Analysis
- **Attribution Analysis**:
  - Factor Attribution
  - Sector Attribution
  - Asset Attribution
- **Scenario Analysis**:
  - Historical Scenarios
  - Custom Scenarios
  - Stress Testing

### How to Use

1. Select a portfolio or backtest result
2. Choose performance metrics to analyze
3. Select a date range
4. Click "Analyze Performance"
5. Explore the performance analysis
6. Generate performance reports

## User Portfolios

The User Portfolios page allows you to manage your portfolios.

### Features

- **Portfolio Management**:
  - Create portfolios
  - Edit portfolios
  - Delete portfolios
- **Portfolio Tracking**:
  - Track portfolio performance
  - Compare with benchmarks
  - Set performance alerts
- **Portfolio Sharing**:
  - Share portfolios with others
  - Export portfolios

### How to Use

1. Create a new portfolio or select an existing one
2. Add or remove stocks
3. Set target weights
4. Save the portfolio
5. Track portfolio performance
6. Share the portfolio if needed

## Settings

The Settings page allows you to customize the application.

### Features

- **User Settings**:
  - Change password
  - Update profile
  - Set preferences
- **Application Settings**:
  - Theme selection
  - Chart preferences
  - Data preferences
- **API Settings**:
  - API key management
  - Rate limit monitoring

### How to Use

1. Navigate to the Settings page
2. Adjust settings as needed
3. Save changes

## Troubleshooting

### Common Issues

- **Data Loading Errors**:
  - Check your internet connection
  - Verify API keys
  - Ensure the stock symbols are valid
  - Try a different date range

- **Optimization Errors**:
  - Ensure you have enough historical data
  - Check for highly correlated assets
  - Adjust constraints to be feasible
  - Try a different optimization method

- **Performance Issues**:
  - Reduce the number of stocks
  - Shorten the date range
  - Close other applications
  - Refresh the browser

### Getting Help

- Check the [FAQ](faq.md)
- Report issues on [GitHub](https://github.com/yourusername/portfolio-optimization/issues)
- Contact <NAME_EMAIL>
