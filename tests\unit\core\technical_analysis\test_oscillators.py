"""
Unit tests for oscillator indicators.
"""
import unittest
import pandas as pd
import numpy as np
from pandas.testing import assert_series_equal

from app.core.technical_analysis.indicators.oscillators import (
    relative_strength_index,
    stochastic_oscillator,
    rate_of_change
)


class TestOscillators(unittest.TestCase):
    """Test cases for oscillator indicators."""

    def setUp(self):
        """Set up test data."""
        # Create sample price data
        self.dates = pd.date_range(start='2023-01-01', periods=20, freq='D')
        
        # Create price series with some up and down movements
        self.prices = pd.Series([
            100, 102, 104, 103, 105, 107, 108, 106, 104, 105,
            107, 109, 108, 110, 112, 111, 109, 107, 105, 106
        ], index=self.dates)
        
        # Create DataFrame with OHLC data
        self.df = pd.DataFrame({
            'open': [
                99, 101, 103, 102, 104, 106, 107, 105, 103, 104,
                106, 108, 107, 109, 111, 110, 108, 106, 104, 105
            ],
            'high': [
                101, 103, 105, 104, 106, 108, 109, 107, 105, 106,
                108, 110, 109, 111, 113, 112, 110, 108, 106, 107
            ],
            'low': [
                98, 100, 102, 101, 103, 105, 106, 104, 102, 103,
                105, 107, 106, 108, 110, 109, 107, 105, 103, 104
            ],
            'close': self.prices.values,
            'volume': [
                1000, 1200, 1100, 1300, 1400, 1500, 1600, 1700, 1800, 1900,
                2000, 2100, 2200, 2300, 2400, 2500, 2600, 2700, 2800, 2900
            ]
        }, index=self.dates)

    def test_relative_strength_index(self):
        """Test Relative Strength Index (RSI) calculation."""
        # Calculate RSI with window=14
        result = relative_strength_index(self.prices, window=14)
        
        # RSI should be between 0 and 100
        self.assertTrue(all((result >= 0) & (result <= 100)))
        
        # First 14 values should be NaN (need 14 periods to calculate first RSI)
        self.assertEqual(sum(result.isna()), 14)
        
        # Test with DataFrame input
        result_df = relative_strength_index(self.df, window=14, column='close')
        assert_series_equal(result, result_df)
        
        # Test with a simple case where we know the expected result
        # Create a series with all gains
        up_prices = pd.Series([100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115])
        up_rsi = relative_strength_index(up_prices, window=14)
        # RSI should be close to 100 for a series with all gains
        self.assertGreater(up_rsi.iloc[-1], 90)
        
        # Create a series with all losses
        down_prices = pd.Series([100, 99, 98, 97, 96, 95, 94, 93, 92, 91, 90, 89, 88, 87, 86, 85])
        down_rsi = relative_strength_index(down_prices, window=14)
        # RSI should be close to 0 for a series with all losses
        self.assertLess(down_rsi.iloc[-1], 10)

    def test_stochastic_oscillator(self):
        """Test Stochastic Oscillator calculation."""
        # Calculate Stochastic Oscillator with k_window=14 and d_window=3
        result = stochastic_oscillator(self.df, k_window=14, d_window=3)
        
        # Check if result is a DataFrame with the expected columns
        self.assertIsInstance(result, pd.DataFrame)
        self.assertIn('k', result.columns)
        self.assertIn('d', result.columns)
        
        # %K and %D should be between 0 and 100
        self.assertTrue(all((result['k'] >= 0) & (result['k'] <= 100)))
        self.assertTrue(all((result['d'] >= 0) & (result['d'] <= 100)))
        
        # First 13 values of %K should be NaN (need 14 periods to calculate first %K)
        self.assertEqual(sum(result['k'].isna()), 13)
        
        # First 15 values of %D should be NaN (need 14 periods for %K + 3 periods for %D)
        self.assertEqual(sum(result['d'].isna()), 15)
        
        # Test with a simple case where we know the expected result
        # Create a DataFrame with a clear high and low
        simple_df = pd.DataFrame({
            'high': [110, 110, 110, 110, 110],
            'low': [100, 100, 100, 100, 100],
            'close': [105, 106, 107, 108, 109]
        })
        simple_result = stochastic_oscillator(simple_df, k_window=5, d_window=3)
        
        # For the last value, close is 109, high is 110, low is 100
        # %K = (109 - 100) / (110 - 100) * 100 = 90
        self.assertAlmostEqual(simple_result['k'].iloc[-1], 90.0)

    def test_rate_of_change(self):
        """Test Rate of Change (ROC) calculation."""
        # Calculate ROC with window=10
        result = rate_of_change(self.prices, window=10)
        
        # First 10 values should be NaN
        self.assertEqual(sum(result.isna()), 10)
        
        # Calculate expected values manually
        expected = pd.Series([np.nan] * 10 + [
            (self.prices[10] / self.prices[0] - 1) * 100,
            (self.prices[11] / self.prices[1] - 1) * 100,
            (self.prices[12] / self.prices[2] - 1) * 100,
            (self.prices[13] / self.prices[3] - 1) * 100,
            (self.prices[14] / self.prices[4] - 1) * 100,
            (self.prices[15] / self.prices[5] - 1) * 100,
            (self.prices[16] / self.prices[6] - 1) * 100,
            (self.prices[17] / self.prices[7] - 1) * 100,
            (self.prices[18] / self.prices[8] - 1) * 100,
            (self.prices[19] / self.prices[9] - 1) * 100
        ], index=self.dates)
        
        # Compare results
        assert_series_equal(result, expected, rtol=1e-10)
        
        # Test with DataFrame input
        result_df = rate_of_change(self.df, window=10, column='close')
        assert_series_equal(result, result_df)


if __name__ == '__main__':
    unittest.main()
