"""
Fundamental data API endpoints.
"""
from datetime import date
from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.data_collection.fundamental import fundamental_data_collector
from app.db.database import get_db
from app.db.models import User
from app.middleware.auth import get_current_user
from app.utils.logging import logger

router = APIRouter()


@router.get("/income-statement/{symbol}")
async def get_income_statement(
    symbol: str,
    period: str = Query("annual", description="Period ('annual' or 'quarter')"),
    limit: int = Query(5, description="Number of periods to retrieve"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get income statement data for a symbol.
    """
    try:
        df = await fundamental_data_collector.get_income_statement(symbol, period, limit)
        
        if df.empty:
            raise HTTPException(
                status_code=404,
                detail=f"Income statement data not found for {symbol}"
            )
        
        # Convert DataFrame to dictionary
        result = df.reset_index().to_dict(orient="records")
        
        return {
            "symbol": symbol,
            "period": period,
            "data": result
        }
    except Exception as e:
        logger.error(f"Error getting income statement for {symbol}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting income statement: {str(e)}"
        )


@router.get("/balance-sheet/{symbol}")
async def get_balance_sheet(
    symbol: str,
    period: str = Query("annual", description="Period ('annual' or 'quarter')"),
    limit: int = Query(5, description="Number of periods to retrieve"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get balance sheet data for a symbol.
    """
    try:
        df = await fundamental_data_collector.get_balance_sheet(symbol, period, limit)
        
        if df.empty:
            raise HTTPException(
                status_code=404,
                detail=f"Balance sheet data not found for {symbol}"
            )
        
        # Convert DataFrame to dictionary
        result = df.reset_index().to_dict(orient="records")
        
        return {
            "symbol": symbol,
            "period": period,
            "data": result
        }
    except Exception as e:
        logger.error(f"Error getting balance sheet for {symbol}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting balance sheet: {str(e)}"
        )


@router.get("/cash-flow/{symbol}")
async def get_cash_flow(
    symbol: str,
    period: str = Query("annual", description="Period ('annual' or 'quarter')"),
    limit: int = Query(5, description="Number of periods to retrieve"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get cash flow statement data for a symbol.
    """
    try:
        df = await fundamental_data_collector.get_cash_flow(symbol, period, limit)
        
        if df.empty:
            raise HTTPException(
                status_code=404,
                detail=f"Cash flow statement data not found for {symbol}"
            )
        
        # Convert DataFrame to dictionary
        result = df.reset_index().to_dict(orient="records")
        
        return {
            "symbol": symbol,
            "period": period,
            "data": result
        }
    except Exception as e:
        logger.error(f"Error getting cash flow statement for {symbol}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting cash flow statement: {str(e)}"
        )


@router.get("/financial-ratios/{symbol}")
async def get_financial_ratios(
    symbol: str,
    period: str = Query("annual", description="Period ('annual' or 'quarter')"),
    limit: int = Query(5, description="Number of periods to retrieve"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get financial ratios for a symbol.
    """
    try:
        df = await fundamental_data_collector.get_financial_ratios(symbol, period, limit)
        
        if df.empty:
            raise HTTPException(
                status_code=404,
                detail=f"Financial ratios not found for {symbol}"
            )
        
        # Convert DataFrame to dictionary
        result = df.reset_index().to_dict(orient="records")
        
        return {
            "symbol": symbol,
            "period": period,
            "data": result
        }
    except Exception as e:
        logger.error(f"Error getting financial ratios for {symbol}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting financial ratios: {str(e)}"
        )


@router.get("/key-metrics/{symbol}")
async def get_key_metrics(
    symbol: str,
    period: str = Query("annual", description="Period ('annual' or 'quarter')"),
    limit: int = Query(5, description="Number of periods to retrieve"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get key metrics for a symbol.
    """
    try:
        df = await fundamental_data_collector.get_key_metrics(symbol, period, limit)
        
        if df.empty:
            raise HTTPException(
                status_code=404,
                detail=f"Key metrics not found for {symbol}"
            )
        
        # Convert DataFrame to dictionary
        result = df.reset_index().to_dict(orient="records")
        
        return {
            "symbol": symbol,
            "period": period,
            "data": result
        }
    except Exception as e:
        logger.error(f"Error getting key metrics for {symbol}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting key metrics: {str(e)}"
        )


@router.get("/financial-growth/{symbol}")
async def get_financial_growth(
    symbol: str,
    period: str = Query("annual", description="Period ('annual' or 'quarter')"),
    limit: int = Query(5, description="Number of periods to retrieve"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get financial growth data for a symbol.
    """
    try:
        df = await fundamental_data_collector.get_financial_growth(symbol, period, limit)
        
        if df.empty:
            raise HTTPException(
                status_code=404,
                detail=f"Financial growth data not found for {symbol}"
            )
        
        # Convert DataFrame to dictionary
        result = df.reset_index().to_dict(orient="records")
        
        return {
            "symbol": symbol,
            "period": period,
            "data": result
        }
    except Exception as e:
        logger.error(f"Error getting financial growth data for {symbol}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting financial growth data: {str(e)}"
        )


@router.get("/earnings/{symbol}")
async def get_earnings(
    symbol: str,
    limit: int = Query(10, description="Number of earnings reports to retrieve"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get earnings data for a symbol.
    """
    try:
        df = await fundamental_data_collector.get_earnings(symbol, limit)
        
        if df.empty:
            raise HTTPException(
                status_code=404,
                detail=f"Earnings data not found for {symbol}"
            )
        
        # Convert DataFrame to dictionary
        result = df.reset_index().to_dict(orient="records")
        
        return {
            "symbol": symbol,
            "data": result
        }
    except Exception as e:
        logger.error(f"Error getting earnings data for {symbol}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting earnings data: {str(e)}"
        )


@router.get("/earnings-calendar")
async def get_earnings_calendar(
    from_date: Optional[date] = Query(None, description="Start date for earnings calendar"),
    to_date: Optional[date] = Query(None, description="End date for earnings calendar"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get earnings calendar.
    """
    try:
        df = await fundamental_data_collector.get_earnings_calendar(from_date, to_date)
        
        if df.empty:
            raise HTTPException(
                status_code=404,
                detail="Earnings calendar not found"
            )
        
        # Convert DataFrame to dictionary
        result = df.reset_index().to_dict(orient="records")
        
        return {
            "from_date": from_date,
            "to_date": to_date,
            "data": result
        }
    except Exception as e:
        logger.error(f"Error getting earnings calendar: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting earnings calendar: {str(e)}"
        )


@router.get("/company-profile/{symbol}")
async def get_company_profile(
    symbol: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get company profile for a symbol.
    """
    try:
        profile = await fundamental_data_collector.get_company_profile(symbol)
        
        if not profile:
            raise HTTPException(
                status_code=404,
                detail=f"Company profile not found for {symbol}"
            )
        
        return {
            "symbol": symbol,
            "data": profile
        }
    except Exception as e:
        logger.error(f"Error getting company profile for {symbol}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting company profile: {str(e)}"
        )
