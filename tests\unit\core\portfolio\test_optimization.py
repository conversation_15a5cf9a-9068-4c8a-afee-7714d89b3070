"""
Unit tests for portfolio optimization strategies.
"""
import unittest
import pandas as pd
import numpy as np

from app.core.portfolio.optimization.mean_variance import MeanVarianceOptimization
from app.core.portfolio.optimization.hierarchical_risk_parity import HierarchicalRiskParity
from app.utils.constants import DEFAULT_RISK_FREE_RATE


class TestOptimizationStrategies(unittest.TestCase):
    """Test cases for portfolio optimization strategies."""

    def setUp(self):
        """Set up test data."""
        # Create sample returns data
        self.dates = pd.date_range(start='2023-01-01', periods=100, freq='D')
        
        # Create returns for 5 assets with different characteristics
        np.random.seed(42)  # For reproducibility
        
        # Asset 1: Low volatility, low return
        asset1_returns = np.random.normal(0.0005, 0.01, 100)
        
        # Asset 2: Medium volatility, medium return
        asset2_returns = np.random.normal(0.001, 0.015, 100)
        
        # Asset 3: High volatility, high return
        asset3_returns = np.random.normal(0.0015, 0.02, 100)
        
        # Asset 4: Negative correlation with Asset 3
        asset4_returns = -0.5 * asset3_returns + np.random.normal(0.0005, 0.01, 100)
        
        # Asset 5: Uncorrelated
        asset5_returns = np.random.normal(0.001, 0.015, 100)
        
        # Create returns DataFrame
        self.returns = pd.DataFrame({
            'Asset1': asset1_returns,
            'Asset2': asset2_returns,
            'Asset3': asset3_returns,
            'Asset4': asset4_returns,
            'Asset5': asset5_returns
        }, index=self.dates)

    def test_mean_variance_optimization(self):
        """Test mean-variance optimization strategy."""
        # Create strategy
        strategy = MeanVarianceOptimization(risk_free_rate=DEFAULT_RISK_FREE_RATE)
        
        # Optimize for maximum Sharpe ratio
        weights_sharpe = strategy.optimize(self.returns, objective="sharpe")
        
        # Check if weights sum to 1
        self.assertAlmostEqual(sum(weights_sharpe.values()), 1.0)
        
        # Check if all weights are non-negative
        for weight in weights_sharpe.values():
            self.assertGreaterEqual(weight, 0.0)
        
        # Optimize for minimum risk
        weights_min_risk = strategy.optimize(self.returns, objective="min_risk")
        
        # Check if weights sum to 1
        self.assertAlmostEqual(sum(weights_min_risk.values()), 1.0)
        
        # Check if all weights are non-negative
        for weight in weights_min_risk.values():
            self.assertGreaterEqual(weight, 0.0)
        
        # Optimize for maximum return
        weights_max_return = strategy.optimize(self.returns, objective="max_return")
        
        # Check if weights sum to 1
        self.assertAlmostEqual(sum(weights_max_return.values()), 1.0)
        
        # For max return, one asset should have weight 1.0 (the one with highest return)
        max_weight = max(weights_max_return.values())
        self.assertAlmostEqual(max_weight, 1.0)
        
        # Optimize with constraints
        constraints = {
            'min_weight': 0.1,
            'max_weight': 0.3
        }
        weights_constrained = strategy.optimize(self.returns, objective="sharpe", constraints=constraints)
        
        # Check if weights sum to 1
        self.assertAlmostEqual(sum(weights_constrained.values()), 1.0)
        
        # Check if all weights are within constraints
        for weight in weights_constrained.values():
            self.assertGreaterEqual(weight, constraints['min_weight'])
            self.assertLessEqual(weight, constraints['max_weight'])
        
        # Generate efficient frontier
        frontier = strategy.efficient_frontier(self.returns, num_portfolios=10)
        
        # Check if frontier has the expected columns
        self.assertIn('return', frontier.columns)
        self.assertIn('risk', frontier.columns)
        self.assertIn('sharpe', frontier.columns)
        
        # Check if frontier has the expected number of portfolios
        self.assertEqual(len(frontier), 10)
        
        # Check if returns and risks are in ascending order
        self.assertTrue(frontier['return'].is_monotonic_increasing)
        
        # Check if all portfolios on the frontier have weights that sum to 1
        for i in range(len(frontier)):
            weights_sum = sum(frontier.iloc[i][f'weight_{asset}'] for asset in self.returns.columns)
            self.assertAlmostEqual(weights_sum, 1.0)

    def test_hierarchical_risk_parity(self):
        """Test hierarchical risk parity optimization strategy."""
        # Create strategy
        strategy = HierarchicalRiskParity()
        
        # Optimize portfolio
        weights = strategy.optimize(self.returns)
        
        # Check if weights sum to 1
        self.assertAlmostEqual(sum(weights.values()), 1.0)
        
        # Check if all weights are non-negative
        for weight in weights.values():
            self.assertGreaterEqual(weight, 0.0)
        
        # Test with different linkage methods
        for method in ['single', 'complete', 'average', 'ward']:
            weights_method = strategy.optimize(self.returns, linkage_method=method)
            
            # Check if weights sum to 1
            self.assertAlmostEqual(sum(weights_method.values()), 1.0)
            
            # Check if all weights are non-negative
            for weight in weights_method.values():
                self.assertGreaterEqual(weight, 0.0)
        
        # Test with different distance metrics
        for metric in ['correlation', 'euclidean']:
            weights_metric = strategy.optimize(self.returns, distance_metric=metric)
            
            # Check if weights sum to 1
            self.assertAlmostEqual(sum(weights_metric.values()), 1.0)
            
            # Check if all weights are non-negative
            for weight in weights_metric.values():
                self.assertGreaterEqual(weight, 0.0)


if __name__ == '__main__':
    unittest.main()
