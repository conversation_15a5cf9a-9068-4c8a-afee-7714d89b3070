"""
Request models for API endpoints.
"""
from datetime import date
from typing import Dict, List, Optional, Union, Any

from pydantic import BaseModel, Field, field_validator


class StockDataRequest(BaseModel):
    """Request model for stock data."""
    symbol: str = Field(..., description="Stock symbol")
    start_date: Optional[date] = Field(None, description="Start date for historical data")
    end_date: Optional[date] = Field(None, description="End date for historical data")
    interval: str = Field("1d", description="Data interval (e.g., '1d', '1wk', '1mo')")


class MultipleStockDataRequest(BaseModel):
    """Request model for multiple stock data."""
    symbols: List[str] = Field(..., description="List of stock symbols")
    start_date: Optional[date] = Field(None, description="Start date for historical data")
    end_date: Optional[date] = Field(None, description="End date for historical data")
    interval: str = Field("1d", description="Data interval (e.g., '1d', '1wk', '1mo')")


class CompanyInfoRequest(BaseModel):
    """Request model for company information."""
    symbol: str = Field(..., description="Stock symbol")


class SearchSymbolsRequest(BaseModel):
    """Request model for symbol search."""
    query: str = Field(..., description="Search query")


class IndicatorRequest(BaseModel):
    """Request model for technical indicator calculation."""
    symbol: str = Field(..., description="Stock symbol")
    indicator: str = Field(..., description="Indicator type")
    start_date: Optional[date] = Field(None, description="Start date for historical data")
    end_date: Optional[date] = Field(None, description="End date for historical data")
    interval: str = Field("1d", description="Data interval (e.g., '1d', '1wk', '1mo')")
    params: Dict = Field({}, description="Additional parameters for the indicator")


class PatternRequest(BaseModel):
    """Request model for pattern recognition."""
    symbol: str = Field(..., description="Stock symbol")
    pattern_type: str = Field(..., description="Pattern type ('candlestick' or 'chart')")
    patterns: Optional[List[str]] = Field(None, description="List of patterns to recognize")
    start_date: Optional[date] = Field(None, description="Start date for historical data")
    end_date: Optional[date] = Field(None, description="End date for historical data")
    interval: str = Field("1d", description="Data interval (e.g., '1d', '1wk', '1mo')")


class ScreenerRequest(BaseModel):
    """Request model for stock screening."""
    symbols: List[str] = Field(..., description="List of stock symbols to screen")
    filters: Dict = Field(..., description="Filter conditions")
    start_date: Optional[date] = Field(None, description="Start date for historical data")
    end_date: Optional[date] = Field(None, description="End date for historical data")
    interval: str = Field("1d", description="Data interval (e.g., '1d', '1wk', '1mo')")
    indicators: Optional[List[Dict]] = Field(None, description="List of indicators to calculate")


class OptimizationRequest(BaseModel):
    """Request model for portfolio optimization."""
    symbols: List[str] = Field(..., description="List of stock symbols")
    start_date: Optional[date] = Field(None, description="Start date for historical data")
    end_date: Optional[date] = Field(None, description="End date for historical data")
    interval: str = Field("1d", description="Data interval (e.g., '1d', '1wk', '1mo')")
    optimization_criterion: str = Field("max_sharpe", description="Optimization criterion (max_sharpe, min_volatility, efficient_risk, efficient_return, max_quadratic_utility, hierarchical_risk_parity)")
    expected_returns_method: str = Field("mean_historical_return", description="Method for calculating expected returns")
    risk_model_method: str = Field("sample_cov", description="Method for calculating the risk model")
    constraints: Optional[Dict] = Field(None, description="Additional constraints for the optimization")
    total_portfolio_value: Optional[float] = Field(None, description="Total portfolio value for discrete allocation")


class RiskMetricsRequest(BaseModel):
    """Request model for risk metrics calculation."""
    symbols: List[str] = Field(..., description="List of stock symbols")
    weights: Optional[Dict[str, float]] = Field(None, description="Portfolio weights")
    start_date: Optional[date] = Field(None, description="Start date for historical data")
    end_date: Optional[date] = Field(None, description="End date for historical data")
    interval: str = Field("1d", description="Data interval (e.g., '1d', '1wk', '1mo')")
    benchmark: Optional[str] = Field(None, description="Benchmark symbol")


class AllocationRequest(BaseModel):
    """Request model for asset allocation."""
    symbols: List[str] = Field(..., description="List of stock symbols")
    strategy: str = Field(..., description="Allocation strategy")
    risk_profile: str = Field("moderate", description="Risk profile")
    start_date: Optional[date] = Field(None, description="Start date for historical data")
    end_date: Optional[date] = Field(None, description="End date for historical data")
    interval: str = Field("1d", description="Data interval (e.g., '1d', '1wk', '1mo')")
    total_portfolio_value: Optional[float] = Field(None, description="Total portfolio value for discrete allocation")


class RebalancingRequest(BaseModel):
    """Request model for portfolio rebalancing."""
    symbols: List[str] = Field(..., description="List of stock symbols")
    initial_weights: Dict[str, float] = Field(..., description="Initial portfolio weights")
    start_date: date = Field(..., description="Start date for historical data")
    end_date: date = Field(..., description="End date for historical data")
    strategy: str = Field("periodic", description="Rebalancing strategy")
    strategy_params: Optional[Dict] = Field(None, description="Strategy-specific parameters")
    total_portfolio_value: Optional[float] = Field(None, description="Total portfolio value")


class PortfolioRequest(BaseModel):
    """Request model for portfolio operations."""
    symbols: List[str] = Field(..., description="List of stock symbols")
    weights: List[float] = Field(..., description="List of portfolio weights")
    start_date: date = Field(..., description="Start date for historical data")
    end_date: date = Field(..., description="End date for historical data")
    interval: str = Field("1d", description="Data interval (e.g., '1d', '1wk', '1mo')")

    @field_validator('weights')
    def validate_weights(cls, v, info):
        """Validate that weights sum to approximately 1."""
        values = info.data
        if 'symbols' in values and len(v) != len(values['symbols']):
            raise ValueError("Number of weights must match number of symbols")

        if abs(sum(v) - 1.0) > 0.0001:
            # Normalize weights to sum to 1
            total = sum(v)
            v = [weight / total for weight in v]

        return v
