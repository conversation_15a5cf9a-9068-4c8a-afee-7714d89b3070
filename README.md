# Portfolio Optimizer

A comprehensive portfolio optimization and stock analysis tool that combines technical analysis with modern portfolio theory to help investors make data-driven decisions.

## Features

- **Data Collection**: Fetch stock data from multiple free sources (Yahoo Finance, Alpha Vantage, Financial Modeling Prep)
- **Technical Analysis**: Calculate and visualize technical indicators and patterns
- **Portfolio Optimization**: Implement modern portfolio theory algorithms for optimal asset allocation
- **Interactive Dashboard**: Visualize data and analysis results through a Streamlit web interface
- **API Backend**: Access data and analysis through a FastAPI backend

## Installation

### Prerequisites

- Python 3.9+
- Git
- Docker (optional, for containerized deployment)

### Setup

1. Clone the repository:
   ```
   git clone <repository-url>
   cd portfolio-optimizer
   ```

2. Create and activate a virtual environment:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

4. Set up environment variables:
   ```
   cp .env.example .env
   # Edit .env with your API keys and configuration
   ```

## Usage

### Running the Streamlit App

```
streamlit run app/streamlit/app.py
```

### Running the FastAPI Backend

```
uvicorn app.api.main:app --reload
```

## Development

For development, install additional dependencies:

```
pip install -r requirements-dev.txt
```

## Testing

Run tests with pytest:

```
pytest
```

## License

[MIT License](LICENSE)
