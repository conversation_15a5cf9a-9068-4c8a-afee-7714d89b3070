"""
Financial Modeling Prep data collector implementation.
"""
import asyncio
import os
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional, Union

import aiohttp
import pandas as pd
from aiolimiter import AsyncLimiter

from app.core.data_collection.base import BaseDataCollector
from app.settings import get_settings
from app.utils.constants import FINANCIAL_MODELING_PREP_RATE_LIMIT
from app.utils.logging import logger


class FinancialModelingPrepCollector(BaseDataCollector):
    """
    Financial Modeling Prep data collector.
    
    This class provides methods to fetch financial data from Financial Modeling Prep API.
    """
    
    def __init__(self):
        """Initialize the Financial Modeling Prep collector."""
        logger.info("Initializing Financial Modeling Prep collector")
        
        # Get API key from environment variable or settings
        self.api_key = os.getenv("FINANCIAL_MODELING_PREP_API_KEY")
        if not self.api_key:
            settings = get_settings()
            self.api_key = getattr(settings, "financial_modeling_prep_api_key", None)
        
        if not self.api_key:
            logger.warning("Financial Modeling Prep API key not found. Set FINANCIAL_MODELING_PREP_API_KEY environment variable.")
        
        # Set base URL
        self.base_url = "https://financialmodelingprep.com/api/v3"
        
        # Set rate limiter (FMP free tier allows 10 requests per minute)
        settings = get_settings()
        rate_limit = settings.data_sources['financial_modeling_prep']['rate_limit'] or FINANCIAL_MODELING_PREP_RATE_LIMIT
        self.limiter = AsyncLimiter(rate_limit, 60)  # requests per minute
    
    async def get_stock_data(
        self,
        symbol: str,
        start_date: date,
        end_date: date,
        interval: str = "1d"
    ) -> pd.DataFrame:
        """
        Get historical stock data for a symbol.
        
        Args:
            symbol: The stock symbol
            start_date: Start date for historical data
            end_date: End date for historical data
            interval: Data interval (e.g., '1d', '1wk', '1mo')
            
        Returns:
            DataFrame with historical stock data
        """
        logger.debug(f"Getting stock data for {symbol} from {start_date} to {end_date}")
        
        if not self.api_key:
            logger.error("Financial Modeling Prep API key not set")
            return pd.DataFrame()
        
        # Map interval to FMP format
        fmp_interval = self._map_interval(interval)
        
        # Prepare URL
        url = f"{self.base_url}/historical-price-full/{symbol}"
        
        # Prepare parameters
        params = {
            "apikey": self.api_key,
            "from": start_date.isoformat(),
            "to": end_date.isoformat()
        }
        
        # Add interval parameter for intraday data
        if fmp_interval != "daily":
            url = f"{self.base_url}/historical-chart/{fmp_interval}/{symbol}"
        
        try:
            # Respect rate limit
            async with self.limiter:
                # Make API request
                async with aiohttp.ClientSession() as session:
                    async with session.get(url, params=params) as response:
                        if response.status != 200:
                            logger.error(f"Error fetching data: {response.status}")
                            return pd.DataFrame()
                        
                        data = await response.json()
                
                # Check for error messages
                if isinstance(data, dict) and "Error Message" in data:
                    logger.error(f"FMP API error: {data['Error Message']}")
                    return pd.DataFrame()
                
                # Process data based on interval
                if fmp_interval == "daily":
                    # Check if historical data exists
                    if "historical" not in data:
                        logger.error(f"No historical data found for {symbol}")
                        return pd.DataFrame()
                    
                    # Convert to DataFrame
                    df = pd.DataFrame(data["historical"])
                else:
                    # Intraday data is returned as a list
                    if not data or not isinstance(data, list):
                        logger.error(f"No data found for {symbol}")
                        return pd.DataFrame()
                    
                    # Convert to DataFrame
                    df = pd.DataFrame(data)
                
                # Rename columns to match our standard format
                column_map = {
                    "date": "date",
                    "open": "open",
                    "high": "high",
                    "low": "low",
                    "close": "close",
                    "adjClose": "adjusted_close",
                    "volume": "volume",
                    "unadjustedVolume": "unadjusted_volume",
                    "change": "change",
                    "changePercent": "change_percent",
                    "vwap": "vwap",
                    "label": "label",
                    "changeOverTime": "change_over_time"
                }
                
                # Rename columns that exist in the DataFrame
                rename_cols = {col: column_map[col] for col in df.columns if col in column_map}
                df = df.rename(columns=rename_cols)
                
                # Convert date column to datetime
                df["date"] = pd.to_datetime(df["date"])
                
                # Sort by date
                df = df.sort_values("date")
                
                # Reset index
                df = df.reset_index(drop=True)
                
                return df
                
        except Exception as e:
            logger.error(f"Error fetching data for {symbol}: {str(e)}")
            return pd.DataFrame()
    
    async def get_multiple_stock_data(
        self,
        symbols: List[str],
        start_date: date,
        end_date: date,
        interval: str = "1d"
    ) -> Dict[str, pd.DataFrame]:
        """
        Get historical stock data for multiple symbols.
        
        Args:
            symbols: List of stock symbols
            start_date: Start date for historical data
            end_date: End date for historical data
            interval: Data interval (e.g., '1d', '1wk', '1mo')
            
        Returns:
            Dictionary mapping symbols to DataFrames with historical stock data
        """
        logger.debug(f"Getting stock data for {len(symbols)} symbols")
        
        result = {}
        
        # FMP doesn't have a batch API for historical data, so we need to fetch each symbol individually
        for symbol in symbols:
            df = await self.get_stock_data(symbol, start_date, end_date, interval)
            result[symbol] = df
        
        return result
    
    async def get_company_info(self, symbol: str) -> Dict:
        """
        Get company information for a symbol.
        
        Args:
            symbol: The stock symbol
            
        Returns:
            Dictionary with company information
        """
        logger.debug(f"Getting company info for {symbol}")
        
        if not self.api_key:
            logger.error("Financial Modeling Prep API key not set")
            return {}
        
        # Prepare URL
        url = f"{self.base_url}/profile/{symbol}"
        
        # Prepare parameters
        params = {
            "apikey": self.api_key
        }
        
        try:
            # Respect rate limit
            async with self.limiter:
                # Make API request
                async with aiohttp.ClientSession() as session:
                    async with session.get(url, params=params) as response:
                        if response.status != 200:
                            logger.error(f"Error fetching company info: {response.status}")
                            return {}
                        
                        data = await response.json()
                
                # Check if data is empty
                if not data or not isinstance(data, list) or len(data) == 0:
                    logger.error(f"No company info found for {symbol}")
                    return {}
                
                # Get the first item (should be the only one)
                company_data = data[0]
                
                # Map FMP fields to our standard format
                info = {
                    "symbol": company_data.get("symbol"),
                    "name": company_data.get("companyName"),
                    "description": company_data.get("description"),
                    "exchange": company_data.get("exchange"),
                    "currency": company_data.get("currency"),
                    "country": company_data.get("country"),
                    "sector": company_data.get("sector"),
                    "industry": company_data.get("industry"),
                    "marketCap": company_data.get("mktCap"),
                    "peRatio": company_data.get("pe"),
                    "dividendYield": company_data.get("lastDiv", 0) * 100,  # Convert to percentage
                    "52WeekHigh": company_data.get("range", "0-0").split("-")[1].strip(),
                    "52WeekLow": company_data.get("range", "0-0").split("-")[0].strip(),
                    "beta": company_data.get("beta"),
                    "website": company_data.get("website"),
                    "ceo": company_data.get("ceo"),
                    "employees": company_data.get("fullTimeEmployees"),
                    "ipoDate": company_data.get("ipoDate")
                }
                
                # Convert numeric values
                for key in ["marketCap", "peRatio", "dividendYield", "52WeekHigh", "52WeekLow", "beta", "employees"]:
                    if key in info and info[key] is not None:
                        try:
                            info[key] = float(info[key])
                        except (ValueError, TypeError):
                            pass
                
                return info
                
        except Exception as e:
            logger.error(f"Error fetching company info for {symbol}: {str(e)}")
            return {}
    
    async def search_symbols(self, query: str) -> List[Dict]:
        """
        Search for symbols matching a query.
        
        Args:
            query: The search query
            
        Returns:
            List of dictionaries with symbol information
        """
        logger.debug(f"Searching for symbols matching '{query}'")
        
        if not self.api_key:
            logger.error("Financial Modeling Prep API key not set")
            return []
        
        # Prepare URL
        url = f"{self.base_url}/search"
        
        # Prepare parameters
        params = {
            "query": query,
            "limit": 10,
            "apikey": self.api_key
        }
        
        try:
            # Respect rate limit
            async with self.limiter:
                # Make API request
                async with aiohttp.ClientSession() as session:
                    async with session.get(url, params=params) as response:
                        if response.status != 200:
                            logger.error(f"Error searching symbols: {response.status}")
                            return []
                        
                        data = await response.json()
                
                # Check if data is empty
                if not data or not isinstance(data, list):
                    logger.error(f"No matches found for '{query}'")
                    return []
                
                # Process results
                results = []
                for item in data:
                    results.append({
                        "symbol": item.get("symbol"),
                        "name": item.get("name"),
                        "type": item.get("exchangeShortName"),
                        "exchange": item.get("stockExchange"),
                        "currency": item.get("currency", "USD")
                    })
                
                return results
                
        except Exception as e:
            logger.error(f"Error searching symbols for '{query}': {str(e)}")
            return []
    
    def _map_interval(self, interval: str) -> str:
        """
        Map interval to Financial Modeling Prep format.
        
        Args:
            interval: Interval in our format ('1d', '1wk', '1mo')
            
        Returns:
            Interval in Financial Modeling Prep format
        """
        interval_map = {
            "1min": "1min",
            "5min": "5min",
            "15min": "15min",
            "30min": "30min",
            "1h": "1hour",
            "4h": "4hour",
            "1d": "daily",
            "daily": "daily",
            "1wk": "weekly",
            "weekly": "weekly",
            "1mo": "monthly",
            "monthly": "monthly"
        }
        
        return interval_map.get(interval, "daily")
