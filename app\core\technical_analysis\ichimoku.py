"""
Ichimoku Cloud technical indicator implementation.
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union

from app.utils.logging import logger


def calculate_ichimoku(
    df: pd.DataFrame,
    high_col: str = 'High',
    low_col: str = 'Low',
    close_col: str = 'Close',
    tenkan_period: int = 9,
    kijun_period: int = 26,
    senkou_span_b_period: int = 52,
    displacement: int = 26
) -> pd.DataFrame:
    """
    Calculate Ichimoku Cloud technical indicator.

    Args:
        df: DataFrame with price data
        high_col: Name of the high price column
        low_col: Name of the low price column
        close_col: Name of the close price column
        tenkan_period: Period for Tenkan-sen (Conversion Line)
        kijun_period: Period for Kijun-sen (Base Line)
        senkou_span_b_period: Period for Senkou Span B (Leading Span B)
        displacement: Displacement period for Senkou Span A and B

    Returns:
        DataFrame with Ichimoku Cloud components
    """
    try:
        # Check if we have enough data
        min_required_data = max(tenkan_period, kijun_period, senkou_span_b_period) + displacement
        if len(df) < min_required_data:
            logger.warning(f"Not enough data for reliable Ichimoku Cloud calculation. Need at least {min_required_data} data points, but only have {len(df)}.")
            # Return the original dataframe with NaN columns for Ichimoku components
            result_df = df.copy()
            for col in ['tenkan_sen', 'kijun_sen', 'senkou_span_a', 'senkou_span_b', 'chikou_span']:
                result_df[col] = np.nan
            return result_df

        # Make a copy of the dataframe to avoid modifying the original
        ichimoku_df = df.copy()

        # Handle multi-level columns if present (from yfinance)
        if isinstance(ichimoku_df.columns, pd.MultiIndex):
            logger.debug("Detected MultiIndex columns, flattening...")
            # Extract the first level if it contains our required columns
            flat_columns = {}
            for col in ichimoku_df.columns:
                if isinstance(col, tuple) and len(col) > 0:
                    # Use the first part of the tuple as the column name
                    if col[0] in ['High', 'Low', 'Close', 'Open', 'Volume']:
                        flat_columns[col] = col[0]

            if flat_columns:
                ichimoku_df.rename(columns=flat_columns, inplace=True)
                logger.debug(f"Flattened columns: {flat_columns}")
                # Convert to a regular Index
                ichimoku_df.columns = pd.Index([col if not isinstance(col, tuple) else col[0] for col in ichimoku_df.columns])

        # Standardize column names if needed (case-insensitive)
        column_mapping = {}

        # Check for high column
        if high_col not in ichimoku_df.columns:
            for col in ichimoku_df.columns:
                if isinstance(col, str) and col.lower() == 'high':
                    column_mapping[col] = 'High'
                    high_col = 'High'
                    break

        # Check for low column
        if low_col not in ichimoku_df.columns:
            for col in ichimoku_df.columns:
                if isinstance(col, str) and col.lower() == 'low':
                    column_mapping[col] = 'Low'
                    low_col = 'Low'
                    break

        # Check for close column
        if close_col not in ichimoku_df.columns:
            for col in ichimoku_df.columns:
                if isinstance(col, str) and col.lower() == 'close':
                    column_mapping[col] = 'Close'
                    close_col = 'Close'
                    break

        # Check for open column (needed for candlestick chart)
        if 'Open' not in ichimoku_df.columns:
            for col in ichimoku_df.columns:
                if isinstance(col, str) and col.lower() == 'open':
                    column_mapping[col] = 'Open'
                    break

        # Apply all column renames at once if needed
        if column_mapping:
            ichimoku_df.rename(columns=column_mapping, inplace=True)
            logger.debug(f"Renamed columns: {column_mapping}")

        # Log the column names for debugging
        logger.debug(f"Standardized columns: {ichimoku_df.columns.tolist()}")
        logger.debug(f"Original columns: {df.columns.tolist()}")
        logger.debug(f"Original index type: {type(df.index)}")

        # Check if required columns exist
        required_columns = [high_col, low_col, close_col]
        missing_columns = [col for col in required_columns if col not in ichimoku_df.columns]

        if missing_columns:
            logger.error(f"Missing required columns for Ichimoku Cloud calculation: {missing_columns}")
            # Return the original dataframe with NaN columns for Ichimoku components
            result_df = df.copy()
            for col in ['tenkan_sen', 'kijun_sen', 'senkou_span_a', 'senkou_span_b', 'chikou_span']:
                result_df[col] = np.nan
            return result_df

        # Ensure we're working with numeric data for required columns
        for col in required_columns:
            # Skip datetime columns
            if pd.api.types.is_datetime64_any_dtype(ichimoku_df[col]):
                logger.warning(f"Column {col} is a datetime type, cannot use for Ichimoku calculation")
                # Return the original dataframe with NaN columns for Ichimoku components
                result_df = df.copy()
                for col in ['tenkan_sen', 'kijun_sen', 'senkou_span_a', 'senkou_span_b', 'chikou_span']:
                    result_df[col] = np.nan
                return result_df

            # Try to convert to numeric, coerce errors to NaN
            try:
                # Check if the column is already a Series (not a DataFrame or other object)
                if not isinstance(ichimoku_df[col], pd.Series):
                    logger.warning(f"Column {col} is not a Series, attempting to convert")
                    if isinstance(ichimoku_df[col], pd.DataFrame):
                        # If it's a DataFrame, take the first column
                        ichimoku_df[col] = ichimoku_df[col].iloc[:, 0]
                    else:
                        # If it's something else, try to convert to a Series
                        ichimoku_df[col] = pd.Series(ichimoku_df[col])

                ichimoku_df[col] = pd.to_numeric(ichimoku_df[col], errors='coerce')
            except Exception as e:
                logger.warning(f"Could not convert column {col} to numeric: {str(e)}")
                # Return the original dataframe with NaN columns for Ichimoku components
                result_df = df.copy()
                for col in ['tenkan_sen', 'kijun_sen', 'senkou_span_a', 'senkou_span_b', 'chikou_span']:
                    result_df[col] = np.nan
                return result_df

        # Log found columns for debugging
        if 'date' in ichimoku_df.columns:
            logger.debug(f"Found date column: date")
        if high_col in ichimoku_df.columns:
            logger.debug(f"Found high column: {high_col}")
        if low_col in ichimoku_df.columns:
            logger.debug(f"Found low column: {low_col}")
        if close_col in ichimoku_df.columns:
            logger.debug(f"Found close column: {close_col}")
        if 'volume' in ichimoku_df.columns:
            logger.debug(f"Found volume column: volume")

        # Create a new DataFrame for the results to avoid column type issues
        result_df = ichimoku_df.copy()

        # Tenkan-sen (Conversion Line): (highest high + lowest low) / 2 for the past tenkan_period
        tenkan_sen_high = result_df[high_col].rolling(window=tenkan_period).max()
        tenkan_sen_low = result_df[low_col].rolling(window=tenkan_period).min()
        result_df['tenkan_sen'] = (tenkan_sen_high + tenkan_sen_low) / 2

        # Kijun-sen (Base Line): (highest high + lowest low) / 2 for the past kijun_period
        kijun_sen_high = result_df[high_col].rolling(window=kijun_period).max()
        kijun_sen_low = result_df[low_col].rolling(window=kijun_period).min()
        result_df['kijun_sen'] = (kijun_sen_high + kijun_sen_low) / 2

        # Senkou Span A (Leading Span A): (Tenkan-sen + Kijun-sen) / 2 displaced forward by displacement period
        result_df['senkou_span_a'] = ((result_df['tenkan_sen'] + result_df['kijun_sen']) / 2).shift(displacement)

        # Senkou Span B (Leading Span B): (highest high + lowest low) / 2 for the past senkou_span_b_period, displaced forward by displacement period
        senkou_span_b_high = result_df[high_col].rolling(window=senkou_span_b_period).max()
        senkou_span_b_low = result_df[low_col].rolling(window=senkou_span_b_period).min()
        result_df['senkou_span_b'] = ((senkou_span_b_high + senkou_span_b_low) / 2).shift(displacement)

        # Chikou Span (Lagging Span): Close price displaced backwards by displacement period
        result_df['chikou_span'] = result_df[close_col].shift(-displacement)

        return result_df

    except Exception as e:
        logger.error(f"Error calculating Ichimoku Cloud: {str(e)}")
        # Return the original dataframe with NaN columns for Ichimoku components
        result_df = df.copy()
        for col in ['tenkan_sen', 'kijun_sen', 'senkou_span_a', 'senkou_span_b', 'chikou_span']:
            result_df[col] = np.nan
        return result_df


def get_ichimoku_signals(
    df: pd.DataFrame,
    close_col: str = 'Close'
) -> pd.DataFrame:
    """
    Generate trading signals based on Ichimoku Cloud.

    Args:
        df: DataFrame with Ichimoku Cloud components
        close_col: Name of the close price column

    Returns:
        DataFrame with Ichimoku Cloud signals
    """
    try:
        # Make a copy of the dataframe to avoid modifying the original
        signals_df = df.copy()

        # Initialize signal columns with default values
        signals_df['ichimoku_signal'] = 0
        signals_df['ichimoku_trend'] = 'neutral'

        # Standardize column names if needed
        if close_col not in signals_df.columns:
            # Try to find a close column with case-insensitive matching
            for col in signals_df.columns:
                if isinstance(col, str) and col.lower() == 'close':
                    signals_df.rename(columns={col: 'Close'}, inplace=True)
                    close_col = 'Close'
                    break

        # Check if required columns exist
        required_columns = ['tenkan_sen', 'kijun_sen', 'senkou_span_a', 'senkou_span_b', close_col]
        missing_columns = [col for col in required_columns if col not in signals_df.columns]

        if missing_columns:
            logger.error(f"Required columns not found in dataframe: {missing_columns}")
            return signals_df  # Return with default signal values

        # Check for NaN values in required columns
        for col in required_columns:
            if signals_df[col].isna().all():
                logger.warning(f"Column '{col}' contains all NaN values")
                return signals_df  # Return with default signal values

        # Ensure all required columns are numeric
        for col in required_columns:
            if not pd.api.types.is_numeric_dtype(signals_df[col]):
                try:
                    signals_df[col] = pd.to_numeric(signals_df[col], errors='coerce')
                except Exception as e:
                    logger.warning(f"Could not convert column '{col}' to numeric: {str(e)}")
                    return signals_df  # Return with default signal values

        # Tenkan-sen crosses above Kijun-sen (bullish signal)
        try:
            tenkan_cross_above = (signals_df['tenkan_sen'].shift(1) <= signals_df['kijun_sen'].shift(1)) & \
                                (signals_df['tenkan_sen'] > signals_df['kijun_sen'])
        except Exception as e:
            logger.warning(f"Error calculating tenkan cross above: {str(e)}")
            tenkan_cross_above = pd.Series(False, index=signals_df.index)

        # Tenkan-sen crosses below Kijun-sen (bearish signal)
        try:
            tenkan_cross_below = (signals_df['tenkan_sen'].shift(1) >= signals_df['kijun_sen'].shift(1)) & \
                                (signals_df['tenkan_sen'] < signals_df['kijun_sen'])
        except Exception as e:
            logger.warning(f"Error calculating tenkan cross below: {str(e)}")
            tenkan_cross_below = pd.Series(False, index=signals_df.index)

        # Price crosses above the cloud (bullish signal)
        try:
            price_above_cloud = (signals_df[close_col] > signals_df['senkou_span_a']) & \
                                (signals_df[close_col] > signals_df['senkou_span_b'])
        except Exception as e:
            logger.warning(f"Error calculating price above cloud: {str(e)}")
            price_above_cloud = pd.Series(False, index=signals_df.index)

        # Price crosses below the cloud (bearish signal)
        try:
            price_below_cloud = (signals_df[close_col] < signals_df['senkou_span_a']) & \
                                (signals_df[close_col] < signals_df['senkou_span_b'])
        except Exception as e:
            logger.warning(f"Error calculating price below cloud: {str(e)}")
            price_below_cloud = pd.Series(False, index=signals_df.index)

        # Price is inside the cloud (neutral)
        try:
            price_in_cloud = ~(price_above_cloud | price_below_cloud)
        except Exception as e:
            logger.warning(f"Error calculating price in cloud: {str(e)}")
            price_in_cloud = pd.Series(True, index=signals_df.index)  # Default to neutral

        # Determine trend - use safe assignment with try/except
        try:
            signals_df.loc[price_above_cloud, 'ichimoku_trend'] = 'bullish'
            signals_df.loc[price_below_cloud, 'ichimoku_trend'] = 'bearish'
            signals_df.loc[price_in_cloud, 'ichimoku_trend'] = 'neutral'
        except Exception as e:
            logger.warning(f"Error setting trend values: {str(e)}")
            signals_df['ichimoku_trend'] = 'neutral'  # Default to neutral

        # Generate signals - ensure the column is float type to avoid warnings
        try:
            signals_df['ichimoku_signal'] = signals_df['ichimoku_signal'].astype(float)

            # Now set the signals
            signals_df.loc[tenkan_cross_above & price_above_cloud, 'ichimoku_signal'] = 1.0  # Strong buy signal
            signals_df.loc[tenkan_cross_above & price_in_cloud, 'ichimoku_signal'] = 0.5  # Weak buy signal
            signals_df.loc[tenkan_cross_below & price_below_cloud, 'ichimoku_signal'] = -1.0  # Strong sell signal
            signals_df.loc[tenkan_cross_below & price_in_cloud, 'ichimoku_signal'] = -0.5  # Weak sell signal
        except Exception as e:
            logger.warning(f"Error setting signal values: {str(e)}")
            signals_df['ichimoku_signal'] = 0.0  # Default to neutral

        return signals_df

    except Exception as e:
        logger.error(f"Error generating Ichimoku Cloud signals: {str(e)}")
        # Return the original dataframe with default signal values
        result_df = df.copy()
        result_df['ichimoku_signal'] = 0.0
        result_df['ichimoku_trend'] = 'neutral'
        return result_df


def get_ichimoku_support_resistance(
    df: pd.DataFrame,
    close_col: str = 'Close'
) -> Dict:
    """
    Identify support and resistance levels based on Ichimoku Cloud.

    Args:
        df: DataFrame with Ichimoku Cloud components
        close_col: Name of the close price column

    Returns:
        Dictionary with support and resistance levels
    """
    # Default return value for error cases
    default_result = {
        'current_price': 0,
        'support_levels': [],
        'resistance_levels': []
    }

    try:
        # Make a copy of the dataframe to avoid modifying the original
        ichimoku_df = df.copy()

        # Check if dataframe is empty
        if ichimoku_df.empty:
            logger.warning("Empty dataframe provided to get_ichimoku_support_resistance")
            return default_result

        # Standardize column names if needed
        if close_col not in ichimoku_df.columns:
            # Try to find a close column with case-insensitive matching
            for col in ichimoku_df.columns:
                if isinstance(col, str) and col.lower() == 'close':
                    ichimoku_df.rename(columns={col: 'Close'}, inplace=True)
                    close_col = 'Close'
                    break

        # Check if required columns exist
        required_columns = ['tenkan_sen', 'kijun_sen', 'senkou_span_a', 'senkou_span_b', close_col]
        missing_columns = [col for col in required_columns if col not in ichimoku_df.columns]

        if missing_columns:
            logger.error(f"Required columns not found in dataframe: {missing_columns}")
            return default_result

        # Check for NaN values in required columns
        for col in required_columns:
            if ichimoku_df[col].isna().all():
                logger.warning(f"Column '{col}' contains all NaN values")
                return default_result

        # Ensure all required columns are numeric
        for col in required_columns:
            if not pd.api.types.is_numeric_dtype(ichimoku_df[col]):
                try:
                    ichimoku_df[col] = pd.to_numeric(ichimoku_df[col], errors='coerce')
                except Exception as e:
                    logger.warning(f"Could not convert column '{col}' to numeric: {str(e)}")
                    return default_result

        # Get the latest data point with error handling
        try:
            if len(ichimoku_df) == 0:
                logger.warning("Empty dataframe after processing")
                return default_result

            latest = ichimoku_df.iloc[-1]
            current_price = latest[close_col]

            # Check if current price is NaN
            if pd.isna(current_price):
                logger.warning("Current price is NaN")
                return default_result
        except Exception as e:
            logger.error(f"Error getting latest data point: {str(e)}")
            return default_result

        # Initialize support and resistance levels
        support_levels = []
        resistance_levels = []

        # Safely get indicator values with NaN checking
        try:
            kijun_sen = latest['kijun_sen']
            tenkan_sen = latest['tenkan_sen']
            senkou_span_a = latest['senkou_span_a']
            senkou_span_b = latest['senkou_span_b']

            # Check for NaN values
            if pd.isna(kijun_sen) or pd.isna(tenkan_sen) or pd.isna(senkou_span_a) or pd.isna(senkou_span_b):
                logger.warning("NaN values found in Ichimoku components")
                return default_result

            # Kijun-sen as support/resistance
            if current_price > kijun_sen:
                support_levels.append(('Kijun-sen', float(kijun_sen)))
            else:
                resistance_levels.append(('Kijun-sen', float(kijun_sen)))

            # Tenkan-sen as support/resistance
            if current_price > tenkan_sen:
                support_levels.append(('Tenkan-sen', float(tenkan_sen)))
            else:
                resistance_levels.append(('Tenkan-sen', float(tenkan_sen)))

            # Cloud top (Senkou Span A or B, whichever is higher)
            cloud_top = max(senkou_span_a, senkou_span_b)
            cloud_bottom = min(senkou_span_a, senkou_span_b)

            # Cloud as support/resistance
            if current_price > cloud_top:
                support_levels.append(('Cloud Top', float(cloud_top)))
                support_levels.append(('Cloud Bottom', float(cloud_bottom)))
            elif current_price < cloud_bottom:
                resistance_levels.append(('Cloud Bottom', float(cloud_bottom)))
                resistance_levels.append(('Cloud Top', float(cloud_top)))
            else:
                # Price is inside the cloud
                resistance_levels.append(('Cloud Top', float(cloud_top)))
                support_levels.append(('Cloud Bottom', float(cloud_bottom)))
        except Exception as e:
            logger.error(f"Error calculating support/resistance levels: {str(e)}")
            return default_result

        # Sort support levels in descending order (closest to price first)
        try:
            support_levels.sort(key=lambda x: x[1], reverse=True)
        except Exception as e:
            logger.warning(f"Error sorting support levels: {str(e)}")
            # Continue without sorting

        # Sort resistance levels in ascending order (closest to price first)
        try:
            resistance_levels.sort(key=lambda x: x[1])
        except Exception as e:
            logger.warning(f"Error sorting resistance levels: {str(e)}")
            # Continue without sorting

        return {
            'current_price': float(current_price),
            'support_levels': support_levels,
            'resistance_levels': resistance_levels
        }

    except Exception as e:
        logger.error(f"Error identifying Ichimoku Cloud support/resistance: {str(e)}")
        return default_result
