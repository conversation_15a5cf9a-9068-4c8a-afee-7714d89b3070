"""
Pattern recognition page for Streamlit app.
"""
import streamlit as st
import pandas as pd
import numpy as np
from datetime import date, timedelta
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import asyncio

from app.core.data_collection.data_manager import data_manager
from app.core.technical_analysis.pattern_recognition import (
    identify_head_and_shoulders,
    identify_double_top_bottom,
    identify_triangle_pattern,
    identify_flag_pennant_pattern
)
from app.utils.logging import logger


# Helper function to create unique keys for plotly charts
def get_chart_key(pattern_type, chart_type, index=1):
    """
    Generate a unique key for plotly charts to avoid duplicate chart ID errors.

    Args:
        pattern_type: Type of pattern (e.g., 'hs', 'dt', 'tr', 'fp')
        chart_type: Type of chart (e.g., 'pattern', 'volume')
        index: Optional index to further differentiate charts

    Returns:
        A unique string key for the chart
    """
    return f"{pattern_type}_{chart_type}_{index}"


async def show_pattern_recognition_page():
    """Show the pattern recognition page."""
    st.title("Chart Pattern Recognition")

    # Create tabs for different patterns
    tabs = st.tabs([
        "Head & Shoulders",
        "Double Tops/Bottoms",
        "Triangle Patterns",
        "Flag & Pennant Patterns"
    ])

    # Head & Shoulders tab
    with tabs[0]:
        await show_head_and_shoulders()

    # Double Tops/Bottoms tab
    with tabs[1]:
        await show_double_top_bottom()

    # Triangle Patterns tab
    with tabs[2]:
        await show_triangle_patterns()

    # Flag & Pennant Patterns tab
    with tabs[3]:
        await show_flag_pennant_patterns()


async def show_double_top_bottom():
    """Show Double Top and Double Bottom pattern analysis."""
    st.header("Double Tops and Bottoms Patterns")

    # Introduction
    st.markdown("""
    Double Tops and Double Bottoms are reversal patterns that signal a trend change.

    **Double Top (Bearish):**
    - Forms after an uptrend
    - Consists of two peaks at approximately the same price level
    - A trough forms between the two peaks
    - A break below the trough (neckline) confirms the pattern

    **Double Bottom (Bullish):**
    - Forms after a downtrend
    - Consists of two troughs at approximately the same price level
    - A peak forms between the two troughs
    - A break above the peak (neckline) confirms the pattern
    """)

    # Settings section
    st.subheader("Pattern Recognition Settings")

    # Create columns for settings
    col1, col2 = st.columns(2)

    with col1:
        # Get symbol
        symbol = st.text_input(
            "Enter stock symbol",
            value="AAPL",
            key="dt_symbol"
        ).upper()

        # Get interval
        interval = st.selectbox(
            "Interval",
            options=["1d", "1wk", "1mo"],
            index=0,
            key="dt_interval"
        )

    with col2:
        # Get date range
        start_date = st.date_input(
            "Start Date",
            value=date.today() - timedelta(days=365),
            key="dt_start_date"
        )

        end_date = st.date_input(
            "End Date",
            value=date.today(),
            key="dt_end_date"
        )

    # Create expander for advanced parameters
    with st.expander("Advanced Parameters", expanded=False):
        col1, col2 = st.columns(2)

        with col1:
            # Get window size for swing points
            window = st.slider(
                "Window Size for Swing Points",
                min_value=3,
                max_value=10,
                value=5,
                key="dt_window"
            )

            # Get pattern length settings
            min_pattern_length = st.slider(
                "Minimum Pattern Length (bars)",
                min_value=10,
                max_value=50,
                value=20,
                key="dt_min_length"
            )

            # Get price tolerance
            price_tolerance = st.slider(
                "Price Tolerance",
                min_value=0.01,
                max_value=0.1,
                value=0.03,
                step=0.01,
                key="dt_tolerance",
                help="Maximum allowed difference between tops/bottoms (as percentage)"
            )

        with col2:
            max_pattern_length = st.slider(
                "Maximum Pattern Length (bars)",
                min_value=50,
                max_value=200,
                value=100,
                key="dt_max_length"
            )

            # Get volume confirmation option
            volume_confirmation = st.checkbox(
                "Check Volume Confirmation",
                value=True,
                key="dt_volume",
                help="Check if volume pattern supports the formation"
            )

    # Get historical data and identify patterns
    with st.spinner(f"Analyzing {symbol} for Double Top/Bottom patterns..."):
        try:
            # Get historical data
            df = await data_manager.get_stock_data(symbol, start_date, end_date, interval)

            if df.empty:
                st.error(f"No historical data found for {symbol}")
                return

            # Ensure column names are standardized
            if 'open' in df.columns: df.rename(columns={'open': 'Open'}, inplace=True)
            if 'high' in df.columns: df.rename(columns={'high': 'High'}, inplace=True)
            if 'low' in df.columns: df.rename(columns={'low': 'Low'}, inplace=True)
            if 'close' in df.columns: df.rename(columns={'close': 'Close'}, inplace=True)
            if 'volume' in df.columns: df.rename(columns={'volume': 'Volume'}, inplace=True)

            # Identify double top/bottom pattern
            pattern_result = identify_double_top_bottom(
                df,
                window=window,
                min_pattern_length=min_pattern_length,
                max_pattern_length=max_pattern_length,
                price_tolerance=price_tolerance,
                volume_confirmation=volume_confirmation
            )

            # Display pattern result
            if pattern_result['pattern'] == 'none' or pattern_result['pattern'] == 'error':
                st.warning(pattern_result['analysis'])

                # Display price chart without pattern
                fig = go.Figure(
                    go.Candlestick(
                        x=df.index,
                        open=df['Open'],
                        high=df['High'],
                        low=df['Low'],
                        close=df['Close'],
                        name="Price"
                    )
                )

                fig.update_layout(
                    title=f"Price Chart for {symbol}",
                    xaxis_title="Date",
                    yaxis_title="Price",
                    height=600
                )

                st.plotly_chart(fig, use_container_width=True, key=get_chart_key("dt", "pattern", 1))

                return

            # Display pattern information
            st.subheader("Pattern Detected")

            # Create columns for pattern details
            col1, col2, col3 = st.columns(3)

            with col1:
                pattern_name = "Double Top" if pattern_result['pattern'] == 'double_top' else "Double Bottom"
                st.metric(
                    label="Pattern",
                    value=pattern_name
                )

            with col2:
                st.metric(
                    label="Direction",
                    value=pattern_result['direction'].capitalize()
                )

            with col3:
                st.metric(
                    label="Confidence",
                    value=f"{pattern_result['confidence']:.0%}"
                )

            # Display price chart with pattern
            st.subheader(f"Price Chart with {pattern_name} Pattern")

            # Create figure
            fig = go.Figure(
                go.Candlestick(
                    x=df.index,
                    open=df['Open'],
                    high=df['High'],
                    low=df['Low'],
                    close=df['Close'],
                    name="Price"
                )
            )

            # Add pattern points
            for point in pattern_result['points']:
                fig.add_trace(
                    go.Scatter(
                        x=[point['date']],
                        y=[point['price']],
                        mode='markers',
                        marker=dict(
                            size=10,
                            color='red' if 'top' in point['name'] else ('green' if 'bottom' in point['name'] else 'blue'),
                            symbol='circle'
                        ),
                        name=point['name'].replace('_', ' ').title()
                    )
                )

            # Add neckline
            neckline = pattern_result['neckline']
            if neckline:
                # Get the last point index
                last_point_idx = pattern_result['points'][-1]['index']

                # Create x-coordinates for neckline
                x_indices = list(range(last_point_idx, len(df)))
                x_dates = [df.index[i] for i in x_indices]

                # Create y-coordinates for neckline (horizontal line at neckline price)
                y_values = [neckline['price']] * len(x_indices)

                # Add neckline to chart
                fig.add_trace(
                    go.Scatter(
                        x=x_dates,
                        y=y_values,
                        mode='lines',
                        line=dict(color='purple', width=2, dash='dash'),
                        name="Neckline"
                    )
                )

                # Add breakout point if confirmed
                if pattern_result.get('breakout_confirmed', False):
                    breakout_idx = pattern_result['breakout_index']
                    breakout_date = df.index[breakout_idx]
                    breakout_price = df.iloc[breakout_idx]['Close']

                    fig.add_trace(
                        go.Scatter(
                            x=[breakout_date],
                            y=[breakout_price],
                            mode='markers',
                            marker=dict(
                                size=12,
                                color='orange',
                                symbol='star'
                            ),
                            name="Breakout Point"
                        )
                    )

                # Add target price line
                if pattern_result['target'] is not None:
                    # Draw horizontal line at target price from breakout point to end of chart
                    start_idx = last_point_idx
                    if pattern_result.get('breakout_confirmed', False):
                        start_idx = pattern_result['breakout_index']

                    target_x = [df.index[start_idx], df.index[-1]]
                    target_y = [pattern_result['target'], pattern_result['target']]

                    fig.add_trace(
                        go.Scatter(
                            x=target_x,
                            y=target_y,
                            mode='lines',
                            line=dict(color='red', width=2, dash='dot'),
                            name="Price Target"
                        )
                    )

            # Update layout
            fig.update_layout(
                title=f"{pattern_name} Pattern for {symbol}",
                xaxis_title="Date",
                yaxis_title="Price",
                height=600,
                legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.02,
                    xanchor="right",
                    x=1
                )
            )

            # Display chart
            st.plotly_chart(fig, use_container_width=True, key=get_chart_key("dt", "pattern", 2))

            # Display analysis
            st.subheader("Pattern Analysis")
            st.markdown(pattern_result['analysis'])

            # Display trading strategy
            st.subheader("Trading Strategy")

            if pattern_result['pattern'] == 'double_top':
                strategy_text = """
                **Bearish Double Top Strategy:**

                **Entry:**
                - Enter a short position when price breaks below the neckline (middle low)
                - Confirm with increased volume on the breakout
                - Wait for a retest of the neckline from below (common but not guaranteed)

                **Stop Loss:**
                - Place stop loss above the second top
                - Alternative: Place stop loss at the midpoint between the second top and the neckline

                **Target:**
                - Primary target: Distance from tops to neckline, projected downward from the breakout point
                - Consider taking partial profits at support levels

                **Risk Management:**
                - Use proper position sizing (1-2% risk per trade)
                - Consider a time stop if price doesn't reach target within a reasonable timeframe
                """
            else:  # double_bottom
                strategy_text = """
                **Bullish Double Bottom Strategy:**

                **Entry:**
                - Enter a long position when price breaks above the neckline (middle high)
                - Confirm with increased volume on the breakout
                - Wait for a retest of the neckline from above (common but not guaranteed)

                **Stop Loss:**
                - Place stop loss below the second bottom
                - Alternative: Place stop loss at the midpoint between the second bottom and the neckline

                **Target:**
                - Primary target: Distance from bottoms to neckline, projected upward from the breakout point
                - Consider taking partial profits at resistance levels

                **Risk Management:**
                - Use proper position sizing (1-2% risk per trade)
                - Consider a time stop if price doesn't reach target within a reasonable timeframe
                """

            st.markdown(strategy_text)

            # Display volume analysis
            st.subheader("Volume Analysis")

            # Create volume chart
            fig_volume = make_subplots(
                rows=2,
                cols=1,
                shared_xaxes=True,
                vertical_spacing=0.05,
                row_heights=[0.7, 0.3],
                subplot_titles=("Price", "Volume")
            )

            # Add price candlesticks
            fig_volume.add_trace(
                go.Candlestick(
                    x=df.index,
                    open=df['Open'],
                    high=df['High'],
                    low=df['Low'],
                    close=df['Close'],
                    name="Price"
                ),
                row=1, col=1
            )

            # Add pattern points to price chart
            for point in pattern_result['points']:
                fig_volume.add_trace(
                    go.Scatter(
                        x=[point['date']],
                        y=[point['price']],
                        mode='markers',
                        marker=dict(
                            size=10,
                            color='red' if 'top' in point['name'] else ('green' if 'bottom' in point['name'] else 'blue'),
                            symbol='circle'
                        ),
                        name=point['name'].replace('_', ' ').title()
                    ),
                    row=1, col=1
                )

            # Add neckline to price chart
            if neckline:
                fig_volume.add_trace(
                    go.Scatter(
                        x=x_dates,
                        y=y_values,
                        mode='lines',
                        line=dict(color='purple', width=2, dash='dash'),
                        name="Neckline"
                    ),
                    row=1, col=1
                )

            # Add volume bars
            fig_volume.add_trace(
                go.Bar(
                    x=df.index,
                    y=df['Volume'],
                    name="Volume",
                    marker=dict(
                        color=df['Close'].diff().apply(
                            lambda x: 'green' if x > 0 else 'red'
                        )
                    )
                ),
                row=2, col=1
            )

            # Add moving average of volume
            volume_ma = df['Volume'].rolling(window=20).mean()
            fig_volume.add_trace(
                go.Scatter(
                    x=df.index,
                    y=volume_ma,
                    name="Volume MA (20)",
                    line=dict(color='blue', width=1.5)
                ),
                row=2, col=1
            )

            # Highlight volume at pattern points
            for point in pattern_result['points']:
                fig_volume.add_trace(
                    go.Scatter(
                        x=[point['date']],
                        y=[df.iloc[point['index']]['Volume']],
                        mode='markers',
                        marker=dict(
                            size=10,
                            color='red' if 'top' in point['name'] else ('green' if 'bottom' in point['name'] else 'blue'),
                            symbol='circle'
                        ),
                        name=f"Volume at {point['name'].replace('_', ' ').title()}"
                    ),
                    row=2, col=1
                )

            # Update layout
            fig_volume.update_layout(
                title=f"Price and Volume Analysis for {symbol}",
                height=800,
                legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.02,
                    xanchor="right",
                    x=1
                ),
                xaxis2_title="Date",
                yaxis_title="Price",
                yaxis2_title="Volume"
            )

            # Display volume chart
            st.plotly_chart(fig_volume, use_container_width=True, key="other_volume_3")

            # Display volume analysis text
            if 'volume_pattern_valid' in pattern_result:
                if pattern_result['pattern'] == 'double_top':
                    if pattern_result['volume_pattern_valid']:
                        volume_text = "Volume pattern supports the Double Top formation. Volume is lower on the second top, indicating weakening buying pressure."
                    else:
                        volume_text = "Volume pattern does not fully support the Double Top formation. Ideally, volume should be lower on the second top."
                else:  # double_bottom
                    if pattern_result['volume_pattern_valid']:
                        volume_text = "Volume pattern supports the Double Bottom formation. Volume is higher on the second bottom, indicating strengthening buying pressure."
                    else:
                        volume_text = "Volume pattern does not fully support the Double Bottom formation. Ideally, volume should be higher on the second bottom."

                st.markdown(volume_text)

            if pattern_result.get('breakout_confirmed', False):
                breakout_idx = pattern_result['breakout_index']
                breakout_volume = df.iloc[breakout_idx]['Volume']
                avg_volume = df['Volume'].mean()

                if breakout_volume > avg_volume * 1.5:
                    volume_text = f"Volume on the breakout day was {breakout_volume / avg_volume:.1f}x the average volume, which is a strong confirmation of the pattern."
                elif breakout_volume > avg_volume:
                    volume_text = f"Volume on the breakout day was {breakout_volume / avg_volume:.1f}x the average volume, which provides some confirmation of the pattern."
                else:
                    volume_text = f"Volume on the breakout day was below average, which may indicate a lack of conviction in the breakout."

                st.markdown(volume_text)
            else:
                st.markdown("Waiting for a breakout to analyze volume confirmation.")

        except Exception as e:
            st.error(f"Error analyzing Double Top/Bottom pattern: {str(e)}")
            logger.error(f"Error analyzing Double Top/Bottom pattern for {symbol}: {str(e)}")


async def show_flag_pennant_patterns():
    """Show Flag and Pennant pattern analysis."""
    st.header("Flag and Pennant Patterns")

    # Introduction
    st.markdown("""
    Flags and Pennants are short-term continuation patterns that form after a strong price move (the pole).

    **Flag Pattern:**
    - Forms after a sharp price move (pole)
    - Consists of a small, parallel channel that slopes against the prior trend
    - Typically resolves in the direction of the prior trend
    - Volume typically decreases during the flag formation

    **Pennant Pattern:**
    - Similar to a flag, but forms a small symmetrical triangle
    - Converging trendlines create a pennant shape
    - Typically resolves in the direction of the prior trend
    - Volume typically decreases during the pennant formation

    Both patterns are considered reliable continuation patterns when they form after a strong move.
    """)

    # Settings section
    st.subheader("Pattern Recognition Settings")

    # Create columns for settings
    col1, col2 = st.columns(2)

    with col1:
        # Get symbol
        symbol = st.text_input(
            "Enter stock symbol",
            value="AAPL",
            key="fp_symbol"
        ).upper()

        # Get interval
        interval = st.selectbox(
            "Interval",
            options=["1d", "1wk", "1mo"],
            index=0,
            key="fp_interval"
        )

    with col2:
        # Get date range
        start_date = st.date_input(
            "Start Date",
            value=date.today() - timedelta(days=365),
            key="fp_start_date"
        )

        end_date = st.date_input(
            "End Date",
            value=date.today(),
            key="fp_end_date"
        )

    # Create expander for advanced parameters
    with st.expander("Advanced Parameters", expanded=False):
        col1, col2 = st.columns(2)

        with col1:
            # Get window size for swing points
            window = st.slider(
                "Window Size for Swing Points",
                min_value=3,
                max_value=10,
                value=5,
                key="fp_window"
            )

            # Get pole length settings
            min_pole_length = st.slider(
                "Minimum Pole Length (bars)",
                min_value=3,
                max_value=20,
                value=5,
                key="fp_min_pole_length"
            )

            max_pole_length = st.slider(
                "Maximum Pole Length (bars)",
                min_value=10,
                max_value=50,
                value=20,
                key="fp_max_pole_length"
            )

            # Get pole height percentage
            min_pole_height_pct = st.slider(
                "Minimum Pole Height (%)",
                min_value=0.05,
                max_value=0.3,
                value=0.1,
                step=0.01,
                key="fp_min_pole_height",
                help="Minimum height of the pole as percentage of price"
            )

        with col2:
            # Get flag/pennant length settings
            min_flag_length = st.slider(
                "Minimum Flag/Pennant Length (bars)",
                min_value=3,
                max_value=20,
                value=5,
                key="fp_min_flag_length"
            )

            max_flag_length = st.slider(
                "Maximum Flag/Pennant Length (bars)",
                min_value=10,
                max_value=50,
                value=20,
                key="fp_max_flag_length"
            )

            # Get flag height percentage
            max_flag_height_pct = st.slider(
                "Maximum Flag/Pennant Height (% of pole)",
                min_value=0.2,
                max_value=0.8,
                value=0.5,
                step=0.05,
                key="fp_max_flag_height",
                help="Maximum height of the flag/pennant as percentage of pole height"
            )

    # Get historical data and identify patterns
    with st.spinner(f"Analyzing {symbol} for Flag and Pennant patterns..."):
        try:
            # Get historical data
            df = await data_manager.get_stock_data(symbol, start_date, end_date, interval)

            if df.empty:
                st.error(f"No historical data found for {symbol}")
                return

            # Ensure column names are standardized
            if 'open' in df.columns: df.rename(columns={'open': 'Open'}, inplace=True)
            if 'high' in df.columns: df.rename(columns={'high': 'High'}, inplace=True)
            if 'low' in df.columns: df.rename(columns={'low': 'Low'}, inplace=True)
            if 'close' in df.columns: df.rename(columns={'close': 'Close'}, inplace=True)
            if 'volume' in df.columns: df.rename(columns={'volume': 'Volume'}, inplace=True)

            # Identify flag/pennant pattern
            pattern_result = identify_flag_pennant_pattern(
                df,
                window=window,
                min_pole_length=min_pole_length,
                max_pole_length=max_pole_length,
                min_flag_length=min_flag_length,
                max_flag_length=max_flag_length,
                min_pole_height_pct=min_pole_height_pct,
                max_flag_height_pct=max_flag_height_pct
            )

            # Display pattern result
            if pattern_result['pattern'] == 'none' or pattern_result['pattern'] == 'error':
                st.warning(pattern_result['analysis'])

                # Display price chart without pattern
                fig = go.Figure(
                    go.Candlestick(
                        x=df.index,
                        open=df['Open'],
                        high=df['High'],
                        low=df['Low'],
                        close=df['Close'],
                        name="Price"
                    )
                )

                fig.update_layout(
                    title=f"Price Chart for {symbol}",
                    xaxis_title="Date",
                    yaxis_title="Price",
                    height=600
                )

                st.plotly_chart(fig, use_container_width=True, key=get_chart_key("fp", "pattern", 1))

                return

            # Display pattern information
            st.subheader("Pattern Detected")

            # Create columns for pattern details
            col1, col2, col3 = st.columns(3)

            with col1:
                pattern_name = pattern_result['pattern'].replace('_', ' ').title()
                st.metric(
                    label="Pattern",
                    value=pattern_name
                )

            with col2:
                st.metric(
                    label="Direction",
                    value=pattern_result['direction'].capitalize()
                )

            with col3:
                st.metric(
                    label="Confidence",
                    value=f"{pattern_result['confidence']:.0%}"
                )

            # Display price chart with pattern
            st.subheader(f"Price Chart with {pattern_name} Pattern")

            # Create figure
            fig = go.Figure(
                go.Candlestick(
                    x=df.index,
                    open=df['Open'],
                    high=df['High'],
                    low=df['Low'],
                    close=df['Close'],
                    name="Price"
                )
            )

            # Add pattern points
            for point in pattern_result['points']:
                fig.add_trace(
                    go.Scatter(
                        x=[point['date']],
                        y=[point['price']],
                        mode='markers',
                        marker=dict(
                            size=10,
                            color='red' if 'pole_start' in point['name'] else
                                  ('green' if 'pole_end' in point['name'] else
                                   ('blue' if 'high' in point['name'] else
                                    ('purple' if 'low' in point['name'] else 'orange'))),
                            symbol='circle'
                        ),
                        name=point['name'].replace('_', ' ').title()
                    )
                )

            # Add pole line
            pole_start = next((p for p in pattern_result['points'] if p['name'] == 'pole_start'), None)
            pole_end = next((p for p in pattern_result['points'] if p['name'] == 'pole_end'), None)

            if pole_start and pole_end:
                fig.add_trace(
                    go.Scatter(
                        x=[pole_start['date'], pole_end['date']],
                        y=[pole_start['price'], pole_end['price']],
                        mode='lines',
                        line=dict(color='black', width=3),
                        name="Pole"
                    )
                )

            # Add flag/pennant trendlines
            trendlines = pattern_result['trendlines']
            if trendlines:
                # Get the start and end indices for the trendlines
                flag_start_idx = pattern_result['flag']['start_idx']
                flag_end_idx = pattern_result['flag']['end_idx']

                # Create x-coordinates for trendlines
                x_indices = list(range(flag_start_idx, flag_end_idx + 1))
                x_dates = [df.index[i] for i in x_indices]

                # Calculate y-coordinates for upper trendline
                upper_trendline = trendlines['upper']
                upper_values = [upper_trendline['slope'] * i + upper_trendline['intercept'] for i in x_indices]

                # Calculate y-coordinates for lower trendline
                lower_trendline = trendlines['lower']
                lower_values = [lower_trendline['slope'] * i + lower_trendline['intercept'] for i in x_indices]

                # Add upper trendline to chart
                fig.add_trace(
                    go.Scatter(
                        x=x_dates,
                        y=upper_values,
                        mode='lines',
                        line=dict(color='red', width=2, dash='dash'),
                        name="Upper Trendline"
                    )
                )

                # Add lower trendline to chart
                fig.add_trace(
                    go.Scatter(
                        x=x_dates,
                        y=lower_values,
                        mode='lines',
                        line=dict(color='green', width=2, dash='dash'),
                        name="Lower Trendline"
                    )
                )

                # Add breakout point if confirmed
                if pattern_result['breakout']['confirmed']:
                    breakout_idx = pattern_result['breakout']['index']
                    breakout_date = df.index[breakout_idx]
                    breakout_price = df.iloc[breakout_idx]['Close']

                    fig.add_trace(
                        go.Scatter(
                            x=[breakout_date],
                            y=[breakout_price],
                            mode='markers',
                            marker=dict(
                                size=12,
                                color='orange',
                                symbol='star'
                            ),
                            name="Breakout Point"
                        )
                    )

                # Add target price line if available
                if pattern_result['target'] is not None:
                    # Draw horizontal line at target price from breakout point to end of chart
                    if pattern_result['breakout']['confirmed']:
                        start_idx = pattern_result['breakout']['index']
                        target_x = [df.index[start_idx], df.index[-1]]
                        target_y = [pattern_result['target'], pattern_result['target']]

                        fig.add_trace(
                            go.Scatter(
                                x=target_x,
                                y=target_y,
                                mode='lines',
                                line=dict(color='purple', width=2, dash='dot'),
                                name="Price Target"
                            )
                        )

            # Update layout
            fig.update_layout(
                title=f"{pattern_name} Pattern for {symbol}",
                xaxis_title="Date",
                yaxis_title="Price",
                height=600,
                legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.02,
                    xanchor="right",
                    x=1
                )
            )

            # Display chart
            st.plotly_chart(fig, use_container_width=True, key=get_chart_key("fp", "pattern", 2))

            # Display analysis
            st.subheader("Pattern Analysis")
            st.markdown(pattern_result['analysis'])

            # Display trading strategy
            st.subheader("Trading Strategy")

            if 'bullish' in pattern_result['pattern']:
                if 'flag' in pattern_result['pattern']:
                    strategy_text = """
                    **Bullish Flag Strategy:**

                    **Entry:**
                    - Enter a long position when price breaks above the upper trendline of the flag
                    - Confirm with increased volume on the breakout
                    - Alternative: Enter on a pullback to the breakout level after confirmation

                    **Stop Loss:**
                    - Place stop loss below the lower trendline of the flag
                    - Alternative: Place stop loss below the most recent swing low inside the flag

                    **Target:**
                    - Primary target: Height of the pole, projected upward from the breakout point
                    - Consider taking partial profits at key resistance levels

                    **Risk Management:**
                    - Use proper position sizing (1-2% risk per trade)
                    - Be cautious of false breakouts - consider waiting for a close above the trendline
                    """
                else:  # pennant
                    strategy_text = """
                    **Bullish Pennant Strategy:**

                    **Entry:**
                    - Enter a long position when price breaks above the upper trendline of the pennant
                    - Confirm with increased volume on the breakout
                    - Alternative: Enter on a pullback to the breakout level after confirmation

                    **Stop Loss:**
                    - Place stop loss below the lower trendline of the pennant
                    - Alternative: Place stop loss below the most recent swing low inside the pennant

                    **Target:**
                    - Primary target: Height of the pole, projected upward from the breakout point
                    - Consider taking partial profits at key resistance levels

                    **Risk Management:**
                    - Use proper position sizing (1-2% risk per trade)
                    - Be cautious of false breakouts - consider waiting for a close above the trendline
                    """
            else:  # bearish
                if 'flag' in pattern_result['pattern']:
                    strategy_text = """
                    **Bearish Flag Strategy:**

                    **Entry:**
                    - Enter a short position when price breaks below the lower trendline of the flag
                    - Confirm with increased volume on the breakout
                    - Alternative: Enter on a pullback to the breakout level after confirmation

                    **Stop Loss:**
                    - Place stop loss above the upper trendline of the flag
                    - Alternative: Place stop loss above the most recent swing high inside the flag

                    **Target:**
                    - Primary target: Height of the pole, projected downward from the breakout point
                    - Consider taking partial profits at key support levels

                    **Risk Management:**
                    - Use proper position sizing (1-2% risk per trade)
                    - Be cautious of false breakouts - consider waiting for a close below the trendline
                    """
                else:  # pennant
                    strategy_text = """
                    **Bearish Pennant Strategy:**

                    **Entry:**
                    - Enter a short position when price breaks below the lower trendline of the pennant
                    - Confirm with increased volume on the breakout
                    - Alternative: Enter on a pullback to the breakout level after confirmation

                    **Stop Loss:**
                    - Place stop loss above the upper trendline of the pennant
                    - Alternative: Place stop loss above the most recent swing high inside the pennant

                    **Target:**
                    - Primary target: Height of the pole, projected downward from the breakout point
                    - Consider taking partial profits at key support levels

                    **Risk Management:**
                    - Use proper position sizing (1-2% risk per trade)
                    - Be cautious of false breakouts - consider waiting for a close below the trendline
                    """

            st.markdown(strategy_text)

            # Display volume analysis
            st.subheader("Volume Analysis")

            # Create volume chart
            fig_volume = make_subplots(
                rows=2,
                cols=1,
                shared_xaxes=True,
                vertical_spacing=0.05,
                row_heights=[0.7, 0.3],
                subplot_titles=("Price", "Volume")
            )

            # Add price candlesticks
            fig_volume.add_trace(
                go.Candlestick(
                    x=df.index,
                    open=df['Open'],
                    high=df['High'],
                    low=df['Low'],
                    close=df['Close'],
                    name="Price"
                ),
                row=1, col=1
            )

            # Add pole line to price chart
            if pole_start and pole_end:
                fig_volume.add_trace(
                    go.Scatter(
                        x=[pole_start['date'], pole_end['date']],
                        y=[pole_start['price'], pole_end['price']],
                        mode='lines',
                        line=dict(color='black', width=3),
                        name="Pole"
                    ),
                    row=1, col=1
                )

            # Add trendlines to price chart
            if trendlines:
                # Add upper trendline
                fig_volume.add_trace(
                    go.Scatter(
                        x=x_dates,
                        y=upper_values,
                        mode='lines',
                        line=dict(color='red', width=2, dash='dash'),
                        name="Upper Trendline"
                    ),
                    row=1, col=1
                )

                # Add lower trendline
                fig_volume.add_trace(
                    go.Scatter(
                        x=x_dates,
                        y=lower_values,
                        mode='lines',
                        line=dict(color='green', width=2, dash='dash'),
                        name="Lower Trendline"
                    ),
                    row=1, col=1
                )

            # Add volume bars
            fig_volume.add_trace(
                go.Bar(
                    x=df.index,
                    y=df['Volume'],
                    name="Volume",
                    marker=dict(
                        color=df['Close'].diff().apply(
                            lambda x: 'green' if x > 0 else 'red'
                        )
                    )
                ),
                row=2, col=1
            )

            # Add moving average of volume
            volume_ma = df['Volume'].rolling(window=20).mean()
            fig_volume.add_trace(
                go.Scatter(
                    x=df.index,
                    y=volume_ma,
                    name="Volume MA (20)",
                    line=dict(color='blue', width=1.5)
                ),
                row=2, col=1
            )

            # Highlight pole and flag/pennant volume
            if 'pole' in pattern_result and 'flag' in pattern_result:
                pole_start_idx = pattern_result['pole']['start_idx']
                pole_end_idx = pattern_result['pole']['end_idx']
                flag_start_idx = pattern_result['flag']['start_idx']
                flag_end_idx = pattern_result['flag']['end_idx']

                # Add pole volume highlight
                pole_x = df.index[pole_start_idx:pole_end_idx+1]
                pole_volume = df.iloc[pole_start_idx:pole_end_idx+1]['Volume']

                fig_volume.add_trace(
                    go.Scatter(
                        x=pole_x,
                        y=pole_volume,
                        mode='markers',
                        marker=dict(
                            size=8,
                            color='black',
                            symbol='circle'
                        ),
                        name="Pole Volume"
                    ),
                    row=2, col=1
                )

                # Add flag/pennant volume highlight
                flag_x = df.index[flag_start_idx:flag_end_idx+1]
                flag_volume = df.iloc[flag_start_idx:flag_end_idx+1]['Volume']

                fig_volume.add_trace(
                    go.Scatter(
                        x=flag_x,
                        y=flag_volume,
                        mode='markers',
                        marker=dict(
                            size=8,
                            color='purple',
                            symbol='circle'
                        ),
                        name="Flag/Pennant Volume"
                    ),
                    row=2, col=1
                )

                # Add breakout volume highlight if confirmed
                if pattern_result['breakout']['confirmed']:
                    breakout_idx = pattern_result['breakout']['index']
                    breakout_date = df.index[breakout_idx]
                    breakout_volume = df.iloc[breakout_idx]['Volume']

                    fig_volume.add_trace(
                        go.Scatter(
                            x=[breakout_date],
                            y=[breakout_volume],
                            mode='markers',
                            marker=dict(
                                size=12,
                                color='orange',
                                symbol='star'
                            ),
                            name="Breakout Volume"
                        ),
                        row=2, col=1
                    )

            # Update layout
            fig_volume.update_layout(
                title=f"Price and Volume Analysis for {symbol}",
                height=800,
                legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.02,
                    xanchor="right",
                    x=1
                ),
                xaxis2_title="Date",
                yaxis_title="Price",
                yaxis2_title="Volume"
            )

            # Display volume chart
            st.plotly_chart(fig_volume, use_container_width=True, key=get_chart_key("fp", "volume", 1))

            # Display volume analysis text
            if 'pole' in pattern_result and 'flag' in pattern_result:
                pole_volume_increased = pattern_result['pole']['volume_increased']
                flag_volume_decreased = pattern_result['flag']['volume_decreased']

                volume_text = "**Volume Analysis:**\n\n"

                if pole_volume_increased:
                    volume_text += "✅ Volume increased during the pole formation, which is ideal for a strong trend.\n\n"
                else:
                    volume_text += "⚠️ Volume did not increase during the pole formation, which is less than ideal.\n\n"

                if flag_volume_decreased:
                    volume_text += "✅ Volume decreased during the flag/pennant formation, which is the expected pattern.\n\n"
                else:
                    volume_text += "⚠️ Volume did not decrease during the flag/pennant formation, which is less than ideal.\n\n"

                if pattern_result['breakout']['confirmed']:
                    breakout_idx = pattern_result['breakout']['index']
                    breakout_volume = df.iloc[breakout_idx]['Volume']
                    avg_volume = df['Volume'].mean()

                    if breakout_volume > avg_volume * 1.5:
                        volume_text += f"✅ Volume on the breakout day was {breakout_volume / avg_volume:.1f}x the average volume, which is a strong confirmation of the pattern."
                    elif breakout_volume > avg_volume:
                        volume_text += f"✅ Volume on the breakout day was {breakout_volume / avg_volume:.1f}x the average volume, which provides some confirmation of the pattern."
                    else:
                        volume_text += f"⚠️ Volume on the breakout day was below average, which may indicate a lack of conviction in the breakout."
                else:
                    volume_text += "Waiting for a breakout to analyze volume confirmation. Ideally, volume should increase significantly on the breakout."

                st.markdown(volume_text)

        except Exception as e:
            st.error(f"Error analyzing Flag/Pennant pattern: {str(e)}")
            logger.error(f"Error analyzing Flag/Pennant pattern for {symbol}: {str(e)}")


async def show_triangle_patterns():
    """Show Triangle pattern analysis."""
    st.header("Triangle Patterns")

    # Introduction
    st.markdown("""
    Triangle patterns are continuation or reversal patterns that form as price consolidates between converging trendlines.

    **Symmetrical Triangle:**
    - Consists of a series of lower highs and higher lows
    - Forms a triangle with converging trendlines
    - Can break in either direction (neutral bias)
    - Often a continuation pattern in the direction of the prior trend

    **Ascending Triangle:**
    - Has a flat upper trendline (resistance) and an upward-sloping lower trendline
    - Typically a bullish pattern
    - Breakout usually occurs to the upside

    **Descending Triangle:**
    - Has a flat lower trendline (support) and a downward-sloping upper trendline
    - Typically a bearish pattern
    - Breakout usually occurs to the downside
    """)

    # Settings section
    st.subheader("Pattern Recognition Settings")

    # Create columns for settings
    col1, col2 = st.columns(2)

    with col1:
        # Get symbol
        symbol = st.text_input(
            "Enter stock symbol",
            value="AAPL",
            key="tr_symbol"
        ).upper()

        # Get interval
        interval = st.selectbox(
            "Interval",
            options=["1d", "1wk", "1mo"],
            index=0,
            key="tr_interval"
        )

    with col2:
        # Get date range
        start_date = st.date_input(
            "Start Date",
            value=date.today() - timedelta(days=365),
            key="tr_start_date"
        )

        end_date = st.date_input(
            "End Date",
            value=date.today(),
            key="tr_end_date"
        )

    # Create expander for advanced parameters
    with st.expander("Advanced Parameters", expanded=False):
        col1, col2 = st.columns(2)

        with col1:
            # Get window size for swing points
            window = st.slider(
                "Window Size for Swing Points",
                min_value=3,
                max_value=10,
                value=5,
                key="tr_window"
            )

            # Get pattern length settings
            min_pattern_length = st.slider(
                "Minimum Pattern Length (bars)",
                min_value=10,
                max_value=50,
                value=15,
                key="tr_min_length"
            )

        with col2:
            max_pattern_length = st.slider(
                "Maximum Pattern Length (bars)",
                min_value=50,
                max_value=200,
                value=100,
                key="tr_max_length"
            )

            # Get minimum touches
            min_touches = st.slider(
                "Minimum Trendline Touches",
                min_value=3,
                max_value=10,
                value=5,
                key="tr_min_touches",
                help="Minimum number of times price must touch the trendlines"
            )

    # Get historical data and identify patterns
    with st.spinner(f"Analyzing {symbol} for Triangle patterns..."):
        try:
            # Get historical data
            df = await data_manager.get_stock_data(symbol, start_date, end_date, interval)

            if df.empty:
                st.error(f"No historical data found for {symbol}")
                return

            # Ensure column names are standardized
            if 'open' in df.columns: df.rename(columns={'open': 'Open'}, inplace=True)
            if 'high' in df.columns: df.rename(columns={'high': 'High'}, inplace=True)
            if 'low' in df.columns: df.rename(columns={'low': 'Low'}, inplace=True)
            if 'close' in df.columns: df.rename(columns={'close': 'Close'}, inplace=True)
            if 'volume' in df.columns: df.rename(columns={'volume': 'Volume'}, inplace=True)

            # Identify triangle pattern
            pattern_result = identify_triangle_pattern(
                df,
                window=window,
                min_pattern_length=min_pattern_length,
                max_pattern_length=max_pattern_length,
                min_touches=min_touches
            )

            # Display pattern result
            if pattern_result['pattern'] == 'none' or pattern_result['pattern'] == 'error':
                st.warning(pattern_result['analysis'])

                # Display price chart without pattern
                fig = go.Figure(
                    go.Candlestick(
                        x=df.index,
                        open=df['Open'],
                        high=df['High'],
                        low=df['Low'],
                        close=df['Close'],
                        name="Price"
                    )
                )

                fig.update_layout(
                    title=f"Price Chart for {symbol}",
                    xaxis_title="Date",
                    yaxis_title="Price",
                    height=600
                )

                st.plotly_chart(fig, use_container_width=True, key=get_chart_key("tr", "pattern", 1))

                return

            # Display pattern information
            st.subheader("Pattern Detected")

            # Create columns for pattern details
            col1, col2, col3 = st.columns(3)

            with col1:
                pattern_name = pattern_result['pattern'].replace('_', ' ').title()
                st.metric(
                    label="Pattern",
                    value=pattern_name
                )

            with col2:
                st.metric(
                    label="Direction",
                    value=pattern_result['direction'].capitalize()
                )

            with col3:
                st.metric(
                    label="Confidence",
                    value=f"{pattern_result['confidence']:.0%}"
                )

            # Display price chart with pattern
            st.subheader(f"Price Chart with {pattern_name} Pattern")

            # Create figure
            fig = go.Figure(
                go.Candlestick(
                    x=df.index,
                    open=df['Open'],
                    high=df['High'],
                    low=df['Low'],
                    close=df['Close'],
                    name="Price"
                )
            )

            # Add pattern points
            for point in pattern_result['points']:
                if point['date'] is not None:  # Skip points without dates
                    fig.add_trace(
                        go.Scatter(
                            x=[point['date']],
                            y=[point['price']],
                            mode='markers',
                            marker=dict(
                                size=10,
                                color='red' if 'upper' in point['name'] else ('green' if 'lower' in point['name'] else 'blue'),
                                symbol='circle'
                            ),
                            name=point['name'].replace('_', ' ').title()
                        )
                    )

            # Add trendlines
            trendlines = pattern_result['trendlines']
            if trendlines:
                # Get the start and end indices for the trendlines
                pattern_points = pattern_result['points']
                start_idx = min([p['index'] for p in pattern_points if 'apex' not in p['name']])

                # For the end index, use either the breakout point or the apex
                if pattern_result['breakout']['confirmed']:
                    end_idx = pattern_result['breakout']['index']
                else:
                    # Find the apex point
                    apex_point = next((p for p in pattern_points if p['name'] == 'apex'), None)
                    end_idx = apex_point['index'] if apex_point else len(df) - 1

                # Ensure end_idx doesn't exceed the dataframe length
                end_idx = min(end_idx, len(df) - 1)

                # Create x-coordinates for trendlines
                x_indices = list(range(start_idx, end_idx + 1))
                x_dates = [df.index[i] if i < len(df) else None for i in x_indices]

                # Filter out None dates
                valid_indices = [i for i, date in enumerate(x_dates) if date is not None]
                x_indices = [x_indices[i] for i in valid_indices]
                x_dates = [x_dates[i] for i in valid_indices]

                # Calculate y-coordinates for upper trendline
                upper_trendline = trendlines['upper']
                upper_values = [upper_trendline['slope'] * i + upper_trendline['intercept'] for i in x_indices]

                # Calculate y-coordinates for lower trendline
                lower_trendline = trendlines['lower']
                lower_values = [lower_trendline['slope'] * i + lower_trendline['intercept'] for i in x_indices]

                # Add upper trendline to chart
                fig.add_trace(
                    go.Scatter(
                        x=x_dates,
                        y=upper_values,
                        mode='lines',
                        line=dict(color='red', width=2, dash='dash'),
                        name="Upper Trendline"
                    )
                )

                # Add lower trendline to chart
                fig.add_trace(
                    go.Scatter(
                        x=x_dates,
                        y=lower_values,
                        mode='lines',
                        line=dict(color='green', width=2, dash='dash'),
                        name="Lower Trendline"
                    )
                )

                # Add breakout point if confirmed
                if pattern_result['breakout']['confirmed']:
                    breakout_idx = pattern_result['breakout']['index']
                    if breakout_idx < len(df):  # Ensure index is valid
                        breakout_date = df.index[breakout_idx]
                        breakout_price = df.iloc[breakout_idx]['Close']

                        fig.add_trace(
                            go.Scatter(
                                x=[breakout_date],
                                y=[breakout_price],
                                mode='markers',
                                marker=dict(
                                    size=12,
                                    color='orange',
                                    symbol='star'
                                ),
                                name="Breakout Point"
                            )
                        )

                # Add target price line if available
                if pattern_result['target'] is not None:
                    # Draw horizontal line at target price from breakout point to end of chart
                    if pattern_result['breakout']['confirmed']:
                        start_idx = pattern_result['breakout']['index']
                        if start_idx < len(df):  # Ensure index is valid
                            target_x = [df.index[start_idx], df.index[-1]]
                            target_y = [pattern_result['target'], pattern_result['target']]

                            fig.add_trace(
                                go.Scatter(
                                    x=target_x,
                                    y=target_y,
                                    mode='lines',
                                    line=dict(color='purple', width=2, dash='dot'),
                                    name="Price Target"
                                )
                            )

            # Update layout
            fig.update_layout(
                title=f"{pattern_name} Pattern for {symbol}",
                xaxis_title="Date",
                yaxis_title="Price",
                height=600,
                legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.02,
                    xanchor="right",
                    x=1
                )
            )

            # Display chart
            st.plotly_chart(fig, use_container_width=True, key=get_chart_key("tr", "pattern", 2))

            # Display analysis
            st.subheader("Pattern Analysis")
            st.markdown(pattern_result['analysis'])

            # Display trading strategy
            st.subheader("Trading Strategy")

            if 'symmetrical' in pattern_result['pattern']:
                strategy_text = """
                **Symmetrical Triangle Strategy:**

                **Entry:**
                - Wait for a breakout in either direction (price closing outside the trendline)
                - Confirm with increased volume on the breakout
                - Enter in the direction of the breakout

                **Stop Loss:**
                - Place stop loss at the opposite side of the triangle
                - Alternative: Place stop loss at the most recent swing high/low inside the triangle

                **Target:**
                - Primary target: Height of the triangle at its widest point, projected from the breakout point
                - Consider taking partial profits at key resistance/support levels

                **Risk Management:**
                - Use proper position sizing (1-2% risk per trade)
                - Be cautious of false breakouts - consider waiting for a retest of the trendline
                """
            elif 'ascending' in pattern_result['pattern']:
                strategy_text = """
                **Ascending Triangle Strategy:**

                **Entry:**
                - Enter a long position when price breaks above the upper resistance line
                - Confirm with increased volume on the breakout
                - Wait for a retest of the breakout level (common but not guaranteed)

                **Stop Loss:**
                - Place stop loss below the most recent swing low inside the triangle
                - Alternative: Place stop loss below the upward-sloping trendline

                **Target:**
                - Primary target: Height of the triangle at its widest point, projected upward from the breakout point
                - Consider taking partial profits at key resistance levels

                **Risk Management:**
                - Use proper position sizing (1-2% risk per trade)
                - Be cautious of false breakouts - consider waiting for a retest of the resistance line
                """
            else:  # descending triangle
                strategy_text = """
                **Descending Triangle Strategy:**

                **Entry:**
                - Enter a short position when price breaks below the lower support line
                - Confirm with increased volume on the breakout
                - Wait for a retest of the breakout level (common but not guaranteed)

                **Stop Loss:**
                - Place stop loss above the most recent swing high inside the triangle
                - Alternative: Place stop loss above the downward-sloping trendline

                **Target:**
                - Primary target: Height of the triangle at its widest point, projected downward from the breakout point
                - Consider taking partial profits at key support levels

                **Risk Management:**
                - Use proper position sizing (1-2% risk per trade)
                - Be cautious of false breakouts - consider waiting for a retest of the support line
                """

            st.markdown(strategy_text)

            # Display volume analysis
            st.subheader("Volume Analysis")

            # Create volume chart
            fig_volume = make_subplots(
                rows=2,
                cols=1,
                shared_xaxes=True,
                vertical_spacing=0.05,
                row_heights=[0.7, 0.3],
                subplot_titles=("Price", "Volume")
            )

            # Add price candlesticks
            fig_volume.add_trace(
                go.Candlestick(
                    x=df.index,
                    open=df['Open'],
                    high=df['High'],
                    low=df['Low'],
                    close=df['Close'],
                    name="Price"
                ),
                row=1, col=1
            )

            # Add trendlines to price chart
            if trendlines:
                # Add upper trendline
                fig_volume.add_trace(
                    go.Scatter(
                        x=x_dates,
                        y=upper_values,
                        mode='lines',
                        line=dict(color='red', width=2, dash='dash'),
                        name="Upper Trendline"
                    ),
                    row=1, col=1
                )

                # Add lower trendline
                fig_volume.add_trace(
                    go.Scatter(
                        x=x_dates,
                        y=lower_values,
                        mode='lines',
                        line=dict(color='green', width=2, dash='dash'),
                        name="Lower Trendline"
                    ),
                    row=1, col=1
                )

            # Add volume bars
            fig_volume.add_trace(
                go.Bar(
                    x=df.index,
                    y=df['Volume'],
                    name="Volume",
                    marker=dict(
                        color=df['Close'].diff().apply(
                            lambda x: 'green' if x > 0 else 'red'
                        )
                    )
                ),
                row=2, col=1
            )

            # Add moving average of volume
            volume_ma = df['Volume'].rolling(window=20).mean()
            fig_volume.add_trace(
                go.Scatter(
                    x=df.index,
                    y=volume_ma,
                    name="Volume MA (20)",
                    line=dict(color='blue', width=1.5)
                ),
                row=2, col=1
            )

            # Add breakout point volume highlight if confirmed
            if pattern_result['breakout']['confirmed']:
                breakout_idx = pattern_result['breakout']['index']
                if breakout_idx < len(df):  # Ensure index is valid
                    breakout_date = df.index[breakout_idx]
                    breakout_volume = df.iloc[breakout_idx]['Volume']

                    fig_volume.add_trace(
                        go.Scatter(
                            x=[breakout_date],
                            y=[breakout_volume],
                            mode='markers',
                            marker=dict(
                                size=12,
                                color='orange',
                                symbol='star'
                            ),
                            name="Breakout Volume"
                        ),
                        row=2, col=1
                    )

            # Update layout
            fig_volume.update_layout(
                title=f"Price and Volume Analysis for {symbol}",
                height=800,
                legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.02,
                    xanchor="right",
                    x=1
                ),
                xaxis2_title="Date",
                yaxis_title="Price",
                yaxis2_title="Volume"
            )

            # Display volume chart
            st.plotly_chart(fig_volume, use_container_width=True, key=get_chart_key("tr", "volume", 1))

            # Display volume analysis text
            if pattern_result['breakout']['confirmed']:
                breakout_idx = pattern_result['breakout']['index']
                if breakout_idx < len(df):  # Ensure index is valid
                    breakout_volume = df.iloc[breakout_idx]['Volume']
                    avg_volume = df['Volume'].mean()

                    if breakout_volume > avg_volume * 1.5:
                        volume_text = f"Volume on the breakout day was {breakout_volume / avg_volume:.1f}x the average volume, which is a strong confirmation of the pattern."
                    elif breakout_volume > avg_volume:
                        volume_text = f"Volume on the breakout day was {breakout_volume / avg_volume:.1f}x the average volume, which provides some confirmation of the pattern."
                    else:
                        volume_text = f"Volume on the breakout day was below average, which may indicate a lack of conviction in the breakout."

                    st.markdown(volume_text)
            else:
                # Volume characteristics during triangle formation
                st.markdown("""
                **Volume Characteristics in Triangle Patterns:**

                Typically, volume tends to decrease as the triangle pattern forms, reflecting decreasing volatility and indecision in the market.

                When a breakout occurs, volume should ideally increase significantly to confirm the validity of the breakout.

                - **Low volume breakout:** Higher probability of a false breakout
                - **High volume breakout:** Higher probability of a successful breakout and continuation

                Watch for volume expansion on the breakout to confirm the pattern.
                """)

        except Exception as e:
            st.error(f"Error analyzing Triangle pattern: {str(e)}")
            logger.error(f"Error analyzing Triangle pattern for {symbol}: {str(e)}")


async def show_head_and_shoulders():
    """Show Head and Shoulders pattern analysis."""
    st.header("Head and Shoulders Pattern")

    # Introduction
    st.markdown("""
    The Head and Shoulders pattern is a reversal pattern that signals a trend change from bullish to bearish (regular H&S)
    or from bearish to bullish (inverse H&S).

    **Regular Head and Shoulders (Bearish):**
    - Forms after an uptrend
    - Consists of three peaks, with the middle peak (head) higher than the two shoulders
    - The neckline connects the lows between the peaks
    - A break below the neckline confirms the pattern

    **Inverse Head and Shoulders (Bullish):**
    - Forms after a downtrend
    - Consists of three troughs, with the middle trough (head) lower than the two shoulders
    - The neckline connects the highs between the troughs
    - A break above the neckline confirms the pattern
    """)

    # Settings section
    st.subheader("Pattern Recognition Settings")

    # Create columns for settings
    col1, col2 = st.columns(2)

    with col1:
        # Get symbol
        symbol = st.text_input(
            "Enter stock symbol",
            value="AAPL",
            key="hs_symbol"
        ).upper()

        # Get interval
        interval = st.selectbox(
            "Interval",
            options=["1d", "1wk", "1mo"],
            index=0,
            key="hs_interval"
        )

    with col2:
        # Get date range
        start_date = st.date_input(
            "Start Date",
            value=date.today() - timedelta(days=365),
            key="hs_start_date"
        )

        end_date = st.date_input(
            "End Date",
            value=date.today(),
            key="hs_end_date"
        )

    # Create expander for advanced parameters
    with st.expander("Advanced Parameters", expanded=False):
        col1, col2 = st.columns(2)

        with col1:
            # Get window size for swing points
            window = st.slider(
                "Window Size for Swing Points",
                min_value=3,
                max_value=10,
                value=5,
                key="hs_window"
            )

            # Get pattern length settings
            min_pattern_length = st.slider(
                "Minimum Pattern Length (bars)",
                min_value=10,
                max_value=50,
                value=20,
                key="hs_min_length"
            )

        with col2:
            max_pattern_length = st.slider(
                "Maximum Pattern Length (bars)",
                min_value=50,
                max_value=200,
                value=100,
                key="hs_max_length"
            )

            # Get shoulder height tolerance
            shoulder_tolerance = st.slider(
                "Shoulder Height Tolerance",
                min_value=0.01,
                max_value=0.2,
                value=0.1,
                step=0.01,
                key="hs_tolerance",
                help="Maximum allowed difference between shoulder heights (as percentage)"
            )

    # Get historical data and identify patterns
    with st.spinner(f"Analyzing {symbol} for Head and Shoulders patterns..."):
        try:
            # Get historical data
            df = await data_manager.get_stock_data(symbol, start_date, end_date, interval)

            if df.empty:
                st.error(f"No historical data found for {symbol}")
                return

            # Ensure column names are standardized
            if 'open' in df.columns: df.rename(columns={'open': 'Open'}, inplace=True)
            if 'high' in df.columns: df.rename(columns={'high': 'High'}, inplace=True)
            if 'low' in df.columns: df.rename(columns={'low': 'Low'}, inplace=True)
            if 'close' in df.columns: df.rename(columns={'close': 'Close'}, inplace=True)
            if 'volume' in df.columns: df.rename(columns={'volume': 'Volume'}, inplace=True)

            # Identify head and shoulders pattern
            pattern_result = identify_head_and_shoulders(
                df,
                window=window,
                min_pattern_length=min_pattern_length,
                max_pattern_length=max_pattern_length,
                shoulder_height_tolerance=shoulder_tolerance
            )

            # Display pattern result
            if pattern_result['pattern'] == 'none' or pattern_result['pattern'] == 'error':
                st.warning(pattern_result['analysis'])

                # Display price chart without pattern
                fig = go.Figure(
                    go.Candlestick(
                        x=df.index,
                        open=df['Open'],
                        high=df['High'],
                        low=df['Low'],
                        close=df['Close'],
                        name="Price"
                    )
                )

                fig.update_layout(
                    title=f"Price Chart for {symbol}",
                    xaxis_title="Date",
                    yaxis_title="Price",
                    height=600
                )

                st.plotly_chart(fig, use_container_width=True, key=get_chart_key("hs", "pattern", 1))

                return

            # Display pattern information
            st.subheader("Pattern Detected")

            # Create columns for pattern details
            col1, col2, col3 = st.columns(3)

            with col1:
                pattern_name = "Head and Shoulders" if pattern_result['pattern'] == 'head_and_shoulders' else "Inverse Head and Shoulders"
                st.metric(
                    label="Pattern",
                    value=pattern_name
                )

            with col2:
                st.metric(
                    label="Direction",
                    value=pattern_result['direction'].capitalize()
                )

            with col3:
                st.metric(
                    label="Confidence",
                    value=f"{pattern_result['confidence']:.0%}"
                )

            # Display price chart with pattern
            st.subheader(f"Price Chart with {pattern_name} Pattern")

            # Create figure
            fig = go.Figure(
                go.Candlestick(
                    x=df.index,
                    open=df['Open'],
                    high=df['High'],
                    low=df['Low'],
                    close=df['Close'],
                    name="Price"
                )
            )

            # Add pattern points
            for point in pattern_result['points']:
                fig.add_trace(
                    go.Scatter(
                        x=[point['date']],
                        y=[point['price']],
                        mode='markers',
                        marker=dict(
                            size=10,
                            color='red' if 'shoulder' in point['name'] else ('green' if 'head' in point['name'] else 'blue'),
                            symbol='circle'
                        ),
                        name=point['name'].replace('_', ' ').title()
                    )
                )

            # Add neckline
            neckline = pattern_result['neckline']
            if neckline:
                # Get x-coordinates for the neckline (from left shoulder to end of chart)
                left_shoulder_idx = next(point['index'] for point in pattern_result['points'] if point['name'] == 'left_shoulder')
                right_shoulder_idx = next(point['index'] for point in pattern_result['points'] if point['name'] == 'right_shoulder')

                # Create x-coordinates for neckline
                x_indices = list(range(left_shoulder_idx, len(df)))
                x_dates = [df.index[i] for i in x_indices]

                # Calculate y-coordinates for neckline
                y_values = [neckline['slope'] * i + neckline['intercept'] for i in x_indices]

                # Add neckline to chart
                fig.add_trace(
                    go.Scatter(
                        x=x_dates,
                        y=y_values,
                        mode='lines',
                        line=dict(color='purple', width=2, dash='dash'),
                        name="Neckline"
                    )
                )

                # Add breakout point if confirmed
                if pattern_result.get('breakout_confirmed', False):
                    breakout_idx = pattern_result['breakout_index']
                    breakout_date = df.index[breakout_idx]
                    breakout_price = df.iloc[breakout_idx]['Close']

                    fig.add_trace(
                        go.Scatter(
                            x=[breakout_date],
                            y=[breakout_price],
                            mode='markers',
                            marker=dict(
                                size=12,
                                color='orange',
                                symbol='star'
                            ),
                            name="Breakout Point"
                        )
                    )

                # Add target price line
                if pattern_result['target'] is not None:
                    # Draw horizontal line at target price from breakout point to end of chart
                    start_idx = right_shoulder_idx
                    if pattern_result.get('breakout_confirmed', False):
                        start_idx = pattern_result['breakout_index']

                    target_x = [df.index[start_idx], df.index[-1]]
                    target_y = [pattern_result['target'], pattern_result['target']]

                    fig.add_trace(
                        go.Scatter(
                            x=target_x,
                            y=target_y,
                            mode='lines',
                            line=dict(color='red', width=2, dash='dot'),
                            name="Price Target"
                        )
                    )

            # Update layout
            fig.update_layout(
                title=f"{pattern_name} Pattern for {symbol}",
                xaxis_title="Date",
                yaxis_title="Price",
                height=600,
                legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.02,
                    xanchor="right",
                    x=1
                )
            )

            # Display chart
            st.plotly_chart(fig, use_container_width=True, key=get_chart_key("hs", "pattern", 2))

            # Display analysis
            st.subheader("Pattern Analysis")
            st.markdown(pattern_result['analysis'])

            # Display trading strategy
            st.subheader("Trading Strategy")

            if pattern_result['pattern'] == 'head_and_shoulders':
                strategy_text = """
                **Bearish Head and Shoulders Strategy:**

                **Entry:**
                - Enter a short position when price breaks below the neckline
                - Confirm with increased volume on the breakout
                - Wait for a retest of the neckline from below (common but not guaranteed)

                **Stop Loss:**
                - Place stop loss above the right shoulder
                - Alternative: Place stop loss above the head for a wider stop

                **Target:**
                - Primary target: Distance from head to neckline, projected downward from the breakout point
                - Consider taking partial profits at support levels

                **Risk Management:**
                - Use proper position sizing (1-2% risk per trade)
                - Consider a time stop if price doesn't reach target within a reasonable timeframe
                """
            else:  # inverse_head_and_shoulders
                strategy_text = """
                **Bullish Inverse Head and Shoulders Strategy:**

                **Entry:**
                - Enter a long position when price breaks above the neckline
                - Confirm with increased volume on the breakout
                - Wait for a retest of the neckline from above (common but not guaranteed)

                **Stop Loss:**
                - Place stop loss below the right shoulder
                - Alternative: Place stop loss below the head for a wider stop

                **Target:**
                - Primary target: Distance from head to neckline, projected upward from the breakout point
                - Consider taking partial profits at resistance levels

                **Risk Management:**
                - Use proper position sizing (1-2% risk per trade)
                - Consider a time stop if price doesn't reach target within a reasonable timeframe
                """

            st.markdown(strategy_text)

            # Display volume analysis
            st.subheader("Volume Analysis")

            # Create volume chart
            fig_volume = make_subplots(
                rows=2,
                cols=1,
                shared_xaxes=True,
                vertical_spacing=0.05,
                row_heights=[0.7, 0.3],
                subplot_titles=("Price", "Volume")
            )

            # Add price candlesticks
            fig_volume.add_trace(
                go.Candlestick(
                    x=df.index,
                    open=df['Open'],
                    high=df['High'],
                    low=df['Low'],
                    close=df['Close'],
                    name="Price"
                ),
                row=1, col=1
            )

            # Add pattern points to price chart
            for point in pattern_result['points']:
                fig_volume.add_trace(
                    go.Scatter(
                        x=[point['date']],
                        y=[point['price']],
                        mode='markers',
                        marker=dict(
                            size=10,
                            color='red' if 'shoulder' in point['name'] else ('green' if 'head' in point['name'] else 'blue'),
                            symbol='circle'
                        ),
                        name=point['name'].replace('_', ' ').title()
                    ),
                    row=1, col=1
                )

            # Add neckline to price chart
            if neckline:
                fig_volume.add_trace(
                    go.Scatter(
                        x=x_dates,
                        y=y_values,
                        mode='lines',
                        line=dict(color='purple', width=2, dash='dash'),
                        name="Neckline"
                    ),
                    row=1, col=1
                )

            # Add volume bars
            fig_volume.add_trace(
                go.Bar(
                    x=df.index,
                    y=df['Volume'],
                    name="Volume",
                    marker=dict(
                        color=df['Close'].diff().apply(
                            lambda x: 'green' if x > 0 else 'red'
                        )
                    )
                ),
                row=2, col=1
            )

            # Add moving average of volume
            volume_ma = df['Volume'].rolling(window=20).mean()
            fig_volume.add_trace(
                go.Scatter(
                    x=df.index,
                    y=volume_ma,
                    name="Volume MA (20)",
                    line=dict(color='blue', width=1.5)
                ),
                row=2, col=1
            )

            # Update layout
            fig_volume.update_layout(
                title=f"Price and Volume Analysis for {symbol}",
                height=800,
                legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.02,
                    xanchor="right",
                    x=1
                ),
                xaxis2_title="Date",
                yaxis_title="Price",
                yaxis2_title="Volume"
            )

            # Display volume chart
            st.plotly_chart(fig_volume, use_container_width=True, key=get_chart_key("hs", "volume", 1))

            # Display volume analysis text
            if pattern_result.get('breakout_confirmed', False):
                breakout_idx = pattern_result['breakout_index']
                breakout_volume = df.iloc[breakout_idx]['Volume']
                avg_volume = df['Volume'].mean()

                if breakout_volume > avg_volume * 1.5:
                    volume_text = f"Volume on the breakout day was {breakout_volume / avg_volume:.1f}x the average volume, which is a strong confirmation of the pattern."
                elif breakout_volume > avg_volume:
                    volume_text = f"Volume on the breakout day was {breakout_volume / avg_volume:.1f}x the average volume, which provides some confirmation of the pattern."
                else:
                    volume_text = f"Volume on the breakout day was below average, which may indicate a lack of conviction in the breakout."

                st.markdown(volume_text)
            else:
                st.markdown("Waiting for a breakout to analyze volume confirmation.")

        except Exception as e:
            st.error(f"Error analyzing Head and Shoulders pattern: {str(e)}")
            logger.error(f"Error analyzing Head and Shoulders pattern for {symbol}: {str(e)}")
