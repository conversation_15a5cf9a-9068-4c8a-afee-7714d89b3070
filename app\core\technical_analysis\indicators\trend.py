"""
Trend indicators implementation.
"""
from typing import Dict, List, Optional, Union

import numpy as np
import pandas as pd
import pandas_ta as ta

from app.core.technical_analysis.indicators.base import BaseIndicator
from app.utils.logging import logger


class MovingAverage(BaseIndicator):
    """
    Moving Average indicator.

    This class implements various types of moving averages:
    - Simple Moving Average (SMA)
    - Exponential Moving Average (EMA)
    - Weighted Moving Average (WMA)
    """

    def __init__(self, ma_type: str = "sma"):
        """
        Initialize the Moving Average indicator.

        Args:
            ma_type: Type of moving average ('sma', 'ema', 'wma')
        """
        super().__init__(f"{ma_type.upper()}")
        self.ma_type = ma_type.lower()

        # Validate MA type
        valid_types = ["sma", "ema", "wma"]
        if self.ma_type not in valid_types:
            raise ValueError(f"Invalid MA type: {ma_type}. Must be one of {valid_types}")

    def calculate(
        self,
        data: pd.DataFrame,
        period: int = 20,
        column: str = "close",
        **kwargs
    ) -> pd.DataFrame:
        """
        Calculate the Moving Average.

        Args:
            data: DataFrame with OHLCV data
            period: Period for the moving average
            column: Column to use for calculation
            **kwargs: Additional parameters

        Returns:
            DataFrame with the indicator values
        """
        # Validate data
        if not self.validate_data(data, [column]):
            logger.warning(f"Invalid data for {self.name} calculation")
            return data

        # Make a copy of the data
        result = data.copy()

        # Calculate the moving average
        if self.ma_type == "sma":
            result[f"sma_{period}"] = ta.sma(result[column], length=period)
        elif self.ma_type == "ema":
            result[f"ema_{period}"] = ta.ema(result[column], length=period)
        elif self.ma_type == "wma":
            result[f"wma_{period}"] = ta.wma(result[column], length=period)

        return result

    def get_signal(
        self,
        data: pd.DataFrame,
        period: int = 20,
        column: str = "close",
        **kwargs
    ) -> pd.DataFrame:
        """
        Get trading signals from the Moving Average.

        Args:
            data: DataFrame with indicator values
            period: Period for the moving average
            column: Column to use for comparison
            **kwargs: Additional parameters

        Returns:
            DataFrame with trading signals
        """
        # Calculate the indicator if not already calculated
        ma_column = f"{self.ma_type}_{period}"
        if ma_column not in data.columns:
            data = self.calculate(data, period=period, column=column, **kwargs)

        # Make a copy of the data
        result = data.copy()

        # Generate signals
        # 1 = buy, -1 = sell, 0 = neutral
        result[f"{ma_column}_signal"] = 0

        # Price crosses above MA = buy signal
        result.loc[result[column] > result[ma_column], f"{ma_column}_signal"] = 1

        # Price crosses below MA = sell signal
        result.loc[result[column] < result[ma_column], f"{ma_column}_signal"] = -1

        return result


class MACD(BaseIndicator):
    """
    Moving Average Convergence Divergence (MACD) indicator.
    """

    def __init__(self):
        """Initialize the MACD indicator."""
        super().__init__("MACD")

    def calculate(
        self,
        data: pd.DataFrame,
        fast_period: int = 12,
        slow_period: int = 26,
        signal_period: int = 9,
        column: str = "close",
        **kwargs
    ) -> pd.DataFrame:
        """
        Calculate the MACD indicator.

        Args:
            data: DataFrame with OHLCV data
            fast_period: Period for the fast EMA
            slow_period: Period for the slow EMA
            signal_period: Period for the signal line
            column: Column to use for calculation
            **kwargs: Additional parameters

        Returns:
            DataFrame with the indicator values
        """
        # Validate data
        if not self.validate_data(data, [column]):
            logger.warning(f"Invalid data for {self.name} calculation")
            return data

        # Make a copy of the data
        result = data.copy()

        # Calculate MACD
        try:
            # Calculate MACD using ta library
            macd = ta.macd(
                result[column],
                fast=fast_period,
                slow=slow_period,
                signal=signal_period
            )

            # Check if macd is None or empty
            if macd is None:
                logger.warning("MACD calculation returned None")
                # Calculate manually as fallback
                exp1 = result[column].ewm(span=fast_period, adjust=False).mean()
                exp2 = result[column].ewm(span=slow_period, adjust=False).mean()
                macd_line = exp1 - exp2
                signal_line = macd_line.ewm(span=signal_period, adjust=False).mean()
                histogram = macd_line - signal_line

                # Add MACD columns to the result
                result["macd"] = macd_line
                result["macd_signal"] = signal_line
                result["macd_histogram"] = histogram
            else:
                # Get column names dynamically
                macd_col = [col for col in macd.columns if 'MACD_' in col][0]
                signal_col = [col for col in macd.columns if 'MACDs_' in col][0]
                hist_col = [col for col in macd.columns if 'MACDh_' in col][0]

                # Add MACD columns to the result
                result["macd"] = macd[macd_col]
                result["macd_signal"] = macd[signal_col]
                result["macd_histogram"] = macd[hist_col]
        except Exception as e:
            logger.error(f"Error calculating MACD: {e}")
            # Calculate manually as fallback
            exp1 = result[column].ewm(span=fast_period, adjust=False).mean()
            exp2 = result[column].ewm(span=slow_period, adjust=False).mean()
            macd_line = exp1 - exp2
            signal_line = macd_line.ewm(span=signal_period, adjust=False).mean()
            histogram = macd_line - signal_line

            # Add MACD columns to the result
            result["macd"] = macd_line
            result["macd_signal"] = signal_line
            result["macd_histogram"] = histogram

        return result

    def get_signal(
        self,
        data: pd.DataFrame,
        **kwargs
    ) -> pd.DataFrame:
        """
        Get trading signals from the MACD indicator.

        Args:
            data: DataFrame with indicator values
            **kwargs: Additional parameters

        Returns:
            DataFrame with trading signals
        """
        # Calculate the indicator if not already calculated
        if "macd" not in data.columns or "macd_signal" not in data.columns:
            data = self.calculate(data, **kwargs)

        # Make a copy of the data
        result = data.copy()

        # Generate signals
        # 1 = buy, -1 = sell, 0 = neutral
        result["macd_crossover_signal"] = 0

        # MACD crosses above signal line = buy signal
        result.loc[
            (result["macd"] > result["macd_signal"]) &
            (result["macd"].shift(1) <= result["macd_signal"].shift(1)),
            "macd_crossover_signal"
        ] = 1

        # MACD crosses below signal line = sell signal
        result.loc[
            (result["macd"] < result["macd_signal"]) &
            (result["macd"].shift(1) >= result["macd_signal"].shift(1)),
            "macd_crossover_signal"
        ] = -1

        return result
