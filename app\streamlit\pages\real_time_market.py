"""
Real-time market data page for Streamlit app.
"""
import asyncio
import time
from datetime import datetime, timedelta

import pandas as pd
import plotly.graph_objects as go
import streamlit as st
from plotly.subplots import make_subplots

from app.core.data_collection.real_time_data import real_time_data_manager
from app.utils.logging import logger


async def show_real_time_market_page():
    """Show the real-time market data page."""
    st.title("Real-Time Market Data")
    
    # Create tabs
    tabs = st.tabs([
        "Market Overview",
        "Stock Watchlist",
        "Intraday Charts"
    ])
    
    # Market Overview tab
    with tabs[0]:
        await show_market_overview()
    
    # Stock Watchlist tab
    with tabs[1]:
        await show_stock_watchlist()
    
    # Intraday Charts tab
    with tabs[2]:
        await show_intraday_charts()


async def show_market_overview():
    """Show market overview section."""
    st.header("Market Overview")
    
    # Market indices
    indices = ["^GSPC", "^DJI", "^IXIC", "^RUT"]
    index_names = {
        "^GSPC": "S&P 500",
        "^DJI": "Dow Jones",
        "^IXIC": "NASDAQ",
        "^RUT": "Russell 2000"
    }
    
    # Get real-time quotes for indices
    with st.spinner("Fetching market data..."):
        quotes = await real_time_data_manager.get_real_time_quotes(indices)
    
    # Display market indices in a grid
    cols = st.columns(len(indices))
    
    for i, (symbol, name) in enumerate(index_names.items()):
        quote = quotes.get(symbol, {})
        price = quote.get("price")
        change = quote.get("change")
        change_percent = quote.get("change_percent")
        
        if price is not None and change is not None:
            # Format values
            price_str = f"${price:.2f}"
            change_str = f"{change:+.2f} ({change_percent:+.2%})"
            
            # Determine color based on change
            delta_color = "normal"
            if change > 0:
                delta_color = "success"
            elif change < 0:
                delta_color = "danger"
            
            # Display metric
            with cols[i]:
                st.metric(
                    label=name,
                    value=price_str,
                    delta=change_str,
                    delta_color=delta_color
                )
        else:
            with cols[i]:
                st.metric(
                    label=name,
                    value="N/A",
                    delta="N/A",
                    delta_color="off"
                )
    
    # Display last updated time
    st.caption(f"Last updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Add refresh button
    if st.button("Refresh Market Data", key="refresh_market"):
        # Clear cache to force refresh
        real_time_data_manager.clear_cache()
        st.experimental_rerun()
    
    # Display market sectors
    st.subheader("Market Sectors")
    
    # Sector ETFs
    sectors = {
        "XLK": "Technology",
        "XLF": "Financials",
        "XLV": "Healthcare",
        "XLE": "Energy",
        "XLI": "Industrials",
        "XLP": "Consumer Staples",
        "XLY": "Consumer Discretionary",
        "XLB": "Materials",
        "XLU": "Utilities",
        "XLRE": "Real Estate",
        "XLC": "Communication Services"
    }
    
    # Get real-time quotes for sectors
    with st.spinner("Fetching sector data..."):
        sector_quotes = await real_time_data_manager.get_real_time_quotes(list(sectors.keys()))
    
    # Create DataFrame for sectors
    sector_data = []
    
    for symbol, name in sectors.items():
        quote = sector_quotes.get(symbol, {})
        price = quote.get("price")
        change = quote.get("change")
        change_percent = quote.get("change_percent")
        
        if price is not None and change is not None:
            sector_data.append({
                "Sector": name,
                "Symbol": symbol,
                "Price": price,
                "Change": change,
                "Change %": change_percent * 100
            })
    
    if sector_data:
        # Create DataFrame
        df = pd.DataFrame(sector_data)
        
        # Sort by Change %
        df = df.sort_values("Change %", ascending=False)
        
        # Format columns
        df["Price"] = df["Price"].map("${:.2f}".format)
        df["Change"] = df["Change"].map("{:+.2f}".format)
        df["Change %"] = df["Change %"].map("{:+.2f}%".format)
        
        # Display as table
        st.dataframe(
            df,
            column_config={
                "Sector": st.column_config.TextColumn("Sector"),
                "Symbol": st.column_config.TextColumn("Symbol"),
                "Price": st.column_config.TextColumn("Price"),
                "Change": st.column_config.TextColumn("Change"),
                "Change %": st.column_config.TextColumn("Change %")
            },
            hide_index=True,
            use_container_width=True
        )
        
        # Create sector performance chart
        fig = go.Figure()
        
        # Add bars
        fig.add_trace(go.Bar(
            x=df["Sector"],
            y=df["Change %"].str.rstrip("%").astype(float),
            marker_color=df["Change %"].str.rstrip("%").astype(float).apply(
                lambda x: "green" if x > 0 else "red"
            ),
            text=df["Change %"],
            textposition="auto"
        ))
        
        # Update layout
        fig.update_layout(
            title="Sector Performance",
            xaxis_title="Sector",
            yaxis_title="Change (%)",
            height=400,
            margin=dict(l=0, r=0, t=40, b=0)
        )
        
        # Display chart
        st.plotly_chart(fig, use_container_width=True)
    else:
        st.warning("No sector data available")


async def show_stock_watchlist():
    """Show stock watchlist section."""
    st.header("Stock Watchlist")
    
    # Get watchlist from session state or initialize
    if "watchlist" not in st.session_state:
        st.session_state.watchlist = ["AAPL", "MSFT", "GOOGL", "AMZN", "META"]
    
    # Add stock to watchlist
    col1, col2 = st.columns([3, 1])
    
    with col1:
        new_symbol = st.text_input("Add Stock to Watchlist", key="add_stock").strip().upper()
    
    with col2:
        if st.button("Add", key="add_stock_button"):
            if new_symbol and new_symbol not in st.session_state.watchlist:
                st.session_state.watchlist.append(new_symbol)
                st.success(f"Added {new_symbol} to watchlist")
            elif new_symbol in st.session_state.watchlist:
                st.warning(f"{new_symbol} is already in your watchlist")
            else:
                st.warning("Please enter a valid symbol")
    
    # Display watchlist
    if st.session_state.watchlist:
        # Get real-time quotes for watchlist
        with st.spinner("Fetching watchlist data..."):
            quotes = await real_time_data_manager.get_real_time_quotes(st.session_state.watchlist)
        
        # Create DataFrame for watchlist
        watchlist_data = []
        
        for symbol in st.session_state.watchlist:
            quote = quotes.get(symbol, {})
            price = quote.get("price")
            change = quote.get("change")
            change_percent = quote.get("change_percent")
            volume = quote.get("volume")
            market_cap = quote.get("market_cap")
            
            if price is not None:
                watchlist_data.append({
                    "Symbol": symbol,
                    "Price": price,
                    "Change": change if change is not None else 0,
                    "Change %": change_percent * 100 if change_percent is not None else 0,
                    "Volume": volume if volume is not None else 0,
                    "Market Cap": market_cap if market_cap is not None else 0,
                    "Actions": "Remove"
                })
            else:
                watchlist_data.append({
                    "Symbol": symbol,
                    "Price": None,
                    "Change": None,
                    "Change %": None,
                    "Volume": None,
                    "Market Cap": None,
                    "Actions": "Remove"
                })
        
        if watchlist_data:
            # Create DataFrame
            df = pd.DataFrame(watchlist_data)
            
            # Format columns
            df["Price"] = df["Price"].apply(lambda x: f"${x:.2f}" if x is not None else "N/A")
            df["Change"] = df["Change"].apply(lambda x: f"{x:+.2f}" if x is not None else "N/A")
            df["Change %"] = df["Change %"].apply(lambda x: f"{x:+.2f}%" if x is not None else "N/A")
            df["Volume"] = df["Volume"].apply(lambda x: f"{x:,.0f}" if x is not None else "N/A")
            df["Market Cap"] = df["Market Cap"].apply(
                lambda x: f"${x/1e9:.2f}B" if x is not None and x > 0 else "N/A"
            )
            
            # Display as table with remove buttons
            edited_df = st.data_editor(
                df,
                column_config={
                    "Symbol": st.column_config.TextColumn("Symbol"),
                    "Price": st.column_config.TextColumn("Price"),
                    "Change": st.column_config.TextColumn("Change"),
                    "Change %": st.column_config.TextColumn("Change %"),
                    "Volume": st.column_config.TextColumn("Volume"),
                    "Market Cap": st.column_config.TextColumn("Market Cap"),
                    "Actions": st.column_config.ButtonColumn("Actions")
                },
                hide_index=True,
                use_container_width=True,
                key="watchlist_table"
            )
            
            # Handle remove button clicks
            if edited_df is not None and "Actions" in edited_df.columns:
                for i, action in enumerate(edited_df["Actions"]):
                    if action == "clicked":
                        symbol_to_remove = df.iloc[i]["Symbol"]
                        st.session_state.watchlist.remove(symbol_to_remove)
                        st.success(f"Removed {symbol_to_remove} from watchlist")
                        st.experimental_rerun()
            
            # Display last updated time
            st.caption(f"Last updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # Add refresh button
            if st.button("Refresh Watchlist", key="refresh_watchlist"):
                # Clear cache to force refresh
                real_time_data_manager.clear_cache()
                st.experimental_rerun()
        else:
            st.warning("No watchlist data available")
    else:
        st.info("Your watchlist is empty. Add stocks to get started.")


async def show_intraday_charts():
    """Show intraday charts section."""
    st.header("Intraday Charts")
    
    # Symbol input
    symbol = st.text_input("Enter Stock Symbol", value="AAPL", key="intraday_symbol").strip().upper()
    
    # Interval selection
    col1, col2, col3 = st.columns(3)
    
    with col1:
        interval = st.selectbox(
            "Interval",
            options=["1m", "5m", "15m", "30m", "1h"],
            index=1,
            key="intraday_interval"
        )
    
    with col2:
        limit = st.slider(
            "Number of Data Points",
            min_value=20,
            max_value=500,
            value=100,
            step=10,
            key="intraday_limit"
        )
    
    with col3:
        provider = st.selectbox(
            "Data Provider",
            options=["yahoo", "alpha_vantage"],
            index=0,
            key="intraday_provider"
        )
    
    # Get intraday data
    if symbol:
        with st.spinner(f"Fetching intraday data for {symbol}..."):
            df = await real_time_data_manager.get_intraday_data(
                symbol=symbol,
                interval=interval,
                limit=limit,
                provider=provider
            )
        
        if not df.empty:
            # Create figure with secondary y-axis
            fig = make_subplots(
                rows=2,
                cols=1,
                shared_xaxes=True,
                vertical_spacing=0.1,
                row_heights=[0.7, 0.3],
                subplot_titles=(f"{symbol} Price", "Volume")
            )
            
            # Add candlestick chart
            fig.add_trace(
                go.Candlestick(
                    x=df["date"],
                    open=df["open"],
                    high=df["high"],
                    low=df["low"],
                    close=df["close"],
                    name="Price"
                ),
                row=1, col=1
            )
            
            # Add volume bar chart
            fig.add_trace(
                go.Bar(
                    x=df["date"],
                    y=df["volume"],
                    name="Volume",
                    marker=dict(
                        color=df["close"].diff().apply(
                            lambda x: "green" if x > 0 else "red"
                        )
                    )
                ),
                row=2, col=1
            )
            
            # Add moving averages
            ma_periods = [9, 20, 50]
            ma_colors = ["blue", "orange", "purple"]
            
            for period, color in zip(ma_periods, ma_colors):
                if len(df) >= period:
                    ma = df["close"].rolling(window=period).mean()
                    fig.add_trace(
                        go.Scatter(
                            x=df["date"],
                            y=ma,
                            name=f"MA {period}",
                            line=dict(color=color, width=1)
                        ),
                        row=1, col=1
                    )
            
            # Update layout
            fig.update_layout(
                title=f"{symbol} Intraday Chart ({interval})",
                xaxis_title="Time",
                yaxis_title="Price",
                xaxis_rangeslider_visible=False,
                height=600,
                margin=dict(l=0, r=0, t=50, b=0)
            )
            
            # Update y-axes
            fig.update_yaxes(title_text="Price", row=1, col=1)
            fig.update_yaxes(title_text="Volume", row=2, col=1)
            
            # Display chart
            st.plotly_chart(fig, use_container_width=True)
            
            # Display last updated time
            st.caption(f"Last updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # Add refresh button
            if st.button("Refresh Chart", key="refresh_chart"):
                # Clear cache to force refresh
                real_time_data_manager.clear_cache()
                st.experimental_rerun()
            
            # Display data table
            with st.expander("View Data Table"):
                # Format DataFrame for display
                display_df = df.copy()
                display_df["date"] = display_df["date"].dt.strftime("%Y-%m-%d %H:%M:%S")
                display_df = display_df.sort_values("date", ascending=False)
                
                st.dataframe(
                    display_df,
                    hide_index=True,
                    use_container_width=True
                )
        else:
            st.warning(f"No intraday data available for {symbol}")
    else:
        st.info("Enter a stock symbol to view intraday chart")
