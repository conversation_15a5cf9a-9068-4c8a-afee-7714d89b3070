"""
Technical Analysis Manager implementation.

This module provides a unified interface for all technical analysis functionality.
"""
from typing import Dict, List, Optional, Union

import pandas as pd

from app.core.technical_analysis.indicators.trend import SMA, EMA, MACD
from app.core.technical_analysis.indicators.momentum import RSI, Stochastic, CCI, MFI
from app.core.technical_analysis.indicators.volatility import BollingerBands, ATR
from app.core.technical_analysis.indicators.volume import OBV, VWAP, AD, ADOSC
from app.core.technical_analysis.indicators.ichimoku import IchimokuCloud
from app.core.technical_analysis.patterns.candlestick import CandlestickPatterns
from app.core.technical_analysis.patterns.chart import ChartPatterns
from app.settings import get_settings
from app.utils.logging import logger


class TechnicalAnalysisManager:
    """
    Technical Analysis Manager.

    This class provides a unified interface for all technical analysis functionality.
    """

    def __init__(self):
        """Initialize the Technical Analysis Manager."""
        logger.info("Initializing Technical Analysis Manager")

        # Get settings
        self.settings = get_settings()

        # Initialize indicators
        self.indicators = {
            # Trend indicators
            "sma": SMA(),
            "ema": EMA(),
            "macd": MACD(),

            # Momentum indicators
            "rsi": RSI(),
            "stochastic": Stochastic(),
            "cci": CCI(),
            "mfi": MFI(),

            # Volatility indicators
            "bollinger_bands": BollingerBands(),
            "atr": ATR(),

            # Volume indicators
            "obv": OBV(),
            "vwap": VWAP(),
            "ad": AD(),
            "adosc": ADOSC(),

            # Advanced indicators
            "ichimoku": IchimokuCloud()
        }

        # Initialize patterns
        self.candlestick_patterns = CandlestickPatterns()
        self.chart_patterns = ChartPatterns()

        # Initialize cache
        self.cache = {}

    def calculate_indicator(
        self,
        data: pd.DataFrame,
        indicator: str,
        **kwargs
    ) -> pd.DataFrame:
        """
        Calculate a technical indicator.

        Args:
            data: DataFrame with OHLCV data
            indicator: Indicator name
            **kwargs: Additional parameters for the indicator

        Returns:
            DataFrame with the indicator values
        """
        # Check if indicator exists
        if indicator not in self.indicators:
            logger.error(f"Indicator '{indicator}' not found")
            return data

        # Calculate indicator
        return self.indicators[indicator].calculate(data, **kwargs)

    def get_indicator_signal(
        self,
        data: pd.DataFrame,
        indicator: str,
        **kwargs
    ) -> pd.DataFrame:
        """
        Get trading signals from a technical indicator.

        Args:
            data: DataFrame with OHLCV data
            indicator: Indicator name
            **kwargs: Additional parameters for the indicator

        Returns:
            DataFrame with trading signals
        """
        # Check if indicator exists
        if indicator not in self.indicators:
            logger.error(f"Indicator '{indicator}' not found")
            return data

        # Get signals
        return self.indicators[indicator].get_signal(data, **kwargs)

    def calculate_multiple_indicators(
        self,
        data: pd.DataFrame,
        indicators: List[Dict]
    ) -> pd.DataFrame:
        """
        Calculate multiple technical indicators.

        Args:
            data: DataFrame with OHLCV data
            indicators: List of dictionaries with indicator parameters
                Each dictionary should have an 'indicator' key and optional parameters

        Returns:
            DataFrame with all indicator values
        """
        result = data.copy()

        for indicator_params in indicators:
            # Get indicator name
            indicator = indicator_params.pop("indicator", None)

            if not indicator:
                logger.warning("Missing indicator name in parameters")
                continue

            # Calculate indicator
            result = self.calculate_indicator(result, indicator, **indicator_params)

        return result

    def get_multiple_indicator_signals(
        self,
        data: pd.DataFrame,
        indicators: List[Dict]
    ) -> pd.DataFrame:
        """
        Get trading signals from multiple technical indicators.

        Args:
            data: DataFrame with OHLCV data
            indicators: List of dictionaries with indicator parameters
                Each dictionary should have an 'indicator' key and optional parameters

        Returns:
            DataFrame with all trading signals
        """
        result = data.copy()

        for indicator_params in indicators:
            # Get indicator name
            indicator = indicator_params.pop("indicator", None)

            if not indicator:
                logger.warning("Missing indicator name in parameters")
                continue

            # Get signals
            result = self.get_indicator_signal(result, indicator, **indicator_params)

        return result

    def identify_candlestick_patterns(
        self,
        data: pd.DataFrame,
        patterns: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """
        Identify candlestick patterns in the data.

        Args:
            data: DataFrame with OHLCV data
            patterns: List of pattern names to identify (None for all patterns)

        Returns:
            DataFrame with pattern signals
        """
        return self.candlestick_patterns.identify_patterns(data, patterns)

    def identify_chart_patterns(
        self,
        data: pd.DataFrame,
        patterns: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """
        Identify chart patterns in the data.

        Args:
            data: DataFrame with OHLCV data
            patterns: List of pattern names to identify (None for all patterns)

        Returns:
            DataFrame with pattern signals
        """
        return self.chart_patterns.identify_patterns(data, patterns)

    def analyze_symbol(
        self,
        data: pd.DataFrame,
        indicators: Optional[List[Dict]] = None,
        candlestick_patterns: Optional[List[str]] = None,
        chart_patterns: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """
        Perform comprehensive technical analysis on a symbol.

        Args:
            data: DataFrame with OHLCV data
            indicators: List of dictionaries with indicator parameters
            candlestick_patterns: List of candlestick pattern names to identify
            chart_patterns: List of chart pattern names to identify

        Returns:
            DataFrame with all analysis results
        """
        result = data.copy()

        # Calculate indicators
        if indicators:
            result = self.calculate_multiple_indicators(result, indicators)

        # Identify candlestick patterns
        if candlestick_patterns is not None:
            result = self.identify_candlestick_patterns(result, candlestick_patterns)

        # Identify chart patterns
        if chart_patterns is not None:
            result = self.identify_chart_patterns(result, chart_patterns)

        return result

    def get_trading_signals(
        self,
        data: pd.DataFrame,
        indicators: Optional[List[Dict]] = None,
        candlestick_patterns: Optional[List[str]] = None,
        chart_patterns: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """
        Get comprehensive trading signals for a symbol.

        Args:
            data: DataFrame with OHLCV data
            indicators: List of dictionaries with indicator parameters
            candlestick_patterns: List of candlestick pattern names to identify
            chart_patterns: List of chart pattern names to identify

        Returns:
            DataFrame with all trading signals
        """
        result = data.copy()

        # Get indicator signals
        if indicators:
            result = self.get_multiple_indicator_signals(result, indicators)

        # Identify candlestick patterns
        if candlestick_patterns is not None:
            result = self.identify_candlestick_patterns(result, candlestick_patterns)

        # Identify chart patterns
        if chart_patterns is not None:
            result = self.identify_chart_patterns(result, chart_patterns)

        # Aggregate signals
        result = self._aggregate_signals(result)

        return result

    def _aggregate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Aggregate all trading signals into a single signal.

        Args:
            data: DataFrame with trading signals

        Returns:
            DataFrame with aggregated signals
        """
        result = data.copy()

        # Find all signal columns
        signal_columns = [col for col in result.columns if col.endswith("_signal")]

        if not signal_columns:
            return result

        # Create aggregated signal column
        result["aggregated_signal"] = 0

        # Simple aggregation: sum all signals
        for col in signal_columns:
            result["aggregated_signal"] += result[col]

        # Normalize aggregated signal
        result["aggregated_signal_strength"] = result["aggregated_signal"].abs() / len(signal_columns)

        # Classify signal strength
        result["signal_strength"] = "neutral"
        result.loc[result["aggregated_signal"] > 0, "signal_strength"] = "bullish"
        result.loc[result["aggregated_signal"] < 0, "signal_strength"] = "bearish"

        # Add strength level
        result.loc[result["aggregated_signal_strength"] >= 0.7, "signal_strength"] += "_strong"
        result.loc[(result["aggregated_signal_strength"] >= 0.3) & (result["aggregated_signal_strength"] < 0.7), "signal_strength"] += "_moderate"
        result.loc[result["aggregated_signal_strength"] < 0.3, "signal_strength"] += "_weak"

        return result


# Create a singleton instance
technical_analysis_manager = TechnicalAnalysisManager()
