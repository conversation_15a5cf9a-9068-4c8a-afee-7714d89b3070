"""
Portfolio performance metrics calculation.
"""
from typing import Dict, List, Optional, Union

import numpy as np
import pandas as pd
from scipy import stats

from app.utils.constants import DEFAULT_RISK_FREE_RATE
from app.utils.logging import logger


def calculate_performance_metrics(
    returns: pd.Series,
    risk_free_rate: float = DEFAULT_RISK_FREE_RATE,
    benchmark_returns: Optional[pd.Series] = None,
    annualization_factor: int = 252
) -> Dict[str, float]:
    """
    Calculate performance metrics for a portfolio.
    
    Args:
        returns: Series of portfolio returns
        risk_free_rate: Risk-free rate (annualized)
        benchmark_returns: Series of benchmark returns
        annualization_factor: Annualization factor (252 for daily, 52 for weekly, 12 for monthly)
        
    Returns:
        Dictionary with performance metrics
    """
    # Validate inputs
    if returns is None or len(returns) == 0:
        logger.error("Returns must be provided for performance calculation")
        return {}
    
    # Convert risk-free rate to daily rate
    daily_risk_free_rate = (1 + risk_free_rate) ** (1 / annualization_factor) - 1
    
    # Calculate basic metrics
    total_return = (1 + returns).prod() - 1
    annualized_return = (1 + total_return) ** (annualization_factor / len(returns)) - 1
    volatility = returns.std() * np.sqrt(annualization_factor)
    
    # Calculate downside metrics
    negative_returns = returns[returns < 0]
    downside_deviation = negative_returns.std() * np.sqrt(annualization_factor) if len(negative_returns) > 0 else 0
    
    # Calculate drawdown metrics
    cumulative_returns = (1 + returns).cumprod() - 1
    running_max = cumulative_returns.cummax()
    drawdown = (cumulative_returns - running_max) / (1 + running_max)
    max_drawdown = drawdown.min()
    
    # Calculate risk-adjusted metrics
    sharpe_ratio = (annualized_return - risk_free_rate) / volatility if volatility > 0 else 0
    sortino_ratio = (annualized_return - risk_free_rate) / downside_deviation if downside_deviation > 0 else 0
    calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown < 0 else 0
    
    # Calculate value at risk (VaR)
    var_95 = np.percentile(returns, 5)
    var_99 = np.percentile(returns, 1)
    
    # Calculate conditional value at risk (CVaR)
    cvar_95 = returns[returns <= var_95].mean()
    cvar_99 = returns[returns <= var_99].mean()
    
    # Calculate benchmark-relative metrics
    if benchmark_returns is not None and len(benchmark_returns) > 0:
        # Align benchmark returns with portfolio returns
        common_index = returns.index.intersection(benchmark_returns.index)
        if len(common_index) > 0:
            portfolio_returns = returns.loc[common_index]
            benchmark_returns = benchmark_returns.loc[common_index]
            
            # Calculate alpha and beta
            covariance = np.cov(portfolio_returns, benchmark_returns)[0, 1]
            benchmark_variance = np.var(benchmark_returns)
            beta = covariance / benchmark_variance if benchmark_variance > 0 else 0
            
            # Calculate alpha (annualized)
            benchmark_return = (1 + benchmark_returns).prod() - 1
            annualized_benchmark_return = (1 + benchmark_return) ** (annualization_factor / len(benchmark_returns)) - 1
            alpha = annualized_return - (risk_free_rate + beta * (annualized_benchmark_return - risk_free_rate))
            
            # Calculate tracking error
            tracking_error = (portfolio_returns - benchmark_returns).std() * np.sqrt(annualization_factor)
            
            # Calculate information ratio
            information_ratio = (annualized_return - annualized_benchmark_return) / tracking_error if tracking_error > 0 else 0
            
            # Calculate up/down capture ratios
            up_months = benchmark_returns > 0
            down_months = benchmark_returns < 0
            
            if up_months.sum() > 0:
                up_capture = (portfolio_returns[up_months].mean() / benchmark_returns[up_months].mean()) * 100
            else:
                up_capture = 0
            
            if down_months.sum() > 0:
                down_capture = (portfolio_returns[down_months].mean() / benchmark_returns[down_months].mean()) * 100
            else:
                down_capture = 0
        else:
            # Not enough common data points
            alpha = 0
            beta = 0
            tracking_error = 0
            information_ratio = 0
            up_capture = 0
            down_capture = 0
    else:
        # No benchmark provided
        alpha = 0
        beta = 0
        tracking_error = 0
        information_ratio = 0
        up_capture = 0
        down_capture = 0
    
    # Calculate higher moments
    skewness = stats.skew(returns)
    kurtosis = stats.kurtosis(returns)
    
    # Calculate win rate and other statistics
    positive_returns = returns > 0
    win_rate = positive_returns.mean() * 100
    avg_win = returns[positive_returns].mean() if positive_returns.sum() > 0 else 0
    avg_loss = returns[~positive_returns].mean() if (~positive_returns).sum() > 0 else 0
    profit_factor = abs(returns[positive_returns].sum() / returns[~positive_returns].sum()) if returns[~positive_returns].sum() != 0 else float('inf')
    
    # Create metrics dictionary
    metrics = {
        "total_return": total_return,
        "annualized_return": annualized_return,
        "volatility": volatility,
        "sharpe_ratio": sharpe_ratio,
        "sortino_ratio": sortino_ratio,
        "calmar_ratio": calmar_ratio,
        "max_drawdown": max_drawdown,
        "var_95": var_95,
        "var_99": var_99,
        "cvar_95": cvar_95,
        "cvar_99": cvar_99,
        "skewness": skewness,
        "kurtosis": kurtosis,
        "win_rate": win_rate,
        "avg_win": avg_win,
        "avg_loss": avg_loss,
        "profit_factor": profit_factor,
        "alpha": alpha,
        "beta": beta,
        "tracking_error": tracking_error,
        "information_ratio": information_ratio,
        "up_capture": up_capture,
        "down_capture": down_capture
    }
    
    return metrics


def calculate_rolling_metrics(
    returns: pd.Series,
    risk_free_rate: float = DEFAULT_RISK_FREE_RATE,
    window: int = 252,
    annualization_factor: int = 252
) -> pd.DataFrame:
    """
    Calculate rolling performance metrics for a portfolio.
    
    Args:
        returns: Series of portfolio returns
        risk_free_rate: Risk-free rate (annualized)
        window: Rolling window size
        annualization_factor: Annualization factor (252 for daily, 52 for weekly, 12 for monthly)
        
    Returns:
        DataFrame with rolling performance metrics
    """
    # Validate inputs
    if returns is None or len(returns) < window:
        logger.error(f"Returns must have at least {window} observations for rolling metrics calculation")
        return pd.DataFrame()
    
    # Convert risk-free rate to daily rate
    daily_risk_free_rate = (1 + risk_free_rate) ** (1 / annualization_factor) - 1
    
    # Initialize DataFrame for rolling metrics
    rolling_metrics = pd.DataFrame(index=returns.index[window-1:])
    
    # Calculate rolling annualized return
    rolling_return = returns.rolling(window=window).apply(
        lambda x: (1 + x).prod() ** (annualization_factor / window) - 1
    )
    rolling_metrics["annualized_return"] = rolling_return
    
    # Calculate rolling volatility
    rolling_volatility = returns.rolling(window=window).std() * np.sqrt(annualization_factor)
    rolling_metrics["volatility"] = rolling_volatility
    
    # Calculate rolling Sharpe ratio
    rolling_metrics["sharpe_ratio"] = (rolling_return - risk_free_rate) / rolling_volatility
    
    # Calculate rolling maximum drawdown
    def rolling_max_drawdown(x):
        cumulative_returns = (1 + x).cumprod() - 1
        running_max = cumulative_returns.cummax()
        drawdown = (cumulative_returns - running_max) / (1 + running_max)
        return drawdown.min()
    
    rolling_metrics["max_drawdown"] = returns.rolling(window=window).apply(rolling_max_drawdown)
    
    # Calculate rolling Calmar ratio
    rolling_metrics["calmar_ratio"] = rolling_return / abs(rolling_metrics["max_drawdown"])
    rolling_metrics["calmar_ratio"] = rolling_metrics["calmar_ratio"].replace([np.inf, -np.inf], np.nan)
    
    # Calculate rolling downside deviation
    def rolling_downside_deviation(x):
        negative_returns = x[x < 0]
        return negative_returns.std() * np.sqrt(annualization_factor) if len(negative_returns) > 0 else 0
    
    rolling_metrics["downside_deviation"] = returns.rolling(window=window).apply(rolling_downside_deviation)
    
    # Calculate rolling Sortino ratio
    rolling_metrics["sortino_ratio"] = (rolling_return - risk_free_rate) / rolling_metrics["downside_deviation"]
    rolling_metrics["sortino_ratio"] = rolling_metrics["sortino_ratio"].replace([np.inf, -np.inf], np.nan)
    
    return rolling_metrics


def calculate_drawdowns(
    returns: pd.Series,
    top_n: int = 5
) -> pd.DataFrame:
    """
    Calculate the top drawdowns for a portfolio.
    
    Args:
        returns: Series of portfolio returns
        top_n: Number of top drawdowns to return
        
    Returns:
        DataFrame with drawdown information
    """
    # Validate inputs
    if returns is None or len(returns) == 0:
        logger.error("Returns must be provided for drawdown calculation")
        return pd.DataFrame()
    
    # Calculate cumulative returns
    cumulative_returns = (1 + returns).cumprod() - 1
    
    # Calculate running maximum
    running_max = cumulative_returns.cummax()
    
    # Calculate drawdown
    drawdown = (cumulative_returns - running_max) / (1 + running_max)
    
    # Identify drawdown periods
    is_drawdown = drawdown < 0
    
    # Initialize list to store drawdown information
    drawdowns = []
    
    # Find drawdown periods
    in_drawdown = False
    start_idx = None
    
    for i, (date, is_dd) in enumerate(is_drawdown.items()):
        if is_dd and not in_drawdown:
            # Start of a new drawdown
            in_drawdown = True
            start_idx = i
        elif not is_dd and in_drawdown:
            # End of a drawdown
            in_drawdown = False
            
            # Calculate drawdown information
            end_idx = i - 1
            start_date = is_drawdown.index[start_idx]
            end_date = is_drawdown.index[end_idx]
            
            # Find the lowest point in the drawdown
            trough_idx = drawdown.iloc[start_idx:end_idx+1].idxmin()
            
            # Calculate drawdown metrics
            max_drawdown = drawdown.loc[trough_idx]
            recovery_date = is_drawdown.index[end_idx + 1] if end_idx + 1 < len(is_drawdown) else None
            
            # Calculate duration
            duration_to_trough = (trough_idx - start_date).days
            duration_to_recovery = (recovery_date - start_date).days if recovery_date else None
            
            # Store drawdown information
            drawdowns.append({
                "start_date": start_date,
                "trough_date": trough_idx,
                "end_date": end_date,
                "recovery_date": recovery_date,
                "max_drawdown": max_drawdown,
                "duration_to_trough": duration_to_trough,
                "duration_to_recovery": duration_to_recovery
            })
    
    # Check if still in a drawdown at the end of the series
    if in_drawdown:
        # Calculate drawdown information for the current drawdown
        end_idx = len(is_drawdown) - 1
        start_date = is_drawdown.index[start_idx]
        end_date = is_drawdown.index[end_idx]
        
        # Find the lowest point in the drawdown
        trough_idx = drawdown.iloc[start_idx:end_idx+1].idxmin()
        
        # Calculate drawdown metrics
        max_drawdown = drawdown.loc[trough_idx]
        
        # Calculate duration
        duration_to_trough = (trough_idx - start_date).days
        
        # Store drawdown information
        drawdowns.append({
            "start_date": start_date,
            "trough_date": trough_idx,
            "end_date": end_date,
            "recovery_date": None,
            "max_drawdown": max_drawdown,
            "duration_to_trough": duration_to_trough,
            "duration_to_recovery": None
        })
    
    # Create DataFrame from drawdowns
    if drawdowns:
        drawdowns_df = pd.DataFrame(drawdowns)
        
        # Sort by maximum drawdown
        drawdowns_df = drawdowns_df.sort_values("max_drawdown").head(top_n)
        
        return drawdowns_df
    else:
        return pd.DataFrame()
