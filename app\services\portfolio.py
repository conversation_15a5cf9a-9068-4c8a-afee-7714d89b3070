"""
Portfolio service.
"""
from datetime import date
from typing import Dict, List, Optional

from sqlalchemy.orm import Session

from app.core.data_collection.data_manager import data_manager
from app.core.portfolio.allocation.strategies import get_allocation_strategy
from app.core.portfolio.optimization import optimizer
from app.core.portfolio.risk.metrics import risk_metrics
from app.db.models import Asset, Portfolio, User
from app.db.repositories.asset import asset_repository
from app.db.repositories.portfolio import portfolio_repository
from app.utils.logging import logger


class PortfolioService:
    """
    Portfolio service.
    
    This service provides methods for portfolio management.
    """
    
    async def create_portfolio(
        self,
        db: Session,
        user: User,
        name: str,
        description: Optional[str] = None,
        risk_profile: Optional[str] = None
    ) -> Portfolio:
        """
        Create a new portfolio.
        
        Args:
            db: Database session
            user: User
            name: Portfolio name
            description: Portfolio description
            risk_profile: Risk profile
            
        Returns:
            Created portfolio
        """
        # Create portfolio
        portfolio = Portfolio(
            user_id=user.id,
            name=name,
            description=description,
            risk_profile=risk_profile
        )
        
        # Save portfolio to database
        db.add(portfolio)
        db.commit()
        db.refresh(portfolio)
        
        return portfolio
    
    async def get_portfolio(
        self,
        db: Session,
        user: User,
        portfolio_id: int
    ) -> Optional[Portfolio]:
        """
        Get a portfolio by ID.
        
        Args:
            db: Database session
            user: User
            portfolio_id: Portfolio ID
            
        Returns:
            Portfolio if found, None otherwise
        """
        # Get portfolio from database
        portfolio = portfolio_repository.get(db, portfolio_id)
        
        # Check if portfolio exists and belongs to the user
        if not portfolio or portfolio.user_id != user.id:
            return None
        
        return portfolio
    
    async def get_portfolios(
        self,
        db: Session,
        user: User
    ) -> List[Portfolio]:
        """
        Get all portfolios for a user.
        
        Args:
            db: Database session
            user: User
            
        Returns:
            List of portfolios
        """
        # Get portfolios from database
        portfolios = portfolio_repository.get_by_user_id(db, user.id)
        
        return portfolios
    
    async def add_asset_to_portfolio(
        self,
        db: Session,
        portfolio_id: int,
        symbol: str,
        weight: float,
        shares: Optional[int] = None
    ) -> None:
        """
        Add an asset to a portfolio.
        
        Args:
            db: Database session
            portfolio_id: Portfolio ID
            symbol: Asset symbol
            weight: Asset weight in the portfolio
            shares: Number of shares
        """
        # Get asset from database
        asset = asset_repository.get_by_symbol(db, symbol)
        
        # If asset doesn't exist, create it
        if not asset:
            # Get asset information
            try:
                asset_info = await data_manager.get_company_info(symbol)
                
                # Create asset
                asset = Asset(
                    symbol=symbol,
                    name=asset_info.get("longName", symbol),
                    asset_type="stock",
                    sector=asset_info.get("sector"),
                    industry=asset_info.get("industry")
                )
                
                # Save asset to database
                db.add(asset)
                db.commit()
                db.refresh(asset)
            
            except Exception as e:
                logger.error(f"Error getting asset information: {e}")
                
                # Create asset with minimal information
                asset = Asset(
                    symbol=symbol,
                    name=symbol,
                    asset_type="stock"
                )
                
                # Save asset to database
                db.add(asset)
                db.commit()
                db.refresh(asset)
        
        # Add asset to portfolio
        portfolio_repository.add_asset(db, portfolio_id, asset.id, weight, shares)
    
    async def remove_asset_from_portfolio(
        self,
        db: Session,
        portfolio_id: int,
        symbol: str
    ) -> None:
        """
        Remove an asset from a portfolio.
        
        Args:
            db: Database session
            portfolio_id: Portfolio ID
            symbol: Asset symbol
        """
        # Get asset from database
        asset = asset_repository.get_by_symbol(db, symbol)
        
        # If asset exists, remove it from the portfolio
        if asset:
            portfolio_repository.remove_asset(db, portfolio_id, asset.id)
    
    async def get_portfolio_assets(
        self,
        db: Session,
        portfolio_id: int
    ) -> List[Dict]:
        """
        Get assets in a portfolio.
        
        Args:
            db: Database session
            portfolio_id: Portfolio ID
            
        Returns:
            List of assets with weights and shares
        """
        # Get assets from database
        assets = portfolio_repository.get_assets(db, portfolio_id)
        
        return assets
    
    async def optimize_portfolio(
        self,
        db: Session,
        portfolio_id: int,
        start_date: date,
        end_date: date,
        optimization_criterion: str = "max_sharpe",
        expected_returns_method: str = "mean_historical_return",
        risk_model_method: str = "sample_cov",
        constraints: Optional[Dict] = None
    ) -> Dict:
        """
        Optimize a portfolio.
        
        Args:
            db: Database session
            portfolio_id: Portfolio ID
            start_date: Start date for historical data
            end_date: End date for historical data
            optimization_criterion: Optimization criterion
            expected_returns_method: Method for calculating expected returns
            risk_model_method: Method for calculating the risk model
            constraints: Additional constraints for the optimization
            
        Returns:
            Optimization results
        """
        # Get portfolio from database
        portfolio = portfolio_repository.get(db, portfolio_id)
        
        # Get assets in the portfolio
        assets = portfolio_repository.get_assets(db, portfolio_id)
        
        # Get symbols
        symbols = [asset["symbol"] for asset in assets]
        
        # Optimize portfolio
        results = await optimizer.optimize(
            symbols,
            start_date,
            end_date,
            "1d",
            optimization_criterion,
            expected_returns_method,
            risk_model_method,
            constraints,
            portfolio.total_value
        )
        
        return results
    
    async def allocate_portfolio(
        self,
        db: Session,
        portfolio_id: int,
        strategy: str,
        risk_profile: str,
        start_date: date,
        end_date: date
    ) -> Dict:
        """
        Allocate assets in a portfolio.
        
        Args:
            db: Database session
            portfolio_id: Portfolio ID
            strategy: Allocation strategy
            risk_profile: Risk profile
            start_date: Start date for historical data
            end_date: End date for historical data
            
        Returns:
            Allocation results
        """
        # Get portfolio from database
        portfolio = portfolio_repository.get(db, portfolio_id)
        
        # Get assets in the portfolio
        assets = portfolio_repository.get_assets(db, portfolio_id)
        
        # Get symbols
        symbols = [asset["symbol"] for asset in assets]
        
        # Get allocation strategy
        allocation_strategy = get_allocation_strategy(strategy)
        
        # Get stock data
        stock_data = await data_manager.get_multiple_stock_data(
            symbols,
            start_date,
            end_date,
            "1d"
        )
        
        # Extract prices
        prices = {}
        latest_prices = {}
        for symbol, df in stock_data.items():
            if not df.empty:
                # Set date as index if it's not already
                if "date" in df.columns:
                    df = df.set_index("date")
                
                prices[symbol] = df["close"]
                latest_prices[symbol] = df["close"].iloc[-1]
        
        # Create a DataFrame with all prices
        import pandas as pd
        price_df = pd.DataFrame(prices)
        
        # Allocate assets
        weights = await allocation_strategy.allocate(
            symbols,
            price_df,
            risk_profile
        )
        
        # Get discrete allocation if total portfolio value is provided
        allocation = None
        leftover = None
        if portfolio.total_value is not None:
            # Create the discrete allocation object
            from pypfopt.discrete_allocation import DiscreteAllocation
            da = DiscreteAllocation(
                weights,
                pd.Series(latest_prices),
                total_portfolio_value=portfolio.total_value
            )
            
            # Get the allocation
            allocation, leftover = da.greedy_portfolio()
        
        # Return results
        return {
            "strategy": strategy,
            "risk_profile": risk_profile,
            "weights": weights,
            "allocation": allocation,
            "leftover": leftover
        }
    
    async def calculate_portfolio_risk(
        self,
        db: Session,
        portfolio_id: int,
        start_date: date,
        end_date: date,
        benchmark: Optional[str] = None
    ) -> Dict:
        """
        Calculate risk metrics for a portfolio.
        
        Args:
            db: Database session
            portfolio_id: Portfolio ID
            start_date: Start date for historical data
            end_date: End date for historical data
            benchmark: Benchmark symbol
            
        Returns:
            Risk metrics
        """
        # Get portfolio from database
        portfolio = portfolio_repository.get(db, portfolio_id)
        
        # Get assets in the portfolio
        assets = portfolio_repository.get_assets(db, portfolio_id)
        
        # Get symbols and weights
        symbols = [asset["symbol"] for asset in assets]
        weights = {asset["symbol"]: asset["weight"] for asset in assets}
        
        # Get stock data
        stock_data = await data_manager.get_multiple_stock_data(
            symbols,
            start_date,
            end_date,
            "1d"
        )
        
        # Extract prices
        prices = {}
        for symbol, df in stock_data.items():
            if not df.empty:
                # Set date as index if it's not already
                if "date" in df.columns:
                    df = df.set_index("date")
                
                prices[symbol] = df["close"]
        
        # Create a DataFrame with all prices
        import pandas as pd
        price_df = pd.DataFrame(prices)
        
        # Calculate returns
        returns_df = price_df.pct_change().dropna()
        
        # Get benchmark returns if provided
        benchmark_returns = None
        if benchmark:
            benchmark_data = await data_manager.get_stock_data(
                benchmark,
                start_date,
                end_date,
                "1d"
            )
            
            if not benchmark_data.empty:
                # Set date as index if it's not already
                if "date" in benchmark_data.columns:
                    benchmark_data = benchmark_data.set_index("date")
                
                benchmark_returns = benchmark_data["close"].pct_change().dropna()
        
        # Calculate portfolio returns
        weights_series = pd.Series(weights)
        weights_series = weights_series / weights_series.sum()
        portfolio_returns = returns_df.dot(weights_series)
        
        # Calculate risk metrics
        metrics = risk_metrics.calculate_all_metrics(
            portfolio_returns,
            benchmark_returns
        )
        
        return metrics


# Create a singleton instance
portfolio_service = PortfolioService()
