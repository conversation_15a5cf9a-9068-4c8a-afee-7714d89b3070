"""
Fundamental data collector implementation.
"""
import asyncio
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional, Union

import pandas as pd
import aiohttp
from aiolimiter import AsyncLimiter

from app.core.data_collection.base import BaseDataCollector
from app.settings import get_settings
from app.utils.logging import logger


class FundamentalDataCollector:
    """
    Fundamental data collector.
    
    This class provides methods for retrieving fundamental financial data.
    It uses Financial Modeling Prep API as the primary source and falls back
    to other sources if needed.
    """
    
    def __init__(self):
        """Initialize the fundamental data collector."""
        self.settings = get_settings()
        self.fmp_api_key = self.settings.data_sources.get('financial_modeling_prep', {}).get('api_key', '')
        self.alpha_vantage_api_key = self.settings.data_sources.get('alpha_vantage', {}).get('api_key', '')
        self.base_url_fmp = "https://financialmodelingprep.com/api/v3"
        self.base_url_av = "https://www.alphavantage.co/query"
        
        # Set up rate limiter (5 requests per minute for free tier)
        self.rate_limiter = AsyncLimiter(5, 60)
        
        # Initialize session
        self.session = None
    
    async def _get_session(self):
        """Get or create an aiohttp session."""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession()
        return self.session
    
    async def _make_request_fmp(self, endpoint: str, params: Dict = None) -> Dict:
        """
        Make a request to the Financial Modeling Prep API.
        
        Args:
            endpoint: API endpoint
            params: Query parameters
            
        Returns:
            API response as dictionary
        """
        # Add API key to parameters
        if params is None:
            params = {}
        params['apikey'] = self.fmp_api_key
        
        # Build URL
        url = f"{self.base_url_fmp}{endpoint}"
        
        # Use rate limiter
        async with self.rate_limiter:
            try:
                session = await self._get_session()
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        logger.error(f"Error from FMP API: {response.status} - {await response.text()}")
                        return {}
            except Exception as e:
                logger.error(f"Error making request to FMP API: {str(e)}")
                return {}
    
    async def _make_request_av(self, function: str, params: Dict = None) -> Dict:
        """
        Make a request to the Alpha Vantage API.
        
        Args:
            function: API function
            params: Query parameters
            
        Returns:
            API response as dictionary
        """
        # Add API key and function to parameters
        if params is None:
            params = {}
        params['apikey'] = self.alpha_vantage_api_key
        params['function'] = function
        
        # Build URL
        url = self.base_url_av
        
        # Use rate limiter
        async with self.rate_limiter:
            try:
                session = await self._get_session()
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        logger.error(f"Error from Alpha Vantage API: {response.status} - {await response.text()}")
                        return {}
            except Exception as e:
                logger.error(f"Error making request to Alpha Vantage API: {str(e)}")
                return {}
    
    async def get_income_statement(
        self,
        symbol: str,
        period: str = "annual",
        limit: int = 5
    ) -> pd.DataFrame:
        """
        Get income statement data for a symbol.
        
        Args:
            symbol: The stock symbol
            period: Period ('annual' or 'quarter')
            limit: Number of periods to retrieve
            
        Returns:
            DataFrame with income statement data
        """
        try:
            # Try Financial Modeling Prep first
            endpoint = f"/income-statement/{symbol}"
            params = {
                'period': period,
                'limit': limit
            }
            
            response = await self._make_request_fmp(endpoint, params)
            
            if response and isinstance(response, list) and len(response) > 0:
                # Convert to DataFrame
                df = pd.DataFrame(response)
                
                # Set date as index
                if 'date' in df.columns:
                    df['date'] = pd.to_datetime(df['date'])
                    df = df.set_index('date')
                
                return df
            
            # Fall back to Alpha Vantage
            params = {
                'symbol': symbol,
                'outputsize': 'full'
            }
            
            if period == 'annual':
                function = 'INCOME_STATEMENT'
            else:
                function = 'INCOME_STATEMENT'  # Alpha Vantage doesn't differentiate
            
            response = await self._make_request_av(function, params)
            
            if response and 'annualReports' in response:
                # Get the appropriate reports
                if period == 'annual':
                    reports = response['annualReports'][:limit]
                else:
                    reports = response.get('quarterlyReports', [])[:limit]
                
                # Convert to DataFrame
                df = pd.DataFrame(reports)
                
                # Set date as index
                if 'fiscalDateEnding' in df.columns:
                    df['date'] = pd.to_datetime(df['fiscalDateEnding'])
                    df = df.set_index('date')
                
                return df
            
            # If both APIs fail, return empty DataFrame
            logger.warning(f"Could not retrieve income statement for {symbol}")
            return pd.DataFrame()
        
        except Exception as e:
            logger.error(f"Error getting income statement for {symbol}: {str(e)}")
            return pd.DataFrame()
    
    async def get_balance_sheet(
        self,
        symbol: str,
        period: str = "annual",
        limit: int = 5
    ) -> pd.DataFrame:
        """
        Get balance sheet data for a symbol.
        
        Args:
            symbol: The stock symbol
            period: Period ('annual' or 'quarter')
            limit: Number of periods to retrieve
            
        Returns:
            DataFrame with balance sheet data
        """
        try:
            # Try Financial Modeling Prep first
            endpoint = f"/balance-sheet-statement/{symbol}"
            params = {
                'period': period,
                'limit': limit
            }
            
            response = await self._make_request_fmp(endpoint, params)
            
            if response and isinstance(response, list) and len(response) > 0:
                # Convert to DataFrame
                df = pd.DataFrame(response)
                
                # Set date as index
                if 'date' in df.columns:
                    df['date'] = pd.to_datetime(df['date'])
                    df = df.set_index('date')
                
                return df
            
            # Fall back to Alpha Vantage
            params = {
                'symbol': symbol,
                'outputsize': 'full'
            }
            
            if period == 'annual':
                function = 'BALANCE_SHEET'
            else:
                function = 'BALANCE_SHEET'  # Alpha Vantage doesn't differentiate
            
            response = await self._make_request_av(function, params)
            
            if response and 'annualReports' in response:
                # Get the appropriate reports
                if period == 'annual':
                    reports = response['annualReports'][:limit]
                else:
                    reports = response.get('quarterlyReports', [])[:limit]
                
                # Convert to DataFrame
                df = pd.DataFrame(reports)
                
                # Set date as index
                if 'fiscalDateEnding' in df.columns:
                    df['date'] = pd.to_datetime(df['fiscalDateEnding'])
                    df = df.set_index('date')
                
                return df
            
            # If both APIs fail, return empty DataFrame
            logger.warning(f"Could not retrieve balance sheet for {symbol}")
            return pd.DataFrame()
        
        except Exception as e:
            logger.error(f"Error getting balance sheet for {symbol}: {str(e)}")
            return pd.DataFrame()
    
    async def get_cash_flow(
        self,
        symbol: str,
        period: str = "annual",
        limit: int = 5
    ) -> pd.DataFrame:
        """
        Get cash flow statement data for a symbol.
        
        Args:
            symbol: The stock symbol
            period: Period ('annual' or 'quarter')
            limit: Number of periods to retrieve
            
        Returns:
            DataFrame with cash flow statement data
        """
        try:
            # Try Financial Modeling Prep first
            endpoint = f"/cash-flow-statement/{symbol}"
            params = {
                'period': period,
                'limit': limit
            }
            
            response = await self._make_request_fmp(endpoint, params)
            
            if response and isinstance(response, list) and len(response) > 0:
                # Convert to DataFrame
                df = pd.DataFrame(response)
                
                # Set date as index
                if 'date' in df.columns:
                    df['date'] = pd.to_datetime(df['date'])
                    df = df.set_index('date')
                
                return df
            
            # Fall back to Alpha Vantage
            params = {
                'symbol': symbol,
                'outputsize': 'full'
            }
            
            if period == 'annual':
                function = 'CASH_FLOW'
            else:
                function = 'CASH_FLOW'  # Alpha Vantage doesn't differentiate
            
            response = await self._make_request_av(function, params)
            
            if response and 'annualReports' in response:
                # Get the appropriate reports
                if period == 'annual':
                    reports = response['annualReports'][:limit]
                else:
                    reports = response.get('quarterlyReports', [])[:limit]
                
                # Convert to DataFrame
                df = pd.DataFrame(reports)
                
                # Set date as index
                if 'fiscalDateEnding' in df.columns:
                    df['date'] = pd.to_datetime(df['fiscalDateEnding'])
                    df = df.set_index('date')
                
                return df
            
            # If both APIs fail, return empty DataFrame
            logger.warning(f"Could not retrieve cash flow statement for {symbol}")
            return pd.DataFrame()
        
        except Exception as e:
            logger.error(f"Error getting cash flow statement for {symbol}: {str(e)}")
            return pd.DataFrame()
    
    async def get_financial_ratios(
        self,
        symbol: str,
        period: str = "annual",
        limit: int = 5
    ) -> pd.DataFrame:
        """
        Get financial ratios for a symbol.
        
        Args:
            symbol: The stock symbol
            period: Period ('annual' or 'quarter')
            limit: Number of periods to retrieve
            
        Returns:
            DataFrame with financial ratios
        """
        try:
            # Try Financial Modeling Prep first
            endpoint = f"/ratios/{symbol}"
            params = {
                'period': period,
                'limit': limit
            }
            
            response = await self._make_request_fmp(endpoint, params)
            
            if response and isinstance(response, list) and len(response) > 0:
                # Convert to DataFrame
                df = pd.DataFrame(response)
                
                # Set date as index
                if 'date' in df.columns:
                    df['date'] = pd.to_datetime(df['date'])
                    df = df.set_index('date')
                
                return df
            
            # Financial ratios are not available in Alpha Vantage
            # Return empty DataFrame
            logger.warning(f"Could not retrieve financial ratios for {symbol}")
            return pd.DataFrame()
        
        except Exception as e:
            logger.error(f"Error getting financial ratios for {symbol}: {str(e)}")
            return pd.DataFrame()
    
    async def get_key_metrics(
        self,
        symbol: str,
        period: str = "annual",
        limit: int = 5
    ) -> pd.DataFrame:
        """
        Get key metrics for a symbol.
        
        Args:
            symbol: The stock symbol
            period: Period ('annual' or 'quarter')
            limit: Number of periods to retrieve
            
        Returns:
            DataFrame with key metrics
        """
        try:
            # Try Financial Modeling Prep first
            endpoint = f"/key-metrics/{symbol}"
            params = {
                'period': period,
                'limit': limit
            }
            
            response = await self._make_request_fmp(endpoint, params)
            
            if response and isinstance(response, list) and len(response) > 0:
                # Convert to DataFrame
                df = pd.DataFrame(response)
                
                # Set date as index
                if 'date' in df.columns:
                    df['date'] = pd.to_datetime(df['date'])
                    df = df.set_index('date')
                
                return df
            
            # Key metrics are not available in Alpha Vantage
            # Return empty DataFrame
            logger.warning(f"Could not retrieve key metrics for {symbol}")
            return pd.DataFrame()
        
        except Exception as e:
            logger.error(f"Error getting key metrics for {symbol}: {str(e)}")
            return pd.DataFrame()
    
    async def get_financial_growth(
        self,
        symbol: str,
        period: str = "annual",
        limit: int = 5
    ) -> pd.DataFrame:
        """
        Get financial growth data for a symbol.
        
        Args:
            symbol: The stock symbol
            period: Period ('annual' or 'quarter')
            limit: Number of periods to retrieve
            
        Returns:
            DataFrame with financial growth data
        """
        try:
            # Try Financial Modeling Prep first
            endpoint = f"/financial-growth/{symbol}"
            params = {
                'period': period,
                'limit': limit
            }
            
            response = await self._make_request_fmp(endpoint, params)
            
            if response and isinstance(response, list) and len(response) > 0:
                # Convert to DataFrame
                df = pd.DataFrame(response)
                
                # Set date as index
                if 'date' in df.columns:
                    df['date'] = pd.to_datetime(df['date'])
                    df = df.set_index('date')
                
                return df
            
            # Financial growth data is not available in Alpha Vantage
            # Return empty DataFrame
            logger.warning(f"Could not retrieve financial growth data for {symbol}")
            return pd.DataFrame()
        
        except Exception as e:
            logger.error(f"Error getting financial growth data for {symbol}: {str(e)}")
            return pd.DataFrame()
    
    async def get_earnings(
        self,
        symbol: str,
        limit: int = 10
    ) -> pd.DataFrame:
        """
        Get earnings data for a symbol.
        
        Args:
            symbol: The stock symbol
            limit: Number of earnings reports to retrieve
            
        Returns:
            DataFrame with earnings data
        """
        try:
            # Try Financial Modeling Prep first
            endpoint = f"/earnings/{symbol}"
            params = {
                'limit': limit
            }
            
            response = await self._make_request_fmp(endpoint, params)
            
            if response and isinstance(response, list) and len(response) > 0:
                # Convert to DataFrame
                df = pd.DataFrame(response)
                
                # Set date as index
                if 'date' in df.columns:
                    df['date'] = pd.to_datetime(df['date'])
                    df = df.set_index('date')
                
                return df
            
            # Fall back to Alpha Vantage
            params = {
                'symbol': symbol
            }
            
            function = 'EARNINGS'
            
            response = await self._make_request_av(function, params)
            
            if response and 'quarterlyEarnings' in response:
                # Get the earnings reports
                reports = response['quarterlyEarnings'][:limit]
                
                # Convert to DataFrame
                df = pd.DataFrame(reports)
                
                # Set date as index
                if 'fiscalDateEnding' in df.columns:
                    df['date'] = pd.to_datetime(df['fiscalDateEnding'])
                    df = df.set_index('date')
                
                return df
            
            # If both APIs fail, return empty DataFrame
            logger.warning(f"Could not retrieve earnings data for {symbol}")
            return pd.DataFrame()
        
        except Exception as e:
            logger.error(f"Error getting earnings data for {symbol}: {str(e)}")
            return pd.DataFrame()
    
    async def get_earnings_calendar(
        self,
        from_date: Optional[date] = None,
        to_date: Optional[date] = None
    ) -> pd.DataFrame:
        """
        Get earnings calendar.
        
        Args:
            from_date: Start date for earnings calendar
            to_date: End date for earnings calendar
            
        Returns:
            DataFrame with earnings calendar
        """
        try:
            # Set default dates if not provided
            if from_date is None:
                from_date = date.today()
            if to_date is None:
                to_date = from_date + timedelta(days=30)
            
            # Format dates
            from_str = from_date.strftime("%Y-%m-%d")
            to_str = to_date.strftime("%Y-%m-%d")
            
            # Try Financial Modeling Prep
            endpoint = f"/earning-calendar"
            params = {
                'from': from_str,
                'to': to_str
            }
            
            response = await self._make_request_fmp(endpoint, params)
            
            if response and isinstance(response, list) and len(response) > 0:
                # Convert to DataFrame
                df = pd.DataFrame(response)
                
                # Set date as index
                if 'date' in df.columns:
                    df['date'] = pd.to_datetime(df['date'])
                    df = df.set_index('date')
                
                return df
            
            # Earnings calendar is not available in Alpha Vantage
            # Return empty DataFrame
            logger.warning(f"Could not retrieve earnings calendar")
            return pd.DataFrame()
        
        except Exception as e:
            logger.error(f"Error getting earnings calendar: {str(e)}")
            return pd.DataFrame()
    
    async def get_company_profile(
        self,
        symbol: str
    ) -> Dict:
        """
        Get company profile for a symbol.
        
        Args:
            symbol: The stock symbol
            
        Returns:
            Dictionary with company profile
        """
        try:
            # Try Financial Modeling Prep first
            endpoint = f"/profile/{symbol}"
            
            response = await self._make_request_fmp(endpoint)
            
            if response and isinstance(response, list) and len(response) > 0:
                return response[0]
            
            # Fall back to Alpha Vantage
            params = {
                'symbol': symbol
            }
            
            function = 'OVERVIEW'
            
            response = await self._make_request_av(function, params)
            
            if response and 'Symbol' in response:
                return response
            
            # If both APIs fail, return empty dictionary
            logger.warning(f"Could not retrieve company profile for {symbol}")
            return {}
        
        except Exception as e:
            logger.error(f"Error getting company profile for {symbol}: {str(e)}")
            return {}
    
    async def close(self):
        """Close the session."""
        if self.session and not self.session.closed:
            await self.session.close()


# Create a singleton instance
fundamental_data_collector = FundamentalDataCollector()
