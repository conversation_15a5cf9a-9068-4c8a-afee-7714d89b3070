apiVersion: apps/v1
kind: Deployment
metadata:
  name: portfolio-streamlit
  labels:
    app: portfolio-streamlit
spec:
  replicas: 1
  selector:
    matchLabels:
      app: portfolio-streamlit
  template:
    metadata:
      labels:
        app: portfolio-streamlit
    spec:
      containers:
      - name: portfolio-streamlit
        image: portfolio-optimization/streamlit:latest
        imagePullPolicy: IfNotPresent
        command: ["/app/entrypoint.sh", "streamlit"]
        ports:
        - containerPort: 8501
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: API_URL
          value: "http://portfolio-api"
        resources:
          limits:
            cpu: "1"
            memory: "1Gi"
          requests:
            cpu: "500m"
            memory: "512Mi"
        livenessProbe:
          httpGet:
            path: /
            port: 8501
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 8501
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: portfolio-streamlit
spec:
  selector:
    app: portfolio-streamlit
  ports:
  - port: 80
    targetPort: 8501
  type: ClusterIP
