"""
Asset allocation strategies.
"""
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd

from app.core.portfolio.optimization import portfolio_optimization_manager
from app.core.portfolio.risk.profiles import get_risk_profile
from app.utils.constants import (RISK_PROFILE_AGGRESSIVE, RISK_PROFILE_CONSERVATIVE,
                                RISK_PROFILE_MODERATE)
from app.utils.logging import logger


class AllocationStrategy:
    """
    Base class for asset allocation strategies.

    This class provides a common interface for different asset allocation
    strategies.
    """

    def __init__(self, name: str, description: str):
        """
        Initialize an allocation strategy.

        Args:
            name: Name of the strategy
            description: Description of the strategy
        """
        self.name = name
        self.description = description

    async def allocate(
        self,
        symbols: List[str],
        prices: pd.DataFrame,
        risk_profile: str = RISK_PROFILE_MODERATE,
        **kwargs
    ) -> Dict[str, float]:
        """
        Allocate assets based on the strategy.

        Args:
            symbols: List of asset symbols
            prices: DataFrame with historical price data
            risk_profile: Risk profile name
            **kwargs: Additional parameters for the strategy

        Returns:
            Dictionary mapping assets to weights
        """
        raise NotImplementedError("Subclasses must implement allocate()")

    def __str__(self) -> str:
        """String representation of the allocation strategy."""
        return f"{self.name}: {self.description}"


class EqualWeightStrategy(AllocationStrategy):
    """
    Equal weight allocation strategy.

    This strategy allocates equal weight to each asset.
    """

    def __init__(self):
        """Initialize the equal weight strategy."""
        super().__init__(
            name="Equal Weight",
            description="Allocates equal weight to each asset."
        )

    async def allocate(
        self,
        symbols: List[str],
        prices: pd.DataFrame,
        risk_profile: str = RISK_PROFILE_MODERATE,
        **kwargs
    ) -> Dict[str, float]:
        """
        Allocate assets with equal weights.

        Args:
            symbols: List of asset symbols
            prices: DataFrame with historical price data
            risk_profile: Risk profile name (not used)
            **kwargs: Additional parameters (not used)

        Returns:
            Dictionary mapping assets to weights
        """
        try:
            # First try to get assets from the prices DataFrame
            if prices is not None and not prices.empty and len(prices.columns) > 0:
                assets = prices.columns.tolist()
            else:
                # If prices DataFrame is empty or invalid, use the provided symbols
                logger.warning("Prices DataFrame is empty or invalid, using provided symbols")
                assets = symbols

            # Ensure we have at least one asset
            if not assets or len(assets) == 0:
                logger.warning("No assets found for allocation, returning empty dictionary")
                return {}

            # Calculate equal weights
            weight = 1.0 / len(assets)

            # Create the allocation dictionary
            allocation = {asset: weight for asset in assets}

            logger.info(f"Equal weight allocation: {weight:.4f} per asset for {len(assets)} assets")
            return allocation

        except Exception as e:
            logger.error(f"Error in equal weight allocation: {e}")
            # Fallback to using symbols directly
            if symbols and len(symbols) > 0:
                weight = 1.0 / len(symbols)
                return {symbol: weight for symbol in symbols}
            else:
                return {}


class RiskParityStrategy(AllocationStrategy):
    """
    Risk parity allocation strategy.

    This strategy allocates assets such that each asset contributes
    equally to the portfolio risk.
    """

    def __init__(self):
        """Initialize the risk parity strategy."""
        super().__init__(
            name="Risk Parity",
            description="Allocates assets such that each asset contributes equally to the portfolio risk."
        )

    async def allocate(
        self,
        symbols: List[str],
        prices: pd.DataFrame,
        risk_profile: str = RISK_PROFILE_MODERATE,
        **kwargs
    ) -> Dict[str, float]:
        """
        Allocate assets based on risk parity.

        Args:
            symbols: List of asset symbols
            prices: DataFrame with historical price data
            risk_profile: Risk profile name (not used)
            **kwargs: Additional parameters

        Returns:
            Dictionary mapping assets to weights
        """
        try:
            # Validate inputs
            if prices is None or prices.empty or len(prices.columns) == 0:
                logger.warning("Invalid price data for risk parity allocation, falling back to equal weights")
                # Fall back to equal weights
                if symbols and len(symbols) > 0:
                    weight = 1.0 / len(symbols)
                    return {symbol: weight for symbol in symbols}
                else:
                    return {}

            # Get the list of assets from the prices DataFrame
            assets = prices.columns.tolist()

            # Ensure we have at least one asset
            if not assets or len(assets) == 0:
                logger.warning("No assets found in prices DataFrame, falling back to symbols")
                if symbols and len(symbols) > 0:
                    weight = 1.0 / len(symbols)
                    return {symbol: weight for symbol in symbols}
                else:
                    return {}

            # Calculate returns
            try:
                returns = prices.pct_change().dropna()
            except Exception as e:
                logger.error(f"Error calculating returns: {e}")
                # Fall back to equal weights
                weight = 1.0 / len(assets)
                return {asset: weight for asset in assets}

            # Check if we have enough data
            if returns.empty or len(returns) < 2:
                logger.warning("Not enough return data for risk parity allocation, falling back to equal weights")
                # Fall back to equal weights
                weight = 1.0 / len(assets)
                return {asset: weight for asset in assets}

            # Calculate volatility for each asset with error handling
            try:
                volatility = returns.std()
            except Exception as e:
                logger.error(f"Error calculating volatility: {e}")
                # Fall back to equal weights
                weight = 1.0 / len(assets)
                return {asset: weight for asset in assets}

            # Check for zero or NaN volatilities
            if (volatility == 0).any() or volatility.isna().any():
                logger.warning("Zero or NaN volatilities detected, using minimum value")
                # Replace zeros and NaNs with a small positive value
                try:
                    min_valid_vol = volatility[(volatility > 0) & ~volatility.isna()].min()
                    if pd.isna(min_valid_vol) or min_valid_vol <= 0:
                        min_valid_vol = 0.01  # Default if no valid volatility
                except Exception as e:
                    logger.error(f"Error finding minimum valid volatility: {e}")
                    min_valid_vol = 0.01  # Default value

                # Replace zeros and NaNs
                try:
                    volatility = volatility.replace(0, min_valid_vol)
                    volatility = volatility.fillna(min_valid_vol)
                except Exception as e:
                    logger.error(f"Error replacing zero/NaN volatilities: {e}")
                    # Create a new volatility series with the default value
                    volatility = pd.Series([min_valid_vol] * len(assets), index=assets)

            # Calculate inverse volatility with a larger epsilon to avoid division by zero
            epsilon = 1e-6  # Increased from 1e-8 for more safety
            try:
                # Ensure all volatilities are positive
                volatility = volatility.clip(lower=epsilon)
                inv_volatility = 1.0 / volatility
            except Exception as e:
                logger.error(f"Error calculating inverse volatility: {e}")
                # Fall back to equal weights
                weight = 1.0 / len(assets)
                return {asset: weight for asset in assets}

            # Check for infinite or NaN values
            if inv_volatility.isinf().any() or inv_volatility.isna().any():
                logger.warning("Infinite or NaN inverse volatilities detected, using equal weights")
                # Fall back to equal weights
                weight = 1.0 / len(assets)
                return {asset: weight for asset in assets}

            # Calculate weights with error handling
            try:
                sum_inv_vol = inv_volatility.sum()
                if sum_inv_vol <= 0.001 or pd.isna(sum_inv_vol):  # Increased threshold
                    logger.warning("Sum of inverse volatilities too small, using equal weights")
                    # Fall back to equal weights
                    weight = 1.0 / len(assets)
                    return {asset: weight for asset in assets}

                weights = inv_volatility / sum_inv_vol
            except Exception as e:
                logger.error(f"Error calculating weights: {e}")
                # Fall back to equal weights
                weight = 1.0 / len(assets)
                return {asset: weight for asset in assets}

            # Check for NaN or negative weights
            if weights.isna().any() or (weights < 0).any():
                logger.warning("NaN or negative weights detected, using equal weights")
                # Fall back to equal weights
                weight = 1.0 / len(assets)
                return {asset: weight for asset in assets}

            # Create the allocation dictionary
            allocation = {asset: weight for asset, weight in zip(assets, weights)}

            logger.info(f"Risk parity allocation completed for {len(assets)} assets")
            return allocation

        except Exception as e:
            logger.error(f"Error in risk parity allocation: {e}")
            # Fall back to equal weights
            if symbols and len(symbols) > 0:
                weight = 1.0 / len(symbols)
                return {symbol: weight for symbol in symbols}
            else:
                # Ultimate fallback - empty dictionary
                return {}


class MinimumVarianceStrategy(AllocationStrategy):
    """
    Minimum variance allocation strategy.

    This strategy allocates assets to minimize portfolio variance.
    """

    def __init__(self):
        """Initialize the minimum variance strategy."""
        super().__init__(
            name="Minimum Variance",
            description="Allocates assets to minimize portfolio variance."
        )

    async def allocate(
        self,
        symbols: List[str],
        prices: pd.DataFrame,
        risk_profile: str = RISK_PROFILE_MODERATE,
        **kwargs
    ) -> Dict[str, float]:
        """
        Allocate assets to minimize variance.

        Args:
            symbols: List of asset symbols
            prices: DataFrame with historical price data
            risk_profile: Risk profile name
            **kwargs: Additional parameters

        Returns:
            Dictionary mapping assets to weights
        """
        # Get the risk profile
        profile = get_risk_profile(risk_profile)

        # Get optimization constraints
        constraints = profile.get_optimization_constraints()

        # Calculate returns
        returns = prices.pct_change().dropna()

        # Calculate expected returns (not used for minimum variance)
        mu = returns.mean()

        # Calculate covariance matrix
        sigma = returns.cov()

        # Optimize portfolio for minimum variance
        weights = await portfolio_optimization_manager.optimize_portfolio_with_returns(
            mu, sigma, "min_volatility", constraints, **kwargs
        )

        return weights


class MaximumSharpeStrategy(AllocationStrategy):
    """
    Maximum Sharpe ratio allocation strategy.

    This strategy allocates assets to maximize the Sharpe ratio.
    """

    def __init__(self):
        """Initialize the maximum Sharpe ratio strategy."""
        super().__init__(
            name="Maximum Sharpe Ratio",
            description="Allocates assets to maximize the Sharpe ratio."
        )

    async def allocate(
        self,
        symbols: List[str],
        prices: pd.DataFrame,
        risk_profile: str = RISK_PROFILE_MODERATE,
        **kwargs
    ) -> Dict[str, float]:
        """
        Allocate assets to maximize Sharpe ratio.

        Args:
            symbols: List of asset symbols
            prices: DataFrame with historical price data
            risk_profile: Risk profile name
            **kwargs: Additional parameters

        Returns:
            Dictionary mapping assets to weights
        """
        # Get the risk profile
        profile = get_risk_profile(risk_profile)

        # Get optimization constraints
        constraints = profile.get_optimization_constraints()

        # Calculate returns
        returns = prices.pct_change().dropna()

        # Calculate expected returns
        mu = returns.mean()

        # Calculate covariance matrix
        sigma = returns.cov()

        # Optimize portfolio for maximum Sharpe ratio
        weights = await portfolio_optimization_manager.optimize_portfolio_with_returns(
            mu, sigma, "max_sharpe", constraints, **kwargs
        )

        return weights


class RiskProfileStrategy(AllocationStrategy):
    """
    Risk profile-based allocation strategy.

    This strategy allocates assets based on the user's risk profile.
    """

    def __init__(self):
        """Initialize the risk profile-based strategy."""
        super().__init__(
            name="Risk Profile",
            description="Allocates assets based on the user's risk profile."
        )

    async def allocate(
        self,
        symbols: List[str],
        prices: pd.DataFrame,
        risk_profile: str = RISK_PROFILE_MODERATE,
        **kwargs
    ) -> Dict[str, float]:
        """
        Allocate assets based on the risk profile.

        Args:
            symbols: List of asset symbols
            prices: DataFrame with historical price data
            risk_profile: Risk profile name
            **kwargs: Additional parameters

        Returns:
            Dictionary mapping assets to weights
        """
        # Get the risk profile
        profile = get_risk_profile(risk_profile)

        # Get optimization constraints and parameters
        constraints = profile.get_optimization_constraints()
        params = profile.get_optimization_params()

        # Calculate returns
        returns = prices.pct_change().dropna()

        # Calculate expected returns
        mu = returns.mean()

        # Calculate covariance matrix
        sigma = returns.cov()

        # Optimize portfolio based on the risk profile
        weights = await portfolio_optimization_manager.optimize_portfolio_with_returns(
            mu, sigma, params.get("optimization_criterion", "max_sharpe"),
            constraints, **{**kwargs, **params}
        )

        return weights


# Define available allocation strategies
ALLOCATION_STRATEGIES = {
    "equal_weight": EqualWeightStrategy(),
    "risk_parity": RiskParityStrategy(),
    "min_variance": MinimumVarianceStrategy(),
    "max_sharpe": MaximumSharpeStrategy(),
    "risk_profile": RiskProfileStrategy()
}


def get_allocation_strategy(name: str) -> AllocationStrategy:
    """
    Get an allocation strategy by name.

    Args:
        name: Name of the allocation strategy

    Returns:
        Allocation strategy (defaults to equal_weight if not found or if there's an error)
    """
    try:
        # Check if the requested strategy exists
        if name not in ALLOCATION_STRATEGIES:
            logger.warning(f"Allocation strategy '{name}' not found, using equal_weight")
            return ALLOCATION_STRATEGIES["equal_weight"]

        # Return the requested strategy
        logger.info(f"Using {name} strategy for allocation")
        return ALLOCATION_STRATEGIES[name]
    except Exception as e:
        logger.error(f"Error getting allocation strategy: {e}")
        # Always fall back to equal weight strategy
        return ALLOCATION_STRATEGIES["equal_weight"]


def get_all_allocation_strategies() -> Dict[str, AllocationStrategy]:
    """
    Get all available allocation strategies.

    Returns:
        Dictionary mapping strategy names to strategies
    """
    return ALLOCATION_STRATEGIES
