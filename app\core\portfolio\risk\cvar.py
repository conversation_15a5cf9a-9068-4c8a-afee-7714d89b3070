"""
Conditional Value at Risk (CVaR) implementation.
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union

from app.utils.logging import logger


def calculate_cvar(
    returns: pd.DataFrame,
    weights: Dict[str, float],
    alpha: float = 0.05,
    window: Optional[int] = None
) -> float:
    """
    Calculate Conditional Value at Risk (CVaR) for a portfolio.
    
    CVaR, also known as Expected Shortfall (ES), measures the expected loss
    in the worst alpha% of cases. It is a more conservative risk measure than VaR.
    
    Args:
        returns: DataFrame of asset returns
        weights: Dictionary mapping assets to weights
        alpha: Confidence level (default: 0.05 for 95% CVaR)
        window: Rolling window size (if None, uses all data)
        
    Returns:
        CVaR value
    """
    try:
        # Convert weights to Series
        weight_series = pd.Series(weights)
        
        # Ensure all assets in weights are in returns
        missing_assets = set(weight_series.index) - set(returns.columns)
        if missing_assets:
            logger.warning(f"Assets in weights but not in returns: {missing_assets}")
            # Filter out missing assets
            weight_series = weight_series[weight_series.index.isin(returns.columns)]
            
            # Renormalize weights
            if not weight_series.empty:
                weight_series = weight_series / weight_series.sum()
        
        # Calculate portfolio returns
        portfolio_returns = returns[weight_series.index].dot(weight_series)
        
        # If window is provided, use rolling calculation
        if window is not None and window > 0:
            # Calculate rolling CVaR
            rolling_cvar = portfolio_returns.rolling(window=window).apply(
                lambda x: -np.mean(x[x <= np.quantile(x, alpha)])
            )
            
            # Return the latest CVaR value
            return rolling_cvar.iloc[-1]
        else:
            # Calculate VaR
            var = np.quantile(portfolio_returns, alpha)
            
            # Calculate CVaR as the mean of returns below VaR
            cvar = -np.mean(portfolio_returns[portfolio_returns <= var])
            
            return cvar
    except Exception as e:
        logger.error(f"Error calculating CVaR: {str(e)}")
        return np.nan


def calculate_component_cvar(
    returns: pd.DataFrame,
    weights: Dict[str, float],
    alpha: float = 0.05
) -> Dict[str, float]:
    """
    Calculate Component CVaR for each asset in the portfolio.
    
    Component CVaR measures the contribution of each asset to the total CVaR.
    
    Args:
        returns: DataFrame of asset returns
        weights: Dictionary mapping assets to weights
        alpha: Confidence level (default: 0.05 for 95% CVaR)
        
    Returns:
        Dictionary mapping assets to Component CVaR values
    """
    try:
        # Convert weights to Series
        weight_series = pd.Series(weights)
        
        # Ensure all assets in weights are in returns
        missing_assets = set(weight_series.index) - set(returns.columns)
        if missing_assets:
            logger.warning(f"Assets in weights but not in returns: {missing_assets}")
            # Filter out missing assets
            weight_series = weight_series[weight_series.index.isin(returns.columns)]
            
            # Renormalize weights
            if not weight_series.empty:
                weight_series = weight_series / weight_series.sum()
        
        # Get relevant returns
        asset_returns = returns[weight_series.index]
        
        # Calculate portfolio returns
        portfolio_returns = asset_returns.dot(weight_series)
        
        # Calculate VaR
        var = np.quantile(portfolio_returns, alpha)
        
        # Identify stress scenarios (returns below VaR)
        stress_scenarios = portfolio_returns <= var
        
        # Calculate marginal CVaR for each asset
        marginal_cvar = {}
        for asset in weight_series.index:
            # Calculate covariance between asset returns and portfolio returns
            # in stress scenarios
            stress_returns = asset_returns.loc[stress_scenarios, asset]
            stress_portfolio = portfolio_returns[stress_scenarios]
            
            # Calculate marginal CVaR
            marginal_cvar[asset] = -np.cov(stress_returns, stress_portfolio)[0, 1] / np.var(stress_portfolio)
        
        # Calculate component CVaR
        component_cvar = {}
        total_cvar = calculate_cvar(returns, weights, alpha)
        
        for asset in weight_series.index:
            component_cvar[asset] = weight_series[asset] * marginal_cvar[asset] * total_cvar
        
        return component_cvar
    except Exception as e:
        logger.error(f"Error calculating Component CVaR: {str(e)}")
        return {asset: np.nan for asset in weights.keys()}


def calculate_incremental_cvar(
    returns: pd.DataFrame,
    weights: Dict[str, float],
    alpha: float = 0.05,
    delta: float = 0.01
) -> Dict[str, float]:
    """
    Calculate Incremental CVaR for each asset in the portfolio.
    
    Incremental CVaR measures the change in CVaR when the weight of an asset
    is increased by a small amount.
    
    Args:
        returns: DataFrame of asset returns
        weights: Dictionary mapping assets to weights
        alpha: Confidence level (default: 0.05 for 95% CVaR)
        delta: Weight increment (default: 0.01)
        
    Returns:
        Dictionary mapping assets to Incremental CVaR values
    """
    try:
        # Calculate base CVaR
        base_cvar = calculate_cvar(returns, weights, alpha)
        
        # Calculate incremental CVaR for each asset
        incremental_cvar = {}
        
        for asset in weights.keys():
            # Create new weights with increased weight for the asset
            new_weights = weights.copy()
            new_weights[asset] += delta
            
            # Renormalize weights
            total_weight = sum(new_weights.values())
            new_weights = {k: v / total_weight for k, v in new_weights.items()}
            
            # Calculate new CVaR
            new_cvar = calculate_cvar(returns, new_weights, alpha)
            
            # Calculate incremental CVaR
            incremental_cvar[asset] = (new_cvar - base_cvar) / delta
        
        return incremental_cvar
    except Exception as e:
        logger.error(f"Error calculating Incremental CVaR: {str(e)}")
        return {asset: np.nan for asset in weights.keys()}


def calculate_cvar_contribution(
    returns: pd.DataFrame,
    weights: Dict[str, float],
    alpha: float = 0.05
) -> Dict[str, float]:
    """
    Calculate CVaR Contribution for each asset in the portfolio.
    
    CVaR Contribution measures the percentage contribution of each asset to the total CVaR.
    
    Args:
        returns: DataFrame of asset returns
        weights: Dictionary mapping assets to weights
        alpha: Confidence level (default: 0.05 for 95% CVaR)
        
    Returns:
        Dictionary mapping assets to CVaR Contribution values (percentages)
    """
    try:
        # Calculate component CVaR
        component_cvar = calculate_component_cvar(returns, weights, alpha)
        
        # Calculate total CVaR
        total_cvar = sum(component_cvar.values())
        
        # Calculate CVaR contribution
        cvar_contribution = {
            asset: component / total_cvar * 100 if total_cvar != 0 else 0
            for asset, component in component_cvar.items()
        }
        
        return cvar_contribution
    except Exception as e:
        logger.error(f"Error calculating CVaR Contribution: {str(e)}")
        return {asset: np.nan for asset in weights.keys()}
