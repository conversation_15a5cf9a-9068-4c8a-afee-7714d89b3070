# Technical Analysis Module

The technical analysis module provides tools for analyzing financial data using various technical indicators and pattern recognition algorithms.

## Architecture

The technical analysis module follows a modular architecture:

1. **BaseIndicator**: Abstract base class that defines the interface for all technical indicators
2. **Specific Indicators**: Implementations for different types of indicators (trend, momentum, volatility, volume)
3. **Pattern Recognition**: Classes for identifying candlestick and chart patterns
4. **Technical Analysis Manager**: Unified interface for all technical analysis functionality

## Available Indicators

### Trend Indicators

Trend indicators help identify the direction of the market trend.

- **SMA (Simple Moving Average)**: Calculates the average price over a specified period
- **EMA (Exponential Moving Average)**: Gives more weight to recent prices
- **MACD (Moving Average Convergence Divergence)**: Shows the relationship between two moving averages

### Momentum Indicators

Momentum indicators measure the rate of change in price movements.

- **RSI (Relative Strength Index)**: Measures the speed and change of price movements
- **Stochastic Oscillator**: Compares a security's closing price to its price range over a specific period
- **CCI (Commodity Channel Index)**: Identifies cyclical trends in price movements
- **MFI (Money Flow Index)**: Combines price and volume to identify overbought or oversold conditions

### Volatility Indicators

Volatility indicators measure the rate of price movement, regardless of direction.

- **Bollinger Bands**: Shows the volatility of a security by plotting bands around a moving average
- **ATR (Average True Range)**: Measures market volatility

### Volume Indicators

Volume indicators use volume data to confirm price movements.

- **OBV (On-Balance Volume)**: Relates volume to price change
- **VWAP (Volume-Weighted Average Price)**: Calculates the average price weighted by volume
- **AD (Accumulation/Distribution)**: Measures the flow of money into and out of a security
- **ADOSC (Chaikin Oscillator)**: Measures the momentum of the Accumulation/Distribution line

## Pattern Recognition

### Candlestick Patterns

Candlestick patterns are specific formations on a candlestick chart that can indicate potential market reversals or continuations.

- **Doji**: Indicates indecision in the market
- **Hammer**: Potential bullish reversal pattern
- **Engulfing**: Potential reversal pattern (bullish or bearish)
- **Morning Star**: Bullish reversal pattern
- **Evening Star**: Bearish reversal pattern

### Chart Patterns

Chart patterns are formations that appear on price charts and can indicate potential market movements.

- **Head and Shoulders**: Reversal pattern indicating a trend change
- **Double Top/Bottom**: Reversal pattern indicating a trend change
- **Triangle**: Continuation pattern (ascending, descending, or symmetrical)
- **Flag/Pennant**: Short-term continuation pattern
- **Cup and Handle**: Bullish continuation pattern

## Using the Technical Analysis Manager

The `TechnicalAnalysisManager` provides a unified interface for all technical analysis functionality.

### Calculating Indicators

```python
from app.core.technical_analysis.manager import technical_analysis_manager

# Calculate a single indicator
df = technical_analysis_manager.calculate_indicator(
    data,
    indicator="rsi",
    period=14
)

# Calculate multiple indicators
indicators = [
    {"indicator": "sma", "period": 20},
    {"indicator": "rsi", "period": 14},
    {"indicator": "bollinger_bands", "period": 20, "std_dev": 2}
]
df = technical_analysis_manager.calculate_multiple_indicators(data, indicators)
```

### Getting Trading Signals

```python
# Get signals from a single indicator
df = technical_analysis_manager.get_indicator_signal(
    data,
    indicator="rsi",
    period=14,
    overbought=70,
    oversold=30
)

# Get signals from multiple indicators
indicators = [
    {"indicator": "sma", "period": 20},
    {"indicator": "rsi", "period": 14},
    {"indicator": "macd", "fast_period": 12, "slow_period": 26, "signal_period": 9}
]
df = technical_analysis_manager.get_multiple_indicator_signals(data, indicators)
```

### Identifying Patterns

```python
# Identify candlestick patterns
df = technical_analysis_manager.identify_candlestick_patterns(
    data,
    patterns=["doji", "hammer", "engulfing"]
)

# Identify chart patterns
df = technical_analysis_manager.identify_chart_patterns(
    data,
    patterns=["head_and_shoulders", "double_top", "double_bottom"]
)
```

### Comprehensive Analysis

```python
# Perform comprehensive analysis
indicators = [
    {"indicator": "sma", "period": 20},
    {"indicator": "rsi", "period": 14},
    {"indicator": "macd", "fast_period": 12, "slow_period": 26, "signal_period": 9}
]
df = technical_analysis_manager.analyze_symbol(
    data,
    indicators=indicators,
    candlestick_patterns=["doji", "hammer", "engulfing"],
    chart_patterns=["head_and_shoulders", "double_top", "double_bottom"]
)

# Get comprehensive trading signals
signals = technical_analysis_manager.get_trading_signals(
    data,
    indicators=indicators,
    candlestick_patterns=["doji", "hammer", "engulfing"],
    chart_patterns=["head_and_shoulders", "double_top", "double_bottom"]
)
```

## Adding a New Indicator

To add a new indicator, follow these steps:

1. Create a new class that inherits from `BaseIndicator`
2. Implement the required methods:
   - `calculate`: Calculate the indicator values
   - `get_signal`: Generate trading signals from the indicator
3. Register the indicator in the `TechnicalAnalysisManager.__init__` method

**Example:**
```python
from app.core.technical_analysis.indicators.base import BaseIndicator

class MyIndicator(BaseIndicator):
    def __init__(self):
        super().__init__("MyIndicator")
    
    def calculate(self, data, period=14, **kwargs):
        # Implementation
        pass
    
    def get_signal(self, data, period=14, **kwargs):
        # Implementation
        pass
```

Then register it in the `TechnicalAnalysisManager`:

```python
# In TechnicalAnalysisManager.__init__
self.indicators["my_indicator"] = MyIndicator()
```

## Signal Aggregation

The `TechnicalAnalysisManager` provides a method for aggregating signals from multiple indicators and patterns:

```python
# Get aggregated signals
signals = technical_analysis_manager.get_trading_signals(data, indicators)
```

The aggregated signals include:

- `aggregated_signal`: Sum of all individual signals
- `aggregated_signal_strength`: Normalized strength of the aggregated signal
- `signal_strength`: Classification of the signal (e.g., "bullish_strong", "bearish_moderate")
