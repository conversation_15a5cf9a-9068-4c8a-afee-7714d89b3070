"""
Unit tests for the portfolio performance module.
"""
import pytest
import numpy as np
import pandas as pd
from datetime import date, timedelta

from app.core.portfolio.performance import calculate_performance_metrics, calculate_rolling_metrics


@pytest.fixture
def sample_returns():
    """Sample returns data for testing."""
    # Create sample daily returns
    np.random.seed(42)
    dates = pd.date_range(start="2020-01-01", periods=252, freq="B")
    
    # Create returns with mean 0.001 (0.1%) and std 0.02 (2%)
    returns = pd.Series(
        np.random.normal(0.001, 0.02, size=len(dates)),
        index=dates
    )
    
    # Add some negative returns for drawdown testing
    returns.iloc[50:70] = -0.02
    
    return returns


@pytest.fixture
def benchmark_returns():
    """Sample benchmark returns data for testing."""
    # Create sample daily returns
    np.random.seed(24)
    dates = pd.date_range(start="2020-01-01", periods=252, freq="B")
    
    # Create returns with mean 0.0005 (0.05%) and std 0.015 (1.5%)
    returns = pd.Series(
        np.random.normal(0.0005, 0.015, size=len(dates)),
        index=dates
    )
    
    return returns


def test_calculate_performance_metrics_basic(sample_returns):
    """Test calculating basic performance metrics."""
    # Arrange
    risk_free_rate = 0.02  # 2% annual
    
    # Act
    metrics = calculate_performance_metrics(
        returns=sample_returns,
        risk_free_rate=risk_free_rate
    )
    
    # Assert
    assert isinstance(metrics, dict)
    
    # Check that all required metrics are present
    required_metrics = [
        "total_return", "annualized_return", "volatility",
        "sharpe_ratio", "sortino_ratio", "calmar_ratio",
        "max_drawdown", "var_95", "var_99", "cvar_95", "cvar_99",
        "skewness", "kurtosis", "win_rate", "avg_win", "avg_loss",
        "profit_factor"
    ]
    
    for metric in required_metrics:
        assert metric in metrics
    
    # Check that metrics have reasonable values
    assert isinstance(metrics["total_return"], float)
    assert isinstance(metrics["annualized_return"], float)
    assert isinstance(metrics["volatility"], float)
    assert metrics["volatility"] > 0
    assert isinstance(metrics["sharpe_ratio"], float)
    assert isinstance(metrics["sortino_ratio"], float)
    assert isinstance(metrics["max_drawdown"], float)
    assert metrics["max_drawdown"] <= 0


def test_calculate_performance_metrics_with_benchmark(sample_returns, benchmark_returns):
    """Test calculating performance metrics with benchmark."""
    # Arrange
    risk_free_rate = 0.02  # 2% annual
    
    # Act
    metrics = calculate_performance_metrics(
        returns=sample_returns,
        risk_free_rate=risk_free_rate,
        benchmark_returns=benchmark_returns
    )
    
    # Assert
    assert isinstance(metrics, dict)
    
    # Check that benchmark-relative metrics are present
    benchmark_metrics = [
        "alpha", "beta", "tracking_error", "information_ratio",
        "up_capture", "down_capture"
    ]
    
    for metric in benchmark_metrics:
        assert metric in metrics
    
    # Check that metrics have reasonable values
    assert isinstance(metrics["alpha"], float)
    assert isinstance(metrics["beta"], float)
    assert isinstance(metrics["tracking_error"], float)
    assert metrics["tracking_error"] > 0
    assert isinstance(metrics["information_ratio"], float)
    assert isinstance(metrics["up_capture"], float)
    assert isinstance(metrics["down_capture"], float)


def test_calculate_rolling_metrics(sample_returns):
    """Test calculating rolling performance metrics."""
    # Arrange
    risk_free_rate = 0.02  # 2% annual
    window = 63  # ~3 months
    
    # Act
    rolling_metrics = calculate_rolling_metrics(
        returns=sample_returns,
        risk_free_rate=risk_free_rate,
        window=window
    )
    
    # Assert
    assert isinstance(rolling_metrics, pd.DataFrame)
    
    # Check that all required metrics are present
    required_metrics = [
        "annualized_return", "volatility", "sharpe_ratio",
        "max_drawdown", "calmar_ratio", "downside_deviation",
        "sortino_ratio"
    ]
    
    for metric in required_metrics:
        assert metric in rolling_metrics.columns
    
    # Check that the DataFrame has the correct length
    assert len(rolling_metrics) == len(sample_returns) - window + 1
    
    # Check that the index is correct
    assert rolling_metrics.index[0] == sample_returns.index[window - 1]
    assert rolling_metrics.index[-1] == sample_returns.index[-1]
