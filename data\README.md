# Data Directory Structure

This directory contains all data used by the Portfolio Optimization project.

## Directory Structure

- **raw/**: Contains raw, immutable data dumps from data sources
  - Stock price data
  - Financial statements
  - Economic indicators
  - Market indices

- **processed/**: Contains cleaned, transformed, and feature-engineered data ready for analysis
  - Cleaned price data
  - Calculated technical indicators
  - Portfolio optimization results
  - Backtest results

- **external/**: Contains data from external sources that doesn't require processing
  - Benchmark data
  - Risk-free rate data
  - Market classification data

- **interim/**: Contains intermediate data that has been transformed but is not yet ready for analysis
  - Partially processed data
  - Temporary results

- **cached/**: Contains cached API responses and database query results
  - Yahoo Finance API responses
  - Alpha Vantage API responses
  - Financial Modeling Prep API responses

- **historical/**: Contains historical data archives
  - Dated snapshots of data
  - Historical portfolio allocations

- **user/**: Contains user-specific data
  - User portfolios
  - User preferences
  - Custom watchlists

## Data Formats

- CSV files for tabular data
- JSON files for structured data
- Pickle files for serialized Python objects
- SQLite databases for relational data

## Data Flow

1. Raw data is collected from various sources and stored in `raw/`
2. Data is cleaned and transformed in `interim/`
3. Final processed data is stored in `processed/`
4. External reference data is stored in `external/`
5. API responses are cached in `cached/`
6. Historical snapshots are archived in `historical/`
7. User-specific data is stored in `user/`
