#!/bin/bash

# Portfolio Optimization Deployment Script
# This script deploys the Portfolio Optimization application to a production environment.

set -e

# Configuration
ENVIRONMENT=${ENVIRONMENT:-production}
DEPLOY_DIR=${DEPLOY_DIR:-/opt/portfolio-optimization}
BACKUP_DIR=${BACKUP_DIR:-/opt/backups/portfolio-optimization}
REPO_URL=${REPO_URL:-https://github.com/yourusername/portfolio-optimization.git}
BRANCH=${BRANCH:-main}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Print usage
usage() {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  -e, --environment <env>   Deployment environment (default: production)"
    echo "  -d, --deploy-dir <dir>    Deployment directory (default: /opt/portfolio-optimization)"
    echo "  -b, --backup-dir <dir>    Backup directory (default: /opt/backups/portfolio-optimization)"
    echo "  -r, --repo-url <url>      Repository URL (default: https://github.com/yourusername/portfolio-optimization.git)"
    echo "  -B, --branch <branch>     Repository branch (default: main)"
    echo "  -h, --help                Show this help message"
    exit 1
}

# Parse command-line arguments
while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        -e|--environment)
            ENVIRONMENT="$2"
            shift
            shift
            ;;
        -d|--deploy-dir)
            DEPLOY_DIR="$2"
            shift
            shift
            ;;
        -b|--backup-dir)
            BACKUP_DIR="$2"
            shift
            shift
            ;;
        -r|--repo-url)
            REPO_URL="$2"
            shift
            shift
            ;;
        -B|--branch)
            BRANCH="$2"
            shift
            shift
            ;;
        -h|--help)
            usage
            ;;
        *)
            echo -e "${RED}Error: Unknown option $1${NC}"
            usage
            ;;
    esac
done

# Check if Docker and Docker Compose are installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Error: Docker is not installed${NC}"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}Error: Docker Compose is not installed${NC}"
    exit 1
fi

# Create deployment directory if it doesn't exist
if [ ! -d "$DEPLOY_DIR" ]; then
    echo -e "${YELLOW}Creating deployment directory: $DEPLOY_DIR${NC}"
    mkdir -p "$DEPLOY_DIR"
fi

# Create backup directory if it doesn't exist
if [ ! -d "$BACKUP_DIR" ]; then
    echo -e "${YELLOW}Creating backup directory: $BACKUP_DIR${NC}"
    mkdir -p "$BACKUP_DIR"
fi

# Backup current deployment
if [ -d "$DEPLOY_DIR/.git" ]; then
    echo -e "${YELLOW}Backing up current deployment${NC}"
    BACKUP_DATE=$(date +%Y%m%d%H%M%S)
    BACKUP_FILE="$BACKUP_DIR/backup-$BACKUP_DATE.tar.gz"
    tar -czf "$BACKUP_FILE" -C "$DEPLOY_DIR" .
    echo -e "${GREEN}Backup created: $BACKUP_FILE${NC}"
fi

# Clone or update repository
if [ -d "$DEPLOY_DIR/.git" ]; then
    echo -e "${YELLOW}Updating repository${NC}"
    cd "$DEPLOY_DIR"
    git fetch
    git checkout "$BRANCH"
    git pull
else
    echo -e "${YELLOW}Cloning repository${NC}"
    git clone -b "$BRANCH" "$REPO_URL" "$DEPLOY_DIR"
    cd "$DEPLOY_DIR"
fi

# Check if .env file exists
if [ ! -f "$DEPLOY_DIR/.env" ]; then
    echo -e "${YELLOW}Creating .env file${NC}"
    cp "$DEPLOY_DIR/.env.example" "$DEPLOY_DIR/.env"
    echo -e "${RED}Warning: Please update the .env file with your configuration${NC}"
fi

# Build and start containers
echo -e "${YELLOW}Building and starting containers${NC}"
cd "$DEPLOY_DIR/deployment/docker"
docker-compose -f docker-compose.yml -f docker-compose.prod.yml down
docker-compose -f docker-compose.yml -f docker-compose.prod.yml build
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Run database migrations
echo -e "${YELLOW}Running database migrations${NC}"
docker-compose -f docker-compose.yml -f docker-compose.prod.yml exec api alembic upgrade head

# Check if services are running
echo -e "${YELLOW}Checking if services are running${NC}"
if docker-compose -f docker-compose.yml -f docker-compose.prod.yml ps | grep -q "Up"; then
    echo -e "${GREEN}Deployment successful!${NC}"
    echo -e "${GREEN}API: https://api.portfolio-optimization.example.com${NC}"
    echo -e "${GREEN}Streamlit: https://app.portfolio-optimization.example.com${NC}"
else
    echo -e "${RED}Deployment failed!${NC}"
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs
    exit 1
fi
