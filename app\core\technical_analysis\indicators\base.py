"""
Base technical indicator class.
"""
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union

import pandas as pd


class BaseIndicator(ABC):
    """
    Abstract base class for technical indicators.
    
    All technical indicators should inherit from this class
    and implement the required methods.
    """
    
    def __init__(self, name: str):
        """
        Initialize the indicator.
        
        Args:
            name: The name of the indicator
        """
        self.name = name
    
    @abstractmethod
    def calculate(
        self,
        data: pd.DataFrame,
        **kwargs
    ) -> pd.DataFrame:
        """
        Calculate the indicator.
        
        Args:
            data: DataFrame with OHLCV data
            **kwargs: Additional parameters for the indicator
            
        Returns:
            DataFrame with the indicator values
        """
        pass
    
    @abstractmethod
    def get_signal(
        self,
        data: pd.DataFrame,
        **kwargs
    ) -> pd.DataFrame:
        """
        Get trading signals from the indicator.
        
        Args:
            data: DataFrame with indicator values
            **kwargs: Additional parameters for signal generation
            
        Returns:
            DataFrame with trading signals
        """
        pass
    
    def validate_data(
        self,
        data: pd.DataFrame,
        required_columns: Optional[List[str]] = None
    ) -> bool:
        """
        Validate input data.
        
        Args:
            data: DataFrame to validate
            required_columns: List of required columns
            
        Returns:
            True if the data is valid, False otherwise
        """
        # Check if DataFrame is empty
        if data is None or data.empty:
            return False
        
        # Check for required columns
        if required_columns:
            if not all(col in data.columns for col in required_columns):
                return False
        
        return True
