"""
Base portfolio allocation strategy.
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Union

import pandas as pd


class AllocationStrategy(ABC):
    """
    Abstract base class for portfolio allocation strategies.
    
    All allocation strategies should inherit from this class
    and implement the required methods.
    """
    
    def __init__(self, name: str):
        """
        Initialize the allocation strategy.
        
        Args:
            name: The name of the strategy
        """
        self.name = name
    
    @abstractmethod
    def allocate(
        self,
        returns: Optional[pd.DataFrame] = None,
        prices: Optional[pd.DataFrame] = None,
        **kwargs
    ) -> Dict[str, float]:
        """
        Allocate portfolio weights.
        
        Args:
            returns: DataFrame of asset returns
            prices: DataFrame of asset prices
            **kwargs: Additional parameters
            
        Returns:
            Dictionary mapping asset names to weights
        """
        pass
    
    @abstractmethod
    def rebalance(
        self,
        current_weights: Dict[str, float],
        returns: Optional[pd.DataFrame] = None,
        prices: Optional[pd.DataFrame] = None,
        **kwargs
    ) -> Dict[str, float]:
        """
        Rebalance portfolio weights.
        
        Args:
            current_weights: Dictionary of current asset weights
            returns: DataFrame of asset returns
            prices: DataFrame of asset prices
            **kwargs: Additional parameters
            
        Returns:
            Dictionary mapping asset names to target weights
        """
        pass
    
    def validate_weights(self, weights: Dict[str, float]) -> bool:
        """
        Validate portfolio weights.
        
        Args:
            weights: Dictionary of asset weights
            
        Returns:
            True if weights are valid, False otherwise
        """
        # Check if weights sum to approximately 1
        weight_sum = sum(weights.values())
        if not (0.99 <= weight_sum <= 1.01):
            return False
        
        # Check if all weights are non-negative
        if any(w < 0 for w in weights.values()):
            return False
        
        return True
