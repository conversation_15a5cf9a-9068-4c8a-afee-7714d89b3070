# Portfolio Optimization API Documentation

This document provides information about the Portfolio Optimization API endpoints, request/response formats, and usage examples.

## Base URL

- Development: `http://localhost:8000`
- Production: `https://api.portfolio-optimization.com`

## Authentication

The API uses JWT (JSON Web Token) authentication. To access protected endpoints, you need to:

1. Obtain a token by logging in
2. Include the token in the `Authorization` header of your requests:
   ```
   Authorization: Bearer <your_token>
   ```

## Endpoints

### Authentication

#### Login

```
POST /api/v1/auth/login
```

Request body:
```json
{
  "username": "<EMAIL>",
  "password": "your_password"
}
```

Response:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}
```

### Data

#### Get Stock Data

```
GET /api/v1/data/stock/{symbol}
```

Query parameters:
- `start_date`: Start date (YYYY-MM-DD)
- `end_date`: End date (YYYY-MM-DD)
- `interval`: Data interval (1d, 1wk, 1mo)

Response:
```json
{
  "symbol": "AAPL",
  "data": [
    {
      "date": "2023-01-01",
      "open": 130.28,
      "high": 132.49,
      "low": 128.72,
      "close": 131.86,
      "volume": 75245600
    },
    ...
  ]
}
```

#### Search Symbols

```
GET /api/v1/data/search
```

Query parameters:
- `query`: Search query

Response:
```json
{
  "results": [
    {
      "symbol": "AAPL",
      "name": "Apple Inc.",
      "exchange": "NASDAQ",
      "type": "stock"
    },
    ...
  ]
}
```

### Technical Analysis

#### Calculate Indicators

```
POST /api/v1/analysis/indicators
```

Request body:
```json
{
  "symbol": "AAPL",
  "start_date": "2023-01-01",
  "end_date": "2023-12-31",
  "indicators": [
    {
      "type": "sma",
      "params": {
        "period": 20
      }
    },
    {
      "type": "rsi",
      "params": {
        "period": 14
      }
    }
  ]
}
```

Response:
```json
{
  "symbol": "AAPL",
  "data": [
    {
      "date": "2023-01-01",
      "close": 131.86,
      "sma_20": 130.45,
      "rsi_14": 58.32
    },
    ...
  ]
}
```

### Portfolio

#### Optimize Portfolio

```
POST /api/v1/portfolio/optimize
```

Request body:
```json
{
  "symbols": ["AAPL", "MSFT", "GOOG", "AMZN"],
  "start_date": "2020-01-01",
  "end_date": "2023-12-31",
  "method": "mean_variance",
  "objective": "sharpe",
  "constraints": {
    "min_weight": 0.05,
    "max_weight": 0.4
  }
}
```

Response:
```json
{
  "weights": {
    "AAPL": 0.25,
    "MSFT": 0.30,
    "GOOG": 0.20,
    "AMZN": 0.25
  },
  "metrics": {
    "expected_return": 0.15,
    "volatility": 0.18,
    "sharpe_ratio": 0.83
  }
}
```

#### Backtest Portfolio

```
POST /api/v1/portfolio/backtest
```

Request body:
```json
{
  "weights": {
    "AAPL": 0.25,
    "MSFT": 0.30,
    "GOOG": 0.20,
    "AMZN": 0.25
  },
  "start_date": "2020-01-01",
  "end_date": "2023-12-31",
  "initial_investment": 10000,
  "rebalance_frequency": "monthly"
}
```

Response:
```json
{
  "performance": [
    {
      "date": "2020-01-31",
      "portfolio_value": 10250.00,
      "return": 0.025
    },
    ...
  ],
  "metrics": {
    "total_return": 0.45,
    "annualized_return": 0.15,
    "volatility": 0.18,
    "sharpe_ratio": 0.83,
    "max_drawdown": 0.25
  }
}
```

## Error Handling

The API returns standard HTTP status codes:

- 200: Success
- 400: Bad request (invalid parameters)
- 401: Unauthorized (authentication required)
- 403: Forbidden (insufficient permissions)
- 404: Not found
- 500: Internal server error

Error response format:

```json
{
  "error": {
    "code": "INVALID_PARAMETERS",
    "message": "Invalid parameters provided",
    "details": {
      "symbols": "At least one symbol is required"
    }
  }
}
```

## Rate Limiting

The API has rate limiting to prevent abuse:

- 100 requests per minute for authenticated users
- 10 requests per minute for unauthenticated users

When rate limit is exceeded, the API returns a 429 (Too Many Requests) status code.

## Pagination

For endpoints that return large datasets, pagination is supported:

Query parameters:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 100, max: 1000)

Response includes pagination metadata:

```json
{
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 100,
    "total_items": 1250,
    "total_pages": 13
  }
}
```
