"""
Real-time data collection module.

This module provides functions to fetch real-time or near real-time market data
from various sources.
"""
import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union

import pandas as pd
import requests
import yfinance as yf
from websocket import create_connection

from app.settings import get_settings
from app.utils.logging import logger


class RealTimeDataProvider:
    """
    Base class for real-time data providers.
    """
    
    def __init__(self, name: str):
        """
        Initialize the real-time data provider.
        
        Args:
            name: Name of the data provider
        """
        self.name = name
        self.settings = get_settings()
    
    async def get_real_time_quote(self, symbol: str) -> Dict:
        """
        Get real-time quote for a symbol.
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Dictionary with quote data
        """
        raise NotImplementedError("Subclasses must implement get_real_time_quote")
    
    async def get_real_time_quotes(self, symbols: List[str]) -> Dict[str, Dict]:
        """
        Get real-time quotes for multiple symbols.
        
        Args:
            symbols: List of stock symbols
            
        Returns:
            Dictionary mapping symbols to quote data
        """
        raise NotImplementedError("Subclasses must implement get_real_time_quotes")
    
    async def get_intraday_data(
        self, 
        symbol: str, 
        interval: str = "1m", 
        limit: int = 100
    ) -> pd.DataFrame:
        """
        Get intraday data for a symbol.
        
        Args:
            symbol: Stock symbol
            interval: Data interval (e.g., '1m', '5m', '15m', '30m', '1h')
            limit: Maximum number of data points
            
        Returns:
            DataFrame with intraday data
        """
        raise NotImplementedError("Subclasses must implement get_intraday_data")


class YahooFinanceRealTime(RealTimeDataProvider):
    """
    Real-time data provider using Yahoo Finance.
    
    Note: Yahoo Finance doesn't provide true real-time data, but rather
    delayed quotes (typically 15-20 minutes for US markets).
    """
    
    def __init__(self):
        """Initialize the Yahoo Finance real-time data provider."""
        super().__init__("Yahoo Finance")
    
    async def get_real_time_quote(self, symbol: str) -> Dict:
        """
        Get real-time quote for a symbol using Yahoo Finance.
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Dictionary with quote data
        """
        try:
            # Get ticker info
            ticker = yf.Ticker(symbol)
            
            # Get real-time quote (actually delayed by ~15-20 minutes)
            quote = ticker.info
            
            # Extract relevant fields
            result = {
                "symbol": symbol,
                "price": quote.get("regularMarketPrice", None),
                "change": quote.get("regularMarketChange", None),
                "change_percent": quote.get("regularMarketChangePercent", None),
                "volume": quote.get("regularMarketVolume", None),
                "market_cap": quote.get("marketCap", None),
                "timestamp": datetime.now().isoformat(),
                "delayed": True,  # Yahoo Finance data is delayed
                "source": self.name
            }
            
            return result
        
        except Exception as e:
            logger.error(f"Error getting real-time quote for {symbol} from Yahoo Finance: {str(e)}")
            return {
                "symbol": symbol,
                "price": None,
                "change": None,
                "change_percent": None,
                "volume": None,
                "market_cap": None,
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "source": self.name
            }
    
    async def get_real_time_quotes(self, symbols: List[str]) -> Dict[str, Dict]:
        """
        Get real-time quotes for multiple symbols using Yahoo Finance.
        
        Args:
            symbols: List of stock symbols
            
        Returns:
            Dictionary mapping symbols to quote data
        """
        try:
            # Get tickers info
            tickers = yf.Tickers(" ".join(symbols))
            
            # Get quotes for all symbols
            results = {}
            
            # Process each ticker
            for symbol in symbols:
                try:
                    # Get ticker info
                    if symbol in tickers.tickers:
                        quote = tickers.tickers[symbol].info
                        
                        # Extract relevant fields
                        results[symbol] = {
                            "symbol": symbol,
                            "price": quote.get("regularMarketPrice", None),
                            "change": quote.get("regularMarketChange", None),
                            "change_percent": quote.get("regularMarketChangePercent", None),
                            "volume": quote.get("regularMarketVolume", None),
                            "market_cap": quote.get("marketCap", None),
                            "timestamp": datetime.now().isoformat(),
                            "delayed": True,  # Yahoo Finance data is delayed
                            "source": self.name
                        }
                    else:
                        results[symbol] = {
                            "symbol": symbol,
                            "price": None,
                            "change": None,
                            "change_percent": None,
                            "volume": None,
                            "market_cap": None,
                            "timestamp": datetime.now().isoformat(),
                            "error": "Ticker not found",
                            "source": self.name
                        }
                
                except Exception as e:
                    logger.error(f"Error getting real-time quote for {symbol} from Yahoo Finance: {str(e)}")
                    results[symbol] = {
                        "symbol": symbol,
                        "price": None,
                        "change": None,
                        "change_percent": None,
                        "volume": None,
                        "market_cap": None,
                        "timestamp": datetime.now().isoformat(),
                        "error": str(e),
                        "source": self.name
                    }
            
            return results
        
        except Exception as e:
            logger.error(f"Error getting real-time quotes from Yahoo Finance: {str(e)}")
            return {symbol: {
                "symbol": symbol,
                "price": None,
                "change": None,
                "change_percent": None,
                "volume": None,
                "market_cap": None,
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "source": self.name
            } for symbol in symbols}
    
    async def get_intraday_data(
        self, 
        symbol: str, 
        interval: str = "1m", 
        limit: int = 100
    ) -> pd.DataFrame:
        """
        Get intraday data for a symbol using Yahoo Finance.
        
        Args:
            symbol: Stock symbol
            interval: Data interval (e.g., '1m', '5m', '15m', '30m', '1h')
            limit: Maximum number of data points
            
        Returns:
            DataFrame with intraday data
        """
        try:
            # Map interval to Yahoo Finance format
            interval_map = {
                "1m": "1m",
                "5m": "5m",
                "15m": "15m",
                "30m": "30m",
                "1h": "60m"
            }
            
            yf_interval = interval_map.get(interval, "1m")
            
            # Calculate start and end dates based on limit and interval
            end_date = datetime.now()
            
            # For 1-minute data, Yahoo only provides 7 days of historical data
            if yf_interval == "1m":
                days_to_fetch = min(7, limit // 390 + 1)  # ~390 minutes in a trading day
            elif yf_interval == "5m":
                days_to_fetch = min(60, limit // 78 + 1)  # ~78 5-minute intervals in a trading day
            elif yf_interval == "15m":
                days_to_fetch = min(60, limit // 26 + 1)  # ~26 15-minute intervals in a trading day
            elif yf_interval == "30m":
                days_to_fetch = min(60, limit // 13 + 1)  # ~13 30-minute intervals in a trading day
            elif yf_interval == "60m":
                days_to_fetch = min(730, limit // 6.5 + 1)  # ~6.5 60-minute intervals in a trading day
            else:
                days_to_fetch = 7
            
            start_date = end_date - timedelta(days=days_to_fetch)
            
            # Get historical data
            ticker = yf.Ticker(symbol)
            df = ticker.history(interval=yf_interval, start=start_date, end=end_date)
            
            # Reset index to make Date a column
            df = df.reset_index()
            
            # Rename columns to match our standard format
            df = df.rename(columns={
                "Date": "date",
                "Datetime": "date",
                "Open": "open",
                "High": "high",
                "Low": "low",
                "Close": "close",
                "Volume": "volume"
            })
            
            # Limit the number of rows
            if len(df) > limit:
                df = df.tail(limit)
            
            # Add source column
            df["source"] = self.name
            
            return df
        
        except Exception as e:
            logger.error(f"Error getting intraday data for {symbol} from Yahoo Finance: {str(e)}")
            return pd.DataFrame()


class AlphaVantageRealTime(RealTimeDataProvider):
    """
    Real-time data provider using Alpha Vantage.
    """
    
    def __init__(self):
        """Initialize the Alpha Vantage real-time data provider."""
        super().__init__("Alpha Vantage")
        self.api_key = getattr(self.settings, "alpha_vantage_api_key", None)
        
        if not self.api_key:
            logger.warning("Alpha Vantage API key not found in settings")
    
    async def get_real_time_quote(self, symbol: str) -> Dict:
        """
        Get real-time quote for a symbol using Alpha Vantage.
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Dictionary with quote data
        """
        if not self.api_key:
            logger.error("Alpha Vantage API key not found")
            return {
                "symbol": symbol,
                "price": None,
                "change": None,
                "change_percent": None,
                "volume": None,
                "market_cap": None,
                "timestamp": datetime.now().isoformat(),
                "error": "API key not found",
                "source": self.name
            }
        
        try:
            # Build URL
            url = f"https://www.alphavantage.co/query?function=GLOBAL_QUOTE&symbol={symbol}&apikey={self.api_key}"
            
            # Make request
            response = requests.get(url)
            data = response.json()
            
            # Check for errors
            if "Error Message" in data:
                logger.error(f"Alpha Vantage API error: {data['Error Message']}")
                return {
                    "symbol": symbol,
                    "price": None,
                    "change": None,
                    "change_percent": None,
                    "volume": None,
                    "market_cap": None,
                    "timestamp": datetime.now().isoformat(),
                    "error": data["Error Message"],
                    "source": self.name
                }
            
            # Extract quote data
            quote = data.get("Global Quote", {})
            
            if not quote:
                logger.error(f"No quote data found for {symbol}")
                return {
                    "symbol": symbol,
                    "price": None,
                    "change": None,
                    "change_percent": None,
                    "volume": None,
                    "market_cap": None,
                    "timestamp": datetime.now().isoformat(),
                    "error": "No quote data found",
                    "source": self.name
                }
            
            # Extract relevant fields
            price = float(quote.get("05. price", 0))
            change = float(quote.get("09. change", 0))
            change_percent = float(quote.get("10. change percent", "0%").replace("%", "")) / 100
            volume = int(quote.get("06. volume", 0))
            
            result = {
                "symbol": symbol,
                "price": price,
                "change": change,
                "change_percent": change_percent,
                "volume": volume,
                "market_cap": None,  # Alpha Vantage doesn't provide market cap in this endpoint
                "timestamp": datetime.now().isoformat(),
                "delayed": False,  # Alpha Vantage provides real-time data
                "source": self.name
            }
            
            return result
        
        except Exception as e:
            logger.error(f"Error getting real-time quote for {symbol} from Alpha Vantage: {str(e)}")
            return {
                "symbol": symbol,
                "price": None,
                "change": None,
                "change_percent": None,
                "volume": None,
                "market_cap": None,
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "source": self.name
            }
    
    async def get_real_time_quotes(self, symbols: List[str]) -> Dict[str, Dict]:
        """
        Get real-time quotes for multiple symbols using Alpha Vantage.
        
        Args:
            symbols: List of stock symbols
            
        Returns:
            Dictionary mapping symbols to quote data
        """
        if not self.api_key:
            logger.error("Alpha Vantage API key not found")
            return {symbol: {
                "symbol": symbol,
                "price": None,
                "change": None,
                "change_percent": None,
                "volume": None,
                "market_cap": None,
                "timestamp": datetime.now().isoformat(),
                "error": "API key not found",
                "source": self.name
            } for symbol in symbols}
        
        try:
            # Alpha Vantage doesn't have a batch quote endpoint, so we need to make
            # separate requests for each symbol. To avoid rate limits, we'll use
            # asyncio to make concurrent requests.
            
            # Create tasks for each symbol
            tasks = [self.get_real_time_quote(symbol) for symbol in symbols]
            
            # Wait for all tasks to complete
            results = await asyncio.gather(*tasks)
            
            # Convert list of results to dictionary
            return {result["symbol"]: result for result in results}
        
        except Exception as e:
            logger.error(f"Error getting real-time quotes from Alpha Vantage: {str(e)}")
            return {symbol: {
                "symbol": symbol,
                "price": None,
                "change": None,
                "change_percent": None,
                "volume": None,
                "market_cap": None,
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "source": self.name
            } for symbol in symbols}
    
    async def get_intraday_data(
        self, 
        symbol: str, 
        interval: str = "1m", 
        limit: int = 100
    ) -> pd.DataFrame:
        """
        Get intraday data for a symbol using Alpha Vantage.
        
        Args:
            symbol: Stock symbol
            interval: Data interval (e.g., '1m', '5m', '15m', '30m', '1h')
            limit: Maximum number of data points
            
        Returns:
            DataFrame with intraday data
        """
        if not self.api_key:
            logger.error("Alpha Vantage API key not found")
            return pd.DataFrame()
        
        try:
            # Map interval to Alpha Vantage format
            interval_map = {
                "1m": "1min",
                "5m": "5min",
                "15m": "15min",
                "30m": "30min",
                "1h": "60min"
            }
            
            av_interval = interval_map.get(interval, "1min")
            
            # Build URL
            url = f"https://www.alphavantage.co/query?function=TIME_SERIES_INTRADAY&symbol={symbol}&interval={av_interval}&outputsize=full&apikey={self.api_key}"
            
            # Make request
            response = requests.get(url)
            data = response.json()
            
            # Check for errors
            if "Error Message" in data:
                logger.error(f"Alpha Vantage API error: {data['Error Message']}")
                return pd.DataFrame()
            
            # Extract time series data
            time_series_key = f"Time Series ({av_interval})"
            time_series = data.get(time_series_key, {})
            
            if not time_series:
                logger.error(f"No intraday data found for {symbol}")
                return pd.DataFrame()
            
            # Convert to DataFrame
            df = pd.DataFrame.from_dict(time_series, orient="index")
            
            # Rename columns
            df = df.rename(columns={
                "1. open": "open",
                "2. high": "high",
                "3. low": "low",
                "4. close": "close",
                "5. volume": "volume"
            })
            
            # Convert index to datetime and reset
            df.index = pd.to_datetime(df.index)
            df = df.reset_index()
            df = df.rename(columns={"index": "date"})
            
            # Convert numeric columns
            for col in ["open", "high", "low", "close", "volume"]:
                df[col] = pd.to_numeric(df[col])
            
            # Sort by date (newest first)
            df = df.sort_values("date", ascending=False)
            
            # Limit the number of rows
            if len(df) > limit:
                df = df.head(limit)
            
            # Add source column
            df["source"] = self.name
            
            return df
        
        except Exception as e:
            logger.error(f"Error getting intraday data for {symbol} from Alpha Vantage: {str(e)}")
            return pd.DataFrame()


class RealTimeDataManager:
    """
    Manager for real-time data providers.
    """
    
    def __init__(self):
        """Initialize the real-time data manager."""
        logger.info("Initializing Real-Time Data Manager")
        
        # Initialize data providers
        self.providers = {
            "yahoo": YahooFinanceRealTime(),
            "alpha_vantage": AlphaVantageRealTime()
        }
        
        # Set default provider
        self.default_provider = "yahoo"
        
        # Initialize cache
        self.cache = {}
        self.cache_expiry = {}
        self.cache_duration = 60  # Cache duration in seconds
    
    async def get_real_time_quote(
        self, 
        symbol: str, 
        provider: Optional[str] = None,
        use_cache: bool = True
    ) -> Dict:
        """
        Get real-time quote for a symbol.
        
        Args:
            symbol: Stock symbol
            provider: Data provider to use (default: self.default_provider)
            use_cache: Whether to use cached data if available
            
        Returns:
            Dictionary with quote data
        """
        # Use default provider if not specified
        if provider is None:
            provider = self.default_provider
        
        # Check if provider exists
        if provider not in self.providers:
            logger.error(f"Provider '{provider}' not found")
            return {
                "symbol": symbol,
                "price": None,
                "change": None,
                "change_percent": None,
                "volume": None,
                "market_cap": None,
                "timestamp": datetime.now().isoformat(),
                "error": f"Provider '{provider}' not found",
                "source": "unknown"
            }
        
        # Check cache
        cache_key = f"{provider}:{symbol}:quote"
        if use_cache and cache_key in self.cache:
            # Check if cache is still valid
            if time.time() < self.cache_expiry.get(cache_key, 0):
                return self.cache[cache_key]
        
        # Get quote from provider
        quote = await self.providers[provider].get_real_time_quote(symbol)
        
        # Update cache
        if use_cache:
            self.cache[cache_key] = quote
            self.cache_expiry[cache_key] = time.time() + self.cache_duration
        
        return quote
    
    async def get_real_time_quotes(
        self, 
        symbols: List[str], 
        provider: Optional[str] = None,
        use_cache: bool = True
    ) -> Dict[str, Dict]:
        """
        Get real-time quotes for multiple symbols.
        
        Args:
            symbols: List of stock symbols
            provider: Data provider to use (default: self.default_provider)
            use_cache: Whether to use cached data if available
            
        Returns:
            Dictionary mapping symbols to quote data
        """
        # Use default provider if not specified
        if provider is None:
            provider = self.default_provider
        
        # Check if provider exists
        if provider not in self.providers:
            logger.error(f"Provider '{provider}' not found")
            return {symbol: {
                "symbol": symbol,
                "price": None,
                "change": None,
                "change_percent": None,
                "volume": None,
                "market_cap": None,
                "timestamp": datetime.now().isoformat(),
                "error": f"Provider '{provider}' not found",
                "source": "unknown"
            } for symbol in symbols}
        
        # Check which symbols are in cache
        cached_quotes = {}
        symbols_to_fetch = []
        
        if use_cache:
            for symbol in symbols:
                cache_key = f"{provider}:{symbol}:quote"
                if cache_key in self.cache and time.time() < self.cache_expiry.get(cache_key, 0):
                    cached_quotes[symbol] = self.cache[cache_key]
                else:
                    symbols_to_fetch.append(symbol)
        else:
            symbols_to_fetch = symbols
        
        # Fetch quotes for symbols not in cache
        fetched_quotes = {}
        if symbols_to_fetch:
            fetched_quotes = await self.providers[provider].get_real_time_quotes(symbols_to_fetch)
            
            # Update cache
            if use_cache:
                for symbol, quote in fetched_quotes.items():
                    cache_key = f"{provider}:{symbol}:quote"
                    self.cache[cache_key] = quote
                    self.cache_expiry[cache_key] = time.time() + self.cache_duration
        
        # Combine cached and fetched quotes
        return {**cached_quotes, **fetched_quotes}
    
    async def get_intraday_data(
        self, 
        symbol: str, 
        interval: str = "1m", 
        limit: int = 100,
        provider: Optional[str] = None,
        use_cache: bool = True
    ) -> pd.DataFrame:
        """
        Get intraday data for a symbol.
        
        Args:
            symbol: Stock symbol
            interval: Data interval (e.g., '1m', '5m', '15m', '30m', '1h')
            limit: Maximum number of data points
            provider: Data provider to use (default: self.default_provider)
            use_cache: Whether to use cached data if available
            
        Returns:
            DataFrame with intraday data
        """
        # Use default provider if not specified
        if provider is None:
            provider = self.default_provider
        
        # Check if provider exists
        if provider not in self.providers:
            logger.error(f"Provider '{provider}' not found")
            return pd.DataFrame()
        
        # Check cache
        cache_key = f"{provider}:{symbol}:{interval}:intraday"
        if use_cache and cache_key in self.cache:
            # Check if cache is still valid
            if time.time() < self.cache_expiry.get(cache_key, 0):
                return self.cache[cache_key]
        
        # Get intraday data from provider
        df = await self.providers[provider].get_intraday_data(symbol, interval, limit)
        
        # Update cache
        if use_cache and not df.empty:
            self.cache[cache_key] = df
            self.cache_expiry[cache_key] = time.time() + self.cache_duration
        
        return df
    
    def clear_cache(self):
        """Clear the cache."""
        self.cache = {}
        self.cache_expiry = {}
        logger.info("Real-time data cache cleared")


# Create a singleton instance
real_time_data_manager = RealTimeDataManager()
