"""
Unit tests for the data manager.
"""
import pytest
import pandas as pd
from datetime import date
from unittest.mock import patch, MagicMock

from app.core.data_collection.data_manager import data_manager


@pytest.fixture
def mock_yahoo_finance():
    """Mock the Yahoo Finance data source."""
    with patch("app.core.data_collection.data_manager.YahooFinanceDataSource") as mock:
        instance = MagicMock()
        mock.return_value = instance
        
        # Mock get_stock_data method
        instance.get_stock_data.return_value = pd.DataFrame({
            "Open": [100.0, 101.0, 102.0],
            "High": [105.0, 106.0, 107.0],
            "Low": [95.0, 96.0, 97.0],
            "Close": [103.0, 104.0, 105.0],
            "Adj Close": [103.0, 104.0, 105.0],
            "Volume": [1000000, 1100000, 1200000]
        }, index=pd.date_range("2020-01-01", periods=3))
        
        yield instance


@pytest.mark.asyncio
async def test_get_stock_data(mock_yahoo_finance, settings):
    """Test getting stock data."""
    # Arrange
    symbol = "AAPL"
    start_date = date(2020, 1, 1)
    end_date = date(2020, 1, 3)
    
    # Act
    result = await data_manager.get_stock_data(
        symbol=symbol,
        start_date=start_date,
        end_date=end_date
    )
    
    # Assert
    assert isinstance(result, pd.DataFrame)
    assert len(result) == 3
    assert "Open" in result.columns
    assert "High" in result.columns
    assert "Low" in result.columns
    assert "Close" in result.columns
    assert "Adj Close" in result.columns
    assert "Volume" in result.columns
    
    # Verify the mock was called correctly
    mock_yahoo_finance.get_stock_data.assert_called_once_with(
        symbol=symbol,
        start_date=start_date,
        end_date=end_date,
        interval="1d"
    )


@pytest.mark.asyncio
async def test_get_multiple_stock_data(mock_yahoo_finance, settings):
    """Test getting multiple stock data."""
    # Arrange
    symbols = ["AAPL", "MSFT", "GOOGL"]
    start_date = date(2020, 1, 1)
    end_date = date(2020, 1, 3)
    
    # Act
    result = await data_manager.get_multiple_stock_data(
        symbols=symbols,
        start_date=start_date,
        end_date=end_date
    )
    
    # Assert
    assert isinstance(result, dict)
    assert len(result) == 3
    assert "AAPL" in result
    assert "MSFT" in result
    assert "GOOGL" in result
    
    for symbol in symbols:
        assert isinstance(result[symbol], pd.DataFrame)
        assert len(result[symbol]) == 3
        assert "Adj Close" in result[symbol].columns
    
    # Verify the mock was called correctly
    assert mock_yahoo_finance.get_stock_data.call_count == 3
