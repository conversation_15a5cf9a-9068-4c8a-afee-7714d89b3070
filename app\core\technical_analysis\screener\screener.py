"""
Stock screener implementation.
"""
from datetime import date, timedelta
from typing import Dict, List, Optional, Union

import pandas as pd

from app.core.data_collection.data_manager import data_manager
from app.core.technical_analysis.indicators.momentum import RSI, Stochastic
from app.core.technical_analysis.indicators.trend import MACD, MovingAverage
from app.core.technical_analysis.indicators.volatility import ATR, BollingerBands
from app.core.technical_analysis.indicators.volume import OBV, VWAP
from app.core.technical_analysis.patterns.candlestick import CandlestickPatterns
from app.core.technical_analysis.patterns.chart_patterns import ChartPatterns
from app.core.technical_analysis.screener.filters import FilterCondition, FilterGroup
from app.utils.date_helpers import get_date_range
from app.utils.logging import logger


class StockScreener:
    """
    Stock screener for filtering stocks based on technical criteria.
    
    This class provides functionality for screening stocks based on
    technical indicators and patterns.
    """
    
    def __init__(self):
        """Initialize the stock screener."""
        # Initialize indicators
        self.indicators = {
            "sma": MovingAverage("sma"),
            "ema": MovingAverage("ema"),
            "wma": MovingAverage("wma"),
            "macd": MACD(),
            "rsi": RSI(),
            "stoch": Stochastic(),
            "bbands": BollingerBands(),
            "atr": ATR(),
            "obv": OBV(),
            "vwap": VWAP()
        }
        
        # Initialize pattern recognizers
        self.candlestick_patterns = CandlestickPatterns()
        self.chart_patterns = ChartPatterns()
    
    async def screen(
        self,
        symbols: List[str],
        filters: Union[FilterCondition, FilterGroup, List[Union[FilterCondition, FilterGroup]]],
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        interval: str = "1d",
        indicators: Optional[List[Dict]] = None
    ) -> Dict[str, pd.DataFrame]:
        """
        Screen stocks based on technical criteria.
        
        Args:
            symbols: List of stock symbols to screen
            filters: Filter conditions or groups to apply
            start_date: Start date for historical data
            end_date: End date for historical data
            interval: Data interval (e.g., '1d', '1wk', '1mo')
            indicators: List of indicators to calculate
            
        Returns:
            Dictionary mapping symbols to DataFrames with screening results
        """
        # Set default dates if not provided
        if end_date is None:
            end_date = date.today()
        if start_date is None:
            # Default to 1 year of data
            start_date = end_date - timedelta(days=365)
        
        # Get stock data
        stock_data = await data_manager.get_multiple_stock_data(
            symbols, start_date, end_date, interval
        )
        
        # Calculate indicators
        stock_data = await self._calculate_indicators(stock_data, indicators)
        
        # Apply filters
        results = {}
        for symbol, data in stock_data.items():
            if data.empty:
                logger.warning(f"No data for {symbol}")
                continue
            
            # Apply filters
            filtered_data = self._apply_filters(data, filters)
            
            # Store results
            results[symbol] = filtered_data
        
        return results
    
    async def _calculate_indicators(
        self,
        stock_data: Dict[str, pd.DataFrame],
        indicators: Optional[List[Dict]] = None
    ) -> Dict[str, pd.DataFrame]:
        """
        Calculate technical indicators for stock data.
        
        Args:
            stock_data: Dictionary mapping symbols to DataFrames
            indicators: List of indicators to calculate
            
        Returns:
            Dictionary mapping symbols to DataFrames with indicators
        """
        # Set default indicators if not provided
        if indicators is None:
            indicators = [
                {"type": "sma", "params": {"period": 20}},
                {"type": "sma", "params": {"period": 50}},
                {"type": "sma", "params": {"period": 200}},
                {"type": "ema", "params": {"period": 20}},
                {"type": "macd", "params": {}},
                {"type": "rsi", "params": {}},
                {"type": "bbands", "params": {}},
                {"type": "obv", "params": {}}
            ]
        
        # Calculate indicators for each symbol
        results = {}
        for symbol, data in stock_data.items():
            if data.empty:
                results[symbol] = data
                continue
            
            # Make a copy of the data
            df = data.copy()
            
            # Calculate each indicator
            for indicator_config in indicators:
                indicator_type = indicator_config["type"]
                params = indicator_config.get("params", {})
                
                if indicator_type in self.indicators:
                    try:
                        df = self.indicators[indicator_type].calculate(df, **params)
                    except Exception as e:
                        logger.error(f"Error calculating {indicator_type} for {symbol}: {e}")
            
            # Recognize candlestick patterns
            try:
                df = self.candlestick_patterns.recognize_patterns(df)
            except Exception as e:
                logger.error(f"Error recognizing candlestick patterns for {symbol}: {e}")
            
            # Recognize chart patterns
            try:
                df = self.chart_patterns.recognize_patterns(df)
            except Exception as e:
                logger.error(f"Error recognizing chart patterns for {symbol}: {e}")
            
            # Store results
            results[symbol] = df
        
        return results
    
    def _apply_filters(
        self,
        data: pd.DataFrame,
        filters: Union[FilterCondition, FilterGroup, List[Union[FilterCondition, FilterGroup]]]
    ) -> pd.DataFrame:
        """
        Apply filters to stock data.
        
        Args:
            data: DataFrame with stock data and indicators
            filters: Filter conditions or groups to apply
            
        Returns:
            DataFrame with filtered data
        """
        # Convert single filter to list
        if isinstance(filters, (FilterCondition, FilterGroup)):
            filters = [filters]
        
        # Apply each filter
        mask = pd.Series(True, index=data.index)
        for filter_item in filters:
            try:
                filter_mask = filter_item.apply(data)
                mask = mask & filter_mask
            except Exception as e:
                logger.error(f"Error applying filter {filter_item}: {e}")
        
        # Apply the mask
        return data[mask]


# Create a global screener instance
screener = StockScreener()
