"""
Response models for API endpoints.
"""
from datetime import date
from typing import Dict, List, Optional, Union

from pydantic import BaseModel, Field


class StockDataResponse(BaseModel):
    """Response model for stock data."""
    symbol: str = Field(..., description="Stock symbol")
    data: List[Dict] = Field(..., description="Historical stock data")


class MultipleStockDataResponse(BaseModel):
    """Response model for multiple stock data."""
    data: Dict[str, List[Dict]] = Field(..., description="Historical stock data for multiple symbols")


class CompanyInfoResponse(BaseModel):
    """Response model for company information."""
    symbol: str = Field(..., description="Stock symbol")
    info: Dict = Field(..., description="Company information")


class SearchSymbolsResponse(BaseModel):
    """Response model for symbol search."""
    results: List[Dict] = Field(..., description="Search results")


class IndicatorResponse(BaseModel):
    """Response model for technical indicator calculation."""
    symbol: str = Field(..., description="Stock symbol")
    indicator: str = Field(..., description="Indicator type")
    data: List[Dict] = Field(..., description="Indicator data")


class PatternResponse(BaseModel):
    """Response model for pattern recognition."""
    symbol: str = Field(..., description="Stock symbol")
    pattern_type: str = Field(..., description="Pattern type")
    patterns: List[str] = Field(..., description="Recognized patterns")
    data: List[Dict] = Field(..., description="Pattern data")


class ScreenerResponse(BaseModel):
    """Response model for stock screening."""
    symbols: List[str] = Field(..., description="List of stock symbols")
    results: Dict[str, List[Dict]] = Field(..., description="Screening results")


class OptimizationResponse(BaseModel):
    """Response model for portfolio optimization."""
    weights: Dict[str, float] = Field(..., description="Optimal portfolio weights")
    performance: Dict[str, float] = Field(..., description="Portfolio performance metrics")
    latest_prices: Dict[str, float] = Field(..., description="Latest prices for each asset")
    allocation: Optional[Dict[str, int]] = Field(None, description="Discrete allocation of shares")
    leftover: Optional[float] = Field(None, description="Leftover cash after discrete allocation")


class RiskMetricsResponse(BaseModel):
    """Response model for risk metrics calculation."""
    metrics: Dict[str, Dict[str, float]] = Field(..., description="Risk metrics")


class AllocationResponse(BaseModel):
    """Response model for asset allocation."""
    strategy: str = Field(..., description="Allocation strategy")
    risk_profile: str = Field(..., description="Risk profile")
    weights: Dict[str, float] = Field(..., description="Asset allocation weights")
    allocation: Optional[Dict[str, int]] = Field(None, description="Discrete allocation of shares")
    leftover: Optional[float] = Field(None, description="Leftover cash after discrete allocation")


class RebalancingResponse(BaseModel):
    """Response model for portfolio rebalancing."""
    strategy: str = Field(..., description="Rebalancing strategy")
    strategy_params: Optional[Dict] = Field(None, description="Strategy-specific parameters")
    performance_metrics: Dict[str, float] = Field(..., description="Performance metrics")
    rebalancing_events: List[Dict] = Field(..., description="Rebalancing events")
    rebalancing_dates: List[str] = Field(..., description="Dates when rebalancing occurred")
    weights_over_time: Dict[str, Dict[str, float]] = Field(..., description="Portfolio weights over time")
    portfolio_value: Dict[str, Union[List[str], List[float]]] = Field(..., description="Portfolio value over time")
    drawdown: Dict[str, Union[List[str], List[float]]] = Field(..., description="Drawdown over time")
    cumulative_returns: Optional[Dict[str, Union[List[str], List[float]]]] = Field(None, description="Cumulative returns over time")
