# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
logs/
*.log

# Local data
data/historical/
data/cached/
data/user/
*.db
*.sqlite3

# Environment variables
.env

# Jupyter Notebooks
.ipynb_checkpoints

# OS specific
.DS_Store
Thumbs.db
