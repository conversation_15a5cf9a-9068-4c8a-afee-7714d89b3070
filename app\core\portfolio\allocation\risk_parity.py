"""
Risk parity portfolio allocation strategy.
"""
from typing import Dict, List, Optional, Union

import numpy as np
import pandas as pd
from scipy import optimize

from app.core.portfolio.allocation.base import AllocationStrategy
from app.utils.logging import logger


class RiskParityStrategy(AllocationStrategy):
    """
    Risk parity portfolio allocation strategy.
    
    This strategy allocates weights such that each asset contributes
    equally to the overall portfolio risk.
    """
    
    def __init__(self):
        """Initialize the risk parity allocation strategy."""
        super().__init__("Risk Parity")
    
    def allocate(
        self,
        returns: pd.DataFrame,
        prices: Optional[pd.DataFrame] = None,
        risk_target: Optional[float] = None,
        max_iterations: int = 1000,
        **kwargs
    ) -> Dict[str, float]:
        """
        Allocate portfolio weights using risk parity.
        
        Args:
            returns: DataFrame of asset returns
            prices: DataFrame of asset prices (optional)
            risk_target: Target portfolio volatility (optional)
            max_iterations: Maximum number of iterations for optimization
            **kwargs: Additional parameters
            
        Returns:
            Dictionary mapping asset names to weights
        """
        # Validate inputs
        if returns is None or returns.empty:
            logger.error("Returns must be provided for risk parity allocation")
            return {}
        
        # Get asset names
        assets = returns.columns.tolist()
        n_assets = len(assets)
        
        if n_assets == 0:
            logger.warning("No assets provided for allocation")
            return {}
        
        # Calculate covariance matrix
        cov_matrix = returns.cov()
        
        # Initial guess: equal weights
        initial_weights = np.ones(n_assets) / n_assets
        
        # Bounds: weights must be between 0 and 1
        bounds = [(0.0, 1.0) for _ in range(n_assets)]
        
        # Constraint: weights must sum to 1
        constraints = [{'type': 'eq', 'fun': lambda x: np.sum(x) - 1.0}]
        
        # Define risk parity objective function
        def risk_parity_objective(weights, cov_matrix):
            # Calculate portfolio risk
            portfolio_risk = np.sqrt(np.dot(weights.T, np.dot(cov_matrix, weights)))
            
            # Calculate risk contribution of each asset
            risk_contribution = np.dot(cov_matrix, weights) * weights / portfolio_risk
            
            # Calculate risk contribution differences
            risk_target_percent = portfolio_risk / n_assets
            risk_diffs = risk_contribution - risk_target_percent
            
            # Return sum of squared differences
            return np.sum(risk_diffs**2)
        
        # Optimize weights
        try:
            result = optimize.minimize(
                risk_parity_objective,
                initial_weights,
                args=(cov_matrix,),
                method='SLSQP',
                bounds=bounds,
                constraints=constraints,
                options={'maxiter': max_iterations}
            )
            
            if result.success:
                # Get optimized weights
                optimized_weights = result.x
                
                # Create weights dictionary
                weights = {asset: weight for asset, weight in zip(assets, optimized_weights)}
                
                # Scale to risk target if provided
                if risk_target is not None:
                    portfolio_vol = np.sqrt(np.dot(optimized_weights.T, np.dot(cov_matrix, optimized_weights)))
                    scaling_factor = risk_target / portfolio_vol
                    weights = {asset: weight * scaling_factor for asset, weight in weights.items()}
                
                logger.info(f"Allocated risk parity weights to {n_assets} assets")
                
                return weights
            else:
                logger.warning(f"Risk parity optimization failed: {result.message}")
                # Fall back to equal weights
                weight = 1.0 / n_assets
                return {asset: weight for asset in assets}
        
        except Exception as e:
            logger.error(f"Error in risk parity optimization: {str(e)}")
            # Fall back to equal weights
            weight = 1.0 / n_assets
            return {asset: weight for asset in assets}
    
    def rebalance(
        self,
        current_weights: Dict[str, float],
        returns: pd.DataFrame,
        prices: Optional[pd.DataFrame] = None,
        risk_target: Optional[float] = None,
        **kwargs
    ) -> Dict[str, float]:
        """
        Rebalance portfolio to risk parity weights.
        
        Args:
            current_weights: Dictionary of current asset weights
            returns: DataFrame of asset returns
            prices: DataFrame of asset prices (optional)
            risk_target: Target portfolio volatility (optional)
            **kwargs: Additional parameters
            
        Returns:
            Dictionary mapping asset names to target weights
        """
        # For risk parity, rebalancing is the same as initial allocation
        return self.allocate(
            returns=returns,
            prices=prices,
            risk_target=risk_target,
            **kwargs
        )
