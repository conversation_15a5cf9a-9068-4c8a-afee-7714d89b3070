{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Technical Analysis Demonstration\n", "\n", "This notebook demonstrates how to perform technical analysis on financial data using the Portfolio Optimizer's technical analysis modules."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.graph_objects as go\n", "from datetime import datetime, timedelta\n", "import asyncio\n", "import pandas_ta as ta\n", "import mplfinance as mpf\n", "\n", "# Add project root to path to import app modules\n", "sys.path.append(os.path.abspath('..'))\n", "\n", "# Set plotting style\n", "plt.style.use('fivethirtyeight')\n", "sns.set_theme(style=\"darkgrid\")\n", "\n", "# Configure pandas display options\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', 1000)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import Data Collection and Technical Analysis Modules"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from app.core.data_collection.data_manager import data_manager\n", "from app.core.technical_analysis.indicators import calculate_indicators\n", "from app.core.technical_analysis.patterns import identify_patterns\n", "from app.settings import get_settings\n", "\n", "# Get settings\n", "settings = get_settings()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define Stock and Time Period"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define stock to analyze\n", "ticker = 'AAPL'\n", "\n", "# Define time period\n", "end_date = datetime.now().date()\n", "start_date = end_date - <PERSON><PERSON><PERSON>(days=365)  # 1 year of data\n", "\n", "print(f\"Fetching data for {ticker} from {start_date} to {end_date}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fetch Historical Stock Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["async def fetch_stock_data():\n", "    \"\"\"Fetch historical stock data for the ticker.\"\"\"\n", "    data = await data_manager.get_stock_data(\n", "        symbol=ticker,\n", "        start_date=start_date,\n", "        end_date=end_date,\n", "        interval='1d'\n", "    )\n", "    return data\n", "\n", "# Run the async function\n", "stock_data = await fetch_stock_data()\n", "\n", "# Rename columns to standard OHLCV format\n", "ohlcv_data = stock_data.rename(columns={\n", "    'Open': 'open',\n", "    'High': 'high',\n", "    'Low': 'low',\n", "    'Close': 'close',\n", "    'Adj Close': 'adj_close',\n", "    'Volume': 'volume'\n", "})\n", "\n", "# Display the first few rows of the data\n", "ohlcv_data.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Candlestick Chart"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Prepare data for mplfinance\n", "mpf_data = ohlcv_data.copy()\n", "mpf_data.columns = [col.capitalize() for col in mpf_data.columns]\n", "mpf_data = mpf_data.rename(columns={'Adj_close': 'Adj Close'})\n", "\n", "# Plot candlestick chart for the last 90 days\n", "mpf.plot(\n", "    mpf_data.tail(90),\n", "    type='candle',\n", "    style='yahoo',\n", "    title=f'{ticker} Candlestick Chart (Last 90 Days)',\n", "    ylabel='Price',\n", "    volume=True,\n", "    figsize=(14, 8)\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Interactive Candlestick Chart with Plotly"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create interactive candlestick chart\n", "fig = go.Figure(data=[\n", "    go.Can<PERSON>(\n", "        x=ohlcv_data.index,\n", "        open=ohlcv_data['open'],\n", "        high=ohlcv_data['high'],\n", "        low=ohlcv_data['low'],\n", "        close=ohlcv_data['close'],\n", "        name='Candlesticks'\n", "    )\n", "])\n", "\n", "fig.update_layout(\n", "    title=f'{ticker} Interactive Candlestick Chart',\n", "    xaxis_title='Date',\n", "    yaxis_title='Price',\n", "    height=600,\n", "    width=1000\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Trend Indicators"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate trend indicators\n", "# Simple Moving Averages\n", "ohlcv_data['sma_20'] = ta.sma(ohlcv_data['close'], length=20)\n", "ohlcv_data['sma_50'] = ta.sma(ohlcv_data['close'], length=50)\n", "ohlcv_data['sma_200'] = ta.sma(ohlcv_data['close'], length=200)\n", "\n", "# Exponential Moving Averages\n", "ohlcv_data['ema_12'] = ta.ema(ohlcv_data['close'], length=12)\n", "ohlcv_data['ema_26'] = ta.ema(ohlcv_data['close'], length=26)\n", "\n", "# MACD\n", "macd = ta.macd(ohlcv_data['close'], fast=12, slow=26, signal=9)\n", "ohlcv_data = pd.concat([ohlcv_data, macd], axis=1)\n", "\n", "# Plot price with moving averages\n", "plt.figure(figsize=(14, 7))\n", "plt.plot(ohlcv_data.index, ohlcv_data['close'], label='Close Price')\n", "plt.plot(ohlcv_data.index, ohlcv_data['sma_20'], label='SMA 20')\n", "plt.plot(ohlcv_data.index, ohlcv_data['sma_50'], label='SMA 50')\n", "plt.plot(ohlcv_data.index, ohlcv_data['sma_200'], label='SMA 200')\n", "plt.title(f'{ticker} Price with Moving Averages')\n", "plt.xlabel('Date')\n", "plt.ylabel('Price')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot MACD\n", "plt.figure(figsize=(14, 7))\n", "\n", "# Plot MACD line and signal line\n", "plt.plot(ohlcv_data.index, ohlcv_data['MACD_12_26_9'], label='MACD')\n", "plt.plot(ohlcv_data.index, ohlcv_data['MACDs_12_26_9'], label='Signal')\n", "\n", "# Plot histogram\n", "plt.bar(ohlcv_data.index, ohlcv_data['MACDh_12_26_9'], label='Histogram', alpha=0.5)\n", "\n", "plt.title(f'{ticker} MACD (12, 26, 9)')\n", "plt.xlabel('Date')\n", "plt.ylabel('Value')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Momentum Indicators"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate momentum indicators\n", "# Relative Strength Index (RSI)\n", "ohlcv_data['rsi_14'] = ta.rsi(ohlcv_data['close'], length=14)\n", "\n", "# Stochastic Oscillator\n", "stoch = ta.stoch(ohlcv_data['high'], ohlcv_data['low'], ohlcv_data['close'], k=14, d=3, smooth_k=3)\n", "ohlcv_data = pd.concat([ohlcv_data, stoch], axis=1)\n", "\n", "# Money Flow Index (MFI)\n", "ohlcv_data['mfi_14'] = ta.mfi(ohlcv_data['high'], ohlcv_data['low'], ohlcv_data['close'], ohlcv_data['volume'], length=14)\n", "\n", "# Plot RSI\n", "plt.figure(figsize=(14, 7))\n", "plt.plot(ohlcv_data.index, ohlcv_data['rsi_14'], label='RSI 14')\n", "plt.axhline(y=70, color='r', linestyle='--', alpha=0.5)\n", "plt.axhline(y=30, color='g', linestyle='--', alpha=0.5)\n", "plt.title(f'{ticker} Relative Strength Index (14)')\n", "plt.xlabel('Date')\n", "plt.ylabel('RSI')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot Stochastic Oscillator\n", "plt.figure(figsize=(14, 7))\n", "plt.plot(ohlcv_data.index, ohlcv_data['STOCHk_14_3_3'], label='%K')\n", "plt.plot(ohlcv_data.index, ohlcv_data['STOCHd_14_3_3'], label='%D')\n", "plt.axhline(y=80, color='r', linestyle='--', alpha=0.5)\n", "plt.axhline(y=20, color='g', linestyle='--', alpha=0.5)\n", "plt.title(f'{ticker} Stochastic Oscillator (14, 3, 3)')\n", "plt.xlabel('Date')\n", "plt.ylabel('Value')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Volatility Indicators"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate volatility indicators\n", "# Bollinger Bands\n", "bbands = ta.bbands(ohlcv_data['close'], length=20, std=2)\n", "ohlcv_data = pd.concat([ohlcv_data, bbands], axis=1)\n", "\n", "# Average True Range (ATR)\n", "ohlcv_data['atr_14'] = ta.atr(ohlcv_data['high'], ohlcv_data['low'], ohlcv_data['close'], length=14)\n", "\n", "# Plot Bollinger Bands\n", "plt.figure(figsize=(14, 7))\n", "plt.plot(ohlcv_data.index, ohlcv_data['close'], label='Close Price')\n", "plt.plot(ohlcv_data.index, ohlcv_data['BBU_20_2.0'], label='Upper Band')\n", "plt.plot(ohlcv_data.index, ohlcv_data['BBM_20_2.0'], label='Middle Band')\n", "plt.plot(ohlcv_data.index, ohlcv_data['BBL_20_2.0'], label='Lower Band')\n", "plt.title(f'{ticker} Bollinger Bands (20, 2)')\n", "plt.xlabel('Date')\n", "plt.ylabel('Price')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot ATR\n", "plt.figure(figsize=(14, 7))\n", "plt.plot(ohlcv_data.index, ohlcv_data['atr_14'], label='ATR 14')\n", "plt.title(f'{ticker} Average True Range (14)')\n", "plt.xlabel('Date')\n", "plt.ylabel('ATR')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Volume Indicators"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate volume indicators\n", "# On-Balance Volume (OBV)\n", "ohlcv_data['obv'] = ta.obv(ohlcv_data['close'], ohlcv_data['volume'])\n", "\n", "# Volume Weighted Average Price (VWAP)\n", "ohlcv_data['vwap'] = ta.vwap(ohlcv_data['high'], ohlcv_data['low'], ohlcv_data['close'], ohlcv_data['volume'])\n", "\n", "# Plot OBV\n", "plt.figure(figsize=(14, 7))\n", "plt.plot(ohlcv_data.index, ohlcv_data['obv'], label='OBV')\n", "plt.title(f'{ticker} On-Balance Volume')\n", "plt.xlabel('Date')\n", "plt.ylabel('OBV')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot VWAP with price\n", "plt.figure(figsize=(14, 7))\n", "plt.plot(ohlcv_data.index, ohlcv_data['close'], label='Close Price')\n", "plt.plot(ohlcv_data.index, ohlcv_data['vwap'], label='VWAP')\n", "plt.title(f'{ticker} Price and VWAP')\n", "plt.xlabel('Date')\n", "plt.ylabel('Price')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Combining Multiple Indicators"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a comprehensive chart with multiple indicators\n", "# Prepare data for mplfinance\n", "mpf_data = ohlcv_data.copy()\n", "mpf_data.columns = [col.capitalize() for col in mpf_data.columns]\n", "mpf_data = mpf_data.rename(columns={'Adj_close': 'Adj Close'})\n", "\n", "# Define additional plots\n", "apds = [\n", "    mpf.make_addplot(mpf_data['Sma_20'], color='blue', width=0.7, panel=0),\n", "    mpf.make_addplot(mpf_data['Sma_50'], color='orange', width=0.7, panel=0),\n", "    mpf.make_addplot(mpf_data['Rsi_14'], color='purple', width=0.7, panel=1),\n", "    mpf.make_addplot(mpf_data['Macd_12_26_9'], color='green', width=0.7, panel=2),\n", "    mpf.make_addplot(mpf_data['Macds_12_26_9'], color='red', width=0.7, panel=2),\n", "    mpf.make_addplot(mpf_data['Volume'], type='bar', color='blue', alpha=0.5, panel=3)\n", "]\n", "\n", "# Plot the chart for the last 90 days\n", "mpf.plot(\n", "    mpf_data.tail(90),\n", "    type='candle',\n", "    style='yahoo',\n", "    title=f'{ticker} Technical Analysis',\n", "    ylabel='Price',\n", "    addplot=apds,\n", "    panel_ratios=(4, 1, 1, 1),\n", "    figsize=(14, 10)\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Technical Analysis Signals"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate technical signals\n", "signals = pd.DataFrame(index=ohlcv_data.index)\n", "signals['price'] = ohlcv_data['close']\n", "\n", "# Moving Average Crossover\n", "signals['sma_20'] = ohlcv_data['sma_20']\n", "signals['sma_50'] = ohlcv_data['sma_50']\n", "signals['ma_crossover'] = np.where(signals['sma_20'] > signals['sma_50'], 1, 0)\n", "signals['ma_crossover_signal'] = signals['ma_crossover'].diff()\n", "\n", "# RSI Signals\n", "signals['rsi_14'] = ohlcv_data['rsi_14']\n", "signals['rsi_overbought'] = np.where(signals['rsi_14'] > 70, 1, 0)\n", "signals['rsi_oversold'] = np.where(signals['rsi_14'] < 30, 1, 0)\n", "\n", "# MACD Signals\n", "signals['macd'] = ohlcv_data['MACD_12_26_9']\n", "signals['macd_signal'] = ohlcv_data['MACDs_12_26_9']\n", "signals['macd_crossover'] = np.where(signals['macd'] > signals['macd_signal'], 1, 0)\n", "signals['macd_crossover_signal'] = signals['macd_crossover'].diff()\n", "\n", "# Bollinger Bands Signals\n", "signals['bb_upper'] = ohlcv_data['BBU_20_2.0']\n", "signals['bb_middle'] = ohlcv_data['BBM_20_2.0']\n", "signals['bb_lower'] = ohlcv_data['BBL_20_2.0']\n", "signals['bb_overbought'] = np.where(signals['price'] > signals['bb_upper'], 1, 0)\n", "signals['bb_oversold'] = np.where(signals['price'] < signals['bb_lower'], 1, 0)\n", "\n", "# Display signals\n", "signals[['price', 'ma_crossover_signal', 'rsi_overbought', 'rsi_oversold', 'macd_crossover_signal', 'bb_overbought', 'bb_oversold']].tail(20)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualize Buy/Sell Signals"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot price with buy/sell signals\n", "plt.figure(figsize=(14, 7))\n", "plt.plot(signals.index, signals['price'], label='Close Price')\n", "\n", "# Plot MA crossover signals\n", "buy_signals = signals[signals['ma_crossover_signal'] == 1]\n", "sell_signals = signals[signals['ma_crossover_signal'] == -1]\n", "plt.scatter(buy_signals.index, buy_signals['price'], marker='^', color='g', s=100, label='MA Buy Signal')\n", "plt.scatter(sell_signals.index, sell_signals['price'], marker='v', color='r', s=100, label='MA Sell Signal')\n", "\n", "# Plot MACD crossover signals\n", "macd_buy = signals[signals['macd_crossover_signal'] == 1]\n", "macd_sell = signals[signals['macd_crossover_signal'] == -1]\n", "plt.scatter(macd_buy.index, macd_buy['price'], marker='o', color='lime', s=50, label='MACD Buy Signal')\n", "plt.scatter(macd_sell.index, macd_sell['price'], marker='o', color='orange', s=50, label='MACD Sell Signal')\n", "\n", "plt.title(f'{ticker} Price with Buy/Sell Signals')\n", "plt.xlabel('Date')\n", "plt.ylabel('Price')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "In this notebook, we've demonstrated various technical analysis techniques for stock market data. We've covered:\n", "\n", "1. Trend indicators (Moving Averages, MACD)\n", "2. Momentum indicators (RSI, Stochastic Oscillator, MFI)\n", "3. Volatility indicators (Bollinger Bands, ATR)\n", "4. Volume indicators (OBV, VWAP)\n", "5. Technical signals and buy/sell indicators\n", "\n", "These technical analysis tools can be used to identify potential trading opportunities and inform portfolio decisions. In the next notebook, we'll explore portfolio optimization strategies."]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 4}