"""
Advanced technical indicators API endpoints.
"""
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
import pandas as pd

from app.core.data_collection.stock import get_historical_data
from app.core.technical_analysis.ichimoku import calculate_ichimoku, get_ichimoku_signals, get_ichimoku_support_resistance
from app.core.technical_analysis.macd import calculate_macd, get_macd_signals, get_macd_strength
from app.core.technical_analysis.fibonacci import calculate_fibonacci_retracement, calculate_fibonacci_extension, identify_fibonacci_levels
from app.core.technical_analysis.elliott_wave import identify_elliott_wave_pattern, get_elliott_wave_projection
from app.db.database import get_db
from app.db.models import User
from app.middleware.auth import get_current_user
from app.utils.logging import logger

router = APIRouter()


@router.get("/ichimoku/{symbol}")
async def get_ichimoku_cloud(
    symbol: str,
    start_date: Optional[date] = Query(None, description="Start date for historical data"),
    end_date: Optional[date] = Query(None, description="End date for historical data"),
    interval: str = Query("1d", description="Data interval (1d, 1wk, 1mo)"),
    tenkan_period: int = Query(9, description="Period for Tenkan-sen (Conversion Line)"),
    kijun_period: int = Query(26, description="Period for Kijun-sen (Base Line)"),
    senkou_span_b_period: int = Query(52, description="Period for Senkou Span B (Leading Span B)"),
    displacement: int = Query(26, description="Displacement period for Senkou Span A and B"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get Ichimoku Cloud technical indicator for a symbol.
    """
    try:
        # Set default dates if not provided
        if end_date is None:
            end_date = date.today()

        if start_date is None:
            # For Ichimoku Cloud, we need more historical data due to displacement
            start_date = end_date - timedelta(days=365 * 2)

        # Get historical data
        df = await get_historical_data(symbol, start_date, end_date, interval)

        if df.empty:
            raise HTTPException(
                status_code=404,
                detail=f"Historical data not found for {symbol}"
            )

        # Calculate Ichimoku Cloud
        ichimoku_df = calculate_ichimoku(
            df,
            tenkan_period=tenkan_period,
            kijun_period=kijun_period,
            senkou_span_b_period=senkou_span_b_period,
            displacement=displacement
        )

        # Generate signals
        signals_df = get_ichimoku_signals(ichimoku_df)

        # Get support and resistance levels
        support_resistance = get_ichimoku_support_resistance(ichimoku_df)

        # Prepare response
        # Convert DataFrame to dictionary
        ichimoku_data = signals_df.reset_index().to_dict(orient="records")

        # Get the latest signal
        latest_signal = signals_df.iloc[-1]['ichimoku_signal'] if not signals_df.empty else 0
        latest_trend = signals_df.iloc[-1]['ichimoku_trend'] if not signals_df.empty else 'neutral'

        return {
            "symbol": symbol,
            "start_date": start_date,
            "end_date": end_date,
            "interval": interval,
            "parameters": {
                "tenkan_period": tenkan_period,
                "kijun_period": kijun_period,
                "senkou_span_b_period": senkou_span_b_period,
                "displacement": displacement
            },
            "latest_signal": latest_signal,
            "latest_trend": latest_trend,
            "support_resistance": support_resistance,
            "data": ichimoku_data
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting Ichimoku Cloud for {symbol}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting Ichimoku Cloud: {str(e)}"
        )


@router.get("/macd/{symbol}")
async def get_macd_indicator(
    symbol: str,
    start_date: Optional[date] = Query(None, description="Start date for historical data"),
    end_date: Optional[date] = Query(None, description="End date for historical data"),
    interval: str = Query("1d", description="Data interval (1d, 1wk, 1mo)"),
    fast_period: int = Query(12, description="Period for fast EMA"),
    slow_period: int = Query(26, description="Period for slow EMA"),
    signal_period: int = Query(9, description="Period for signal line EMA"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get MACD (Moving Average Convergence Divergence) technical indicator for a symbol.
    """
    try:
        # Set default dates if not provided
        if end_date is None:
            end_date = date.today()

        if start_date is None:
            # For MACD, we need enough historical data for the slow period
            start_date = end_date - timedelta(days=365)

        # Get historical data
        df = await get_historical_data(symbol, start_date, end_date, interval)

        if df.empty:
            raise HTTPException(
                status_code=404,
                detail=f"Historical data not found for {symbol}"
            )

        # Calculate MACD
        macd_df = calculate_macd(
            df,
            fast_period=fast_period,
            slow_period=slow_period,
            signal_period=signal_period
        )

        # Generate signals
        signals_df = get_macd_signals(macd_df)

        # Get MACD strength
        macd_strength = get_macd_strength(signals_df)

        # Prepare response
        # Convert DataFrame to dictionary
        macd_data = signals_df.reset_index().to_dict(orient="records")

        # Get the latest signal
        latest_signal = signals_df.iloc[-1]['macd_signal'] if not signals_df.empty else 0
        latest_trend = signals_df.iloc[-1]['macd_trend'] if not signals_df.empty else 'neutral'

        return {
            "symbol": symbol,
            "start_date": start_date,
            "end_date": end_date,
            "interval": interval,
            "parameters": {
                "fast_period": fast_period,
                "slow_period": slow_period,
                "signal_period": signal_period
            },
            "latest_signal": latest_signal,
            "latest_trend": latest_trend,
            "macd_strength": macd_strength,
            "data": macd_data
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting MACD for {symbol}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting MACD: {str(e)}"
        )


@router.get("/fibonacci/{symbol}")
async def get_fibonacci_analysis(
    symbol: str,
    start_date: Optional[date] = Query(None, description="Start date for historical data"),
    end_date: Optional[date] = Query(None, description="End date for historical data"),
    interval: str = Query("1d", description="Data interval (1d, 1wk, 1mo)"),
    window: int = Query(5, description="Window size for identifying swing points"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get Fibonacci retracement and extension analysis for a symbol.
    """
    try:
        # Set default dates if not provided
        if end_date is None:
            end_date = date.today()

        if start_date is None:
            # For Fibonacci analysis, we need enough historical data to identify patterns
            start_date = end_date - timedelta(days=365)

        # Get historical data
        df = await get_historical_data(symbol, start_date, end_date, interval)

        if df.empty:
            raise HTTPException(
                status_code=404,
                detail=f"Historical data not found for {symbol}"
            )

        # Identify Fibonacci levels
        fibonacci_levels = identify_fibonacci_levels(df, window=window)

        return {
            "symbol": symbol,
            "start_date": start_date,
            "end_date": end_date,
            "interval": interval,
            "parameters": {
                "window": window
            },
            "fibonacci_analysis": fibonacci_levels
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting Fibonacci analysis for {symbol}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting Fibonacci analysis: {str(e)}"
        )


@router.get("/elliott-wave/{symbol}")
async def get_elliott_wave_analysis(
    symbol: str,
    start_date: Optional[date] = Query(None, description="Start date for historical data"),
    end_date: Optional[date] = Query(None, description="End date for historical data"),
    interval: str = Query("1d", description="Data interval (1d, 1wk, 1mo)"),
    window: int = Query(5, description="Window size for identifying swing points"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get Elliott Wave pattern analysis for a symbol.
    """
    try:
        # Set default dates if not provided
        if end_date is None:
            end_date = date.today()

        if start_date is None:
            # For Elliott Wave analysis, we need enough historical data to identify patterns
            start_date = end_date - timedelta(days=365 * 2)

        # Get historical data
        df = await get_historical_data(symbol, start_date, end_date, interval)

        if df.empty:
            raise HTTPException(
                status_code=404,
                detail=f"Historical data not found for {symbol}"
            )

        # Get Elliott Wave projection
        elliott_wave_projection = get_elliott_wave_projection(df, window=window)

        return {
            "symbol": symbol,
            "start_date": start_date,
            "end_date": end_date,
            "interval": interval,
            "parameters": {
                "window": window
            },
            "elliott_wave_analysis": elliott_wave_projection
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting Elliott Wave analysis for {symbol}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting Elliott Wave analysis: {str(e)}"
        )


@router.get("/combined-analysis/{symbol}")
async def get_combined_technical_analysis(
    symbol: str,
    start_date: Optional[date] = Query(None, description="Start date for historical data"),
    end_date: Optional[date] = Query(None, description="End date for historical data"),
    interval: str = Query("1d", description="Data interval (1d, 1wk, 1mo)"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get combined technical analysis for a symbol using multiple advanced indicators.
    """
    try:
        # Set default dates if not provided
        if end_date is None:
            end_date = date.today()

        if start_date is None:
            # Need enough historical data for all indicators
            start_date = end_date - timedelta(days=365 * 2)

        # Get historical data
        df = await get_historical_data(symbol, start_date, end_date, interval)

        if df.empty:
            raise HTTPException(
                status_code=404,
                detail=f"Historical data not found for {symbol}"
            )

        # Calculate Ichimoku Cloud
        ichimoku_df = calculate_ichimoku(df)
        ichimoku_signals = get_ichimoku_signals(ichimoku_df)
        ichimoku_support_resistance = get_ichimoku_support_resistance(ichimoku_df)

        # Calculate MACD
        macd_df = calculate_macd(df)
        macd_signals = get_macd_signals(macd_df)
        macd_strength = get_macd_strength(macd_signals)

        # Identify Fibonacci levels
        try:
            fibonacci_levels = identify_fibonacci_levels(df)
        except Exception as e:
            logger.error(f"Error identifying Fibonacci levels: {str(e)}")
            fibonacci_levels = {
                'retracement_levels': {},
                'extension_levels': {},
                'support_levels': [],
                'resistance_levels': [],
                'analysis': f'Error: {str(e)}'
            }

        # Get Elliott Wave projection
        try:
            elliott_wave_projection = get_elliott_wave_projection(df)
        except Exception as e:
            logger.error(f"Error getting Elliott Wave projection: {str(e)}")
            elliott_wave_projection = {
                'pattern': 'Unknown',
                'confidence': 0,
                'current_wave': 0,
                'projection': 'Error',
                'target_price': None,
                'analysis': f'Error: {str(e)}'
            }

        # Get latest signals
        latest_ichimoku_signal = ichimoku_signals.iloc[-1]['ichimoku_signal'] if not ichimoku_signals.empty else 0
        latest_ichimoku_trend = ichimoku_signals.iloc[-1]['ichimoku_trend'] if not ichimoku_signals.empty else 'neutral'

        latest_macd_signal = macd_signals.iloc[-1]['macd_signal'] if not macd_signals.empty else 0
        latest_macd_trend = macd_signals.iloc[-1]['macd_trend'] if not macd_signals.empty else 'neutral'

        # Calculate combined signal
        # Weight the signals based on their reliability
        combined_signal = (
            0.4 * latest_ichimoku_signal +
            0.3 * latest_macd_signal
        )

        # Determine combined trend
        if combined_signal > 0.3:
            combined_trend = 'bullish'
        elif combined_signal < -0.3:
            combined_trend = 'bearish'
        else:
            combined_trend = 'neutral'

        # Generate analysis text
        analysis_text = "Combined Technical Analysis:\n\n"

        # Ichimoku analysis
        analysis_text += f"Ichimoku Cloud: {latest_ichimoku_trend.capitalize()} trend"
        if latest_ichimoku_signal > 0:
            analysis_text += f", buy signal (strength: {latest_ichimoku_signal:.1f})"
        elif latest_ichimoku_signal < 0:
            analysis_text += f", sell signal (strength: {abs(latest_ichimoku_signal):.1f})"
        else:
            analysis_text += ", no clear signal"

        # Add support/resistance
        if ichimoku_support_resistance.get('support_levels'):
            support = ichimoku_support_resistance['support_levels'][0][1]
            analysis_text += f"\nNearest support: {support:.2f}"

        if ichimoku_support_resistance.get('resistance_levels'):
            resistance = ichimoku_support_resistance['resistance_levels'][0][1]
            analysis_text += f"\nNearest resistance: {resistance:.2f}"

        # MACD analysis
        analysis_text += f"\n\nMACD: {latest_macd_trend.capitalize()} trend"
        if latest_macd_signal > 0:
            analysis_text += f", buy signal (strength: {latest_macd_signal:.1f})"
        elif latest_macd_signal < 0:
            analysis_text += f", sell signal (strength: {abs(latest_macd_signal):.1f})"
        else:
            analysis_text += ", no clear signal"

        # Add MACD strength details
        analysis_text += f"\nMACD trend strength: {macd_strength.get('trend_strength', 0):.4f}"
        if macd_strength.get('is_overbought'):
            analysis_text += ", potentially overbought"
        if macd_strength.get('is_oversold'):
            analysis_text += ", potentially oversold"

        # Fibonacci analysis
        analysis_text += f"\n\nFibonacci Analysis: {fibonacci_levels.get('analysis', 'No analysis available')}"

        # Elliott Wave analysis
        analysis_text += f"\n\nElliott Wave Analysis: {elliott_wave_projection.get('analysis', 'No analysis available')}"
        if elliott_wave_projection.get('target_price'):
            analysis_text += f"\nPrice target: {elliott_wave_projection['target_price']:.2f}"

        # Overall recommendation
        analysis_text += "\n\nOverall Recommendation: "
        if combined_trend == 'bullish':
            analysis_text += "BULLISH - Consider buying or holding positions."
        elif combined_trend == 'bearish':
            analysis_text += "BEARISH - Consider selling or reducing positions."
        else:
            analysis_text += "NEUTRAL - No clear directional bias, consider waiting for clearer signals."

        return {
            "symbol": symbol,
            "start_date": start_date,
            "end_date": end_date,
            "interval": interval,
            "current_price": df.iloc[-1]['Close'],
            "combined_signal": combined_signal,
            "combined_trend": combined_trend,
            "ichimoku": {
                "signal": latest_ichimoku_signal,
                "trend": latest_ichimoku_trend,
                "support_resistance": ichimoku_support_resistance
            },
            "macd": {
                "signal": latest_macd_signal,
                "trend": latest_macd_trend,
                "strength": macd_strength
            },
            "fibonacci": fibonacci_levels,
            "elliott_wave": elliott_wave_projection,
            "analysis": analysis_text
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting combined technical analysis for {symbol}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting combined technical analysis: {str(e)}"
        )
