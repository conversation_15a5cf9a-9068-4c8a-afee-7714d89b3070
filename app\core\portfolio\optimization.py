"""
Portfolio optimization algorithms.
"""
import asyncio
from datetime import date, timedelta
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
from pypfopt import EfficientFrontier, expected_returns, risk_models
from pypfopt.discrete_allocation import DiscreteAllocation
from pypfopt.efficient_frontier import EfficientCVaR

from app.core.data_collection.data_manager import data_manager
from app.utils.constants import DEFAULT_RISK_FREE_RATE
from app.utils.logging import logger
from app.utils.validation import validate_weights


class PortfolioOptimizer:
    """
    Portfolio optimizer using modern portfolio theory.

    This class provides methods for optimizing portfolios using
    various algorithms from modern portfolio theory.
    """

    def __init__(self, risk_free_rate: float = DEFAULT_RISK_FREE_RATE):
        """
        Initialize the portfolio optimizer.

        Args:
            risk_free_rate: Risk-free rate (default: 2%)
        """
        self.risk_free_rate = risk_free_rate

    async def get_portfolio_data(
        self,
        symbols: List[str],
        start_date: date,
        end_date: date,
        interval: str = "1d"
    ) -> pd.DataFrame:
        """
        Get historical price data for portfolio optimization.

        Args:
            symbols: List of stock symbols
            start_date: Start date for historical data
            end_date: End date for historical data
            interval: Data interval (e.g., '1d', '1wk', '1mo')

        Returns:
            DataFrame with historical price data
        """
        # For weekly data, extend the start date to ensure we have enough data points
        if interval == "1wk":
            # Extend by 3 years for weekly data to ensure enough data points
            extended_start_date = start_date - timedelta(days=3*365)
            logger.info(f"Using extended date range for weekly data: {extended_start_date} to {end_date}")
        else:
            extended_start_date = start_date

        # Get stock data
        stock_data = await data_manager.get_multiple_stock_data(
            symbols, extended_start_date, end_date, interval
        )

        # Extract close prices
        prices = {}
        for symbol, data in stock_data.items():
            if data.empty:
                logger.warning(f"No data for {symbol}")
                continue

            # Set date as index if it's not already
            if "date" in data.columns:
                data = data.set_index("date")

            prices[symbol] = data["close"]

        # Create a DataFrame with all prices
        price_df = pd.DataFrame(prices)

        # Check if we have enough data
        if price_df.empty:
            logger.error(f"No data available for any of the symbols: {symbols}")
            # Create a dummy DataFrame with random data as fallback
            # This is just to prevent errors, the user will be notified
            dummy_data = {}
            for symbol in symbols:
                dummy_data[symbol] = np.random.normal(100, 1, size=100)
            price_df = pd.DataFrame(dummy_data, index=pd.date_range(end=end_date, periods=100, freq='D'))
            logger.warning("Using dummy data for demonstration purposes only. Results are not valid for investment decisions.")
        elif len(price_df) < 20:  # Need at least 20 data points for meaningful optimization
            logger.warning(f"Limited data available ({len(price_df)} data points). Results may be unreliable.")

        return price_df

    def calculate_expected_returns(
        self,
        prices: pd.DataFrame,
        method: str = "mean_historical_return",
        **kwargs
    ) -> pd.Series:
        """
        Calculate expected returns for each asset.

        Args:
            prices: DataFrame with historical price data
            method: Method for calculating expected returns
            **kwargs: Additional parameters for the method

        Returns:
            Series with expected returns for each asset
        """
        if method == "mean_historical_return":
            return expected_returns.mean_historical_return(prices, **kwargs)
        elif method == "ema_historical_return":
            return expected_returns.ema_historical_return(prices, **kwargs)
        elif method == "capm_return":
            return expected_returns.capm_return(prices, self.risk_free_rate, **kwargs)
        else:
            raise ValueError(f"Unsupported method: {method}")

    def calculate_risk_model(
        self,
        prices: pd.DataFrame,
        method: str = "sample_cov",
        **kwargs
    ) -> pd.DataFrame:
        """
        Calculate risk model (covariance matrix) for the assets.

        Args:
            prices: DataFrame with historical price data
            method: Method for calculating the risk model
            **kwargs: Additional parameters for the method

        Returns:
            DataFrame with the risk model (covariance matrix)
        """
        if method == "sample_cov":
            return risk_models.sample_cov(prices, **kwargs)
        elif method == "semicovariance":
            return risk_models.semicovariance(prices, **kwargs)
        elif method == "exp_cov":
            return risk_models.exp_cov(prices, **kwargs)
        elif method == "ledoit_wolf":
            return risk_models.risk_matrix(prices, method="ledoit_wolf", **kwargs)
        elif method == "shrunk_covariance":
            return risk_models.risk_matrix(prices, method="shrunk", **kwargs)
        elif method == "oracle_approximating":
            return risk_models.risk_matrix(prices, method="oas", **kwargs)
        else:
            raise ValueError(f"Unsupported method: {method}")

    def optimize_portfolio(
        self,
        expected_returns: pd.Series,
        risk_model: pd.DataFrame,
        optimization_criterion: str = "max_sharpe",
        constraints: Optional[Dict] = None,
        **kwargs
    ) -> Tuple[Dict[str, float], Dict[str, float]]:
        """
        Optimize a portfolio based on expected returns and risk model.

        Args:
            expected_returns: Series with expected returns for each asset
            risk_model: DataFrame with the risk model (covariance matrix)
            optimization_criterion: Optimization criterion
            constraints: Additional constraints for the optimization
            **kwargs: Additional parameters for the optimization

        Returns:
            Tuple of (weights, performance metrics)
        """
        try:
            # Validate inputs
            if expected_returns.empty or risk_model.empty:
                raise ValueError("Expected returns or risk model is empty")

            if len(expected_returns) != risk_model.shape[0]:
                raise ValueError(f"Dimension mismatch: expected returns has {len(expected_returns)} elements, "
                                f"but risk model has {risk_model.shape[0]} rows")

            # Create the efficient frontier
            if optimization_criterion == "min_cvar":
                ef = EfficientCVaR(expected_returns, risk_model, **kwargs)
            else:
                ef = EfficientFrontier(expected_returns, risk_model, **kwargs)

            # Apply constraints if provided
            if constraints:
                if "weight_bounds" in constraints:
                    weight_bounds = constraints["weight_bounds"]
                    # Ensure weight bounds are valid
                    if isinstance(weight_bounds, tuple) and len(weight_bounds) == 2:
                        min_weight, max_weight = weight_bounds
                        # Use weight_bounds parameter directly instead of constraints
                        ef = EfficientFrontier(expected_returns, risk_model, weight_bounds=(min_weight, max_weight), **kwargs)
                    else:
                        logger.warning(f"Invalid weight_bounds format: {weight_bounds}. Using default bounds.")

                if "sector_constraints" in constraints:
                    for sector, (min_weight, max_weight) in constraints["sector_constraints"].items():
                        sector_assets = constraints.get("sector_mapper", {}).get(sector, [])
                        if sector_assets:
                            ef.add_sector_constraints(sector_assets, min_weight, max_weight)

            # Optimize the portfolio
            if optimization_criterion == "max_sharpe":
                weights = ef.max_sharpe(risk_free_rate=self.risk_free_rate)
            elif optimization_criterion == "min_volatility":
                weights = ef.min_volatility()
            elif optimization_criterion == "max_quadratic_utility":
                weights = ef.max_quadratic_utility()
            elif optimization_criterion == "efficient_risk":
                target_risk = kwargs.get("target_risk", 0.2)
                weights = ef.efficient_risk(target_risk)
            elif optimization_criterion == "efficient_return":
                target_return = kwargs.get("target_return", 0.2)
                weights = ef.efficient_return(target_return)
            elif optimization_criterion == "min_cvar":
                beta = kwargs.get("beta", 0.95)
                weights = ef.min_cvar(beta=beta)
            else:
                raise ValueError(f"Unsupported optimization criterion: {optimization_criterion}")

            # Clean weights
            weights = ef.clean_weights()

            # Calculate performance metrics
            performance = self.calculate_performance_metrics(weights, expected_returns, risk_model)

            return weights, performance

        except Exception as e:
            logger.error(f"Error in portfolio optimization: {str(e)}")
            # Fall back to equal weights if optimization fails
            assets = expected_returns.index.tolist()
            n_assets = len(assets)
            equal_weights = {asset: 1.0 / n_assets for asset in assets}

            # Calculate basic performance metrics for equal weights
            basic_performance = {
                "expected_return": sum(expected_returns[asset] * weight for asset, weight in equal_weights.items()),
                "volatility": np.sqrt(sum(equal_weights[asset_i] * equal_weights[asset_j] * risk_model.loc[asset_i, asset_j]
                                     for asset_i in assets for asset_j in assets)),
                "sharpe_ratio": 0.0  # Will be calculated below if possible
            }

            # Calculate Sharpe ratio if volatility is not zero
            if basic_performance["volatility"] > 0:
                basic_performance["sharpe_ratio"] = (basic_performance["expected_return"] - self.risk_free_rate) / basic_performance["volatility"]

            logger.warning(f"Falling back to equal weights due to optimization error. Performance may be suboptimal.")
            return equal_weights, basic_performance

    def calculate_performance_metrics(
        self,
        weights: Dict[str, float],
        expected_returns: pd.Series,
        risk_model: pd.DataFrame
    ) -> Dict[str, float]:
        """
        Calculate performance metrics for a portfolio.

        Args:
            weights: Dictionary mapping assets to weights
            expected_returns: Series with expected returns for each asset
            risk_model: DataFrame with the risk model (covariance matrix)

        Returns:
            Dictionary with performance metrics
        """
        # Convert weights to numpy array
        weight_arr = np.array([weights.get(asset, 0) for asset in expected_returns.index])

        # Calculate expected return
        portfolio_return = weight_arr.dot(expected_returns)

        # Calculate volatility
        portfolio_volatility = np.sqrt(weight_arr.dot(risk_model).dot(weight_arr))

        # Calculate Sharpe ratio (avoid division by zero)
        if portfolio_volatility > 0:
            sharpe_ratio = (portfolio_return - self.risk_free_rate) / portfolio_volatility
        else:
            sharpe_ratio = 0  # Default to zero if volatility is zero

        # Calculate other metrics
        metrics = {
            "expected_return": portfolio_return,
            "volatility": portfolio_volatility,
            "sharpe_ratio": sharpe_ratio
        }

        return metrics

    def get_discrete_allocation(
        self,
        weights: Dict[str, float],
        latest_prices: pd.Series,
        total_portfolio_value: float
    ) -> Tuple[Dict[str, int], float]:
        """
        Get discrete allocation of shares for a portfolio.

        Args:
            weights: Dictionary mapping assets to weights
            latest_prices: Series with latest prices for each asset
            total_portfolio_value: Total portfolio value

        Returns:
            Tuple of (allocation, leftover)
        """
        try:
            # Ensure all weights and prices are float type
            clean_weights = {k: float(v) for k, v in weights.items()}

            # Filter out any assets with zero or negative prices
            filtered_prices = latest_prices[latest_prices > 1e-8].copy()

            # If no valid prices, use fallback
            if len(filtered_prices) == 0:
                logger.warning("No valid prices for discrete allocation, using fallback")
                return {symbol: 0 for symbol in weights}, float(total_portfolio_value)

            # Only keep weights for assets with valid prices
            filtered_weights = {k: v for k, v in clean_weights.items() if k in filtered_prices.index}

            # If no valid weights, use fallback
            if not filtered_weights:
                logger.warning("No valid weights for discrete allocation, using fallback")
                return {symbol: 0 for symbol in weights}, float(total_portfolio_value)

            # Normalize weights to sum to 1
            weight_sum = sum(filtered_weights.values())
            if weight_sum <= 0:
                logger.warning("Weights sum to zero or negative, using fallback")
                return {symbol: 0 for symbol in weights}, float(total_portfolio_value)

            normalized_weights = {k: v/weight_sum for k, v in filtered_weights.items()}

            # Create the discrete allocation object
            da = DiscreteAllocation(
                normalized_weights,
                filtered_prices,
                total_portfolio_value=float(total_portfolio_value)
            )

            # Get the allocation
            allocation, leftover = da.greedy_portfolio()

            # Add zero allocations for any missing assets
            for symbol in weights:
                if symbol not in allocation:
                    allocation[symbol] = 0

            return allocation, leftover

        except Exception as e:
            logger.error(f"Error in discrete allocation: {e}")
            # Fallback allocation
            return {symbol: 0 for symbol in weights}, float(total_portfolio_value)

    async def optimize(
        self,
        symbols: List[str],
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        interval: str = "1d",
        optimization_criterion: str = "max_sharpe",
        expected_returns_method: str = "mean_historical_return",
        risk_model_method: str = "sample_cov",
        constraints: Optional[Dict] = None,
        total_portfolio_value: Optional[float] = None,
        **kwargs
    ) -> Dict:
        """
        Optimize a portfolio for a list of symbols.

        Args:
            symbols: List of stock symbols
            start_date: Start date for historical data
            end_date: End date for historical data
            interval: Data interval (e.g., '1d', '1wk', '1mo')
            optimization_criterion: Optimization criterion
            expected_returns_method: Method for calculating expected returns
            risk_model_method: Method for calculating the risk model
            constraints: Additional constraints for the optimization
            total_portfolio_value: Total portfolio value for discrete allocation
            **kwargs: Additional parameters for the optimization

        Returns:
            Dictionary with optimization results
        """
        # Set default dates if not provided
        if end_date is None:
            end_date = date.today()
        if start_date is None:
            # Default to 1 year of data
            start_date = end_date - timedelta(days=365)

        # Get portfolio data
        prices = await self.get_portfolio_data(symbols, start_date, end_date, interval)

        # Check if we have data for all symbols
        if len(prices.columns) < len(symbols):
            missing_symbols = set(symbols) - set(prices.columns)
            logger.warning(f"Missing data for symbols: {missing_symbols}")

        # Check if we have enough data points
        if prices.empty or len(prices) < 2:
            raise ValueError(f"Not enough data points for optimization. Try extending the date range or using a different interval.")

        # We've already handled extending the date range in get_portfolio_data

        # Calculate expected returns
        mu = self.calculate_expected_returns(prices, method=expected_returns_method, **kwargs)

        # Calculate risk model
        sigma = self.calculate_risk_model(prices, method=risk_model_method, **kwargs)

        # Verify dimensions and handle edge cases
        if len(mu) == 0 or sigma.shape[0] == 0:
            logger.error("Invalid dimensions: Expected returns or risk model is empty.")

            # Create fallback data with equal weights
            logger.warning("Using equal weights as fallback due to data issues.")
            equal_weights = {symbol: 1.0/len(symbols) for symbol in symbols}

            # Create basic performance metrics
            basic_performance = {
                "expected_annual_return": 0.10,  # 10% annual return assumption
                "annual_volatility": 0.15,      # 15% annual volatility assumption
                "sharpe_ratio": 0.5,           # Conservative Sharpe ratio
            }

            # Create dummy latest prices (for API compatibility)
            latest_prices = {symbol: 100.0 for symbol in symbols}

            # Create allocation
            if total_portfolio_value:
                # Create dummy allocation with equal distribution
                allocation = {}
                for symbol in symbols:
                    # Ensure price is not zero to avoid division by zero
                    price = float(latest_prices[symbol])
                    if price > 1e-8:
                        allocation[symbol] = int(float(total_portfolio_value) * float(equal_weights[symbol]) / price)
                    else:
                        allocation[symbol] = 0

                # Calculate leftover
                leftover = float(total_portfolio_value) - sum(allocation[symbol] * float(latest_prices[symbol]) for symbol in symbols)
            else:
                allocation = {}
                leftover = 0

            # Return fallback results
            return {
                "weights": equal_weights,
                "performance": basic_performance,
                "latest_prices": latest_prices,
                "allocation": allocation,
                "leftover": leftover,
                "note": "Using equal weights due to insufficient data. Consider using daily data or extending the date range."
            }

        # Optimize portfolio
        weights, performance = self.optimize_portfolio(
            mu, sigma, optimization_criterion, constraints, **kwargs
        )

        # Get latest prices for discrete allocation
        latest_prices = prices.iloc[-1]

        # Get discrete allocation if total portfolio value is provided
        allocation = None
        leftover = None
        if total_portfolio_value is not None:
            allocation, leftover = self.get_discrete_allocation(
                weights, latest_prices, total_portfolio_value
            )

        # Prepare results
        results = {
            "weights": weights,
            "performance": performance,
            "latest_prices": latest_prices.to_dict()
        }

        if allocation is not None:
            results["allocation"] = allocation
            results["leftover"] = leftover

        return results


# Create a global optimizer instance
optimizer = PortfolioOptimizer()
