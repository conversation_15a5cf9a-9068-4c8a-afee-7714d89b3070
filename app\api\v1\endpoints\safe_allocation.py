"""
Safe allocation functions to prevent division by zero errors.
"""
from typing import Dict, List, Optional

from app.utils.logging import logger


def create_safe_allocation_response(
    symbols: List[str],
    strategy: str,
    risk_profile: str,
    total_portfolio_value: Optional[float] = None
) -> Dict:
    """
    Create a completely safe allocation response that will never cause division by zero.
    
    Args:
        symbols: List of stock symbols
        strategy: Allocation strategy
        risk_profile: Risk profile
        total_portfolio_value: Total portfolio value
        
    Returns:
        Safe allocation response
    """
    try:
        # Ensure we have valid symbols
        if not symbols or len(symbols) == 0:
            logger.warning("No symbols provided for safe allocation")
            return {
                "strategy": strategy,
                "risk_profile": risk_profile,
                "weights": {},
                "allocation": {},
                "leftover": total_portfolio_value or 0.0
            }
        
        # Use a fixed portfolio value if none provided
        portfolio_value = 10000.0
        if total_portfolio_value is not None:
            try:
                portfolio_value = float(total_portfolio_value)
                if portfolio_value <= 0:
                    portfolio_value = 10000.0
            except Exception as e:
                logger.warning(f"Invalid portfolio value: {e}, using default 10000.0")
        
        # Calculate equal weights
        num_symbols = len(symbols)
        weight = 1.0 / num_symbols
        weights = {symbol: weight for symbol in symbols}
        
        # Use fixed price of $100 per share
        price_per_share = 100.0
        
        # Calculate shares per symbol (with explicit type conversion)
        shares_per_symbol = int(portfolio_value / (price_per_share * num_symbols))
        allocation = {symbol: shares_per_symbol for symbol in symbols}
        
        # Calculate leftover
        allocated_value = shares_per_symbol * price_per_share * num_symbols
        leftover = portfolio_value - allocated_value
        
        # Return the allocation response
        return {
            "strategy": strategy,
            "risk_profile": risk_profile,
            "weights": weights,
            "allocation": allocation,
            "leftover": leftover
        }
    except Exception as e:
        logger.error(f"Error in safe allocation: {e}")
        # Ultimate fallback with hardcoded values
        return {
            "strategy": strategy,
            "risk_profile": risk_profile,
            "weights": {symbol: 1.0/len(symbols) for symbol in symbols} if symbols else {},
            "allocation": {symbol: 10 for symbol in symbols} if symbols else {},
            "leftover": 0.0
        }
