"""
Market capitalization weighted portfolio allocation strategy.
"""
from typing import Dict, List, Optional, Union

import numpy as np
import pandas as pd

from app.core.portfolio.allocation.base import AllocationStrategy
from app.utils.logging import logger


class MarketCapWeightStrategy(AllocationStrategy):
    """
    Market capitalization weighted portfolio allocation strategy.
    
    This strategy allocates weights proportional to each asset's market capitalization.
    """
    
    def __init__(self):
        """Initialize the market cap weighted allocation strategy."""
        super().__init__("Market Cap Weight")
    
    def allocate(
        self,
        returns: Optional[pd.DataFrame] = None,
        prices: Optional[pd.DataFrame] = None,
        market_caps: Optional[Dict[str, float]] = None,
        **kwargs
    ) -> Dict[str, float]:
        """
        Allocate portfolio weights using market cap weighting.
        
        Args:
            returns: DataFrame of asset returns (optional)
            prices: DataFrame of asset prices (optional)
            market_caps: Dictionary of asset market capitalizations
            **kwargs: Additional parameters
            
        Returns:
            Dictionary mapping asset names to weights
        """
        # Validate inputs
        if market_caps is None or len(market_caps) == 0:
            logger.error("Market caps must be provided for market cap weighting")
            return {}
        
        # Get asset names from market caps
        assets = list(market_caps.keys())
        
        # Filter assets based on returns or prices if provided
        if returns is not None:
            assets = [asset for asset in assets if asset in returns.columns]
        elif prices is not None:
            assets = [asset for asset in assets if asset in prices.columns]
        
        # Calculate total market cap
        total_market_cap = sum(market_caps[asset] for asset in assets)
        
        if total_market_cap == 0:
            logger.warning("Total market cap is zero, using equal weights")
            weight = 1.0 / len(assets)
            return {asset: weight for asset in assets}
        
        # Calculate weights proportional to market cap
        weights = {asset: market_caps[asset] / total_market_cap for asset in assets}
        
        logger.info(f"Allocated market cap weights to {len(assets)} assets")
        
        return weights
    
    def rebalance(
        self,
        current_weights: Dict[str, float],
        returns: Optional[pd.DataFrame] = None,
        prices: Optional[pd.DataFrame] = None,
        market_caps: Optional[Dict[str, float]] = None,
        **kwargs
    ) -> Dict[str, float]:
        """
        Rebalance portfolio to market cap weights.
        
        Args:
            current_weights: Dictionary of current asset weights
            returns: DataFrame of asset returns (optional)
            prices: DataFrame of asset prices (optional)
            market_caps: Dictionary of asset market capitalizations
            **kwargs: Additional parameters
            
        Returns:
            Dictionary mapping asset names to target weights
        """
        # For market cap weighting, rebalancing is the same as initial allocation
        return self.allocate(
            returns=returns,
            prices=prices,
            market_caps=market_caps,
            **kwargs
        )
