"""
Equal weight portfolio allocation strategy.
"""
from typing import Dict, List, Optional, Union

import numpy as np
import pandas as pd

from app.core.portfolio.allocation.base import AllocationStrategy
from app.utils.logging import logger


class EqualWeightStrategy(AllocationStrategy):
    """
    Equal weight portfolio allocation strategy.
    
    This strategy allocates equal weight to each asset in the portfolio.
    """
    
    def __init__(self):
        """Initialize the equal weight allocation strategy."""
        super().__init__("Equal Weight")
    
    def allocate(
        self,
        returns: Optional[pd.DataFrame] = None,
        prices: Optional[pd.DataFrame] = None,
        **kwargs
    ) -> Dict[str, float]:
        """
        Allocate portfolio weights using equal weight strategy.
        
        Args:
            returns: DataFrame of asset returns (optional)
            prices: DataFrame of asset prices (optional)
            **kwargs: Additional parameters
            
        Returns:
            Dictionary mapping asset names to weights
        """
        # Validate inputs
        if returns is None and prices is None:
            logger.error("Either returns or prices must be provided")
            return {}
        
        # Get asset names from returns or prices
        if returns is not None:
            assets = returns.columns.tolist()
        else:
            assets = prices.columns.tolist()
        
        # Calculate equal weights
        n_assets = len(assets)
        if n_assets == 0:
            logger.warning("No assets provided for allocation")
            return {}
        
        weight = 1.0 / n_assets
        
        # Create weights dictionary
        weights = {asset: weight for asset in assets}
        
        logger.info(f"Allocated equal weights ({weight:.4f}) to {n_assets} assets")
        
        return weights
    
    def rebalance(
        self,
        current_weights: Dict[str, float],
        returns: Optional[pd.DataFrame] = None,
        prices: Optional[pd.DataFrame] = None,
        **kwargs
    ) -> Dict[str, float]:
        """
        Rebalance portfolio to equal weights.
        
        Args:
            current_weights: Dictionary of current asset weights
            returns: DataFrame of asset returns (optional)
            prices: DataFrame of asset prices (optional)
            **kwargs: Additional parameters
            
        Returns:
            Dictionary mapping asset names to target weights
        """
        # For equal weight strategy, rebalancing is the same as initial allocation
        return self.allocate(returns=returns, prices=prices, **kwargs)
