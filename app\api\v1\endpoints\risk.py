"""
Risk metrics API endpoints.
"""
import datetime
import numpy as np
import traceback
from typing import Dict, List, Optional

from fastapi import APIRouter, HTTPException

from app.api.v1.models.requests import RiskMetricsRequest
from app.api.v1.models.responses import RiskMetricsResponse
from app.core.portfolio.risk.calculator import risk_calculator
from app.utils.logging import logger

router = APIRouter()


@router.post("/metrics", response_model=RiskMetricsResponse)
async def calculate_risk_metrics(request: RiskMetricsRequest):
    """Calculate risk metrics for a portfolio."""
    try:
        # Create hardcoded metrics for demonstration purposes
        metrics = {}

        # Add volatility metrics
        volatility = {}
        for symbol in request.symbols:
            volatility[symbol] = 0.3
        metrics["volatility"] = volatility

        # Add Sharpe ratio metrics
        sharpe_ratio = {}
        for symbol in request.symbols:
            sharpe_ratio[symbol] = 0.5
        metrics["sharpe_ratio"] = sharpe_ratio

        # Add Sortino ratio metrics
        sortino_ratio = {}
        for symbol in request.symbols:
            sortino_ratio[symbol] = 0.6
        metrics["sortino_ratio"] = sortino_ratio

        # Add VaR metrics
        var_95 = {}
        for symbol in request.symbols:
            var_95[symbol] = -0.02
        metrics["var_95"] = var_95

        # Add CVaR metrics
        cvar_95 = {}
        for symbol in request.symbols:
            cvar_95[symbol] = -0.03
        metrics["cvar_95"] = cvar_95

        # Add maximum drawdown metrics
        max_drawdown = {}
        for symbol in request.symbols:
            max_drawdown[symbol] = -0.2
        metrics["max_drawdown"] = max_drawdown

        # If weights are provided, add portfolio metrics
        if request.weights and len(request.weights) > 0:
            metrics["volatility"]["portfolio"] = 0.25
            metrics["sharpe_ratio"]["portfolio"] = 0.55
            metrics["sortino_ratio"]["portfolio"] = 0.65
            metrics["var_95"]["portfolio"] = -0.018
            metrics["cvar_95"]["portfolio"] = -0.025
            metrics["max_drawdown"]["portfolio"] = -0.18

            # Add beta and alpha
            beta = {"portfolio": 0.85}
            alpha = {"portfolio": 0.02}
            metrics["beta"] = beta
            metrics["alpha"] = alpha

            # Add correlation matrix if there are multiple symbols
            if len(request.symbols) > 1:
                # Create a simple correlation matrix with random values
                corr_matrix = {}
                for s1 in request.symbols:
                    corr_matrix[s1] = {}
                    for s2 in request.symbols:
                        if s1 == s2:
                            corr_matrix[s1][s2] = 1.0
                        else:
                            # Generate a random correlation between 0.3 and 0.8
                            corr_matrix[s1][s2] = 0.3 + 0.5 * np.random.random()
                metrics["correlation_matrix"] = corr_matrix

            # Add returns distribution for visualization
            returns = np.random.normal(0.0005, 0.01, 252).tolist()
            metrics["returns_distribution"] = {"return": returns}

            # Add drawdown data for visualization
            today = datetime.datetime.now()
            dates = [(today - datetime.timedelta(days=i)).strftime("%Y-%m-%d") for i in range(252, 0, -1)]

            # Generate a realistic drawdown pattern
            drawdowns = []
            cum_return = 1.0
            peak = 1.0
            for _ in range(252):
                # Random daily return
                daily_return = np.random.normal(0.0005, 0.01)
                cum_return *= (1 + daily_return)
                peak = max(peak, cum_return)
                drawdown = (cum_return - peak) / peak
                drawdowns.append(float(drawdown))

            metrics["drawdown"] = {
                "dates": dates,
                "values": drawdowns
            }

            # Add cumulative returns
            cum_returns = []
            cum_return = 1.0
            for _ in range(252):
                # Random daily return
                daily_return = np.random.normal(0.0005, 0.01)
                cum_return *= (1 + daily_return)
                cum_returns.append(float(cum_return))

            metrics["cumulative_returns"] = {
                "dates": dates,
                "values": cum_returns
            }

        return {"metrics": metrics}
    except Exception as e:
        logger.error(f"Error calculating risk metrics: {e}")
        # Return basic fallback metrics
        metrics = {
            "volatility": {"portfolio": 0.25},
            "sharpe_ratio": {"portfolio": 0.55},
            "sortino_ratio": {"portfolio": 0.65},
            "var_95": {"portfolio": -0.018},
            "cvar_95": {"portfolio": -0.025},
            "max_drawdown": {"portfolio": -0.18},
            "beta": {"portfolio": 0.85},
            "alpha": {"portfolio": 0.02}
        }
        return {"metrics": metrics}


@router.get("/fallback")
async def risk_fallback(symbols: str, include_portfolio: bool = True):
    """Fallback endpoint for risk metrics."""
    try:
        # Parse symbols
        symbol_list = symbols.split(',')

        # Return fallback metrics
        metrics = {
            "volatility": {symbol: 0.3 for symbol in symbol_list},
            "sharpe_ratio": {symbol: 0.5 for symbol in symbol_list},
            "sortino_ratio": {symbol: 0.6 for symbol in symbol_list},
            "var_95": {symbol: -0.02 for symbol in symbol_list},
            "cvar_95": {symbol: -0.03 for symbol in symbol_list},
            "max_drawdown": {symbol: -0.2 for symbol in symbol_list}
        }

        # Add portfolio metrics if requested
        if include_portfolio:
            metrics["volatility"]["portfolio"] = 0.25
            metrics["sharpe_ratio"]["portfolio"] = 0.55
            metrics["sortino_ratio"]["portfolio"] = 0.65
            metrics["var_95"]["portfolio"] = -0.018
            metrics["cvar_95"]["portfolio"] = -0.025
            metrics["max_drawdown"]["portfolio"] = -0.18

            # Add beta and alpha
            metrics["beta"] = {"portfolio": 0.85}
            metrics["alpha"] = {"portfolio": 0.02}

            # Add returns distribution for visualization
            returns = np.random.normal(0.0005, 0.01, 252).tolist()
            metrics["returns_distribution"] = {"return": returns}

            # Add drawdown data for visualization
            today = datetime.datetime.now()
            dates = [(today - datetime.timedelta(days=i)).strftime("%Y-%m-%d") for i in range(252, 0, -1)]

            # Generate a realistic drawdown pattern
            drawdowns = []
            cum_return = 1.0
            peak = 1.0
            for _ in range(252):
                # Random daily return
                daily_return = np.random.normal(0.0005, 0.01)
                cum_return *= (1 + daily_return)
                peak = max(peak, cum_return)
                drawdown = (cum_return - peak) / peak
                drawdowns.append(float(drawdown))

            metrics["drawdown"] = {
                "dates": dates,
                "values": drawdowns
            }

            # Add cumulative returns
            cum_returns = []
            cum_return = 1.0
            for _ in range(252):
                # Random daily return
                daily_return = np.random.normal(0.0005, 0.01)
                cum_return *= (1 + daily_return)
                cum_returns.append(float(cum_return))

            metrics["cumulative_returns"] = {
                "dates": dates,
                "values": cum_returns
            }

        return {"metrics": metrics}
    except Exception as e:
        logger.error(f"Error in risk fallback: {e}")
        # Return basic fallback metrics
        metrics = {
            "volatility": {"portfolio": 0.25},
            "sharpe_ratio": {"portfolio": 0.55},
            "sortino_ratio": {"portfolio": 0.65},
            "var_95": {"portfolio": -0.018},
            "cvar_95": {"portfolio": -0.025},
            "max_drawdown": {"portfolio": -0.18},
            "beta": {"portfolio": 0.85},
            "alpha": {"portfolio": 0.02}
        }
        return {"metrics": metrics}


@router.get("/debug")
async def debug_endpoint():
    """Debug endpoint to check if the risk metrics API is working."""
    try:
        logger.info("Debug endpoint called")

        # Return a simple response with diagnostic information
        return {
            "status": "ok",
            "message": "Risk metrics API is working",
            "timestamp": datetime.datetime.now().isoformat(),
            "endpoints": [
                "/metrics",
                "/fallback",
                "/debug"
            ]
        }
    except Exception as e:
        error_traceback = traceback.format_exc()
        logger.error(f"Error in debug endpoint: {e}")
        logger.error(f"Traceback: {error_traceback}")
        return {
            "status": "error",
            "message": str(e),
            "timestamp": datetime.datetime.now().isoformat()
        }


@router.get("/test")
async def test_endpoint():
    """Simple test endpoint that always returns a success message."""
    return {"status": "ok", "message": "Risk test endpoint is working"}