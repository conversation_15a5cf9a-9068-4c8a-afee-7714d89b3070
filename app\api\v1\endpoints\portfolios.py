"""
Portfolio API endpoints.
"""
from datetime import date
from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.api.v1.models.portfolio import (
    PortfolioCreate,
    PortfolioResponse,
    PortfolioUpdate,
    PortfolioAssetResponse,
    TransactionCreate,
    TransactionResponse
)
from app.db.database import get_db
from app.db.models import User
from app.middleware.auth import get_current_user
from app.services.portfolio import portfolio_service

router = APIRouter()


@router.post("", response_model=PortfolioResponse, status_code=status.HTTP_201_CREATED)
async def create_portfolio(
    portfolio_in: PortfolioCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create a new portfolio.
    """
    # Create portfolio
    portfolio = await portfolio_service.create_portfolio(
        db=db,
        user=current_user,
        name=portfolio_in.name,
        description=portfolio_in.description,
        risk_profile=portfolio_in.risk_profile
    )
    
    # Return portfolio data
    return {
        "id": portfolio.id,
        "name": portfolio.name,
        "description": portfolio.description,
        "risk_profile": portfolio.risk_profile,
        "total_value": portfolio.total_value,
        "created_at": portfolio.created_at,
        "updated_at": portfolio.updated_at
    }


@router.get("", response_model=List[PortfolioResponse])
async def get_portfolios(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all portfolios for the current user.
    """
    # Get portfolios
    portfolios = await portfolio_service.get_portfolios(
        db=db,
        user=current_user
    )
    
    # Return portfolios data
    return [
        {
            "id": portfolio.id,
            "name": portfolio.name,
            "description": portfolio.description,
            "risk_profile": portfolio.risk_profile,
            "total_value": portfolio.total_value,
            "created_at": portfolio.created_at,
            "updated_at": portfolio.updated_at
        }
        for portfolio in portfolios
    ]


@router.get("/{portfolio_id}", response_model=PortfolioResponse)
async def get_portfolio(
    portfolio_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get a portfolio by ID.
    """
    # Get portfolio
    portfolio = await portfolio_service.get_portfolio(
        db=db,
        user=current_user,
        portfolio_id=portfolio_id
    )
    
    # Return portfolio data
    return {
        "id": portfolio.id,
        "name": portfolio.name,
        "description": portfolio.description,
        "risk_profile": portfolio.risk_profile,
        "total_value": portfolio.total_value,
        "created_at": portfolio.created_at,
        "updated_at": portfolio.updated_at
    }


@router.put("/{portfolio_id}", response_model=PortfolioResponse)
async def update_portfolio(
    portfolio_id: int,
    portfolio_in: PortfolioUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update a portfolio.
    """
    # Update portfolio
    portfolio = await portfolio_service.update_portfolio(
        db=db,
        user=current_user,
        portfolio_id=portfolio_id,
        name=portfolio_in.name,
        description=portfolio_in.description,
        risk_profile=portfolio_in.risk_profile
    )
    
    # Return portfolio data
    return {
        "id": portfolio.id,
        "name": portfolio.name,
        "description": portfolio.description,
        "risk_profile": portfolio.risk_profile,
        "total_value": portfolio.total_value,
        "created_at": portfolio.created_at,
        "updated_at": portfolio.updated_at
    }


@router.delete("/{portfolio_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_portfolio(
    portfolio_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete a portfolio.
    """
    # Delete portfolio
    await portfolio_service.delete_portfolio(
        db=db,
        user=current_user,
        portfolio_id=portfolio_id
    )


@router.get("/{portfolio_id}/assets", response_model=List[PortfolioAssetResponse])
async def get_portfolio_assets(
    portfolio_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get assets in a portfolio.
    """
    # Get portfolio assets
    assets = await portfolio_service.get_portfolio_assets(
        db=db,
        user=current_user,
        portfolio_id=portfolio_id
    )
    
    # Return assets data
    return assets


@router.post("/{portfolio_id}/assets/{symbol}", status_code=status.HTTP_204_NO_CONTENT)
async def add_asset_to_portfolio(
    portfolio_id: int,
    symbol: str,
    weight: float = Query(..., gt=0, le=1),
    shares: Optional[int] = Query(None, gt=0),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Add an asset to a portfolio.
    """
    # Add asset to portfolio
    await portfolio_service.add_asset_to_portfolio(
        db=db,
        user=current_user,
        portfolio_id=portfolio_id,
        symbol=symbol,
        weight=weight,
        shares=shares
    )


@router.delete("/{portfolio_id}/assets/{symbol}", status_code=status.HTTP_204_NO_CONTENT)
async def remove_asset_from_portfolio(
    portfolio_id: int,
    symbol: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Remove an asset from a portfolio.
    """
    # Remove asset from portfolio
    await portfolio_service.remove_asset_from_portfolio(
        db=db,
        user=current_user,
        portfolio_id=portfolio_id,
        symbol=symbol
    )


@router.post("/{portfolio_id}/optimize", response_model=Dict[str, float])
async def optimize_portfolio(
    portfolio_id: int,
    strategy: str = Query("mean_variance", description="Optimization strategy"),
    start_date: Optional[date] = Query(None, description="Start date for historical data"),
    end_date: Optional[date] = Query(None, description="End date for historical data"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Optimize a portfolio.
    """
    # Optimize portfolio
    weights = await portfolio_service.optimize_portfolio(
        db=db,
        user=current_user,
        portfolio_id=portfolio_id,
        strategy=strategy,
        start_date=start_date,
        end_date=end_date
    )
    
    # Return weights
    return weights


@router.post("/{portfolio_id}/backtest", response_model=Dict)
async def backtest_portfolio(
    portfolio_id: int,
    start_date: Optional[date] = Query(None, description="Start date for historical data"),
    end_date: Optional[date] = Query(None, description="End date for historical data"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Backtest a portfolio.
    """
    # Backtest portfolio
    results = await portfolio_service.backtest_portfolio(
        db=db,
        user=current_user,
        portfolio_id=portfolio_id,
        start_date=start_date,
        end_date=end_date
    )
    
    # Return results
    return results


@router.post("/{portfolio_id}/transactions", response_model=TransactionResponse)
async def add_transaction(
    portfolio_id: int,
    transaction_in: TransactionCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Add a transaction to a portfolio.
    """
    # Add transaction
    transaction = await portfolio_service.add_transaction(
        db=db,
        user=current_user,
        portfolio_id=portfolio_id,
        symbol=transaction_in.symbol,
        transaction_type=transaction_in.transaction_type,
        shares=transaction_in.shares,
        price=transaction_in.price,
        transaction_date=transaction_in.transaction_date
    )
    
    # Return transaction data
    return transaction


@router.get("/{portfolio_id}/transactions", response_model=List[TransactionResponse])
async def get_transactions(
    portfolio_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get transactions for a portfolio.
    """
    # Get transactions
    transactions = await portfolio_service.get_transactions(
        db=db,
        user=current_user,
        portfolio_id=portfolio_id
    )
    
    # Return transactions data
    return transactions
