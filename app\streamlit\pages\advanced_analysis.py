"""
Advanced portfolio analysis page for Streamlit app.
"""
import numpy as np
import pandas as pd
import streamlit as st
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional, Tuple, Union

from app.core.data_collection.data_manager import data_manager
from app.core.portfolio.optimization.manager import portfolio_optimization_manager
from app.core.portfolio.risk.cvar import calculate_cvar, calculate_component_cvar, calculate_cvar_contribution
from app.core.portfolio.risk.monte_carlo import generate_simulation_report, simulate_portfolio_values
from app.core.portfolio.risk.stress_testing import historical_stress_test, scenario_stress_test
from app.streamlit.components.risk_visualizations import (
    plot_efficient_frontier, plot_monte_carlo_simulation, plot_risk_contribution,
    plot_correlation_matrix, plot_rolling_metrics, plot_drawdown_chart
)
from app.utils.constants import DEFAULT_RISK_FREE_RATE
from app.utils.logging import logger


async def show_advanced_analysis_page():
    """Show the advanced portfolio analysis page."""
    st.title("Advanced Portfolio Analysis")
    
    # Create tabs for different analysis types
    tabs = st.tabs([
        "Risk Metrics",
        "Monte Carlo Simulation",
        "Stress Testing",
        "Correlation Analysis",
        "Efficient Frontier"
    ])
    
    # Get user inputs
    with st.sidebar:
        st.header("Portfolio Settings")
        
        # Get symbols
        symbols_input = st.text_input(
            "Enter stock symbols (comma-separated)",
            value="AAPL,MSFT,GOOGL,AMZN"
        )
        symbols = [s.strip() for s in symbols_input.split(",")]
        
        # Get weights
        weights_input = st.text_input(
            "Enter weights (comma-separated)",
            value="0.25,0.25,0.25,0.25"
        )
        weights = [float(w.strip()) for w in weights_input.split(",")]
        
        # Normalize weights
        weights = [w / sum(weights) for w in weights]
        
        # Create weights dictionary
        weights_dict = {symbol: weight for symbol, weight in zip(symbols, weights)}
        
        # Get date range
        col1, col2 = st.columns(2)
        with col1:
            start_date = st.date_input(
                "Start Date",
                value=date.today() - timedelta(days=365*3)
            )
        with col2:
            end_date = st.date_input(
                "End Date",
                value=date.today()
            )
        
        # Get interval
        interval = st.selectbox(
            "Interval",
            options=["1d", "1wk", "1mo"],
            index=0
        )
        
        # Get risk-free rate
        risk_free_rate = st.number_input(
            "Risk-Free Rate (%)",
            value=2.0,
            min_value=0.0,
            max_value=10.0,
            step=0.1
        ) / 100
    
    # Get historical data
    with st.spinner("Loading data..."):
        try:
            data_dict = await data_manager.get_multiple_stock_data(
                symbols, start_date, end_date, interval
            )
            
            # Combine data
            combined_data = data_manager.combine_stock_data(data_dict)
            
            if combined_data.empty:
                st.error("No data found for the specified symbols and date range")
                return
            
            # Calculate returns
            returns = combined_data.pct_change().dropna()
            
            # Calculate portfolio returns
            portfolio_returns = returns.dot(pd.Series(weights_dict))
            
            # Calculate expected returns and covariance matrix
            expected_returns = returns.mean()
            cov_matrix = returns.cov()
            
        except Exception as e:
            st.error(f"Error loading data: {str(e)}")
            logger.error(f"Error loading data: {str(e)}")
            return
    
    # Risk Metrics tab
    with tabs[0]:
        st.header("Risk Metrics")
        
        # Create columns for metrics
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.subheader("Basic Metrics")
            
            # Calculate basic metrics
            volatility = portfolio_returns.std() * np.sqrt(252)
            sharpe_ratio = (portfolio_returns.mean() - risk_free_rate / 252) / portfolio_returns.std() * np.sqrt(252)
            sortino_ratio = (portfolio_returns.mean() - risk_free_rate / 252) / portfolio_returns[portfolio_returns < 0].std() * np.sqrt(252)
            max_drawdown = (1 + portfolio_returns).cumprod().div((1 + portfolio_returns).cumprod().cummax()) - 1
            
            # Display metrics
            st.metric("Volatility (Annualized)", f"{volatility:.2%}")
            st.metric("Sharpe Ratio", f"{sharpe_ratio:.2f}")
            st.metric("Sortino Ratio", f"{sortino_ratio:.2f}")
            st.metric("Maximum Drawdown", f"{max_drawdown.min():.2%}")
        
        with col2:
            st.subheader("Value at Risk (VaR)")
            
            # Get confidence level
            confidence_level = st.slider(
                "Confidence Level",
                min_value=0.01,
                max_value=0.1,
                value=0.05,
                step=0.01,
                key="var_confidence"
            )
            
            # Calculate VaR
            var_hist = np.percentile(portfolio_returns, confidence_level * 100) * -1
            var_param = portfolio_returns.std() * np.sqrt(252) * 1.96 * np.sqrt(1/252)
            
            # Display VaR
            st.metric("Historical VaR (1-day)", f"{var_hist:.2%}")
            st.metric("Parametric VaR (1-day)", f"{var_param:.2%}")
            st.metric("Historical VaR (1-month)", f"{var_hist * np.sqrt(21):.2%}")
        
        with col3:
            st.subheader("Conditional VaR (CVaR)")
            
            # Get confidence level
            confidence_level = st.slider(
                "Confidence Level",
                min_value=0.01,
                max_value=0.1,
                value=0.05,
                step=0.01,
                key="cvar_confidence"
            )
            
            # Calculate CVaR
            cvar = calculate_cvar(returns, weights_dict, confidence_level)
            component_cvar = calculate_component_cvar(returns, weights_dict, confidence_level)
            cvar_contribution = calculate_cvar_contribution(returns, weights_dict, confidence_level)
            
            # Display CVaR
            st.metric("CVaR (1-day)", f"{cvar:.2%}")
            st.metric("CVaR (1-month)", f"{cvar * np.sqrt(21):.2%}")
        
        # Display CVaR contribution chart
        st.subheader("CVaR Contribution by Asset")
        cvar_fig = plot_risk_contribution(cvar_contribution, risk_type="CVaR")
        st.plotly_chart(cvar_fig, use_container_width=True)
        
        # Display rolling metrics
        st.subheader("Rolling Risk Metrics")
        window = st.slider(
            "Rolling Window (days)",
            min_value=30,
            max_value=504,
            value=252,
            step=21
        )
        
        rolling_fig = plot_rolling_metrics(returns, weights_dict, window=window)
        st.plotly_chart(rolling_fig, use_container_width=True)
        
        # Display drawdown chart
        st.subheader("Drawdown Analysis")
        drawdown_fig = plot_drawdown_chart(portfolio_returns)
        st.plotly_chart(drawdown_fig, use_container_width=True)
    
    # Monte Carlo Simulation tab
    with tabs[1]:
        st.header("Monte Carlo Simulation")
        
        # Get simulation parameters
        col1, col2, col3 = st.columns(3)
        
        with col1:
            n_simulations = st.number_input(
                "Number of Simulations",
                min_value=100,
                max_value=10000,
                value=1000,
                step=100
            )
        
        with col2:
            n_periods = st.number_input(
                "Number of Periods",
                min_value=21,
                max_value=1008,
                value=252,
                step=21
            )
        
        with col3:
            initial_value = st.number_input(
                "Initial Portfolio Value",
                min_value=1000,
                max_value=1000000,
                value=10000,
                step=1000
            )
        
        # Run simulation
        if st.button("Run Monte Carlo Simulation"):
            with st.spinner("Running simulation..."):
                try:
                    # Generate simulation report
                    report = generate_simulation_report(
                        returns,
                        weights_dict,
                        initial_value,
                        n_simulations,
                        n_periods
                    )
                    
                    # Simulate portfolio values
                    simulated_values = simulate_portfolio_values(
                        returns,
                        weights_dict,
                        initial_value,
                        n_simulations,
                        n_periods
                    )
                    
                    # Display simulation chart
                    st.subheader("Simulation Results")
                    sim_fig = plot_monte_carlo_simulation(simulated_values, initial_value)
                    st.plotly_chart(sim_fig, use_container_width=True)
                    
                    # Display statistics
                    st.subheader("Simulation Statistics")
                    
                    # Create columns for statistics
                    col1, col2, col3 = st.columns(3)
                    
                    with col1:
                        st.metric("Mean Final Value", f"${report['statistics']['mean_final_value']:.2f}")
                        st.metric("Median Final Value", f"${report['statistics']['median_final_value']:.2f}")
                        st.metric("Minimum Final Value", f"${report['statistics']['min_final_value']:.2f}")
                        st.metric("Maximum Final Value", f"${report['statistics']['max_final_value']:.2f}")
                    
                    with col2:
                        st.metric("Mean Return", f"{report['statistics']['mean_return']:.2%}")
                        st.metric("Median Return", f"{report['statistics']['median_return']:.2%}")
                        st.metric("Minimum Return", f"{report['statistics']['min_return']:.2%}")
                        st.metric("Maximum Return", f"{report['statistics']['max_return']:.2%}")
                    
                    with col3:
                        st.metric("Probability of Positive Return", f"{report['statistics']['probability_positive_return']:.2%}")
                        st.metric("Probability of Negative Return", f"{report['statistics']['probability_negative_return']:.2%}")
                        st.metric("VaR (95%)", f"{report['statistics'].get('var_5', 0):.2%}")
                        st.metric("CVaR (95%)", f"{report['statistics'].get('cvar_5', 0):.2%}")
                    
                    # Display percentiles
                    st.subheader("Percentiles")
                    
                    # Create DataFrame for percentiles
                    percentiles = pd.DataFrame({
                        'Percentile': [1, 5, 10, 25, 50, 75, 90, 95, 99],
                        'Value': [
                            report['percentiles'].get(f'percentile_{p}', 0)
                            for p in [1, 5, 10, 25, 50, 75, 90, 95, 99]
                        ]
                    })
                    
                    # Display percentiles as a table
                    st.dataframe(percentiles.style.format({'Value': '${:.2f}'}))
                    
                except Exception as e:
                    st.error(f"Error running Monte Carlo simulation: {str(e)}")
                    logger.error(f"Error running Monte Carlo simulation: {str(e)}")
    
    # Stress Testing tab
    with tabs[2]:
        st.header("Stress Testing")
        
        # Create tabs for different stress testing methods
        stress_tabs = st.tabs([
            "Historical Scenarios",
            "Custom Scenarios"
        ])
        
        # Historical Scenarios tab
        with stress_tabs[0]:
            st.subheader("Historical Scenarios")
            
            # Define historical scenarios
            historical_scenarios = {
                "2008 Financial Crisis": (date(2008, 9, 1), date(2009, 3, 31)),
                "2020 COVID-19 Crash": (date(2020, 2, 19), date(2020, 3, 23)),
                "2022 Tech Selloff": (date(2021, 11, 1), date(2022, 6, 30)),
                "2018 Q4 Selloff": (date(2018, 10, 1), date(2018, 12, 24)),
                "2011 Debt Ceiling Crisis": (date(2011, 7, 1), date(2011, 10, 3)),
                "2015-2016 Oil Crash": (date(2015, 6, 1), date(2016, 2, 11))
            }
            
            # Select scenarios
            selected_scenarios = st.multiselect(
                "Select Historical Scenarios",
                options=list(historical_scenarios.keys()),
                default=["2008 Financial Crisis", "2020 COVID-19 Crash"]
            )
            
            # Filter selected scenarios
            selected_historical_scenarios = {
                scenario: historical_scenarios[scenario]
                for scenario in selected_scenarios
            }
            
            # Run stress test
            if st.button("Run Historical Stress Test"):
                with st.spinner("Running stress test..."):
                    try:
                        # Run historical stress test
                        results = historical_stress_test(returns, weights_dict, selected_historical_scenarios)
                        
                        # Display results
                        st.subheader("Stress Test Results")
                        
                        # Create DataFrame for results
                        results_df = pd.DataFrame({
                            'Scenario': list(results.keys()),
                            'Portfolio Return': list(results.values())
                        })
                        
                        # Sort by return
                        results_df = results_df.sort_values('Portfolio Return')
                        
                        # Display results as a bar chart
                        st.bar_chart(
                            results_df.set_index('Scenario')['Portfolio Return'],
                            use_container_width=True
                        )
                        
                        # Display results as a table
                        st.dataframe(results_df.style.format({'Portfolio Return': '{:.2%}'}))
                        
                    except Exception as e:
                        st.error(f"Error running historical stress test: {str(e)}")
                        logger.error(f"Error running historical stress test: {str(e)}")
        
        # Custom Scenarios tab
        with stress_tabs[1]:
            st.subheader("Custom Scenarios")
            
            # Define custom scenarios
            custom_scenarios = {
                "Market Crash": {symbol: -0.3 for symbol in symbols},
                "Tech Selloff": {symbol: -0.2 if symbol in ['AAPL', 'MSFT', 'GOOGL', 'AMZN'] else -0.1 for symbol in symbols},
                "Interest Rate Hike": {symbol: -0.15 for symbol in symbols},
                "Economic Recovery": {symbol: 0.2 for symbol in symbols}
            }
            
            # Display custom scenarios
            st.write("Custom Scenarios:")
            
            # Create tabs for each scenario
            scenario_tabs = st.tabs(list(custom_scenarios.keys()))
            
            # Display and edit each scenario
            for i, (scenario_name, scenario_returns) in enumerate(custom_scenarios.items()):
                with scenario_tabs[i]:
                    # Create a dictionary to store edited returns
                    edited_returns = {}
                    
                    # Create columns for each symbol
                    cols = st.columns(len(symbols))
                    
                    for j, symbol in enumerate(symbols):
                        with cols[j]:
                            # Display number input for each symbol
                            edited_returns[symbol] = st.number_input(
                                symbol,
                                value=float(scenario_returns[symbol] * 100),
                                min_value=-100.0,
                                max_value=100.0,
                                step=1.0,
                                key=f"{scenario_name}_{symbol}"
                            ) / 100
                    
                    # Update scenario returns
                    custom_scenarios[scenario_name] = edited_returns
            
            # Run stress test
            if st.button("Run Custom Stress Test"):
                with st.spinner("Running stress test..."):
                    try:
                        # Run scenario stress test
                        results = scenario_stress_test(returns, weights_dict, custom_scenarios)
                        
                        # Display results
                        st.subheader("Stress Test Results")
                        
                        # Create DataFrame for portfolio returns
                        portfolio_returns_df = pd.DataFrame({
                            'Scenario': list(results.keys()),
                            'Portfolio Return': [result['portfolio_return'] for result in results.values()]
                        })
                        
                        # Sort by return
                        portfolio_returns_df = portfolio_returns_df.sort_values('Portfolio Return')
                        
                        # Display results as a bar chart
                        st.bar_chart(
                            portfolio_returns_df.set_index('Scenario')['Portfolio Return'],
                            use_container_width=True
                        )
                        
                        # Display results as a table
                        st.dataframe(portfolio_returns_df.style.format({'Portfolio Return': '{:.2%}'}))
                        
                        # Display asset contributions
                        st.subheader("Asset Contributions")
                        
                        # Select scenario
                        selected_scenario = st.selectbox(
                            "Select Scenario",
                            options=list(results.keys())
                        )
                        
                        # Get asset contributions
                        asset_contributions = results[selected_scenario]['asset_contributions']
                        
                        # Create DataFrame for asset contributions
                        asset_contributions_df = pd.DataFrame({
                            'Asset': list(asset_contributions.keys()),
                            'Contribution': list(asset_contributions.values())
                        })
                        
                        # Sort by contribution
                        asset_contributions_df = asset_contributions_df.sort_values('Contribution')
                        
                        # Display asset contributions as a bar chart
                        st.bar_chart(
                            asset_contributions_df.set_index('Asset')['Contribution'],
                            use_container_width=True
                        )
                        
                    except Exception as e:
                        st.error(f"Error running custom stress test: {str(e)}")
                        logger.error(f"Error running custom stress test: {str(e)}")
    
    # Correlation Analysis tab
    with tabs[3]:
        st.header("Correlation Analysis")
        
        # Display correlation matrix
        st.subheader("Correlation Matrix")
        corr_fig = plot_correlation_matrix(returns)
        st.plotly_chart(corr_fig, use_container_width=True)
        
        # Display rolling correlation
        st.subheader("Rolling Correlation")
        
        # Select assets
        col1, col2 = st.columns(2)
        
        with col1:
            asset1 = st.selectbox(
                "Asset 1",
                options=symbols,
                index=0
            )
        
        with col2:
            asset2 = st.selectbox(
                "Asset 2",
                options=symbols,
                index=min(1, len(symbols) - 1)
            )
        
        # Get window size
        window = st.slider(
            "Rolling Window (days)",
            min_value=30,
            max_value=504,
            value=90,
            step=30,
            key="rolling_corr_window"
        )
        
        # Calculate rolling correlation
        rolling_corr = returns[[asset1, asset2]].rolling(window=window).corr().unstack()[asset1][asset2]
        
        # Display rolling correlation
        st.line_chart(rolling_corr)
    
    # Efficient Frontier tab
    with tabs[4]:
        st.header("Efficient Frontier")
        
        # Get optimization parameters
        col1, col2 = st.columns(2)
        
        with col1:
            n_portfolios = st.number_input(
                "Number of Random Portfolios",
                min_value=100,
                max_value=10000,
                value=1000,
                step=100
            )
        
        with col2:
            risk_free_rate_ef = st.number_input(
                "Risk-Free Rate (%)",
                value=risk_free_rate * 100,
                min_value=0.0,
                max_value=10.0,
                step=0.1,
                key="ef_risk_free_rate"
            ) / 100
        
        # Plot efficient frontier
        ef_fig = plot_efficient_frontier(
            expected_returns,
            cov_matrix,
            weights_dict,
            n_portfolios,
            risk_free_rate_ef
        )
        
        st.plotly_chart(ef_fig, use_container_width=True)
        
        # Optimize portfolio
        st.subheader("Portfolio Optimization")
        
        # Select optimization objective
        objective = st.selectbox(
            "Optimization Objective",
            options=[
                "max_sharpe",
                "min_volatility",
                "efficient_risk",
                "efficient_return"
            ],
            index=0
        )
        
        # Get additional parameters
        if objective == "efficient_risk":
            target_risk = st.slider(
                "Target Risk",
                min_value=0.01,
                max_value=0.5,
                value=0.2,
                step=0.01
            )
        elif objective == "efficient_return":
            target_return = st.slider(
                "Target Return",
                min_value=0.01,
                max_value=0.5,
                value=0.2,
                step=0.01
            )
        
        # Optimize portfolio
        if st.button("Optimize Portfolio"):
            with st.spinner("Optimizing portfolio..."):
                try:
                    # Prepare optimization parameters
                    kwargs = {}
                    if objective == "efficient_risk":
                        kwargs["target_risk"] = target_risk
                    elif objective == "efficient_return":
                        kwargs["target_return"] = target_return
                    
                    # Optimize portfolio
                    optimized_weights = await portfolio_optimization_manager.optimize_portfolio_with_returns(
                        expected_returns,
                        cov_matrix,
                        objective,
                        None,
                        **kwargs
                    )
                    
                    # Display optimized weights
                    st.subheader("Optimized Portfolio Weights")
                    
                    # Create DataFrame for weights
                    weights_df = pd.DataFrame({
                        'Asset': list(optimized_weights.keys()),
                        'Weight': list(optimized_weights.values())
                    })
                    
                    # Sort by weight
                    weights_df = weights_df.sort_values('Weight', ascending=False)
                    
                    # Display weights as a bar chart
                    st.bar_chart(
                        weights_df.set_index('Asset')['Weight'],
                        use_container_width=True
                    )
                    
                    # Display weights as a table
                    st.dataframe(weights_df.style.format({'Weight': '{:.2%}'}))
                    
                    # Calculate portfolio metrics
                    optimized_return = sum(expected_returns[asset] * weight for asset, weight in optimized_weights.items())
                    optimized_volatility = np.sqrt(
                        sum(optimized_weights[asset1] * optimized_weights[asset2] * cov_matrix.loc[asset1, asset2]
                            for asset1 in optimized_weights for asset2 in optimized_weights)
                    )
                    optimized_sharpe = (optimized_return - risk_free_rate_ef) / optimized_volatility
                    
                    # Display portfolio metrics
                    st.subheader("Optimized Portfolio Metrics")
                    
                    col1, col2, col3 = st.columns(3)
                    
                    with col1:
                        st.metric("Expected Return", f"{optimized_return:.2%}")
                    
                    with col2:
                        st.metric("Expected Volatility", f"{optimized_volatility:.2%}")
                    
                    with col3:
                        st.metric("Sharpe Ratio", f"{optimized_sharpe:.2f}")
                    
                except Exception as e:
                    st.error(f"Error optimizing portfolio: {str(e)}")
                    logger.error(f"Error optimizing portfolio: {str(e)}")
