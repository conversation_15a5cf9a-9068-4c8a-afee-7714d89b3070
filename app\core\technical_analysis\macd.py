"""
MACD (Moving Average Convergence Divergence) technical indicator implementation.
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union

from app.utils.logging import logger


def calculate_macd(
    df: pd.DataFrame,
    close_col: str = 'Close',
    fast_period: int = 12,
    slow_period: int = 26,
    signal_period: int = 9,
    macd_col: str = 'macd',
    signal_col: str = 'macd_signal',
    hist_col: str = 'macd_hist'
) -> pd.DataFrame:
    """
    Calculate MACD (Moving Average Convergence Divergence) technical indicator.

    Args:
        df: DataFrame with price data
        close_col: Name of the close price column
        fast_period: Period for fast EMA
        slow_period: Period for slow EMA
        signal_period: Period for signal line EMA
        macd_col: Name for the MACD line column
        signal_col: Name for the signal line column
        hist_col: Name for the histogram column

    Returns:
        DataFrame with MACD components
    """
    try:
        # Check if we have enough data
        min_required_data = max(fast_period, slow_period, signal_period)
        if len(df) < min_required_data:
            logger.warning(f"Not enough data for reliable MACD calculation. Need at least {min_required_data} data points, but only have {len(df)}.")
            # Return the original dataframe with NaN columns for MACD components
            result_df = df.copy()
            result_df[macd_col] = np.nan
            result_df[signal_col] = np.nan
            result_df[hist_col] = np.nan
            return result_df

        # Make a copy of the dataframe to avoid modifying the original
        macd_df = df.copy()

        # Handle multi-level columns if present (from yfinance)
        if isinstance(macd_df.columns, pd.MultiIndex):
            logger.debug("Detected MultiIndex columns, flattening...")

            # First, try to find the close column in the multi-index
            close_col_found = False
            for col in macd_df.columns:
                if isinstance(col, tuple) and len(col) >= 2:
                    # Check if this is a Close column for any symbol
                    if col[0] == 'Close':
                        logger.debug(f"Found Close column in multi-index: {col}")
                        # Create a new column with the standard name
                        macd_df['Close'] = macd_df[col]
                        close_col = 'Close'
                        close_col_found = True
                        break

            # If we didn't find a Close column, try case-insensitive search
            if not close_col_found:
                for col in macd_df.columns:
                    if isinstance(col, tuple) and len(col) >= 2:
                        if col[0].lower() == 'close':
                            logger.debug(f"Found Close column (case-insensitive) in multi-index: {col}")
                            macd_df['Close'] = macd_df[col]
                            close_col = 'Close'
                            close_col_found = True
                            break

            # If we still didn't find a Close column, try to extract all columns
            if not close_col_found:
                # Extract all columns from the multi-index
                flat_columns = {}
                for col in macd_df.columns:
                    if isinstance(col, tuple) and len(col) > 0:
                        # Use the first part of the tuple as the column name
                        if col[0] in ['Close', 'Open', 'High', 'Low', 'Volume']:
                            flat_columns[col] = col[0]

                if flat_columns:
                    macd_df.rename(columns=flat_columns, inplace=True)
                    logger.debug(f"Flattened columns: {flat_columns}")
                    # Convert to a regular Index
                    macd_df.columns = pd.Index([col if not isinstance(col, tuple) else col[0] for col in macd_df.columns])

        # Standardize column names if needed (case-insensitive)
        if close_col not in macd_df.columns:
            for col in macd_df.columns:
                if isinstance(col, str) and col.lower() == 'close':
                    macd_df.rename(columns={col: 'Close'}, inplace=True)
                    close_col = 'Close'
                    break

        # Check if required column exists
        if close_col not in macd_df.columns:
            logger.error(f"Required column '{close_col}' not found in dataframe")
            # Return the original dataframe with NaN columns for MACD components
            result_df = df.copy()
            result_df[macd_col] = np.nan
            result_df[signal_col] = np.nan
            result_df[hist_col] = np.nan
            return result_df

        # Ensure close column is numeric
        try:
            # Check if the column is already a Series (not a DataFrame or other object)
            if not isinstance(macd_df[close_col], pd.Series):
                logger.warning(f"Column {close_col} is not a Series, attempting to convert")
                if isinstance(macd_df[close_col], pd.DataFrame):
                    # If it's a DataFrame, take the first column
                    macd_df[close_col] = macd_df[close_col].iloc[:, 0]
                else:
                    # If it's something else, try to convert to a Series
                    macd_df[close_col] = pd.Series(macd_df[close_col])

            macd_df[close_col] = pd.to_numeric(macd_df[close_col], errors='coerce')
        except Exception as e:
            logger.warning(f"Could not convert column {close_col} to numeric: {str(e)}")
            # Return the original dataframe with NaN columns for MACD components
            result_df = df.copy()
            result_df[macd_col] = np.nan
            result_df[signal_col] = np.nan
            result_df[hist_col] = np.nan
            return result_df

        # Check for NaN values
        if macd_df[close_col].isna().all():
            logger.warning(f"Column '{close_col}' contains all NaN values")
            # Return the original dataframe with NaN columns for MACD components
            result_df = df.copy()
            result_df[macd_col] = np.nan
            result_df[signal_col] = np.nan
            result_df[hist_col] = np.nan
            return result_df

        # Create a new DataFrame for the results to avoid column type issues
        result_df = macd_df.copy()

        # Calculate fast and slow EMAs
        fast_ema = result_df[close_col].ewm(span=fast_period, adjust=False).mean()
        slow_ema = result_df[close_col].ewm(span=slow_period, adjust=False).mean()

        # Calculate MACD line
        result_df[macd_col] = fast_ema - slow_ema

        # Calculate signal line
        result_df[signal_col] = result_df[macd_col].ewm(span=signal_period, adjust=False).mean()

        # Calculate histogram
        result_df[hist_col] = result_df[macd_col] - result_df[signal_col]

        return result_df

    except Exception as e:
        logger.error(f"Error calculating MACD: {str(e)}")
        # Return the original dataframe with NaN columns for MACD components
        result_df = df.copy()
        result_df[macd_col] = np.nan
        result_df[signal_col] = np.nan
        result_df[hist_col] = np.nan
        return result_df


def get_macd_signals(
    df: pd.DataFrame,
    macd_col: str = 'macd',
    signal_col: str = 'macd_signal',
    hist_col: str = 'macd_hist',
    close_col: str = 'Close'
) -> pd.DataFrame:
    """
    Generate trading signals based on MACD.

    Args:
        df: DataFrame with MACD components
        macd_col: Name of the MACD line column
        signal_col: Name of the signal line column
        hist_col: Name of the histogram column
        close_col: Name of the close price column

    Returns:
        DataFrame with MACD signals
    """
    try:
        # Make a copy of the dataframe to avoid modifying the original
        signals_df = df.copy()

        # Initialize signal columns with default values
        signals_df['macd_signal'] = 0
        signals_df['macd_trend'] = 'neutral'

        # Check if required columns exist
        required_columns = [macd_col, signal_col, hist_col]
        missing_columns = [col for col in required_columns if col not in signals_df.columns]

        if missing_columns:
            logger.error(f"Required columns not found in dataframe: {missing_columns}")
            return signals_df  # Return with default signal values

        # Check for NaN values in required columns
        for col in required_columns:
            if signals_df[col].isna().all():
                logger.warning(f"Column '{col}' contains all NaN values")
                return signals_df  # Return with default signal values

        # Ensure all required columns are numeric
        for col in required_columns:
            if not pd.api.types.is_numeric_dtype(signals_df[col]):
                try:
                    signals_df[col] = pd.to_numeric(signals_df[col], errors='coerce')
                except Exception as e:
                    logger.warning(f"Could not convert column '{col}' to numeric: {str(e)}")
                    return signals_df  # Return with default signal values

        # MACD line crosses above signal line (bullish signal)
        try:
            macd_cross_above = (signals_df[macd_col].shift(1) <= signals_df[signal_col].shift(1)) & \
                               (signals_df[macd_col] > signals_df[signal_col])
        except Exception as e:
            logger.warning(f"Error calculating MACD cross above: {str(e)}")
            macd_cross_above = pd.Series(False, index=signals_df.index)

        # MACD line crosses below signal line (bearish signal)
        try:
            macd_cross_below = (signals_df[macd_col].shift(1) >= signals_df[signal_col].shift(1)) & \
                               (signals_df[macd_col] < signals_df[signal_col])
        except Exception as e:
            logger.warning(f"Error calculating MACD cross below: {str(e)}")
            macd_cross_below = pd.Series(False, index=signals_df.index)

        # MACD line above zero (bullish trend)
        try:
            macd_above_zero = signals_df[macd_col] > 0
        except Exception as e:
            logger.warning(f"Error calculating MACD above zero: {str(e)}")
            macd_above_zero = pd.Series(False, index=signals_df.index)

        # MACD line below zero (bearish trend)
        try:
            macd_below_zero = signals_df[macd_col] < 0
        except Exception as e:
            logger.warning(f"Error calculating MACD below zero: {str(e)}")
            macd_below_zero = pd.Series(False, index=signals_df.index)

        # Determine trend - use safe assignment with try/except
        try:
            signals_df.loc[macd_above_zero, 'macd_trend'] = 'bullish'
            signals_df.loc[macd_below_zero, 'macd_trend'] = 'bearish'
        except Exception as e:
            logger.warning(f"Error setting trend values: {str(e)}")
            signals_df['macd_trend'] = 'neutral'  # Default to neutral

        # Generate signals - ensure the column is float type to avoid warnings
        try:
            signals_df['macd_signal'] = signals_df['macd_signal'].astype(float)

            # Now set the signals
            signals_df.loc[macd_cross_above & macd_above_zero, 'macd_signal'] = 1.0  # Strong buy signal
            signals_df.loc[macd_cross_above & macd_below_zero, 'macd_signal'] = 0.5  # Weak buy signal
            signals_df.loc[macd_cross_below & macd_below_zero, 'macd_signal'] = -1.0  # Strong sell signal
            signals_df.loc[macd_cross_below & macd_above_zero, 'macd_signal'] = -0.5  # Weak sell signal
        except Exception as e:
            logger.warning(f"Error setting signal values: {str(e)}")
            signals_df['macd_signal'] = 0.0  # Default to neutral

        # Check if close column exists for divergence calculation
        if close_col in signals_df.columns:
            try:
                # Ensure close column is numeric
                if not pd.api.types.is_numeric_dtype(signals_df[close_col]):
                    signals_df[close_col] = pd.to_numeric(signals_df[close_col], errors='coerce')

                # Bullish divergence: Price makes lower lows but MACD makes higher lows
                # This is a complex pattern that requires more sophisticated analysis
                # For simplicity, we'll just check if histogram is increasing while price is decreasing
                price_decreasing = signals_df[close_col].diff() < 0
                hist_increasing = signals_df[hist_col].diff() > 0
                signals_df.loc[price_decreasing & hist_increasing & macd_below_zero, 'macd_signal'] = 0.7  # Bullish divergence

                # Bearish divergence: Price makes higher highs but MACD makes lower highs
                price_increasing = signals_df[close_col].diff() > 0
                hist_decreasing = signals_df[hist_col].diff() < 0
                signals_df.loc[price_increasing & hist_decreasing & macd_above_zero, 'macd_signal'] = -0.7  # Bearish divergence
            except Exception as e:
                logger.warning(f"Error calculating divergence: {str(e)}")
                # Continue without divergence signals

        return signals_df

    except Exception as e:
        logger.error(f"Error generating MACD signals: {str(e)}")
        # Return the original dataframe with default signal values
        result_df = df.copy()
        result_df['macd_signal'] = 0.0
        result_df['macd_trend'] = 'neutral'
        return result_df


def get_macd_strength(
    df: pd.DataFrame,
    macd_col: str = 'macd',
    signal_col: str = 'macd_signal',
    hist_col: str = 'macd_hist'
) -> Dict:
    """
    Calculate MACD strength indicators.

    Args:
        df: DataFrame with MACD components
        macd_col: Name of the MACD line column
        signal_col: Name of the signal line column
        hist_col: Name of the histogram column

    Returns:
        Dictionary with MACD strength indicators
    """
    # Default return value for error cases
    default_result = {
        'macd_value': 0,
        'signal_value': 0,
        'histogram_value': 0,
        'macd_momentum': 0,
        'histogram_momentum': 0,
        'trend_strength': 0,
        'is_overbought': False,
        'is_oversold': False,
        'trend': 'Unknown'
    }

    try:
        # Check if dataframe is empty
        if df.empty:
            logger.warning("Empty dataframe provided to get_macd_strength")
            return default_result

        # Check if required columns exist
        required_columns = [macd_col, signal_col, hist_col]
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            logger.error(f"Required columns not found in dataframe: {missing_columns}")
            return default_result

        # Check for NaN values in required columns
        for col in required_columns:
            if df[col].isna().all():
                logger.warning(f"Column '{col}' contains all NaN values")
                return default_result

        # Ensure all required columns are numeric
        for col in required_columns:
            if not pd.api.types.is_numeric_dtype(df[col]):
                try:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                except Exception as e:
                    logger.warning(f"Could not convert column '{col}' to numeric: {str(e)}")
                    return default_result

        # Get the latest data point with error handling
        try:
            if len(df) == 0:
                logger.warning("Empty dataframe after processing")
                return default_result

            latest = df.iloc[-1]

            # Check if values are NaN
            if pd.isna(latest[macd_col]) or pd.isna(latest[signal_col]) or pd.isna(latest[hist_col]):
                logger.warning("NaN values found in MACD components")
                return default_result
        except Exception as e:
            logger.error(f"Error getting latest data point: {str(e)}")
            return default_result

        # Calculate MACD momentum (rate of change) with error handling
        try:
            if len(df) > 3:
                macd_momentum = df[macd_col].diff(3).iloc[-1]
                if pd.isna(macd_momentum):
                    macd_momentum = 0
            else:
                logger.warning("Not enough data points for momentum calculation")
                macd_momentum = 0
        except Exception as e:
            logger.warning(f"Error calculating MACD momentum: {str(e)}")
            macd_momentum = 0

        # Calculate histogram momentum with error handling
        try:
            if len(df) > 3:
                hist_momentum = df[hist_col].diff(3).iloc[-1]
                if pd.isna(hist_momentum):
                    hist_momentum = 0
            else:
                logger.warning("Not enough data points for histogram momentum calculation")
                hist_momentum = 0
        except Exception as e:
            logger.warning(f"Error calculating histogram momentum: {str(e)}")
            hist_momentum = 0

        # Calculate MACD trend strength (distance between MACD and signal line)
        try:
            trend_strength = abs(float(latest[macd_col]) - float(latest[signal_col]))
        except Exception as e:
            logger.warning(f"Error calculating trend strength: {str(e)}")
            trend_strength = 0

        # Determine if MACD is overbought/oversold with error handling
        try:
            # This is subjective, but we can use some heuristics
            macd_values = df[macd_col].dropna()
            if len(macd_values) > 0:
                macd_mean = macd_values.mean()
                macd_std = macd_values.std()

                if pd.isna(macd_mean) or pd.isna(macd_std) or macd_std == 0:
                    is_overbought = False
                    is_oversold = False
                else:
                    is_overbought = latest[macd_col] > (macd_mean + 2 * macd_std)
                    is_oversold = latest[macd_col] < (macd_mean - 2 * macd_std)
            else:
                is_overbought = False
                is_oversold = False
        except Exception as e:
            logger.warning(f"Error determining overbought/oversold: {str(e)}")
            is_overbought = False
            is_oversold = False

        # Determine overall trend with error handling
        try:
            macd_value = float(latest[macd_col])
            signal_value = float(latest[signal_col])

            if macd_value > 0 and macd_value > signal_value:
                trend = "Strong Bullish"
            elif macd_value > 0:
                trend = "Bullish"
            elif macd_value < 0 and macd_value < signal_value:
                trend = "Strong Bearish"
            else:
                trend = "Bearish"
        except Exception as e:
            logger.warning(f"Error determining trend: {str(e)}")
            trend = "Unknown"

        return {
            'macd_value': float(latest[macd_col]),
            'signal_value': float(latest[signal_col]),
            'histogram_value': float(latest[hist_col]),
            'macd_momentum': float(macd_momentum),
            'histogram_momentum': float(hist_momentum),
            'trend_strength': float(trend_strength),
            'is_overbought': bool(is_overbought),
            'is_oversold': bool(is_oversold),
            'trend': trend
        }

    except Exception as e:
        logger.error(f"Error calculating MACD strength: {str(e)}")
        return default_result
