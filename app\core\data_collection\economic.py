"""
Economic data collector implementation.
"""
import asyncio
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional, Union

import pandas as pd
import aiohttp
from aiolimiter import AsyncLimiter

from app.core.data_collection.base import BaseDataCollector
from app.settings import get_settings
from app.utils.logging import logger


class EconomicDataCollector:
    """
    Economic data collector.
    
    This class provides methods for retrieving economic indicators and data.
    It uses Alpha Vantage and FRED (Federal Reserve Economic Data) as sources.
    """
    
    def __init__(self):
        """Initialize the economic data collector."""
        self.settings = get_settings()
        self.alpha_vantage_api_key = self.settings.data_sources.get('alpha_vantage', {}).get('api_key', '')
        self.base_url_av = "https://www.alphavantage.co/query"
        self.base_url_fred = "https://api.stlouisfed.org/fred/series"
        
        # Set up rate limiter (5 requests per minute for free tier)
        self.rate_limiter = AsyncLimiter(5, 60)
        
        # Initialize session
        self.session = None
    
    async def _get_session(self):
        """Get or create an aiohttp session."""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession()
        return self.session
    
    async def _make_request_av(self, function: str, params: Dict = None) -> Dict:
        """
        Make a request to the Alpha Vantage API.
        
        Args:
            function: API function
            params: Query parameters
            
        Returns:
            API response as dictionary
        """
        # Add API key and function to parameters
        if params is None:
            params = {}
        params['apikey'] = self.alpha_vantage_api_key
        params['function'] = function
        
        # Build URL
        url = self.base_url_av
        
        # Use rate limiter
        async with self.rate_limiter:
            try:
                session = await self._get_session()
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        logger.error(f"Error from Alpha Vantage API: {response.status} - {await response.text()}")
                        return {}
            except Exception as e:
                logger.error(f"Error making request to Alpha Vantage API: {str(e)}")
                return {}
    
    async def get_economic_indicator(
        self,
        indicator: str,
        interval: str = "monthly",
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> pd.DataFrame:
        """
        Get economic indicator data.
        
        Args:
            indicator: Economic indicator (e.g., 'GDP', 'INFLATION', 'UNEMPLOYMENT')
            interval: Data interval ('daily', 'weekly', 'monthly', 'quarterly', 'annual')
            start_date: Start date for historical data
            end_date: End date for historical data
            
        Returns:
            DataFrame with economic indicator data
        """
        try:
            # Map indicator to Alpha Vantage function
            indicator_map = {
                'GDP': 'REAL_GDP',
                'INFLATION': 'INFLATION',
                'CPI': 'CPI',
                'UNEMPLOYMENT': 'UNEMPLOYMENT',
                'RETAIL_SALES': 'RETAIL_SALES',
                'NONFARM_PAYROLL': 'NONFARM_PAYROLL',
                'INTEREST_RATE': 'FEDERAL_FUNDS_RATE',
                'TREASURY_YIELD': 'TREASURY_YIELD',
                'CONSUMER_SENTIMENT': 'CONSUMER_SENTIMENT'
            }
            
            # Check if indicator is supported
            if indicator not in indicator_map:
                logger.error(f"Unsupported economic indicator: {indicator}")
                return pd.DataFrame()
            
            # Get Alpha Vantage function
            function = indicator_map[indicator]
            
            # Set parameters
            params = {
                'interval': interval
            }
            
            # Add maturity parameter for Treasury Yield
            if function == 'TREASURY_YIELD':
                params['maturity'] = '10year'
            
            # Make request
            response = await self._make_request_av(function, params)
            
            # Check if response is valid
            if not response or 'data' not in response:
                logger.error(f"Invalid response from Alpha Vantage API for {indicator}")
                return pd.DataFrame()
            
            # Extract data
            data = response['data']
            
            # Convert to DataFrame
            df = pd.DataFrame(data)
            
            # Set date as index
            if 'date' in df.columns:
                df['date'] = pd.to_datetime(df['date'])
                df = df.set_index('date')
            
            # Filter by date range if provided
            if start_date is not None:
                df = df[df.index >= pd.Timestamp(start_date)]
            if end_date is not None:
                df = df[df.index <= pd.Timestamp(end_date)]
            
            # Sort by date
            df = df.sort_index()
            
            return df
        
        except Exception as e:
            logger.error(f"Error getting economic indicator {indicator}: {str(e)}")
            return pd.DataFrame()
    
    async def get_multiple_economic_indicators(
        self,
        indicators: List[str],
        interval: str = "monthly",
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> Dict[str, pd.DataFrame]:
        """
        Get multiple economic indicators.
        
        Args:
            indicators: List of economic indicators
            interval: Data interval ('daily', 'weekly', 'monthly', 'quarterly', 'annual')
            start_date: Start date for historical data
            end_date: End date for historical data
            
        Returns:
            Dictionary mapping indicators to DataFrames with economic indicator data
        """
        results = {}
        
        # Get data for each indicator
        for indicator in indicators:
            try:
                df = await self.get_economic_indicator(indicator, interval, start_date, end_date)
                if not df.empty:
                    results[indicator] = df
            except Exception as e:
                logger.error(f"Error getting data for {indicator}: {str(e)}")
        
        return results
    
    async def get_yield_curve(
        self,
        date_str: Optional[str] = None
    ) -> pd.DataFrame:
        """
        Get Treasury yield curve data.
        
        Args:
            date_str: Date string in format 'YYYY-MM-DD' (if None, uses latest available data)
            
        Returns:
            DataFrame with yield curve data
        """
        try:
            # Set parameters
            params = {}
            
            if date_str is not None:
                params['date'] = date_str
            
            # Make request
            response = await self._make_request_av('TREASURY_YIELD', params)
            
            # Check if response is valid
            if not response or 'data' not in response:
                logger.error("Invalid response from Alpha Vantage API for yield curve")
                return pd.DataFrame()
            
            # Extract data
            data = response['data']
            
            # Convert to DataFrame
            df = pd.DataFrame(data)
            
            # Set date as index
            if 'date' in df.columns:
                df['date'] = pd.to_datetime(df['date'])
                df = df.set_index('date')
            
            return df
        
        except Exception as e:
            logger.error(f"Error getting yield curve: {str(e)}")
            return pd.DataFrame()
    
    async def close(self):
        """Close the session."""
        if self.session and not self.session.closed:
            await self.session.close()


# Create a singleton instance
economic_data_collector = EconomicDataCollector()
