"""
Database models for the application.
"""
from datetime import datetime
from typing import List, Optional

from sqlalchemy import Column, DateTime, Float, Foreign<PERSON>ey, Integer, String, Table
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

# Create base model
Base = declarative_base()

# Association table for many-to-many relationship between portfolios and assets
portfolio_assets = Table(
    "portfolio_assets",
    Base.metadata,
    Column("portfolio_id", Integer, ForeignKey("portfolios.id"), primary_key=True),
    Column("asset_id", Integer, ForeignKey("assets.id"), primary_key=True),
    Column("weight", Float, nullable=False),
    Column("shares", Integer, nullable=True)
)


class User(Base):
    """User model."""
    
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True, nullable=False)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    is_active = Column(Integer, default=1)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    portfolios = relationship("Portfolio", back_populates="user")
    watchlists = relationship("Watchlist", back_populates="user")


class Asset(Base):
    """Asset model."""
    
    __tablename__ = "assets"
    
    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String, unique=True, index=True, nullable=False)
    name = Column(String, nullable=True)
    asset_type = Column(String, nullable=True)
    sector = Column(String, nullable=True)
    industry = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    portfolios = relationship("Portfolio", secondary=portfolio_assets, back_populates="assets")
    watchlists = relationship("Watchlist", secondary="watchlist_assets", back_populates="assets")
    price_history = relationship("PriceHistory", back_populates="asset")


class Portfolio(Base):
    """Portfolio model."""
    
    __tablename__ = "portfolios"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    name = Column(String, nullable=False)
    description = Column(String, nullable=True)
    total_value = Column(Float, nullable=True)
    risk_profile = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="portfolios")
    assets = relationship("Asset", secondary=portfolio_assets, back_populates="portfolios")
    transactions = relationship("Transaction", back_populates="portfolio")


class Watchlist(Base):
    """Watchlist model."""
    
    __tablename__ = "watchlists"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    name = Column(String, nullable=False)
    description = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="watchlists")
    assets = relationship("Asset", secondary="watchlist_assets", back_populates="watchlists")


# Association table for many-to-many relationship between watchlists and assets
watchlist_assets = Table(
    "watchlist_assets",
    Base.metadata,
    Column("watchlist_id", Integer, ForeignKey("watchlists.id"), primary_key=True),
    Column("asset_id", Integer, ForeignKey("assets.id"), primary_key=True)
)


class Transaction(Base):
    """Transaction model."""
    
    __tablename__ = "transactions"
    
    id = Column(Integer, primary_key=True, index=True)
    portfolio_id = Column(Integer, ForeignKey("portfolios.id"), nullable=False)
    asset_id = Column(Integer, ForeignKey("assets.id"), nullable=False)
    transaction_type = Column(String, nullable=False)  # "buy" or "sell"
    shares = Column(Float, nullable=False)
    price = Column(Float, nullable=False)
    total_amount = Column(Float, nullable=False)
    transaction_date = Column(DateTime, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    portfolio = relationship("Portfolio", back_populates="transactions")
    asset = relationship("Asset")


class PriceHistory(Base):
    """Price history model."""
    
    __tablename__ = "price_history"
    
    id = Column(Integer, primary_key=True, index=True)
    asset_id = Column(Integer, ForeignKey("assets.id"), nullable=False)
    date = Column(DateTime, nullable=False)
    open = Column(Float, nullable=False)
    high = Column(Float, nullable=False)
    low = Column(Float, nullable=False)
    close = Column(Float, nullable=False)
    volume = Column(Integer, nullable=False)
    
    # Relationships
    asset = relationship("Asset", back_populates="price_history")
