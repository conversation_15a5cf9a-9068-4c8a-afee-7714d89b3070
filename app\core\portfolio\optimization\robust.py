"""
Robust portfolio optimization strategy.

This module implements robust optimization techniques for portfolio optimization,
which are designed to be less sensitive to estimation errors in the inputs.
"""
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
from scipy import optimize

from app.core.portfolio.optimization.base import OptimizationStrategy
from app.core.portfolio.optimization.mean_variance import MeanVarianceOptimization
from app.utils.constants import DEFAULT_RISK_FREE_RATE
from app.utils.logging import logger


class RobustOptimization(OptimizationStrategy):
    """
    Robust portfolio optimization strategy.
    
    This class implements robust optimization techniques for portfolio optimization,
    which are designed to be less sensitive to estimation errors in the inputs.
    """
    
    def __init__(self, risk_free_rate: float = DEFAULT_RISK_FREE_RATE):
        """
        Initialize the robust optimization strategy.
        
        Args:
            risk_free_rate: Risk-free rate (default: 2%)
        """
        super().__init__("Robust Optimization")
        self.risk_free_rate = risk_free_rate
        self.mv_optimizer = MeanVarianceOptimization(risk_free_rate=risk_free_rate)
    
    def optimize(
        self,
        returns: pd.DataFrame,
        method: str = "bayes_stein",
        uncertainty: float = 0.05,
        objective: str = "sharpe",
        **kwargs
    ) -> Dict[str, float]:
        """
        Optimize portfolio weights using robust optimization.
        
        Args:
            returns: DataFrame of asset returns
            method: Robust optimization method ('bayes_stein', 'resampling', 'worst_case')
            uncertainty: Uncertainty parameter for worst-case optimization
            objective: Optimization objective for the final portfolio
            **kwargs: Additional parameters
            
        Returns:
            Dictionary mapping asset names to weights
        """
        # Validate inputs
        if returns is None or returns.empty:
            logger.error("Returns must be provided for robust optimization")
            return {}
        
        # Get assets
        assets = returns.columns.tolist()
        n_assets = len(assets)
        
        # Calculate sample mean and covariance
        sample_mean = returns.mean()
        sample_cov = returns.cov()
        
        # Apply robust estimation method
        if method == "bayes_stein":
            # Bayes-Stein shrinkage of expected returns
            expected_returns, cov_matrix = self._bayes_stein_shrinkage(returns)
        elif method == "resampling":
            # Resampled efficient frontier
            return self._resampled_optimization(returns, objective=objective, **kwargs)
        elif method == "worst_case":
            # Worst-case optimization
            expected_returns, cov_matrix = self._worst_case_optimization(returns, uncertainty)
        else:
            logger.warning(f"Unknown method: {method}, using Bayes-Stein shrinkage")
            expected_returns, cov_matrix = self._bayes_stein_shrinkage(returns)
        
        # Create temporary returns DataFrame for mean-variance optimization
        temp_returns = pd.DataFrame({asset: [ret] for asset, ret in zip(assets, expected_returns)})
        
        # Use mean-variance optimization with the robust estimates
        return self.mv_optimizer.optimize(
            temp_returns,
            objective=objective,
            cov_matrix=cov_matrix,
            **kwargs
        )
    
    def _bayes_stein_shrinkage(
        self,
        returns: pd.DataFrame
    ) -> Tuple[np.ndarray, pd.DataFrame]:
        """
        Apply Bayes-Stein shrinkage to expected returns.
        
        Args:
            returns: DataFrame of asset returns
            
        Returns:
            Tuple of (shrunk expected returns, covariance matrix)
        """
        # Get assets
        assets = returns.columns.tolist()
        n_assets = len(assets)
        n_obs = len(returns)
        
        # Calculate sample mean and covariance
        sample_mean = returns.mean().values
        sample_cov = returns.cov().values
        
        # Calculate global minimum variance portfolio
        inv_cov = np.linalg.inv(sample_cov)
        ones = np.ones(n_assets)
        gmv_weights = inv_cov @ ones / (ones @ inv_cov @ ones)
        
        # Calculate global minimum variance portfolio mean
        gmv_mean = gmv_weights @ sample_mean
        
        # Calculate shrinkage intensity
        # Formula from Jorion (1986)
        delta = (n_assets + 2) / (
            (n_assets + 2) + (sample_mean - gmv_mean * ones) @ inv_cov @ (sample_mean - gmv_mean * ones)
        )
        
        # Apply shrinkage
        shrunk_mean = delta * gmv_mean * ones + (1 - delta) * sample_mean
        
        # Return shrunk mean and original covariance
        return shrunk_mean, pd.DataFrame(sample_cov, index=assets, columns=assets)
    
    def _resampled_optimization(
        self,
        returns: pd.DataFrame,
        n_samples: int = 100,
        objective: str = "sharpe",
        **kwargs
    ) -> Dict[str, float]:
        """
        Perform resampled efficient frontier optimization.
        
        Args:
            returns: DataFrame of asset returns
            n_samples: Number of resamples to generate
            objective: Optimization objective
            **kwargs: Additional parameters
            
        Returns:
            Dictionary mapping asset names to weights
        """
        # Get assets
        assets = returns.columns.tolist()
        n_assets = len(assets)
        n_obs = len(returns)
        
        # Calculate sample mean and covariance
        sample_mean = returns.mean().values
        sample_cov = returns.cov().values
        
        # Initialize weights matrix
        all_weights = np.zeros((n_samples, n_assets))
        
        # Generate resampled frontiers
        for i in range(n_samples):
            # Generate multivariate normal sample
            sample = np.random.multivariate_normal(sample_mean, sample_cov, n_obs)
            sample_df = pd.DataFrame(sample, columns=assets)
            
            # Optimize portfolio for this sample
            weights = self.mv_optimizer.optimize(sample_df, objective=objective, **kwargs)
            
            # Store weights
            for j, asset in enumerate(assets):
                all_weights[i, j] = weights.get(asset, 0.0)
        
        # Average weights across all samples
        avg_weights = np.mean(all_weights, axis=0)
        
        # Normalize weights to sum to 1
        avg_weights = avg_weights / np.sum(avg_weights)
        
        # Convert to dictionary
        result_weights = {asset: weight for asset, weight in zip(assets, avg_weights)}
        
        return result_weights
    
    def _worst_case_optimization(
        self,
        returns: pd.DataFrame,
        uncertainty: float = 0.05
    ) -> Tuple[np.ndarray, pd.DataFrame]:
        """
        Apply worst-case optimization.
        
        Args:
            returns: DataFrame of asset returns
            uncertainty: Uncertainty parameter (0-1)
            
        Returns:
            Tuple of (adjusted expected returns, adjusted covariance matrix)
        """
        # Get assets
        assets = returns.columns.tolist()
        n_assets = len(assets)
        
        # Calculate sample mean and covariance
        sample_mean = returns.mean().values
        sample_cov = returns.cov().values
        
        # Calculate standard errors of mean estimates
        std_errors = np.sqrt(np.diag(sample_cov) / len(returns))
        
        # Adjust expected returns downward by uncertainty * standard error
        adjusted_mean = sample_mean - uncertainty * std_errors
        
        # Adjust covariance matrix upward by uncertainty factor
        adjusted_cov = sample_cov * (1 + uncertainty)
        
        # Return adjusted estimates
        return adjusted_mean, pd.DataFrame(adjusted_cov, index=assets, columns=assets)
