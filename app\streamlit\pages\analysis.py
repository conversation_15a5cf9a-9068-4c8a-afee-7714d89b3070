"""
Technical analysis page for the Streamlit app.
"""
import asyncio
from datetime import date, datetime, timedelta

import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import streamlit as st
from plotly.subplots import make_subplots

from app.core.data_collection.data_manager import data_manager
from app.core.technical_analysis.indicators.momentum import RSI, Stochastic
from app.core.technical_analysis.indicators.trend import MACD, MovingAverage
from app.core.technical_analysis.indicators.volatility import ATR, BollingerBands
from app.core.technical_analysis.indicators.volume import OBV, VWAP
from app.core.technical_analysis.patterns.candlestick import CandlestickPatterns
from app.core.technical_analysis.patterns.chart_patterns import ChartPatterns
from app.streamlit.components.charts import create_candlestick_chart
from app.utils.constants import (MOMENTUM_INDICATORS, TREND_INDICATORS,
                               VOLATILITY_INDICATORS, VOLUME_INDICATORS)
from app.streamlit.pages.advanced_indicators import show_advanced_indicators_page
from app.streamlit.pages.pattern_recognition import show_pattern_recognition_page
from app.utils.logging import logger


# Initialize indicators
indicators = {
    "sma": MovingAverage("sma"),
    "ema": MovingAverage("ema"),
    "wma": MovingAverage("wma"),
    "macd": MACD(),
    "rsi": RSI(),
    "stoch": Stochastic(),
    "bbands": BollingerBands(),
    "atr": ATR(),
    "obv": OBV(),
    "vwap": VWAP()
}

# Initialize pattern recognizers
candlestick_patterns = CandlestickPatterns()
chart_patterns = ChartPatterns()


def add_indicator_to_chart(fig, df, indicator_type, params=None):
    """Add an indicator to a chart."""
    if params is None:
        params = {}

    # Calculate the indicator
    indicator = indicators[indicator_type]
    df = indicator.calculate(df, **params)

    # Add the indicator to the chart
    if indicator_type in ["sma", "ema", "wma"]:
        period = params.get("period", 20)
        column = f"{indicator_type}_{period}"

        fig.add_trace(
            go.Scatter(
                x=df["date"],
                y=df[column],
                mode="lines",
                name=f"{indicator_type.upper()}({period})",
                line=dict(width=1.5)
            ),
            row=1, col=1
        )
    elif indicator_type == "macd":
        # Find the MACD row (if RSI is enabled, MACD is row 3, otherwise row 2)
        macd_row = 2
        if "rsi_14" in df.columns or any(col.startswith("rsi_") for col in df.columns):
            macd_row = 3

        # Add MACD line
        fig.add_trace(
            go.Scatter(
                x=df["date"],
                y=df["macd"],
                mode="lines",
                name="MACD",
                line=dict(width=1.5, color="blue")
            ),
            row=macd_row, col=1
        )

        # Add signal line
        fig.add_trace(
            go.Scatter(
                x=df["date"],
                y=df["macd_signal"],
                mode="lines",
                name="Signal",
                line=dict(width=1.5, color="red")
            ),
            row=macd_row, col=1
        )

        # Add histogram
        fig.add_trace(
            go.Bar(
                x=df["date"],
                y=df["macd_histogram"],
                name="Histogram",
                marker=dict(
                    color=["green" if val >= 0 else "red" for val in df["macd_histogram"]]
                )
            ),
            row=macd_row, col=1
        )
    elif indicator_type == "rsi":
        period = params.get("period", 14)
        column = f"rsi_{period}"

        # RSI is always in row 2
        rsi_row = 2

        fig.add_trace(
            go.Scatter(
                x=df["date"],
                y=df[column],
                mode="lines",
                name=f"RSI({period})",
                line=dict(width=1.5, color="purple")
            ),
            row=rsi_row, col=1
        )

        # Add overbought/oversold lines
        fig.add_trace(
            go.Scatter(
                x=[df["date"].iloc[0], df["date"].iloc[-1]],
                y=[70, 70],
                mode="lines",
                name="Overbought",
                line=dict(width=1, color="red", dash="dash")
            ),
            row=rsi_row, col=1
        )

        fig.add_trace(
            go.Scatter(
                x=[df["date"].iloc[0], df["date"].iloc[-1]],
                y=[30, 30],
                mode="lines",
                name="Oversold",
                line=dict(width=1, color="green", dash="dash")
            ),
            row=rsi_row, col=1
        )
    elif indicator_type == "bbands":
        # Add upper band
        fig.add_trace(
            go.Scatter(
                x=df["date"],
                y=df["bb_upper"],
                mode="lines",
                name="Upper Band",
                line=dict(width=1, color="rgba(173, 204, 255, 0.7)")
            ),
            row=1, col=1
        )

        # Add middle band
        fig.add_trace(
            go.Scatter(
                x=df["date"],
                y=df["bb_middle"],
                mode="lines",
                name="Middle Band",
                line=dict(width=1.5, color="rgba(0, 0, 255, 0.7)")
            ),
            row=1, col=1
        )

        # Add lower band
        fig.add_trace(
            go.Scatter(
                x=df["date"],
                y=df["bb_lower"],
                mode="lines",
                name="Lower Band",
                line=dict(width=1, color="rgba(173, 204, 255, 0.7)")
            ),
            row=1, col=1
        )

        # Fill between upper and lower bands
        fig.add_trace(
            go.Scatter(
                x=df["date"],
                y=df["bb_upper"],
                mode="lines",
                name="Bollinger Bands",
                line=dict(width=0),
                showlegend=False,
                fill=None
            ),
            row=1, col=1
        )

        fig.add_trace(
            go.Scatter(
                x=df["date"],
                y=df["bb_lower"],
                mode="lines",
                name="Bollinger Bands",
                line=dict(width=0),
                fill="tonexty",
                fillcolor="rgba(173, 204, 255, 0.2)"
            ),
            row=1, col=1
        )
    elif indicator_type == "atr":
        period = params.get("period", 14)
        column = f"atr_{period}"

        # Find the ATR row based on subplot titles
        atr_row = 1  # Default to price panel if not found

        # Look for the ATR panel in subplot titles
        if hasattr(fig, 'layout') and hasattr(fig.layout, 'annotations'):
            for i, annotation in enumerate(fig.layout.annotations):
                if annotation.text == "ATR":
                    atr_row = i + 1  # +1 because subplot indices start at 1
                    break

        fig.add_trace(
            go.Scatter(
                x=df["date"],
                y=df[column],
                mode="lines",
                name=f"ATR({period})",
                line=dict(width=1.5, color="orange")
            ),
            row=atr_row, col=1
        )
    elif indicator_type == "obv":
        # Find the OBV row based on subplot titles
        obv_row = 1  # Default to price panel if not found

        # Look for the OBV panel in subplot titles
        if hasattr(fig, 'layout') and hasattr(fig.layout, 'annotations'):
            for i, annotation in enumerate(fig.layout.annotations):
                if annotation.text == "OBV":
                    obv_row = i + 1  # +1 because subplot indices start at 1
                    break

        fig.add_trace(
            go.Scatter(
                x=df["date"],
                y=df["obv"],
                mode="lines",
                name="OBV",
                line=dict(width=1.5, color="green")
            ),
            row=obv_row, col=1
        )
    elif indicator_type == "vwap":
        fig.add_trace(
            go.Scatter(
                x=df["date"],
                y=df["vwap"],
                mode="lines",
                name="VWAP",
                line=dict(width=1.5, color="blue")
            ),
            row=1, col=1
        )

    return fig, df


def show_analysis_page():
    """Show the technical analysis page."""
    st.title("Technical Analysis")

    # Create tabs for different analysis types
    tabs = st.tabs([
        "Basic Indicators",
        "Advanced Indicators",
        "Pattern Recognition"
    ])

    # Basic Indicators tab
    with tabs[0]:
        show_basic_indicators()

    # Advanced Indicators tab
    with tabs[1]:
        asyncio.run(show_advanced_indicators_page())

    # Pattern Recognition tab
    with tabs[2]:
        asyncio.run(show_pattern_recognition_page())


def show_basic_indicators():
    """Show basic technical indicators."""
    st.header("Basic Technical Indicators")

    # Create a form for stock analysis
    with st.form(key="stock_analysis_form"):
        symbol = st.text_input("Enter a stock symbol (e.g., AAPL, MSFT, GOOGL)")

        col1, col2 = st.columns(2)
        with col1:
            period = st.selectbox(
                "Select period",
                ["1mo", "3mo", "6mo", "1y", "2y", "5y"],
                index=2
            )
        with col2:
            interval = st.selectbox(
                "Select interval",
                ["1d", "1wk", "1mo"],
                index=0
            )

        # Indicator selection
        st.subheader("Technical Indicators")

        # Trend indicators
        trend_expander = st.expander("Trend Indicators")
        with trend_expander:
            use_sma = st.checkbox("Simple Moving Average (SMA)")
            if use_sma:
                sma_period = st.slider("SMA Period", 5, 200, 20, key="sma_period")

            use_ema = st.checkbox("Exponential Moving Average (EMA)")
            if use_ema:
                ema_period = st.slider("EMA Period", 5, 200, 20, key="ema_period")

            use_macd = st.checkbox("MACD")

        # Momentum indicators
        momentum_expander = st.expander("Momentum Indicators")
        with momentum_expander:
            use_rsi = st.checkbox("Relative Strength Index (RSI)")
            if use_rsi:
                rsi_period = st.slider("RSI Period", 5, 50, 14, key="rsi_period")

            use_stoch = st.checkbox("Stochastic Oscillator")

        # Volatility indicators
        volatility_expander = st.expander("Volatility Indicators")
        with volatility_expander:
            use_bbands = st.checkbox("Bollinger Bands")
            if use_bbands:
                bbands_period = st.slider("Bollinger Bands Period", 5, 50, 20, key="bbands_period")
                bbands_std = st.slider("Bollinger Bands Standard Deviation", 1.0, 3.0, 2.0, 0.1, key="bbands_std")

            use_atr = st.checkbox("Average True Range (ATR)")
            if use_atr:
                atr_period = st.slider("ATR Period", 5, 50, 14, key="atr_period")

        # Volume indicators
        volume_expander = st.expander("Volume Indicators")
        with volume_expander:
            use_obv = st.checkbox("On-Balance Volume (OBV)")
            use_vwap = st.checkbox("Volume-Weighted Average Price (VWAP)")

        # Pattern recognition
        st.subheader("Pattern Recognition")

        pattern_expander = st.expander("Patterns")
        with pattern_expander:
            use_candlestick_patterns = st.checkbox("Candlestick Patterns")
            use_chart_patterns = st.checkbox("Chart Patterns")

        submit_button = st.form_submit_button(label="Analyze")

    # Display analysis if form is submitted
    if submit_button and symbol:
        # Convert period to start and end dates
        end_date = date.today()
        if period == "1mo":
            start_date = end_date - timedelta(days=30)
        elif period == "3mo":
            start_date = end_date - timedelta(days=90)
        elif period == "6mo":
            start_date = end_date - timedelta(days=180)
        elif period == "1y":
            start_date = end_date - timedelta(days=365)
        elif period == "2y":
            start_date = end_date - timedelta(days=730)
        else:  # 5y
            start_date = end_date - timedelta(days=1825)

        # Get stock data
        try:
            stock_data = asyncio.run(data_manager.get_stock_data(
                symbol.upper(),
                start_date,
                end_date,
                interval
            ))

            if stock_data is None or stock_data.empty:
                st.error(f"No data found for {symbol}. Please check the symbol and try again.")
                return

            # Display stock info
            try:
                company_info = asyncio.run(data_manager.get_company_info(symbol.upper()))
                if company_info:
                    st.subheader(f"{company_info.get('longName', symbol.upper())}")
                else:
                    st.subheader(symbol.upper())
            except Exception as e:
                st.warning(f"Could not fetch company info: {e}")
                st.subheader(symbol.upper())
        except Exception as e:
            st.error(f"Error fetching data: {e}")
            return

        # Create a figure with subplots
        # Determine how many rows we need based on selected indicators
        num_rows = 1  # Price panel always exists
        subplot_titles = ["Price"]
        row_heights = [0.5]

        # Add rows for RSI and MACD if selected
        if use_rsi:
            num_rows += 1
            subplot_titles.append("RSI")
            row_heights.append(0.15)

        if use_macd:
            num_rows += 1
            subplot_titles.append("MACD")
            row_heights.append(0.15)

        # Add row for ATR if selected
        if use_atr:
            num_rows += 1
            subplot_titles.append("ATR")
            row_heights.append(0.15)

        # Add row for OBV if selected
        if use_obv:
            num_rows += 1
            subplot_titles.append("OBV")
            row_heights.append(0.15)

        # Always add volume at the bottom
        if "volume" in stock_data.columns:
            num_rows += 1
            subplot_titles.append("Volume")
            row_heights.append(0.2)

        fig = make_subplots(
            rows=num_rows, cols=1,
            shared_xaxes=True,
            vertical_spacing=0.05,
            subplot_titles=subplot_titles,
            row_heights=row_heights
        )

        # Check if the required columns exist
        required_columns = ["open", "high", "low", "close"]
        date_column = "date"

        # Find the date column (might be 'Date' or 'date')
        if "Date" in stock_data.columns:
            date_column = "Date"
        elif "date" in stock_data.columns:
            date_column = "date"
        elif "datetime" in stock_data.columns:
            date_column = "datetime"
        elif "index" in stock_data.columns:
            date_column = "index"

        # Check if all required columns exist
        if date_column in stock_data.columns and all(col in stock_data.columns for col in required_columns):
            # Add candlestick chart
            fig.add_trace(
                go.Candlestick(
                    x=stock_data[date_column],
                    open=stock_data["open"],
                    high=stock_data["high"],
                    low=stock_data["low"],
                    close=stock_data["close"],
                    name="Price"
                ),
                row=1, col=1
            )
        else:
            st.error(f"Missing required columns in data. Available columns: {stock_data.columns.tolist()}")
            return

        # Add volume as bar chart if volume column exists
        if "volume" in stock_data.columns:
            # Volume is always the last panel
            volume_row = num_rows
            fig.add_trace(
                go.Bar(
                    x=stock_data[date_column],
                    y=stock_data["volume"],
                    name="Volume",
                    marker=dict(
                        color="rgba(0, 0, 255, 0.3)"
                    )
                ),
                row=volume_row, col=1
            )

        # Add selected indicators
        if use_sma:
            fig, stock_data = add_indicator_to_chart(
                fig, stock_data, "sma", {"period": sma_period}
            )

        if use_ema:
            fig, stock_data = add_indicator_to_chart(
                fig, stock_data, "ema", {"period": ema_period}
            )

        if use_macd:
            fig, stock_data = add_indicator_to_chart(
                fig, stock_data, "macd"
            )

        if use_rsi:
            fig, stock_data = add_indicator_to_chart(
                fig, stock_data, "rsi", {"period": rsi_period}
            )

        if use_bbands:
            fig, stock_data = add_indicator_to_chart(
                fig, stock_data, "bbands", {"period": bbands_period, "std_dev": bbands_std}
            )

        if use_atr:
            fig, stock_data = add_indicator_to_chart(
                fig, stock_data, "atr", {"period": atr_period}
            )

        if use_obv:
            fig, stock_data = add_indicator_to_chart(
                fig, stock_data, "obv"
            )

        if use_vwap:
            fig, stock_data = add_indicator_to_chart(
                fig, stock_data, "vwap"
            )

        # Update layout
        fig.update_layout(
            title=f"{symbol.upper()} Technical Analysis",
            xaxis_title="Date",
            yaxis_title="Price",
            xaxis_rangeslider_visible=False,
            height=800,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            )
        )

        # Display the chart
        st.plotly_chart(fig, use_container_width=True)

        # Pattern recognition
        if use_candlestick_patterns or use_chart_patterns:
            st.subheader("Pattern Recognition")

            # Recognize patterns
            if use_candlestick_patterns:
                stock_data = candlestick_patterns.recognize_patterns(stock_data)

                # Get recognized patterns
                pattern_columns = [
                    col for col in stock_data.columns
                    if col not in ["open", "high", "low", "close", "volume", "date", "body_size", "total_range", "upper_shadow", "lower_shadow"]
                    and not col.startswith("sma_") and not col.startswith("ema_") and not col.startswith("wma_")
                    and not col.startswith("rsi_") and not col.startswith("bb_") and not col.startswith("atr_")
                    and col not in ["macd", "macd_signal", "macd_histogram", "obv", "obv_ema", "vwap", "typical_price"]
                    and not col.endswith("_signal")
                ]

                if pattern_columns:
                    st.subheader("Candlestick Patterns")

                    # Create a table of patterns
                    pattern_data = []
                    for _, row in stock_data.iterrows():
                        for pattern in pattern_columns:
                            if row[pattern]:
                                pattern_data.append({
                                    "Date": row[date_column] if date_column in row else "Unknown",
                                    "Pattern": pattern.replace("_", " ").title(),
                                    "Price": row["close"]
                                })

                    if pattern_data:
                        pattern_df = pd.DataFrame(pattern_data)
                        st.dataframe(pattern_df)
                    else:
                        st.info("No candlestick patterns found in the selected period.")

            if use_chart_patterns:
                stock_data = chart_patterns.recognize_patterns(stock_data)

                # Get recognized patterns
                pattern_columns = [
                    col for col in stock_data.columns
                    if col in ["double_top", "double_bottom", "head_and_shoulders", "inverse_head_and_shoulders",
                              "ascending_triangle", "descending_triangle", "symmetric_triangle",
                              "ascending_channel", "descending_channel", "horizontal_channel"]
                ]

                if pattern_columns:
                    st.subheader("Chart Patterns")

                    # Create a table of patterns
                    pattern_data = []
                    for _, row in stock_data.iterrows():
                        for pattern in pattern_columns:
                            if row[pattern]:
                                pattern_data.append({
                                    "Date": row[date_column] if date_column in row else "Unknown",
                                    "Pattern": pattern.replace("_", " ").title(),
                                    "Price": row["close"]
                                })

                    if pattern_data:
                        pattern_df = pd.DataFrame(pattern_data)
                        st.dataframe(pattern_df)
                    else:
                        st.info("No chart patterns found in the selected period.")

        # Display recent data with indicators
        st.subheader("Recent Data with Indicators")

        # Select columns to display
        display_columns = ["date", "open", "high", "low", "close", "volume"]

        if use_sma:
            display_columns.append(f"sma_{sma_period}")

        if use_ema:
            display_columns.append(f"ema_{ema_period}")

        if use_macd:
            display_columns.extend(["macd", "macd_signal", "macd_histogram"])

        if use_rsi:
            display_columns.append(f"rsi_{rsi_period}")

        if use_bbands:
            display_columns.extend(["bb_upper", "bb_middle", "bb_lower"])

        if use_atr:
            display_columns.append(f"atr_{atr_period}")

        if use_obv:
            display_columns.append("obv")

        if use_vwap:
            display_columns.append("vwap")

        # Make sure to use the correct date column
        if date_column != "date" and "date" in display_columns:
            display_columns.remove("date")
            display_columns.append(date_column)

        # Filter to only include columns that exist in the DataFrame
        available_columns = [col for col in display_columns if col in stock_data.columns]

        # Display the data
        if available_columns:
            st.dataframe(stock_data[available_columns].tail(10).sort_index(ascending=False))
        else:
            st.error("No columns available to display")
        # Check if stock_data is empty (should be unreachable due to early return)
        if stock_data is None or stock_data.empty:
            st.error(f"No data found for {symbol.upper()}")

    # Add a note about technical analysis
    st.markdown("---")
    st.markdown(
        """
        ## About Technical Analysis

        Technical analysis is a method of evaluating securities by analyzing statistics generated by market activity,
        such as past prices and volume. Technical analysts do not attempt to measure a security's intrinsic value,
        but instead use charts and other tools to identify patterns that can suggest future activity.

        ### Types of Indicators

        - **Trend Indicators**: Help identify the direction of the market (e.g., Moving Averages, MACD)
        - **Momentum Indicators**: Measure the rate of price changes (e.g., RSI, Stochastic Oscillator)
        - **Volatility Indicators**: Measure the rate of price fluctuations (e.g., Bollinger Bands, ATR)
        - **Volume Indicators**: Measure the strength of a trend based on volume (e.g., OBV, VWAP)

        ### Patterns

        - **Candlestick Patterns**: Patterns formed by one or more candlesticks (e.g., Doji, Hammer, Engulfing)
        - **Chart Patterns**: Patterns formed by price movements over time (e.g., Head and Shoulders, Double Top/Bottom, Triangles)
        """
    )
