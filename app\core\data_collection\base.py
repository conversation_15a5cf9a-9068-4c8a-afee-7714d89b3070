"""
Base data collector abstract class.
"""
from abc import ABC, abstractmethod
from datetime import date
from typing import Dict, List, Optional, Union

import pandas as pd


class BaseDataCollector(ABC):
    """
    Abstract base class for data collectors.
    
    All data source implementations should inherit from this class
    and implement the required methods.
    """
    
    @abstractmethod
    async def get_stock_data(
        self,
        symbol: str,
        start_date: date,
        end_date: date,
        interval: str = "1d"
    ) -> pd.DataFrame:
        """
        Get historical stock data for a symbol.
        
        Args:
            symbol: The stock symbol
            start_date: Start date for historical data
            end_date: End date for historical data
            interval: Data interval (e.g., '1d', '1wk', '1mo')
            
        Returns:
            DataFrame with historical stock data
        """
        pass
    
    @abstractmethod
    async def get_multiple_stock_data(
        self,
        symbols: List[str],
        start_date: date,
        end_date: date,
        interval: str = "1d"
    ) -> Dict[str, pd.DataFrame]:
        """
        Get historical stock data for multiple symbols.
        
        Args:
            symbols: List of stock symbols
            start_date: Start date for historical data
            end_date: End date for historical data
            interval: Data interval (e.g., '1d', '1wk', '1mo')
            
        Returns:
            Dictionary mapping symbols to DataFrames with historical stock data
        """
        pass
    
    @abstractmethod
    async def get_company_info(self, symbol: str) -> Dict:
        """
        Get company information for a symbol.
        
        Args:
            symbol: The stock symbol
            
        Returns:
            Dictionary with company information
        """
        pass
    
    @abstractmethod
    async def search_symbols(self, query: str) -> List[Dict]:
        """
        Search for symbols matching a query.
        
        Args:
            query: The search query
            
        Returns:
            List of dictionaries with symbol information
        """
        pass
