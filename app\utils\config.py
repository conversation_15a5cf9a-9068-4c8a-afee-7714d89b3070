from pydantic_settings import BaseSettings
from pydantic import Field
from typing import Dict, Any, Optional, List


class DataSourcesSettings(BaseSettings):
    alpha_vantage_api_key: str = ""
    financial_modeling_prep_api_key: str = ""
    yahoo_finance_api_key: str = ""
    rate_limit: int = 5  # Added rate_limit with default value


class CacheSettings(BaseSettings):
    type: str = "memory"
    redis_url: str = "redis://localhost:6379/0"
    expiry: int = 3600
    max_size: int = 1000


class ApiSettings(BaseSettings):
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = True
    prefix: str = "/api/v1"
    cors_origins: List[str] = ["http://localhost:8501", "http://127.0.0.1:8501"]


class DatabaseSettings(BaseSettings):
    url: str = "sqlite:///./data/portfolio_optimizer.db"
    echo: bool = False


class LoggingSettings(BaseSettings):
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file: str = "logs/app.log"


class SecuritySettings(BaseSettings):
    secret_key: str = "replace_this_with_a_secure_random_key"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30


class StreamlitSettings(BaseSettings):
    server_port: int = 8501
    server_headless: bool = True
    browser_server_address: str = "localhost"


class Settings(BaseSettings):
    app: Dict[str, Any] = {
        "name": "Portfolio Optimizer",
        "version": "0.1.0",
        "description": "A portfolio optimization and stock analysis tool",
        "environment": "development",
        "debug": True
    }
    api: ApiSettings = Field(default_factory=ApiSettings)
    cache: CacheSettings = Field(default_factory=CacheSettings)
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    logging: LoggingSettings = Field(default_factory=LoggingSettings)
    security: SecuritySettings = Field(default_factory=SecuritySettings)
    streamlit: StreamlitSettings = Field(default_factory=StreamlitSettings)
    data_sources: Dict[str, Any] = Field(default_factory=lambda: {
        "yahoo_finance": {
            "enabled": True,
            "priority": 1
        },
        "alpha_vantage": {
            "enabled": True,
            "priority": 2,
            "api_key": "",
            "rate_limit": 5
        },
        "financial_modeling_prep": {
            "enabled": True,
            "priority": 3,
            "api_key": ""
        }
    })

    class Config:
        env_file = ".env"
        extra = "allow"


def get_settings() -> Settings:
    return Settings()
