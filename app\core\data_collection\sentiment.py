"""
Sentiment analysis data collector implementation.
"""
import asyncio
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional, Union

import pandas as pd
import aiohttp
from aiolimiter import AsyncLimiter
import re
import nltk
from nltk.sentiment.vader import SentimentIntensityAnalyzer

from app.core.data_collection.base import BaseDataCollector
from app.settings import get_settings
from app.utils.logging import logger


# Download NLTK resources if not already downloaded
try:
    nltk.data.find('vader_lexicon')
except LookupError:
    nltk.download('vader_lexicon')


class SentimentAnalysisCollector:
    """
    Sentiment analysis data collector.
    
    This class provides methods for retrieving and analyzing sentiment data for stocks.
    """
    
    def __init__(self):
        """Initialize the sentiment analysis collector."""
        self.settings = get_settings()
        self.alpha_vantage_api_key = self.settings.data_sources.get('alpha_vantage', {}).get('api_key', '')
        self.fmp_api_key = self.settings.data_sources.get('financial_modeling_prep', {}).get('api_key', '')
        self.base_url_av = "https://www.alphavantage.co/query"
        self.base_url_fmp = "https://financialmodelingprep.com/api/v3"
        
        # Set up rate limiter (5 requests per minute for free tier)
        self.rate_limiter = AsyncLimiter(5, 60)
        
        # Initialize session
        self.session = None
        
        # Initialize sentiment analyzer
        self.sentiment_analyzer = SentimentIntensityAnalyzer()
    
    async def _get_session(self):
        """Get or create an aiohttp session."""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession()
        return self.session
    
    async def _make_request_av(self, function: str, params: Dict = None) -> Dict:
        """
        Make a request to the Alpha Vantage API.
        
        Args:
            function: API function
            params: Query parameters
            
        Returns:
            API response as dictionary
        """
        # Add API key and function to parameters
        if params is None:
            params = {}
        params['apikey'] = self.alpha_vantage_api_key
        params['function'] = function
        
        # Build URL
        url = self.base_url_av
        
        # Use rate limiter
        async with self.rate_limiter:
            try:
                session = await self._get_session()
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        logger.error(f"Error from Alpha Vantage API: {response.status} - {await response.text()}")
                        return {}
            except Exception as e:
                logger.error(f"Error making request to Alpha Vantage API: {str(e)}")
                return {}
    
    async def _make_request_fmp(self, endpoint: str, params: Dict = None) -> Dict:
        """
        Make a request to the Financial Modeling Prep API.
        
        Args:
            endpoint: API endpoint
            params: Query parameters
            
        Returns:
            API response as dictionary
        """
        # Add API key to parameters
        if params is None:
            params = {}
        params['apikey'] = self.fmp_api_key
        
        # Build URL
        url = f"{self.base_url_fmp}{endpoint}"
        
        # Use rate limiter
        async with self.rate_limiter:
            try:
                session = await self._get_session()
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        logger.error(f"Error from FMP API: {response.status} - {await response.text()}")
                        return {}
            except Exception as e:
                logger.error(f"Error making request to FMP API: {str(e)}")
                return {}
    
    async def get_news_sentiment(
        self,
        symbol: str,
        time_from: Optional[date] = None,
        time_to: Optional[date] = None,
        limit: int = 50
    ) -> pd.DataFrame:
        """
        Get news sentiment for a symbol.
        
        Args:
            symbol: The stock symbol
            time_from: Start date for news
            time_to: End date for news
            limit: Maximum number of news items to retrieve
            
        Returns:
            DataFrame with news sentiment data
        """
        try:
            # Set default dates if not provided
            if time_from is None:
                time_from = date.today() - timedelta(days=30)
            if time_to is None:
                time_to = date.today()
            
            # Format dates
            time_from_str = time_from.strftime("%Y-%m-%d")
            time_to_str = time_to.strftime("%Y-%m-%d")
            
            # Try Alpha Vantage first
            params = {
                'symbol': symbol,
                'limit': limit
            }
            
            response = await self._make_request_av('NEWS_SENTIMENT', params)
            
            if response and 'feed' in response:
                # Extract news items
                news_items = response['feed']
                
                # Create DataFrame
                df = pd.DataFrame(news_items)
                
                # Filter by date if needed
                if 'time_published' in df.columns:
                    df['date'] = pd.to_datetime(df['time_published']).dt.date
                    df = df[(df['date'] >= time_from) & (df['date'] <= time_to)]
                
                # Add sentiment analysis if not already present
                if 'overall_sentiment_score' not in df.columns and 'title' in df.columns:
                    # Analyze sentiment of titles
                    df['sentiment_score'] = df['title'].apply(self._analyze_sentiment)
                    
                    # Categorize sentiment
                    df['sentiment'] = df['sentiment_score'].apply(self._categorize_sentiment)
                
                return df
            
            # Fall back to Financial Modeling Prep
            endpoint = f"/stock_news"
            params = {
                'tickers': symbol,
                'limit': limit
            }
            
            response = await self._make_request_fmp(endpoint, params)
            
            if response and isinstance(response, list) and len(response) > 0:
                # Create DataFrame
                df = pd.DataFrame(response)
                
                # Filter by date if needed
                if 'publishedDate' in df.columns:
                    df['date'] = pd.to_datetime(df['publishedDate']).dt.date
                    df = df[(df['date'] >= time_from) & (df['date'] <= time_to)]
                
                # Analyze sentiment of titles
                if 'title' in df.columns:
                    df['sentiment_score'] = df['title'].apply(self._analyze_sentiment)
                    
                    # Categorize sentiment
                    df['sentiment'] = df['sentiment_score'].apply(self._categorize_sentiment)
                
                return df
            
            # If both APIs fail, return empty DataFrame
            logger.warning(f"Could not retrieve news sentiment for {symbol}")
            return pd.DataFrame()
        
        except Exception as e:
            logger.error(f"Error getting news sentiment for {symbol}: {str(e)}")
            return pd.DataFrame()
    
    async def get_social_media_sentiment(
        self,
        symbol: str,
        time_from: Optional[date] = None,
        time_to: Optional[date] = None,
        limit: int = 50
    ) -> pd.DataFrame:
        """
        Get social media sentiment for a symbol.
        
        Args:
            symbol: The stock symbol
            time_from: Start date for social media posts
            time_to: End date for social media posts
            limit: Maximum number of posts to retrieve
            
        Returns:
            DataFrame with social media sentiment data
        """
        try:
            # Set default dates if not provided
            if time_from is None:
                time_from = date.today() - timedelta(days=30)
            if time_to is None:
                time_to = date.today()
            
            # Format dates
            time_from_str = time_from.strftime("%Y-%m-%d")
            time_to_str = time_to.strftime("%Y-%m-%d")
            
            # Try Financial Modeling Prep
            endpoint = f"/stock-tweets/{symbol}"
            
            response = await self._make_request_fmp(endpoint)
            
            if response and isinstance(response, list) and len(response) > 0:
                # Create DataFrame
                df = pd.DataFrame(response)
                
                # Filter by date if needed
                if 'date' in df.columns:
                    df['date'] = pd.to_datetime(df['date']).dt.date
                    df = df[(df['date'] >= time_from) & (df['date'] <= time_to)]
                
                # Limit number of posts
                df = df.head(limit)
                
                # Analyze sentiment of tweets
                if 'text' in df.columns:
                    df['sentiment_score'] = df['text'].apply(self._analyze_sentiment)
                    
                    # Categorize sentiment
                    df['sentiment'] = df['sentiment_score'].apply(self._categorize_sentiment)
                
                return df
            
            # If API fails, return empty DataFrame
            logger.warning(f"Could not retrieve social media sentiment for {symbol}")
            return pd.DataFrame()
        
        except Exception as e:
            logger.error(f"Error getting social media sentiment for {symbol}: {str(e)}")
            return pd.DataFrame()
    
    async def get_analyst_ratings(
        self,
        symbol: str,
        limit: int = 20
    ) -> pd.DataFrame:
        """
        Get analyst ratings for a symbol.
        
        Args:
            symbol: The stock symbol
            limit: Maximum number of ratings to retrieve
            
        Returns:
            DataFrame with analyst ratings
        """
        try:
            # Try Financial Modeling Prep
            endpoint = f"/analyst-stock-recommendations/{symbol}"
            
            response = await self._make_request_fmp(endpoint)
            
            if response and isinstance(response, list) and len(response) > 0:
                # Create DataFrame
                df = pd.DataFrame(response)
                
                # Limit number of ratings
                df = df.head(limit)
                
                return df
            
            # If API fails, return empty DataFrame
            logger.warning(f"Could not retrieve analyst ratings for {symbol}")
            return pd.DataFrame()
        
        except Exception as e:
            logger.error(f"Error getting analyst ratings for {symbol}: {str(e)}")
            return pd.DataFrame()
    
    async def get_insider_trading(
        self,
        symbol: str,
        limit: int = 20
    ) -> pd.DataFrame:
        """
        Get insider trading data for a symbol.
        
        Args:
            symbol: The stock symbol
            limit: Maximum number of transactions to retrieve
            
        Returns:
            DataFrame with insider trading data
        """
        try:
            # Try Financial Modeling Prep
            endpoint = f"/insider-trading"
            params = {
                'symbol': symbol,
                'limit': limit
            }
            
            response = await self._make_request_fmp(endpoint, params)
            
            if response and isinstance(response, list) and len(response) > 0:
                # Create DataFrame
                df = pd.DataFrame(response)
                
                return df
            
            # If API fails, return empty DataFrame
            logger.warning(f"Could not retrieve insider trading data for {symbol}")
            return pd.DataFrame()
        
        except Exception as e:
            logger.error(f"Error getting insider trading data for {symbol}: {str(e)}")
            return pd.DataFrame()
    
    async def get_aggregate_sentiment(
        self,
        symbol: str,
        time_from: Optional[date] = None,
        time_to: Optional[date] = None
    ) -> Dict:
        """
        Get aggregate sentiment data for a symbol.
        
        Args:
            symbol: The stock symbol
            time_from: Start date for sentiment data
            time_to: End date for sentiment data
            
        Returns:
            Dictionary with aggregate sentiment data
        """
        try:
            # Get news sentiment
            news_df = await self.get_news_sentiment(symbol, time_from, time_to)
            
            # Get social media sentiment
            social_df = await self.get_social_media_sentiment(symbol, time_from, time_to)
            
            # Get analyst ratings
            analyst_df = await self.get_analyst_ratings(symbol)
            
            # Calculate aggregate sentiment
            aggregate_sentiment = {}
            
            # News sentiment
            if not news_df.empty and 'sentiment_score' in news_df.columns:
                aggregate_sentiment['news_sentiment'] = {
                    'average_score': news_df['sentiment_score'].mean(),
                    'positive_count': (news_df['sentiment'] == 'positive').sum(),
                    'neutral_count': (news_df['sentiment'] == 'neutral').sum(),
                    'negative_count': (news_df['sentiment'] == 'negative').sum(),
                    'total_count': len(news_df)
                }
            
            # Social media sentiment
            if not social_df.empty and 'sentiment_score' in social_df.columns:
                aggregate_sentiment['social_sentiment'] = {
                    'average_score': social_df['sentiment_score'].mean(),
                    'positive_count': (social_df['sentiment'] == 'positive').sum(),
                    'neutral_count': (social_df['sentiment'] == 'neutral').sum(),
                    'negative_count': (social_df['sentiment'] == 'negative').sum(),
                    'total_count': len(social_df)
                }
            
            # Analyst ratings
            if not analyst_df.empty and 'recommendation' in analyst_df.columns:
                # Count recommendations
                recommendation_counts = analyst_df['recommendation'].value_counts().to_dict()
                
                # Calculate average rating
                rating_map = {
                    'Strong Buy': 5,
                    'Buy': 4,
                    'Hold': 3,
                    'Sell': 2,
                    'Strong Sell': 1
                }
                
                # Map recommendations to numeric values
                analyst_df['rating_value'] = analyst_df['recommendation'].map(
                    lambda x: rating_map.get(x, 3)  # Default to 'Hold' (3) if not found
                )
                
                aggregate_sentiment['analyst_sentiment'] = {
                    'average_rating': analyst_df['rating_value'].mean(),
                    'recommendation_counts': recommendation_counts,
                    'total_count': len(analyst_df)
                }
            
            # Calculate overall sentiment
            scores = []
            weights = []
            
            if 'news_sentiment' in aggregate_sentiment:
                scores.append(aggregate_sentiment['news_sentiment']['average_score'])
                weights.append(0.4)  # 40% weight for news
            
            if 'social_sentiment' in aggregate_sentiment:
                scores.append(aggregate_sentiment['social_sentiment']['average_score'])
                weights.append(0.3)  # 30% weight for social media
            
            if 'analyst_sentiment' in aggregate_sentiment:
                # Normalize analyst rating to [-1, 1] range
                analyst_score = (aggregate_sentiment['analyst_sentiment']['average_rating'] - 3) / 2
                scores.append(analyst_score)
                weights.append(0.3)  # 30% weight for analysts
            
            if scores and weights:
                # Calculate weighted average
                overall_score = sum(score * weight for score, weight in zip(scores, weights)) / sum(weights)
                
                # Categorize overall sentiment
                overall_sentiment = self._categorize_sentiment(overall_score)
                
                aggregate_sentiment['overall'] = {
                    'score': overall_score,
                    'sentiment': overall_sentiment
                }
            
            return aggregate_sentiment
        
        except Exception as e:
            logger.error(f"Error getting aggregate sentiment for {symbol}: {str(e)}")
            return {}
    
    def _analyze_sentiment(self, text: str) -> float:
        """
        Analyze sentiment of text.
        
        Args:
            text: Text to analyze
            
        Returns:
            Sentiment score (-1 to 1, where -1 is negative, 0 is neutral, 1 is positive)
        """
        if not text or not isinstance(text, str):
            return 0.0
        
        # Clean text
        text = self._clean_text(text)
        
        # Get sentiment scores
        sentiment_scores = self.sentiment_analyzer.polarity_scores(text)
        
        # Return compound score
        return sentiment_scores['compound']
    
    def _categorize_sentiment(self, score: float) -> str:
        """
        Categorize sentiment score.
        
        Args:
            score: Sentiment score
            
        Returns:
            Sentiment category ('positive', 'neutral', or 'negative')
        """
        if score >= 0.05:
            return 'positive'
        elif score <= -0.05:
            return 'negative'
        else:
            return 'neutral'
    
    def _clean_text(self, text: str) -> str:
        """
        Clean text for sentiment analysis.
        
        Args:
            text: Text to clean
            
        Returns:
            Cleaned text
        """
        # Remove URLs
        text = re.sub(r'http\S+', '', text)
        
        # Remove special characters and numbers
        text = re.sub(r'[^a-zA-Z\s]', '', text)
        
        # Convert to lowercase
        text = text.lower()
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    async def close(self):
        """Close the session."""
        if self.session and not self.session.closed:
            await self.session.close()


# Create a singleton instance
sentiment_analysis_collector = SentimentAnalysisCollector()
