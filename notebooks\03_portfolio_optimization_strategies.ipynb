{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Portfolio Optimization Strategies\n", "\n", "This notebook demonstrates various portfolio optimization techniques using the PyPortfolioOpt library. We'll explore:\n", "\n", "1. Mean-<PERSON><PERSON>ce Optimization (Markowitz)\n", "2. Minimum Volatility Portfolio\n", "3. <PERSON> Sharpe <PERSON>\n", "4. Risk Parity Portfolio\n", "5. <PERSON><PERSON><PERSON><PERSON><PERSON> Model\n", "6. Hierarchical Risk Parity\n", "\n", "Each approach has its strengths and weaknesses, which we'll discuss throughout the notebook."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Import necessary libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import yfinance as yf\n", "import plotly.graph_objects as go\n", "from datetime import datetime, timedelta\n", "from pypfopt import EfficientFrontier, risk_models, expected_returns, objective_functions, black_litterman, hierarchical_portfolio, risk_parity\n", "from pypfopt.discrete_allocation import DiscreteAllocation, get_latest_prices\n", "\n", "# Set plotting style\n", "plt.style.use('fivethirtyeight')\n", "sns.set_theme(style=\"whitegrid\")\n", "\n", "# Suppress warnings\n", "import warnings\n", "warnings.filterwarnings('ignore')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Preparation\n", "\n", "Let's download historical price data for a diverse set of assets."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Define the tickers we want to include in our portfolio\n", "tickers = ['AAPL', 'MS<PERSON>', 'AMZ<PERSON>', 'GOOGL', 'BRK-B', '<PERSON>N<PERSON>', 'JP<PERSON>', 'V', 'PG', 'UNH',\n", "           'HD', 'BAC', 'MA', 'DIS', 'NVDA', 'PYPL', 'ADBE', 'CRM', 'NFLX', 'CMCSA']\n", "\n", "# Download historical data (5 years)\n", "end_date = datetime.now()\n", "start_date = end_date - <PERSON><PERSON><PERSON>(days=5*365)\n", "\n", "# Download the data\n", "df = yf.download(tickers, start=start_date, end=end_date)\n", "\n", "# Use Adjusted Close prices\n", "prices = df['Adj Close']\n", "\n", "# Display the first few rows\n", "prices.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Calculate daily returns\n", "returns = prices.pct_change().dropna()\n", "\n", "# Plot the cumulative returns\n", "cumulative_returns = (1 + returns).cumprod()\n", "plt.figure(figsize=(15, 10))\n", "cumulative_returns.plot()\n", "plt.title('Cumulative Returns')\n", "plt.ylabel('Cumulative Return')\n", "plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Mean-V<PERSON>ce Optimization (Markowitz Model)\n", "\n", "The classic approach to portfolio optimization, which balances risk and return."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Calculate expected returns and sample covariance\n", "mu = expected_returns.mean_historical_return(prices)\n", "S = risk_models.sample_cov(prices)\n", "\n", "# Optimize for maximum Sharpe ratio\n", "ef = EfficientFrontier(mu, S)\n", "weights = ef.max_sharpe()\n", "cleaned_weights = ef.clean_weights()\n", "\n", "print(\"Markowitz Optimized Portfolio Weights:\")\n", "for ticker, weight in cleaned_weights.items():\n", "    if weight > 0.01:  # Only show allocations above 1%\n", "        print(f\"{ticker}: {weight:.4f}\")\n", "\n", "# Get the performance\n", "expected_annual_return, annual_volatility, sharpe_ratio = ef.portfolio_performance(verbose=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Minimum Volatility Portfolio\n", "\n", "This approach focuses on minimizing the portfolio's volatility without regard to returns."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Optimize for minimum volatility\n", "ef = EfficientFrontier(mu, S)\n", "min_vol_weights = ef.min_volatility()\n", "min_vol_cleaned_weights = ef.clean_weights()\n", "\n", "print(\"Minimum Volatility Portfolio Weights:\")\n", "for ticker, weight in min_vol_cleaned_weights.items():\n", "    if weight > 0.01:  # Only show allocations above 1%\n", "        print(f\"{ticker}: {weight:.4f}\")\n", "\n", "# Get the performance\n", "min_vol_expected_return, min_vol_volatility, min_vol_sharpe = ef.portfolio_performance(verbose=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Maximum Sharpe Ratio Portfolio with L2 Regularization\n", "\n", "Adding regularization can help create more diversified portfolios."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Optimize for maximum Sharpe ratio with L2 regularization\n", "ef = EfficientFrontier(mu, S)\n", "ef.add_objective(objective_functions.L2_reg, gamma=0.1)  # Add L2 regularization\n", "reg_weights = ef.max_sharpe()\n", "reg_cleaned_weights = ef.clean_weights()\n", "\n", "print(\"Regularized Maximum Sharpe Portfolio Weights:\")\n", "for ticker, weight in reg_cleaned_weights.items():\n", "    if weight > 0.01:  # Only show allocations above 1%\n", "        print(f\"{ticker}: {weight:.4f}\")\n", "\n", "# Get the performance\n", "reg_expected_return, reg_volatility, reg_sharpe = ef.portfolio_performance(verbose=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Risk Parity Portfolio\n", "\n", "Risk parity allocates weights so that each asset contributes equally to the portfolio's risk."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Calculate the risk parity portfolio\n", "rp = risk_parity.RiskParityPortfolio(S)\n", "rp_weights = rp.optimize()\n", "\n", "# Normalize weights to sum to 1\n", "rp_weights = rp_weights / np.sum(rp_weights)\n", "rp_weights_dict = {ticker: weight for ticker, weight in zip(prices.columns, rp_weights)}\n", "\n", "print(\"Risk Parity Portfolio Weights:\")\n", "for ticker, weight in rp_weights_dict.items():\n", "    if weight > 0.01:  # Only show allocations above 1%\n", "        print(f\"{ticker}: {weight:.4f}\")\n", "\n", "# Calculate portfolio performance\n", "portfolio_return = (mu * rp_weights).sum()\n", "portfolio_volatility = np.sqrt(rp_weights.T @ S @ rp_weights)\n", "sharpe = portfolio_return / portfolio_volatility\n", "\n", "print(f\"\\nExpected annual return: {portfolio_return:.2%}\")\n", "print(f\"Annual volatility: {portfolio_volatility:.2%}\")\n", "print(f\"<PERSON>: {sharpe:.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "\n", "The Black-<PERSON>tterman model incorporates investor views into the optimization process."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Market-implied risk aversion\n", "risk_aversion = black_litterman.market_implied_risk_aversion(prices)\n", "\n", "# Market-implied prior\n", "market_prior = black_litterman.market_implied_prior_returns(prices)\n", "\n", "# Define investor views\n", "viewdict = {\n", "    \"AAPL\": 0.20,  # We believe Apple will return 20%\n", "    \"MSFT\": 0.15,  # We believe Microsoft will return 15%\n", "    \"AMZN\": 0.25   # We believe Amazon will return 25%\n", "}\n", "\n", "# Confidence in each view (diagonal of the picking matrix)\n", "confidence = np.array([0.6, 0.6, 0.6])\n", "\n", "# <PERSON><PERSON> Black-<PERSON><PERSON><PERSON> expected returns\n", "bl = black_litterman.BlackLittermanModel(S, pi=market_prior, absolute_views=viewdict, omega=black_litterman.omega_from_confidence(confidence))\n", "bl_returns = bl.bl_returns()\n", "\n", "# Optimize portfolio with <PERSON><PERSON><PERSON><PERSON><PERSON> returns\n", "ef = EfficientFrontier(bl_returns, S)\n", "bl_weights = ef.max_sharpe()\n", "bl_cleaned_weights = ef.clean_weights()\n", "\n", "print(\"Black-Litterman Portfolio Weights:\")\n", "for ticker, weight in bl_cleaned_weights.items():\n", "    if weight > 0.01:  # Only show allocations above 1%\n", "        print(f\"{ticker}: {weight:.4f}\")\n", "\n", "# Get the performance\n", "bl_expected_return, bl_volatility, bl_sharpe = ef.portfolio_performance(verbose=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Hierarchical Risk Parity (HRP)\n", "\n", "HRP uses hierarchical clustering to build a diversified portfolio."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Calculate HRP weights\n", "hrp = hierarchical_portfolio.HRPOpt(returns)\n", "hrp_weights = hrp.optimize()\n", "\n", "print(\"Hierarchical Risk Parity Portfolio Weights:\")\n", "for ticker, weight in hrp_weights.items():\n", "    if weight > 0.01:  # Only show allocations above 1%\n", "        print(f\"{ticker}: {weight:.4f}\")\n", "\n", "# Get the performance\n", "hrp_expected_return, hrp_volatility, hrp_sharpe = hrp.portfolio_performance(verbose=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Comparing All Strategies\n", "\n", "Let's compare the performance metrics of all the strategies we've implemented."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Create a DataFrame to compare all strategies\n", "strategies = [\n", "    \"<PERSON><PERSON><PERSON> (<PERSON>)\",\n", "    \"Minimum Volatility\",\n", "    \"Regularized <PERSON> Sharpe\",\n", "    \"Risk Parity\",\n", "    \"Black-Litterman\",\n", "    \"Hierarchical Risk Parity\"\n", "]\n", "\n", "returns_list = [\n", "    expected_annual_return,\n", "    min_vol_expected_return,\n", "    reg_expected_return,\n", "    portfolio_return,\n", "    bl_expected_return,\n", "    hrp_expected_return\n", "]\n", "\n", "volatility_list = [\n", "    annual_volatility,\n", "    min_vol_volatility,\n", "    reg_volatility,\n", "    portfolio_volatility,\n", "    bl_volatility,\n", "    hrp_volatility\n", "]\n", "\n", "sharpe_list = [\n", "    sharpe_ratio,\n", "    min_vol_sharpe,\n", "    reg_sharpe,\n", "    sharpe,\n", "    bl_sharpe,\n", "    hrp_sharpe\n", "]\n", "\n", "comparison_df = pd.DataFrame({\n", "    'Strategy': strategies,\n", "    'Expected Return': returns_list,\n", "    'Volatility': volatility_list,\n", "    'Sharpe Ratio': sharpe_list\n", "})\n", "\n", "comparison_df.set_index('Strategy', inplace=True)\n", "\n", "# Format as percentages\n", "comparison_df['Expected Return'] = comparison_df['Expected Return'].apply(lambda x: f\"{x:.2%}\")\n", "comparison_df['Volatility'] = comparison_df['Volatility'].apply(lambda x: f\"{x:.2%}\")\n", "\n", "comparison_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualizing the Efficient Frontier\n", "\n", "Let's plot the efficient frontier and mark our optimized portfolios."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Calculate the efficient frontier\n", "ef = EfficientFrontier(mu, S)\n", "ef_max_sharpe = ef.max_sharpe()\n", "ret_tangent, std_tangent, _ = ef.portfolio_performance()\n", "\n", "ef = EfficientFrontier(mu, S)\n", "ef_min_vol = ef.min_volatility()\n", "ret_min_vol, std_min_vol, _ = ef.portfolio_performance()\n", "\n", "# Generate random portfolios\n", "n_samples = 10000\n", "weights = np.random.dirichlet(np.ones(len(mu)), n_samples)\n", "rets = weights @ mu\n", "stds = np.sqrt(np.diag(weights @ S @ weights.T))\n", "\n", "# Plot the efficient frontier with a scatter plot\n", "plt.figure(figsize=(12, 8))\n", "plt.scatter(stds, rets, c=rets/stds, marker='.', cmap='viridis', alpha=0.5)\n", "\n", "# Plot the optimized portfolios\n", "plt.scatter(std_tangent, ret_tangent, marker='*', s=500, c='r', label='Maximum Sharpe')\n", "plt.scatter(std_min_vol, ret_min_vol, marker='*', s=500, c='g', label='Minimum Volatility')\n", "plt.scatter(portfolio_volatility, portfolio_return, marker='*', s=500, c='b', label='Risk Parity')\n", "plt.scatter(hrp_volatility, hrp_expected_return, marker='*', s=500, c='purple', label='HRP')\n", "plt.scatter(bl_volatility, bl_expected_return, marker='*', s=500, c='orange', label='Black-Litterman')\n", "\n", "# Plot individual assets\n", "for i, ticker in enumerate(mu.index):\n", "    plt.scatter(np.sqrt(S.iloc[i, i]), mu.iloc[i], marker='o', s=100, label=ticker)\n", "\n", "plt.colorbar(label='Sharpe Ratio')\n", "plt.xlabel('Expected Volatility')\n", "plt.ylabel('Expected Return')\n", "plt.title('Efficient Frontier with Optimized Portfolios')\n", "plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Discrete Allocation\n", "\n", "Let's convert our portfolio weights into actual numbers of shares for a given investment amount."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Get latest prices\n", "latest_prices = get_latest_prices(prices)\n", "\n", "# Allocate a portfolio of $100,000\n", "portfolio_value = 100000\n", "\n", "# Discrete allocation for the maximum Sharpe ratio portfolio\n", "da = DiscreteAllocation(cleaned_weights, latest_prices, total_portfolio_value=portfolio_value)\n", "allocation, leftover = da.greedy_portfolio()\n", "\n", "print(\"Discrete Allocation for Maximum Sharpe Portfolio:\")\n", "for ticker, shares in allocation.items():\n", "    if shares > 0:\n", "        stock_value = shares * latest_prices[ticker]\n", "        print(f\"{ticker}: {shares} shares (${stock_value:.2f})\")\n", "print(f\"Funds remaining: ${leftover:.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "In this notebook, we've explored various portfolio optimization strategies:\n", "\n", "1. **Mean-Variance Optimization (<PERSON><PERSON><PERSON>)**: The classic approach that balances risk and return.\n", "2. **Minimum Volatility Portfolio**: Focuses on minimizing risk without regard to returns.\n", "3. **Regularized Maximum Sharpe Portfolio**: Uses L2 regularization to create more diversified portfolios.\n", "4. **Risk Parity Portfolio**: Allocates weights so each asset contributes equally to portfolio risk.\n", "5. **<PERSON><PERSON><PERSON><PERSON><PERSON> Model**: Incorporates investor views into the optimization process.\n", "6. **Hierarchical Risk Parity**: Uses hierarchical clustering for diversification.\n", "\n", "Each strategy has its strengths and weaknesses:\n", "\n", "- **<PERSON><PERSON><PERSON>** is theoretically sound but can lead to concentrated portfolios.\n", "- **Minimum Volatility** is good for risk-averse investors but may sacrifice returns.\n", "- **Regularized approaches** help with diversification but require parameter tuning.\n", "- **Risk Parity** creates well-balanced portfolios but may underperform in bull markets.\n", "- **Black-<PERSON><PERSON><PERSON>** allows for investor views but requires careful calibration.\n", "- **HRP** is robust to estimation errors but less intuitive than other methods.\n", "\n", "The choice of strategy depends on investor preferences, market conditions, and investment goals."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}