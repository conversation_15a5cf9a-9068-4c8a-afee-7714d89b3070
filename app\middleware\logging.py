"""
Logging middleware for the FastAPI application.
"""
import time
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.utils.logging import logger


class LoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware for logging HTTP requests and responses.
    """
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process the request and log information.
        
        Args:
            request: The HTTP request
            call_next: The next middleware or route handler
            
        Returns:
            The HTTP response
        """
        # Get request information
        start_time = time.time()
        method = request.method
        path = request.url.path
        query_params = str(request.query_params)
        client_host = request.client.host if request.client else "unknown"
        
        # Log request
        logger.info(f"Request: {method} {path} from {client_host}")
        
        # Process request
        try:
            response = await call_next(request)
            
            # Calculate processing time
            process_time = time.time() - start_time
            
            # Log response
            logger.info(
                f"Response: {method} {path} - {response.status_code} "
                f"({process_time:.4f}s)"
            )
            
            # Add processing time header
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
        
        except Exception as e:
            # Log error
            logger.error(f"Error processing {method} {path}: {str(e)}")
            raise
