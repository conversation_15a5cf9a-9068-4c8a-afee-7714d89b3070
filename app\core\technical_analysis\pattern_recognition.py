"""
Chart pattern recognition module.

This module provides functions to identify common chart patterns in price data,
such as head and shoulders, double tops/bottoms, triangles, and flags/pennants.
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union
from datetime import datetime, timedelta

from app.utils.logging import logger
from app.core.technical_analysis.fibonacci import find_swing_points


def identify_head_and_shoulders(
    df: pd.DataFrame,
    high_col: str = 'High',
    low_col: str = 'Low',
    close_col: str = 'Close',
    window: int = 5,
    min_pattern_length: int = 20,
    max_pattern_length: int = 100,
    shoulder_height_tolerance: float = 0.1
) -> Dict:
    """
    Identify head and shoulders pattern in price data.

    Args:
        df: DataFrame with price data
        high_col: Name of the high price column
        low_col: Name of the low price column
        close_col: Name of the close price column
        window: Window size for identifying swing points
        min_pattern_length: Minimum number of bars for a valid pattern
        max_pattern_length: Maximum number of bars for a valid pattern
        shoulder_height_tolerance: Maximum allowed difference between shoulder heights (as percentage)

    Returns:
        Dictionary with pattern information
    """
    try:
        # Find swing points
        swing_highs, swing_lows = find_swing_points(df, high_col, low_col, window)

        # If not enough swing points, return empty result
        if len(swing_highs) < 3:
            return {
                'pattern': 'none',
                'confidence': 0,
                'direction': 'none',
                'points': [],
                'neckline': {},
                'target': None,
                'analysis': 'Insufficient swing points to identify head and shoulders pattern'
            }

        # Sort swing highs by index (oldest first)
        swing_highs.sort(key=lambda x: x['index'])

        # Look for head and shoulders pattern (regular - bearish)
        bearish_patterns = []

        # We need at least 5 swing highs to form a head and shoulders pattern
        for i in range(len(swing_highs) - 4):
            # Get potential left shoulder, head, and right shoulder
            left_shoulder = swing_highs[i]
            head_candidate1 = swing_highs[i+1]
            head_candidate2 = swing_highs[i+2]
            right_shoulder_candidate1 = swing_highs[i+3]
            right_shoulder_candidate2 = swing_highs[i+4]

            # Determine which is the actual head (highest point)
            if head_candidate1['price'] > head_candidate2['price']:
                head = head_candidate1
                right_shoulder = right_shoulder_candidate1
            else:
                head = head_candidate2
                right_shoulder = right_shoulder_candidate2

            # Check if head is higher than both shoulders
            if head['price'] <= left_shoulder['price'] or head['price'] <= right_shoulder['price']:
                continue

            # Check if shoulders are at similar heights
            shoulder_height_diff = abs(left_shoulder['price'] - right_shoulder['price'])
            shoulder_height_avg = (left_shoulder['price'] + right_shoulder['price']) / 2
            if shoulder_height_diff / shoulder_height_avg > shoulder_height_tolerance:
                continue

            # Check if pattern length is within acceptable range
            pattern_length = right_shoulder['index'] - left_shoulder['index']
            if pattern_length < min_pattern_length or pattern_length > max_pattern_length:
                continue

            # Find neckline (connecting lows between shoulders and head)
            # Find the lowest point between left shoulder and head
            left_low_idx = None
            left_low_price = float('inf')

            for low in swing_lows:
                if left_shoulder['index'] < low['index'] < head['index']:
                    if low['price'] < left_low_price:
                        left_low_price = low['price']
                        left_low_idx = low['index']
                        left_low = low

            # Find the lowest point between head and right shoulder
            right_low_idx = None
            right_low_price = float('inf')

            for low in swing_lows:
                if head['index'] < low['index'] < right_shoulder['index']:
                    if low['price'] < right_low_price:
                        right_low_price = low['price']
                        right_low_idx = low['index']
                        right_low = low

            # If we couldn't find both lows, skip this pattern
            if left_low_idx is None or right_low_idx is None:
                continue

            # Calculate neckline slope and intercept
            x1, y1 = left_low_idx, left_low_price
            x2, y2 = right_low_idx, right_low_price

            if x2 - x1 == 0:  # Avoid division by zero
                slope = 0
            else:
                slope = (y2 - y1) / (x2 - x1)

            intercept = y1 - slope * x1

            # Calculate neckline price at right shoulder
            neckline_price_at_right_shoulder = slope * right_shoulder['index'] + intercept

            # Check if price has broken the neckline after the right shoulder
            breakout_confirmed = False
            breakout_idx = None

            for i in range(right_shoulder['index'] + 1, len(df)):
                neckline_price_at_i = slope * i + intercept
                if df.iloc[i][close_col] < neckline_price_at_i:
                    breakout_confirmed = True
                    breakout_idx = i
                    break

            # Calculate pattern height and target
            pattern_height = head['price'] - min(left_low_price, right_low_price)
            target_price = neckline_price_at_right_shoulder - pattern_height

            # Calculate confidence score
            confidence = 0.5  # Base confidence

            # Adjust confidence based on various factors

            # 1. Shoulder symmetry
            shoulder_symmetry = 1 - (shoulder_height_diff / shoulder_height_avg)
            confidence += shoulder_symmetry * 0.1

            # 2. Head prominence
            head_prominence = (head['price'] - max(left_shoulder['price'], right_shoulder['price'])) / max(left_shoulder['price'], right_shoulder['price'])
            confidence += min(head_prominence * 2, 0.1)

            # 3. Neckline slope
            # Ideally, neckline should be horizontal or slightly upward for bearish H&S
            if abs(slope) < 0.001:  # Almost horizontal
                confidence += 0.1
            elif slope > 0:  # Upward sloping
                confidence += 0.05

            # 4. Breakout confirmation
            if breakout_confirmed:
                confidence += 0.2

            # Ensure confidence doesn't exceed 1.0
            confidence = min(confidence, 1.0)

            # Add pattern to list
            bearish_patterns.append({
                'pattern': 'head_and_shoulders',
                'confidence': confidence,
                'direction': 'bearish',
                'points': [
                    {'name': 'left_shoulder', 'index': left_shoulder['index'], 'price': left_shoulder['price'], 'date': left_shoulder['date']},
                    {'name': 'head', 'index': head['index'], 'price': head['price'], 'date': head['date']},
                    {'name': 'right_shoulder', 'index': right_shoulder['index'], 'price': right_shoulder['price'], 'date': right_shoulder['date']},
                    {'name': 'left_low', 'index': left_low_idx, 'price': left_low_price, 'date': left_low['date']},
                    {'name': 'right_low', 'index': right_low_idx, 'price': right_low_price, 'date': right_low['date']}
                ],
                'neckline': {
                    'slope': slope,
                    'intercept': intercept,
                    'price_at_right_shoulder': neckline_price_at_right_shoulder
                },
                'target': target_price,
                'breakout_confirmed': breakout_confirmed,
                'breakout_index': breakout_idx
            })

        # Look for inverse head and shoulders pattern (bullish)
        bullish_patterns = []

        # Sort swing lows by index (oldest first)
        swing_lows.sort(key=lambda x: x['index'])

        # We need at least 5 swing lows to form an inverse head and shoulders pattern
        for i in range(len(swing_lows) - 4):
            # Get potential left shoulder, head, and right shoulder
            left_shoulder = swing_lows[i]
            head_candidate1 = swing_lows[i+1]
            head_candidate2 = swing_lows[i+2]
            right_shoulder_candidate1 = swing_lows[i+3]
            right_shoulder_candidate2 = swing_lows[i+4]

            # Determine which is the actual head (lowest point)
            if head_candidate1['price'] < head_candidate2['price']:
                head = head_candidate1
                right_shoulder = right_shoulder_candidate1
            else:
                head = head_candidate2
                right_shoulder = right_shoulder_candidate2

            # Check if head is lower than both shoulders
            if head['price'] >= left_shoulder['price'] or head['price'] >= right_shoulder['price']:
                continue

            # Check if shoulders are at similar heights
            shoulder_height_diff = abs(left_shoulder['price'] - right_shoulder['price'])
            shoulder_height_avg = (left_shoulder['price'] + right_shoulder['price']) / 2
            if shoulder_height_diff / shoulder_height_avg > shoulder_height_tolerance:
                continue

            # Check if pattern length is within acceptable range
            pattern_length = right_shoulder['index'] - left_shoulder['index']
            if pattern_length < min_pattern_length or pattern_length > max_pattern_length:
                continue

            # Find neckline (connecting highs between shoulders and head)
            # Find the highest point between left shoulder and head
            left_high_idx = None
            left_high_price = float('-inf')

            for high in swing_highs:
                if left_shoulder['index'] < high['index'] < head['index']:
                    if high['price'] > left_high_price:
                        left_high_price = high['price']
                        left_high_idx = high['index']
                        left_high = high

            # Find the highest point between head and right shoulder
            right_high_idx = None
            right_high_price = float('-inf')

            for high in swing_highs:
                if head['index'] < high['index'] < right_shoulder['index']:
                    if high['price'] > right_high_price:
                        right_high_price = high['price']
                        right_high_idx = high['index']
                        right_high = high

            # If we couldn't find both highs, skip this pattern
            if left_high_idx is None or right_high_idx is None:
                continue

            # Calculate neckline slope and intercept
            x1, y1 = left_high_idx, left_high_price
            x2, y2 = right_high_idx, right_high_price

            if x2 - x1 == 0:  # Avoid division by zero
                slope = 0
            else:
                slope = (y2 - y1) / (x2 - x1)

            intercept = y1 - slope * x1

            # Calculate neckline price at right shoulder
            neckline_price_at_right_shoulder = slope * right_shoulder['index'] + intercept

            # Check if price has broken the neckline after the right shoulder
            breakout_confirmed = False
            breakout_idx = None

            for i in range(right_shoulder['index'] + 1, len(df)):
                neckline_price_at_i = slope * i + intercept
                if df.iloc[i][close_col] > neckline_price_at_i:
                    breakout_confirmed = True
                    breakout_idx = i
                    break

            # Calculate pattern height and target
            pattern_height = max(left_high_price, right_high_price) - head['price']
            target_price = neckline_price_at_right_shoulder + pattern_height

            # Calculate confidence score
            confidence = 0.5  # Base confidence

            # Adjust confidence based on various factors

            # 1. Shoulder symmetry
            shoulder_symmetry = 1 - (shoulder_height_diff / shoulder_height_avg)
            confidence += shoulder_symmetry * 0.1

            # 2. Head prominence
            head_prominence = (min(left_shoulder['price'], right_shoulder['price']) - head['price']) / min(left_shoulder['price'], right_shoulder['price'])
            confidence += min(head_prominence * 2, 0.1)

            # 3. Neckline slope
            # Ideally, neckline should be horizontal or slightly downward for bullish inverse H&S
            if abs(slope) < 0.001:  # Almost horizontal
                confidence += 0.1
            elif slope < 0:  # Downward sloping
                confidence += 0.05

            # 4. Breakout confirmation
            if breakout_confirmed:
                confidence += 0.2

            # Ensure confidence doesn't exceed 1.0
            confidence = min(confidence, 1.0)

            # Add pattern to list
            bullish_patterns.append({
                'pattern': 'inverse_head_and_shoulders',
                'confidence': confidence,
                'direction': 'bullish',
                'points': [
                    {'name': 'left_shoulder', 'index': left_shoulder['index'], 'price': left_shoulder['price'], 'date': left_shoulder['date']},
                    {'name': 'head', 'index': head['index'], 'price': head['price'], 'date': head['date']},
                    {'name': 'right_shoulder', 'index': right_shoulder['index'], 'price': right_shoulder['price'], 'date': right_shoulder['date']},
                    {'name': 'left_high', 'index': left_high_idx, 'price': left_high_price, 'date': left_high['date']},
                    {'name': 'right_high', 'index': right_high_idx, 'price': right_high_price, 'date': right_high['date']}
                ],
                'neckline': {
                    'slope': slope,
                    'intercept': intercept,
                    'price_at_right_shoulder': neckline_price_at_right_shoulder
                },
                'target': target_price,
                'breakout_confirmed': breakout_confirmed,
                'breakout_index': breakout_idx
            })

        # Combine all patterns and sort by confidence
        all_patterns = bearish_patterns + bullish_patterns
        all_patterns.sort(key=lambda x: x['confidence'], reverse=True)

        # Return the highest confidence pattern, or empty result if none found
        if all_patterns:
            best_pattern = all_patterns[0]

            # Add analysis text
            if best_pattern['pattern'] == 'head_and_shoulders':
                if best_pattern['breakout_confirmed']:
                    analysis = (
                        f"Bearish Head and Shoulders pattern detected with {best_pattern['confidence']:.0%} confidence. "
                        f"Neckline has been broken, suggesting a potential downward move. "
                        f"Price target: {best_pattern['target']:.2f}"
                    )
                else:
                    analysis = (
                        f"Bearish Head and Shoulders pattern detected with {best_pattern['confidence']:.0%} confidence. "
                        f"Watching for a break below the neckline at {best_pattern['neckline']['price_at_right_shoulder']:.2f}. "
                        f"If confirmed, price target: {best_pattern['target']:.2f}"
                    )
            else:  # inverse_head_and_shoulders
                if best_pattern['breakout_confirmed']:
                    analysis = (
                        f"Bullish Inverse Head and Shoulders pattern detected with {best_pattern['confidence']:.0%} confidence. "
                        f"Neckline has been broken, suggesting a potential upward move. "
                        f"Price target: {best_pattern['target']:.2f}"
                    )
                else:
                    analysis = (
                        f"Bullish Inverse Head and Shoulders pattern detected with {best_pattern['confidence']:.0%} confidence. "
                        f"Watching for a break above the neckline at {best_pattern['neckline']['price_at_right_shoulder']:.2f}. "
                        f"If confirmed, price target: {best_pattern['target']:.2f}"
                    )

            best_pattern['analysis'] = analysis
            return best_pattern
        else:
            return {
                'pattern': 'none',
                'confidence': 0,
                'direction': 'none',
                'points': [],
                'neckline': {},
                'target': None,
                'analysis': 'No head and shoulders pattern detected'
            }

    except Exception as e:
        logger.error(f"Error identifying head and shoulders pattern: {str(e)}")
        return {
            'pattern': 'error',
            'confidence': 0,
            'direction': 'none',
            'points': [],
            'neckline': {},
            'target': None,
            'analysis': f'Error: {str(e)}'
        }


def identify_double_top_bottom(
    df: pd.DataFrame,
    high_col: str = 'High',
    low_col: str = 'Low',
    close_col: str = 'Close',
    window: int = 5,
    min_pattern_length: int = 10,
    max_pattern_length: int = 100,
    price_tolerance: float = 0.03,
    volume_confirmation: bool = True
) -> Dict:
    """
    Identify double top and double bottom patterns in price data.

    Args:
        df: DataFrame with price data
        high_col: Name of the high price column
        low_col: Name of the low price column
        close_col: Name of the close price column
        window: Window size for identifying swing points
        min_pattern_length: Minimum number of bars for a valid pattern
        max_pattern_length: Maximum number of bars for a valid pattern
        price_tolerance: Maximum allowed difference between tops/bottoms (as percentage)
        volume_confirmation: Whether to check for volume confirmation

    Returns:
        Dictionary with pattern information
    """
    try:
        # Find swing points
        swing_highs, swing_lows = find_swing_points(df, high_col, low_col, window)

        # If not enough swing points, return empty result
        if len(swing_highs) < 2 or len(swing_lows) < 1:
            return {
                'pattern': 'none',
                'confidence': 0,
                'direction': 'none',
                'points': [],
                'neckline': {},
                'target': None,
                'analysis': 'Insufficient swing points to identify double top/bottom pattern'
            }

        # Sort swing points by index (oldest first)
        swing_highs.sort(key=lambda x: x['index'])
        swing_lows.sort(key=lambda x: x['index'])

        # Look for double top pattern (bearish)
        double_top_patterns = []

        # We need at least 2 swing highs and 1 swing low to form a double top
        for i in range(len(swing_highs) - 1):
            for j in range(i + 1, len(swing_highs)):
                # Get potential first and second tops
                first_top = swing_highs[i]
                second_top = swing_highs[j]

                # Check if tops are at similar heights
                price_diff = abs(first_top['price'] - second_top['price'])
                avg_price = (first_top['price'] + second_top['price']) / 2
                if price_diff / avg_price > price_tolerance:
                    continue

                # Check if pattern length is within acceptable range
                pattern_length = second_top['index'] - first_top['index']
                if pattern_length < min_pattern_length or pattern_length > max_pattern_length:
                    continue

                # Find the lowest point between the two tops
                middle_low = None
                middle_low_price = float('inf')

                for low in swing_lows:
                    if first_top['index'] < low['index'] < second_top['index']:
                        if low['price'] < middle_low_price:
                            middle_low_price = low['price']
                            middle_low = low

                # If we couldn't find a middle low, skip this pattern
                if middle_low is None:
                    continue

                # Check if the middle low is significantly lower than the tops
                # Typically, the middle low should be at least 3% lower than the tops
                if (avg_price - middle_low_price) / avg_price < 0.03:
                    continue

                # Define the neckline as the middle low
                neckline_price = middle_low_price

                # Check if price has broken the neckline after the second top
                breakout_confirmed = False
                breakout_idx = None

                for idx in range(second_top['index'] + 1, len(df)):
                    if df.iloc[idx][close_col] < neckline_price:
                        breakout_confirmed = True
                        breakout_idx = idx
                        break

                # Calculate pattern height and target
                pattern_height = avg_price - neckline_price
                target_price = neckline_price - pattern_height

                # Check volume pattern (typically, volume should be lower on the second top)
                volume_pattern_valid = True
                if volume_confirmation and 'Volume' in df.columns:
                    first_top_volume = df.iloc[first_top['index']]['Volume']
                    second_top_volume = df.iloc[second_top['index']]['Volume']
                    volume_pattern_valid = second_top_volume < first_top_volume

                # Calculate confidence score
                confidence = 0.5  # Base confidence

                # Adjust confidence based on various factors

                # 1. Price similarity between tops
                price_similarity = 1 - (price_diff / avg_price)
                confidence += price_similarity * 0.1

                # 2. Pattern symmetry (time between tops)
                # Ideally, the time between the first top and middle low should be similar to
                # the time between the middle low and second top
                first_segment = middle_low['index'] - first_top['index']
                second_segment = second_top['index'] - middle_low['index']
                time_ratio = min(first_segment, second_segment) / max(first_segment, second_segment)
                confidence += time_ratio * 0.1

                # 3. Volume pattern
                if volume_pattern_valid:
                    confidence += 0.1

                # 4. Breakout confirmation
                if breakout_confirmed:
                    confidence += 0.2

                # Ensure confidence doesn't exceed 1.0
                confidence = min(confidence, 1.0)

                # Add pattern to list
                double_top_patterns.append({
                    'pattern': 'double_top',
                    'confidence': confidence,
                    'direction': 'bearish',
                    'points': [
                        {'name': 'first_top', 'index': first_top['index'], 'price': first_top['price'], 'date': first_top['date']},
                        {'name': 'middle_low', 'index': middle_low['index'], 'price': middle_low_price, 'date': middle_low['date']},
                        {'name': 'second_top', 'index': second_top['index'], 'price': second_top['price'], 'date': second_top['date']}
                    ],
                    'neckline': {
                        'price': neckline_price
                    },
                    'target': target_price,
                    'breakout_confirmed': breakout_confirmed,
                    'breakout_index': breakout_idx,
                    'volume_pattern_valid': volume_pattern_valid
                })

        # Look for double bottom pattern (bullish)
        double_bottom_patterns = []

        # We need at least 2 swing lows and 1 swing high to form a double bottom
        for i in range(len(swing_lows) - 1):
            for j in range(i + 1, len(swing_lows)):
                # Get potential first and second bottoms
                first_bottom = swing_lows[i]
                second_bottom = swing_lows[j]

                # Check if bottoms are at similar heights
                price_diff = abs(first_bottom['price'] - second_bottom['price'])
                avg_price = (first_bottom['price'] + second_bottom['price']) / 2
                if price_diff / avg_price > price_tolerance:
                    continue

                # Check if pattern length is within acceptable range
                pattern_length = second_bottom['index'] - first_bottom['index']
                if pattern_length < min_pattern_length or pattern_length > max_pattern_length:
                    continue

                # Find the highest point between the two bottoms
                middle_high = None
                middle_high_price = float('-inf')

                for high in swing_highs:
                    if first_bottom['index'] < high['index'] < second_bottom['index']:
                        if high['price'] > middle_high_price:
                            middle_high_price = high['price']
                            middle_high = high

                # If we couldn't find a middle high, skip this pattern
                if middle_high is None:
                    continue

                # Check if the middle high is significantly higher than the bottoms
                # Typically, the middle high should be at least 3% higher than the bottoms
                if (middle_high_price - avg_price) / avg_price < 0.03:
                    continue

                # Define the neckline as the middle high
                neckline_price = middle_high_price

                # Check if price has broken the neckline after the second bottom
                breakout_confirmed = False
                breakout_idx = None

                for idx in range(second_bottom['index'] + 1, len(df)):
                    if df.iloc[idx][close_col] > neckline_price:
                        breakout_confirmed = True
                        breakout_idx = idx
                        break

                # Calculate pattern height and target
                pattern_height = neckline_price - avg_price
                target_price = neckline_price + pattern_height

                # Check volume pattern (typically, volume should be higher on the second bottom)
                volume_pattern_valid = True
                if volume_confirmation and 'Volume' in df.columns:
                    first_bottom_volume = df.iloc[first_bottom['index']]['Volume']
                    second_bottom_volume = df.iloc[second_bottom['index']]['Volume']
                    volume_pattern_valid = second_bottom_volume > first_bottom_volume

                # Calculate confidence score
                confidence = 0.5  # Base confidence

                # Adjust confidence based on various factors

                # 1. Price similarity between bottoms
                price_similarity = 1 - (price_diff / avg_price)
                confidence += price_similarity * 0.1

                # 2. Pattern symmetry (time between bottoms)
                # Ideally, the time between the first bottom and middle high should be similar to
                # the time between the middle high and second bottom
                first_segment = middle_high['index'] - first_bottom['index']
                second_segment = second_bottom['index'] - middle_high['index']
                time_ratio = min(first_segment, second_segment) / max(first_segment, second_segment)
                confidence += time_ratio * 0.1

                # 3. Volume pattern
                if volume_pattern_valid:
                    confidence += 0.1

                # 4. Breakout confirmation
                if breakout_confirmed:
                    confidence += 0.2

                # Ensure confidence doesn't exceed 1.0
                confidence = min(confidence, 1.0)

                # Add pattern to list
                double_bottom_patterns.append({
                    'pattern': 'double_bottom',
                    'confidence': confidence,
                    'direction': 'bullish',
                    'points': [
                        {'name': 'first_bottom', 'index': first_bottom['index'], 'price': first_bottom['price'], 'date': first_bottom['date']},
                        {'name': 'middle_high', 'index': middle_high['index'], 'price': middle_high_price, 'date': middle_high['date']},
                        {'name': 'second_bottom', 'index': second_bottom['index'], 'price': second_bottom['price'], 'date': second_bottom['date']}
                    ],
                    'neckline': {
                        'price': neckline_price
                    },
                    'target': target_price,
                    'breakout_confirmed': breakout_confirmed,
                    'breakout_index': breakout_idx,
                    'volume_pattern_valid': volume_pattern_valid
                })

        # Combine all patterns and sort by confidence
        all_patterns = double_top_patterns + double_bottom_patterns
        all_patterns.sort(key=lambda x: x['confidence'], reverse=True)

        # Return the highest confidence pattern, or empty result if none found
        if all_patterns:
            best_pattern = all_patterns[0]

            # Add analysis text
            if best_pattern['pattern'] == 'double_top':
                if best_pattern['breakout_confirmed']:
                    analysis = (
                        f"Bearish Double Top pattern detected with {best_pattern['confidence']:.0%} confidence. "
                        f"Neckline has been broken, suggesting a potential downward move. "
                        f"Price target: {best_pattern['target']:.2f}"
                    )
                else:
                    analysis = (
                        f"Bearish Double Top pattern detected with {best_pattern['confidence']:.0%} confidence. "
                        f"Watching for a break below the neckline at {best_pattern['neckline']['price']:.2f}. "
                        f"If confirmed, price target: {best_pattern['target']:.2f}"
                    )
            else:  # double_bottom
                if best_pattern['breakout_confirmed']:
                    analysis = (
                        f"Bullish Double Bottom pattern detected with {best_pattern['confidence']:.0%} confidence. "
                        f"Neckline has been broken, suggesting a potential upward move. "
                        f"Price target: {best_pattern['target']:.2f}"
                    )
                else:
                    analysis = (
                        f"Bullish Double Bottom pattern detected with {best_pattern['confidence']:.0%} confidence. "
                        f"Watching for a break above the neckline at {best_pattern['neckline']['price']:.2f}. "
                        f"If confirmed, price target: {best_pattern['target']:.2f}"
                    )

            # Add volume analysis
            if 'volume_pattern_valid' in best_pattern:
                if best_pattern['pattern'] == 'double_top':
                    if best_pattern['volume_pattern_valid']:
                        analysis += " Volume pattern supports the formation (lower volume on second top)."
                    else:
                        analysis += " Note: Volume pattern does not fully support the formation (higher volume on second top)."
                else:  # double_bottom
                    if best_pattern['volume_pattern_valid']:
                        analysis += " Volume pattern supports the formation (higher volume on second bottom)."
                    else:
                        analysis += " Note: Volume pattern does not fully support the formation (lower volume on second bottom)."

            best_pattern['analysis'] = analysis
            return best_pattern
        else:
            return {
                'pattern': 'none',
                'confidence': 0,
                'direction': 'none',
                'points': [],
                'neckline': {},
                'target': None,
                'analysis': 'No double top/bottom pattern detected'
            }

    except Exception as e:
        logger.error(f"Error identifying double top/bottom pattern: {str(e)}")
        return {
            'pattern': 'error',
            'confidence': 0,
            'direction': 'none',
            'points': [],
            'neckline': {},
            'target': None,
            'analysis': f'Error: {str(e)}'
        }


def identify_triangle_pattern(
    df: pd.DataFrame,
    high_col: str = 'High',
    low_col: str = 'Low',
    close_col: str = 'Close',
    window: int = 5,
    min_pattern_length: int = 15,
    max_pattern_length: int = 100,
    min_touches: int = 5
) -> Dict:
    """
    Identify triangle patterns in price data (ascending, descending, and symmetrical).

    Args:
        df: DataFrame with price data
        high_col: Name of the high price column
        low_col: Name of the low price column
        close_col: Name of the close price column
        window: Window size for identifying swing points
        min_pattern_length: Minimum number of bars for a valid pattern
        max_pattern_length: Maximum number of bars for a valid pattern
        min_touches: Minimum number of touches of the trendlines

    Returns:
        Dictionary with pattern information
    """
    try:
        # Find swing points
        swing_highs, swing_lows = find_swing_points(df, high_col, low_col, window)

        # If not enough swing points, return empty result
        if len(swing_highs) < 3 or len(swing_lows) < 3:
            return {
                'pattern': 'none',
                'confidence': 0,
                'direction': 'none',
                'points': [],
                'trendlines': {},
                'target': None,
                'analysis': 'Insufficient swing points to identify triangle pattern'
            }

        # Sort swing points by index (oldest first)
        swing_highs.sort(key=lambda x: x['index'])
        swing_lows.sort(key=lambda x: x['index'])

        # Find potential triangle patterns
        triangle_patterns = []

        # We need at least 3 swing highs and 3 swing lows to form a triangle
        # Try different combinations of starting points
        for high_start_idx in range(len(swing_highs) - 2):
            for low_start_idx in range(len(swing_lows) - 2):
                # Get starting points for upper and lower trendlines
                high_points = swing_highs[high_start_idx:]
                low_points = swing_lows[low_start_idx:]

                # Need at least 3 points for each trendline
                if len(high_points) < 3 or len(low_points) < 3:
                    continue

                # Calculate upper trendline (connecting swing highs)
                upper_trendline = calculate_trendline(high_points)

                # Calculate lower trendline (connecting swing lows)
                lower_trendline = calculate_trendline(low_points)

                # Skip if either trendline calculation failed
                if not upper_trendline or not lower_trendline:
                    continue

                # Check if trendlines are converging
                if not are_trendlines_converging(upper_trendline, lower_trendline):
                    continue

                # Find the intersection point of the trendlines
                intersection = find_trendline_intersection(upper_trendline, lower_trendline)

                # Skip if intersection is in the past or too far in the future
                if intersection['index'] < max(high_points[0]['index'], low_points[0]['index']) or \
                   intersection['index'] > len(df) + 50:  # Allow some future projection
                    continue

                # Calculate pattern length
                pattern_start_idx = min(high_points[0]['index'], low_points[0]['index'])
                pattern_length = intersection['index'] - pattern_start_idx

                # Check if pattern length is within acceptable range
                if pattern_length < min_pattern_length or pattern_length > max_pattern_length:
                    continue

                # Count how many times price touches each trendline
                upper_touches = count_trendline_touches(high_points, upper_trendline)
                lower_touches = count_trendline_touches(low_points, lower_trendline)

                # Check if there are enough touches
                if upper_touches + lower_touches < min_touches:
                    continue

                # Determine triangle type based on trendline slopes
                upper_slope = upper_trendline['slope']
                lower_slope = lower_trendline['slope']

                if upper_slope < -0.0001 and lower_slope > 0.0001:
                    triangle_type = 'symmetrical'
                    direction = 'neutral'
                elif abs(upper_slope) < 0.0001 and lower_slope > 0.0001:
                    triangle_type = 'ascending'
                    direction = 'bullish'
                elif upper_slope < -0.0001 and abs(lower_slope) < 0.0001:
                    triangle_type = 'descending'
                    direction = 'bearish'
                else:
                    # Not a valid triangle
                    continue

                # Check if price has broken out of the triangle
                breakout_confirmed = False
                breakout_idx = None
                breakout_direction = None

                # Get the last price point
                last_idx = len(df) - 1
                last_price = df.iloc[last_idx][close_col]

                # Calculate expected upper and lower trendline values at the last price point
                upper_value = upper_trendline['slope'] * last_idx + upper_trendline['intercept']
                lower_value = lower_trendline['slope'] * last_idx + lower_trendline['intercept']

                # Check if price has broken out
                if last_price > upper_value * 1.01:  # 1% buffer for breakout confirmation
                    breakout_confirmed = True
                    breakout_direction = 'up'

                    # Find the exact breakout point
                    for i in range(max(high_points[-1]['index'], low_points[-1]['index']), len(df)):
                        upper_value_at_i = upper_trendline['slope'] * i + upper_trendline['intercept']
                        if df.iloc[i][close_col] > upper_value_at_i * 1.01:
                            breakout_idx = i
                            break

                elif last_price < lower_value * 0.99:  # 1% buffer for breakout confirmation
                    breakout_confirmed = True
                    breakout_direction = 'down'

                    # Find the exact breakout point
                    for i in range(max(high_points[-1]['index'], low_points[-1]['index']), len(df)):
                        lower_value_at_i = lower_trendline['slope'] * i + lower_trendline['intercept']
                        if df.iloc[i][close_col] < lower_value_at_i * 0.99:
                            breakout_idx = i
                            break

                # Calculate target price based on pattern height
                # The target is typically the height of the triangle at the start, projected from the breakout point
                pattern_start_upper = upper_trendline['slope'] * pattern_start_idx + upper_trendline['intercept']
                pattern_start_lower = lower_trendline['slope'] * pattern_start_idx + lower_trendline['intercept']
                pattern_height = pattern_start_upper - pattern_start_lower

                target_price = None
                if breakout_confirmed:
                    if breakout_direction == 'up':
                        breakout_price = df.iloc[breakout_idx][close_col]
                        target_price = breakout_price + pattern_height
                    else:  # down
                        breakout_price = df.iloc[breakout_idx][close_col]
                        target_price = breakout_price - pattern_height

                # Calculate confidence score
                confidence = 0.5  # Base confidence

                # Adjust confidence based on various factors

                # 1. Number of touches
                touch_score = min((upper_touches + lower_touches) / min_touches, 1.0) * 0.2
                confidence += touch_score

                # 2. Pattern clarity (how well the points fit the trendlines)
                upper_r2 = upper_trendline.get('r2', 0)
                lower_r2 = lower_trendline.get('r2', 0)
                clarity_score = ((upper_r2 + lower_r2) / 2) * 0.1
                confidence += clarity_score

                # 3. Breakout confirmation
                if breakout_confirmed:
                    # Check if breakout direction matches expected direction
                    if (direction == 'bullish' and breakout_direction == 'up') or \
                       (direction == 'bearish' and breakout_direction == 'down') or \
                       (direction == 'neutral'):
                        confidence += 0.2
                    else:
                        # Breakout in unexpected direction
                        confidence -= 0.1

                # Ensure confidence doesn't exceed 1.0 or go below 0.0
                confidence = max(min(confidence, 1.0), 0.0)

                # Collect points for visualization
                points = []

                # Add high points used for upper trendline
                for i, point in enumerate(high_points):
                    if i < 3:  # Only include the first 3 points for clarity
                        points.append({
                            'name': f'upper_{i+1}',
                            'index': point['index'],
                            'price': point['price'],
                            'date': point['date']
                        })

                # Add low points used for lower trendline
                for i, point in enumerate(low_points):
                    if i < 3:  # Only include the first 3 points for clarity
                        points.append({
                            'name': f'lower_{i+1}',
                            'index': point['index'],
                            'price': point['price'],
                            'date': point['date']
                        })

                # Add intersection point
                points.append({
                    'name': 'apex',
                    'index': intersection['index'],
                    'price': intersection['price'],
                    'date': None  # Will be calculated later if needed
                })

                # Add breakout point if confirmed
                if breakout_confirmed and breakout_idx is not None:
                    points.append({
                        'name': 'breakout',
                        'index': breakout_idx,
                        'price': df.iloc[breakout_idx][close_col],
                        'date': df.index[breakout_idx]
                    })

                # Add pattern to list
                triangle_patterns.append({
                    'pattern': f'{triangle_type}_triangle',
                    'confidence': confidence,
                    'direction': direction,
                    'points': points,
                    'trendlines': {
                        'upper': upper_trendline,
                        'lower': lower_trendline,
                        'intersection': intersection
                    },
                    'breakout': {
                        'confirmed': breakout_confirmed,
                        'direction': breakout_direction,
                        'index': breakout_idx
                    },
                    'target': target_price,
                    'touches': {
                        'upper': upper_touches,
                        'lower': lower_touches
                    }
                })

        # Sort patterns by confidence
        triangle_patterns.sort(key=lambda x: x['confidence'], reverse=True)

        # Return the highest confidence pattern, or empty result if none found
        if triangle_patterns:
            best_pattern = triangle_patterns[0]

            # Add analysis text
            pattern_name = best_pattern['pattern'].replace('_', ' ').title()

            if best_pattern['breakout']['confirmed']:
                breakout_direction = best_pattern['breakout']['direction']
                analysis = (
                    f"{pattern_name} detected with {best_pattern['confidence']:.0%} confidence. "
                    f"Breakout {breakout_direction} has been confirmed. "
                )
                if best_pattern['target'] is not None:
                    analysis += f"Price target: {best_pattern['target']:.2f}"
            else:
                analysis = (
                    f"{pattern_name} detected with {best_pattern['confidence']:.0%} confidence. "
                    f"Watching for a breakout. "
                )

                # Add expected direction based on pattern type
                if best_pattern['direction'] == 'bullish':
                    analysis += "This pattern typically resolves to the upside."
                elif best_pattern['direction'] == 'bearish':
                    analysis += "This pattern typically resolves to the downside."
                else:  # neutral
                    analysis += "This pattern can break in either direction. Watch for the breakout direction."

            # Add touch count information
            upper_touches = best_pattern['touches']['upper']
            lower_touches = best_pattern['touches']['lower']
            analysis += f" The pattern has {upper_touches} touches on the upper trendline and {lower_touches} touches on the lower trendline."

            best_pattern['analysis'] = analysis

            # Calculate dates for points that don't have them (like the apex)
            for point in best_pattern['points']:
                if point['date'] is None and point['index'] < len(df):
                    point['date'] = df.index[point['index']]
                elif point['date'] is None:
                    # For future points, use the last date plus some days
                    days_ahead = point['index'] - len(df) + 1
                    if isinstance(df.index[0], pd.Timestamp):
                        point['date'] = df.index[-1] + pd.Timedelta(days=days_ahead)
                    else:
                        point['date'] = None

            return best_pattern
        else:
            return {
                'pattern': 'none',
                'confidence': 0,
                'direction': 'none',
                'points': [],
                'trendlines': {},
                'target': None,
                'analysis': 'No triangle pattern detected'
            }

    except Exception as e:
        logger.error(f"Error identifying triangle pattern: {str(e)}")
        return {
            'pattern': 'error',
            'confidence': 0,
            'direction': 'none',
            'points': [],
            'trendlines': {},
            'target': None,
            'analysis': f'Error: {str(e)}'
        }


def calculate_trendline(points: List[Dict]) -> Dict:
    """
    Calculate a trendline from a list of points.

    Args:
        points: List of points with 'index' and 'price' keys

    Returns:
        Dictionary with trendline information (slope, intercept, r2)
    """
    try:
        # Need at least 2 points to calculate a trendline
        if len(points) < 2:
            return None

        # Extract x and y values
        x = np.array([point['index'] for point in points])
        y = np.array([point['price'] for point in points])

        # Calculate linear regression
        slope, intercept = np.polyfit(x, y, 1)

        # Calculate R-squared (coefficient of determination)
        y_pred = slope * x + intercept
        ss_total = np.sum((y - np.mean(y)) ** 2)
        ss_residual = np.sum((y - y_pred) ** 2)
        r2 = 1 - (ss_residual / ss_total) if ss_total != 0 else 0

        return {
            'slope': slope,
            'intercept': intercept,
            'r2': r2
        }

    except Exception as e:
        logger.error(f"Error calculating trendline: {str(e)}")
        return None


def are_trendlines_converging(upper_trendline: Dict, lower_trendline: Dict) -> bool:
    """
    Check if two trendlines are converging.

    Args:
        upper_trendline: Upper trendline dictionary with slope and intercept
        lower_trendline: Lower trendline dictionary with slope and intercept

    Returns:
        True if trendlines are converging, False otherwise
    """
    try:
        # For trendlines to converge, they must have different slopes
        if upper_trendline['slope'] == lower_trendline['slope']:
            return False

        # For a triangle, the upper trendline should have a negative or flat slope
        # and the lower trendline should have a positive or flat slope
        if upper_trendline['slope'] > 0.0001 or lower_trendline['slope'] < -0.0001:
            return False

        # At least one of the trendlines should have a non-zero slope
        if abs(upper_trendline['slope']) < 0.0001 and abs(lower_trendline['slope']) < 0.0001:
            return False

        return True

    except Exception as e:
        logger.error(f"Error checking if trendlines are converging: {str(e)}")
        return False


def find_trendline_intersection(line1: Dict, line2: Dict) -> Dict:
    """
    Find the intersection point of two trendlines.

    Args:
        line1: First trendline dictionary with slope and intercept
        line2: Second trendline dictionary with slope and intercept

    Returns:
        Dictionary with intersection point information (index, price)
    """
    try:
        # For parallel lines (same slope), there is no intersection
        if line1['slope'] == line2['slope']:
            return None

        # Calculate intersection point
        x = (line2['intercept'] - line1['intercept']) / (line1['slope'] - line2['slope'])
        y = line1['slope'] * x + line1['intercept']

        return {
            'index': int(x),
            'price': y
        }

    except Exception as e:
        logger.error(f"Error finding trendline intersection: {str(e)}")
        return None


def count_trendline_touches(points: List[Dict], trendline: Dict) -> int:
    """
    Count how many times price touches a trendline.

    Args:
        points: List of points with 'index' and 'price' keys
        trendline: Trendline dictionary with slope and intercept

    Returns:
        Number of touches
    """
    try:
        touches = 0

        for point in points:
            # Calculate expected trendline value at this index
            expected_value = trendline['slope'] * point['index'] + trendline['intercept']

            # Check if point is close to trendline (within 0.5%)
            actual_value = point['price']
            percent_diff = abs(actual_value - expected_value) / expected_value

            if percent_diff < 0.005:
                touches += 1

        return touches

    except Exception as e:
        logger.error(f"Error counting trendline touches: {str(e)}")
        return 0


def identify_flag_pennant_pattern(
    df: pd.DataFrame,
    high_col: str = 'High',
    low_col: str = 'Low',
    close_col: str = 'Close',
    volume_col: str = 'Volume',
    window: int = 5,
    min_pole_length: int = 5,
    max_pole_length: int = 20,
    min_flag_length: int = 5,
    max_flag_length: int = 20,
    min_pole_height_pct: float = 0.1,  # Minimum 10% move for the pole
    max_flag_height_pct: float = 0.5   # Flag should be less than 50% of pole height
) -> Dict:
    """
    Identify flag and pennant patterns in price data.

    Args:
        df: DataFrame with price data
        high_col: Name of the high price column
        low_col: Name of the low price column
        close_col: Name of the close price column
        volume_col: Name of the volume column
        window: Window size for identifying swing points
        min_pole_length: Minimum number of bars for the pole
        max_pole_length: Maximum number of bars for the pole
        min_flag_length: Minimum number of bars for the flag/pennant
        max_flag_length: Maximum number of bars for the flag/pennant
        min_pole_height_pct: Minimum height of the pole as percentage of price
        max_flag_height_pct: Maximum height of the flag as percentage of pole height

    Returns:
        Dictionary with pattern information
    """
    try:
        # Find swing points
        swing_highs, swing_lows = find_swing_points(df, high_col, low_col, window)

        # If not enough swing points, return empty result
        if len(swing_highs) < 2 or len(swing_lows) < 2:
            return {
                'pattern': 'none',
                'confidence': 0,
                'direction': 'none',
                'points': [],
                'trendlines': {},
                'target': None,
                'analysis': 'Insufficient swing points to identify flag/pennant pattern'
            }

        # Sort swing points by index (oldest first)
        swing_highs.sort(key=lambda x: x['index'])
        swing_lows.sort(key=lambda x: x['index'])

        # Find potential flag/pennant patterns
        patterns = []

        # Look for bullish flags/pennants (uptrend followed by consolidation)
        for i in range(len(df) - min_pole_length - min_flag_length):
            # Check if there's a strong upward move (pole)
            pole_start_idx = i
            pole_end_idx = i + min_pole_length

            # Ensure pole_end_idx is within range
            if pole_end_idx >= len(df):
                continue

            # Calculate pole height
            pole_start_price = df.iloc[pole_start_idx][low_col]
            pole_end_price = df.iloc[pole_end_idx][high_col]
            pole_height = pole_end_price - pole_start_price

            # Check if pole is significant enough
            avg_price = (pole_start_price + pole_end_price) / 2
            if pole_height / avg_price < min_pole_height_pct:
                continue

            # Check if pole length is within acceptable range
            pole_length = pole_end_idx - pole_start_idx
            if pole_length < min_pole_length or pole_length > max_pole_length:
                continue

            # Check if volume increased during the pole formation
            pole_volume = df.iloc[pole_start_idx:pole_end_idx+1][volume_col].mean()
            pre_pole_volume = df.iloc[max(0, pole_start_idx-pole_length):pole_start_idx][volume_col].mean() if pole_start_idx > 0 else pole_volume
            volume_increased = pole_volume > pre_pole_volume

            # Look for consolidation after the pole (flag/pennant)
            flag_start_idx = pole_end_idx

            # Try different flag lengths
            for flag_length in range(min_flag_length, min(max_flag_length + 1, len(df) - flag_start_idx)):
                flag_end_idx = flag_start_idx + flag_length

                # Get flag/pennant price data
                flag_df = df.iloc[flag_start_idx:flag_end_idx+1]

                # Calculate flag height
                flag_high = flag_df[high_col].max()
                flag_low = flag_df[low_col].min()
                flag_height = flag_high - flag_low

                # Check if flag height is within acceptable range (not too large compared to pole)
                if flag_height > pole_height * max_flag_height_pct:
                    continue

                # Check if volume decreased during the flag formation
                flag_volume = flag_df[volume_col].mean()
                volume_decreased = flag_volume < pole_volume

                # Determine if it's a flag or pennant
                # Flag: Parallel channel (rectangle or slight downward slope)
                # Pennant: Converging trendlines (triangle)

                # Get swing points within the flag/pennant
                flag_swing_highs = [p for p in swing_highs if flag_start_idx <= p['index'] <= flag_end_idx]
                flag_swing_lows = [p for p in swing_lows if flag_start_idx <= p['index'] <= flag_end_idx]

                # Need at least 2 swing highs and 2 swing lows to form trendlines
                if len(flag_swing_highs) < 2 or len(flag_swing_lows) < 2:
                    continue

                # Calculate upper trendline (connecting swing highs)
                upper_trendline = calculate_trendline(flag_swing_highs)

                # Calculate lower trendline (connecting swing lows)
                lower_trendline = calculate_trendline(flag_swing_lows)

                # Skip if either trendline calculation failed
                if not upper_trendline or not lower_trendline:
                    continue

                # Determine pattern type based on trendline slopes
                upper_slope = upper_trendline['slope']
                lower_slope = lower_trendline['slope']

                # Flag: Both trendlines have similar slopes (parallel)
                # Pennant: Converging trendlines
                is_pennant = False
                is_flag = False

                if abs(upper_slope - lower_slope) < 0.001:  # Parallel trendlines (flag)
                    is_flag = True
                    pattern_type = 'bullish_flag'
                elif upper_slope < 0 and lower_slope > 0:  # Converging trendlines (pennant)
                    is_pennant = True
                    pattern_type = 'bullish_pennant'
                else:
                    # Not a valid flag or pennant
                    continue

                # Check if price has broken out of the flag/pennant
                breakout_confirmed = False
                breakout_idx = None

                # For bullish patterns, look for upward breakout
                for j in range(flag_end_idx + 1, min(flag_end_idx + 10, len(df))):
                    if j >= len(df):
                        break

                    # Calculate expected upper trendline value at this index
                    upper_value = upper_trendline['slope'] * j + upper_trendline['intercept']

                    # Check if price has broken above the upper trendline
                    if df.iloc[j][close_col] > upper_value * 1.01:  # 1% buffer for breakout confirmation
                        breakout_confirmed = True
                        breakout_idx = j
                        break

                # Calculate target price based on pole height
                target_price = None
                if breakout_confirmed:
                    breakout_price = df.iloc[breakout_idx][close_col]
                    target_price = breakout_price + pole_height

                # Calculate confidence score
                confidence = 0.5  # Base confidence

                # Adjust confidence based on various factors

                # 1. Pole strength
                pole_strength = min(pole_height / (avg_price * min_pole_height_pct), 2.0) * 0.1
                confidence += pole_strength

                # 2. Volume pattern
                if volume_increased:
                    confidence += 0.1
                if volume_decreased:
                    confidence += 0.1

                # 3. Flag/pennant quality
                if is_pennant:
                    # For pennants, check trendline convergence
                    upper_r2 = upper_trendline.get('r2', 0)
                    lower_r2 = lower_trendline.get('r2', 0)
                    trendline_quality = ((upper_r2 + lower_r2) / 2) * 0.1
                    confidence += trendline_quality
                else:  # flag
                    # For flags, check if it's a clean channel
                    upper_r2 = upper_trendline.get('r2', 0)
                    lower_r2 = lower_trendline.get('r2', 0)
                    channel_quality = ((upper_r2 + lower_r2) / 2) * 0.1
                    confidence += channel_quality

                # 4. Breakout confirmation
                if breakout_confirmed:
                    confidence += 0.2

                # Ensure confidence doesn't exceed 1.0
                confidence = min(confidence, 1.0)

                # Collect points for visualization
                points = [
                    {
                        'name': 'pole_start',
                        'index': pole_start_idx,
                        'price': pole_start_price,
                        'date': df.index[pole_start_idx]
                    },
                    {
                        'name': 'pole_end',
                        'index': pole_end_idx,
                        'price': pole_end_price,
                        'date': df.index[pole_end_idx]
                    }
                ]

                # Add flag/pennant swing points
                for i, point in enumerate(flag_swing_highs):
                    if i < 3:  # Only include the first 3 points for clarity
                        points.append({
                            'name': f'flag_high_{i+1}',
                            'index': point['index'],
                            'price': point['price'],
                            'date': point['date']
                        })

                for i, point in enumerate(flag_swing_lows):
                    if i < 3:  # Only include the first 3 points for clarity
                        points.append({
                            'name': f'flag_low_{i+1}',
                            'index': point['index'],
                            'price': point['price'],
                            'date': point['date']
                        })

                # Add breakout point if confirmed
                if breakout_confirmed:
                    points.append({
                        'name': 'breakout',
                        'index': breakout_idx,
                        'price': df.iloc[breakout_idx][close_col],
                        'date': df.index[breakout_idx]
                    })

                # Add pattern to list
                patterns.append({
                    'pattern': pattern_type,
                    'confidence': confidence,
                    'direction': 'bullish',
                    'points': points,
                    'trendlines': {
                        'upper': upper_trendline,
                        'lower': lower_trendline
                    },
                    'pole': {
                        'start_idx': pole_start_idx,
                        'end_idx': pole_end_idx,
                        'height': pole_height,
                        'volume_increased': volume_increased
                    },
                    'flag': {
                        'start_idx': flag_start_idx,
                        'end_idx': flag_end_idx,
                        'height': flag_height,
                        'volume_decreased': volume_decreased
                    },
                    'breakout': {
                        'confirmed': breakout_confirmed,
                        'index': breakout_idx
                    },
                    'target': target_price
                })

        # Look for bearish flags/pennants (downtrend followed by consolidation)
        for i in range(len(df) - min_pole_length - min_flag_length):
            # Check if there's a strong downward move (pole)
            pole_start_idx = i
            pole_end_idx = i + min_pole_length

            # Ensure pole_end_idx is within range
            if pole_end_idx >= len(df):
                continue

            # Calculate pole height
            pole_start_price = df.iloc[pole_start_idx][high_col]
            pole_end_price = df.iloc[pole_end_idx][low_col]
            pole_height = pole_start_price - pole_end_price  # Note: positive value for downward move

            # Check if pole is significant enough
            avg_price = (pole_start_price + pole_end_price) / 2
            if pole_height / avg_price < min_pole_height_pct:
                continue

            # Check if pole length is within acceptable range
            pole_length = pole_end_idx - pole_start_idx
            if pole_length < min_pole_length or pole_length > max_pole_length:
                continue

            # Check if volume increased during the pole formation
            pole_volume = df.iloc[pole_start_idx:pole_end_idx+1][volume_col].mean()
            pre_pole_volume = df.iloc[max(0, pole_start_idx-pole_length):pole_start_idx][volume_col].mean() if pole_start_idx > 0 else pole_volume
            volume_increased = pole_volume > pre_pole_volume

            # Look for consolidation after the pole (flag/pennant)
            flag_start_idx = pole_end_idx

            # Try different flag lengths
            for flag_length in range(min_flag_length, min(max_flag_length + 1, len(df) - flag_start_idx)):
                flag_end_idx = flag_start_idx + flag_length

                # Get flag/pennant price data
                flag_df = df.iloc[flag_start_idx:flag_end_idx+1]

                # Calculate flag height
                flag_high = flag_df[high_col].max()
                flag_low = flag_df[low_col].min()
                flag_height = flag_high - flag_low

                # Check if flag height is within acceptable range (not too large compared to pole)
                if flag_height > pole_height * max_flag_height_pct:
                    continue

                # Check if volume decreased during the flag formation
                flag_volume = flag_df[volume_col].mean()
                volume_decreased = flag_volume < pole_volume

                # Get swing points within the flag/pennant
                flag_swing_highs = [p for p in swing_highs if flag_start_idx <= p['index'] <= flag_end_idx]
                flag_swing_lows = [p for p in swing_lows if flag_start_idx <= p['index'] <= flag_end_idx]

                # Need at least 2 swing highs and 2 swing lows to form trendlines
                if len(flag_swing_highs) < 2 or len(flag_swing_lows) < 2:
                    continue

                # Calculate upper trendline (connecting swing highs)
                upper_trendline = calculate_trendline(flag_swing_highs)

                # Calculate lower trendline (connecting swing lows)
                lower_trendline = calculate_trendline(flag_swing_lows)

                # Skip if either trendline calculation failed
                if not upper_trendline or not lower_trendline:
                    continue

                # Determine pattern type based on trendline slopes
                upper_slope = upper_trendline['slope']
                lower_slope = lower_trendline['slope']

                # Flag: Both trendlines have similar slopes (parallel)
                # Pennant: Converging trendlines
                is_pennant = False
                is_flag = False

                if abs(upper_slope - lower_slope) < 0.001:  # Parallel trendlines (flag)
                    is_flag = True
                    pattern_type = 'bearish_flag'
                elif upper_slope > 0 and lower_slope < 0:  # Converging trendlines (pennant)
                    is_pennant = True
                    pattern_type = 'bearish_pennant'
                else:
                    # Not a valid flag or pennant
                    continue

                # Check if price has broken out of the flag/pennant
                breakout_confirmed = False
                breakout_idx = None

                # For bearish patterns, look for downward breakout
                for j in range(flag_end_idx + 1, min(flag_end_idx + 10, len(df))):
                    if j >= len(df):
                        break

                    # Calculate expected lower trendline value at this index
                    lower_value = lower_trendline['slope'] * j + lower_trendline['intercept']

                    # Check if price has broken below the lower trendline
                    if df.iloc[j][close_col] < lower_value * 0.99:  # 1% buffer for breakout confirmation
                        breakout_confirmed = True
                        breakout_idx = j
                        break

                # Calculate target price based on pole height
                target_price = None
                if breakout_confirmed:
                    breakout_price = df.iloc[breakout_idx][close_col]
                    target_price = breakout_price - pole_height

                # Calculate confidence score
                confidence = 0.5  # Base confidence

                # Adjust confidence based on various factors

                # 1. Pole strength
                pole_strength = min(pole_height / (avg_price * min_pole_height_pct), 2.0) * 0.1
                confidence += pole_strength

                # 2. Volume pattern
                if volume_increased:
                    confidence += 0.1
                if volume_decreased:
                    confidence += 0.1

                # 3. Flag/pennant quality
                if is_pennant:
                    # For pennants, check trendline convergence
                    upper_r2 = upper_trendline.get('r2', 0)
                    lower_r2 = lower_trendline.get('r2', 0)
                    trendline_quality = ((upper_r2 + lower_r2) / 2) * 0.1
                    confidence += trendline_quality
                else:  # flag
                    # For flags, check if it's a clean channel
                    upper_r2 = upper_trendline.get('r2', 0)
                    lower_r2 = lower_trendline.get('r2', 0)
                    channel_quality = ((upper_r2 + lower_r2) / 2) * 0.1
                    confidence += channel_quality

                # 4. Breakout confirmation
                if breakout_confirmed:
                    confidence += 0.2

                # Ensure confidence doesn't exceed 1.0
                confidence = min(confidence, 1.0)

                # Collect points for visualization
                points = [
                    {
                        'name': 'pole_start',
                        'index': pole_start_idx,
                        'price': pole_start_price,
                        'date': df.index[pole_start_idx]
                    },
                    {
                        'name': 'pole_end',
                        'index': pole_end_idx,
                        'price': pole_end_price,
                        'date': df.index[pole_end_idx]
                    }
                ]

                # Add flag/pennant swing points
                for i, point in enumerate(flag_swing_highs):
                    if i < 3:  # Only include the first 3 points for clarity
                        points.append({
                            'name': f'flag_high_{i+1}',
                            'index': point['index'],
                            'price': point['price'],
                            'date': point['date']
                        })

                for i, point in enumerate(flag_swing_lows):
                    if i < 3:  # Only include the first 3 points for clarity
                        points.append({
                            'name': f'flag_low_{i+1}',
                            'index': point['index'],
                            'price': point['price'],
                            'date': point['date']
                        })

                # Add breakout point if confirmed
                if breakout_confirmed:
                    points.append({
                        'name': 'breakout',
                        'index': breakout_idx,
                        'price': df.iloc[breakout_idx][close_col],
                        'date': df.index[breakout_idx]
                    })

                # Add pattern to list
                patterns.append({
                    'pattern': pattern_type,
                    'confidence': confidence,
                    'direction': 'bearish',
                    'points': points,
                    'trendlines': {
                        'upper': upper_trendline,
                        'lower': lower_trendline
                    },
                    'pole': {
                        'start_idx': pole_start_idx,
                        'end_idx': pole_end_idx,
                        'height': pole_height,
                        'volume_increased': volume_increased
                    },
                    'flag': {
                        'start_idx': flag_start_idx,
                        'end_idx': flag_end_idx,
                        'height': flag_height,
                        'volume_decreased': volume_decreased
                    },
                    'breakout': {
                        'confirmed': breakout_confirmed,
                        'index': breakout_idx
                    },
                    'target': target_price
                })

        # Sort patterns by confidence
        patterns.sort(key=lambda x: x['confidence'], reverse=True)

        # Return the highest confidence pattern, or empty result if none found
        if patterns:
            best_pattern = patterns[0]

            # Add analysis text
            pattern_name = best_pattern['pattern'].replace('_', ' ').title()

            if best_pattern['breakout']['confirmed']:
                analysis = (
                    f"{pattern_name} detected with {best_pattern['confidence']:.0%} confidence. "
                    f"Breakout has been confirmed. "
                )
                if best_pattern['target'] is not None:
                    analysis += f"Price target: {best_pattern['target']:.2f}"
            else:
                analysis = (
                    f"{pattern_name} detected with {best_pattern['confidence']:.0%} confidence. "
                    f"Watching for a breakout. "
                )

                # Add expected direction based on pattern type
                if 'bullish' in best_pattern['pattern']:
                    analysis += "This pattern typically resolves to the upside."
                else:  # bearish
                    analysis += "This pattern typically resolves to the downside."

            # Add volume analysis
            pole_volume_increased = best_pattern['pole']['volume_increased']
            flag_volume_decreased = best_pattern['flag']['volume_decreased']

            if pole_volume_increased and flag_volume_decreased:
                analysis += " Volume pattern supports the formation (increased during pole, decreased during flag/pennant)."
            elif pole_volume_increased:
                analysis += " Volume increased during the pole formation, which is supportive."
            elif flag_volume_decreased:
                analysis += " Volume decreased during the flag/pennant formation, which is supportive."

            best_pattern['analysis'] = analysis
            return best_pattern
        else:
            return {
                'pattern': 'none',
                'confidence': 0,
                'direction': 'none',
                'points': [],
                'trendlines': {},
                'target': None,
                'analysis': 'No flag or pennant pattern detected'
            }

    except Exception as e:
        logger.error(f"Error identifying flag/pennant pattern: {str(e)}")
        return {
            'pattern': 'error',
            'confidence': 0,
            'direction': 'none',
            'points': [],
            'trendlines': {},
            'target': None,
            'analysis': f'Error: {str(e)}'
        }