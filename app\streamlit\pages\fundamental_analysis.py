"""
Fundamental analysis page for Streamlit app.
"""
import asyncio
import streamlit as st
import pandas as pd
from datetime import date, datetime, timedelta
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots

from app.core.data_collection.fundamental import fundamental_data_collector
from app.core.data_collection.economic import economic_data_collector
from app.core.data_collection.sentiment import sentiment_analysis_collector
from app.streamlit.pages.economic_analysis import show_economic_analysis_page
from app.streamlit.pages.sentiment_analysis import show_sentiment_analysis_page
from app.utils.logging import logger


async def show_fundamental_analysis_page():
    """Show the fundamental analysis page."""
    st.title("Fundamental Analysis")

    # Create main tabs for different analysis categories
    main_tabs = st.tabs([
        "Company Fundamentals",
        "Economic Analysis",
        "Sentiment Analysis"
    ])

    # Company Fundamentals tab
    with main_tabs[0]:
        await show_company_fundamentals()

    # Economic Analysis tab
    with main_tabs[1]:
        await show_economic_analysis_page()

    # Sentiment Analysis tab
    with main_tabs[2]:
        await show_sentiment_analysis_page()


async def show_company_fundamentals():
    """Show company fundamental analysis."""
    st.header("Company Fundamentals")

    # Get user inputs
    with st.sidebar:
        st.header("Company Settings")

        # Get symbol
        symbol = st.text_input(
            "Enter stock symbol",
            value="AAPL"
        ).upper()

        # Get period
        period = st.selectbox(
            "Period",
            options=["annual", "quarter"],
            index=0
        )

        # Get limit
        limit = st.slider(
            "Number of periods",
            min_value=1,
            max_value=10,
            value=5
        )

    # Create tabs for different analysis types
    tabs = st.tabs([
        "Company Profile",
        "Financial Statements",
        "Financial Ratios",
        "Key Metrics",
        "Earnings"
    ])

    # Company Profile tab
    with tabs[0]:
        await show_company_profile(symbol)

    # Financial Statements tab
    with tabs[1]:
        await show_financial_statements(symbol, period, limit)

    # Financial Ratios tab
    with tabs[2]:
        await show_financial_ratios(symbol, period, limit)

    # Key Metrics tab
    with tabs[3]:
        await show_key_metrics(symbol, period, limit)

    # Earnings tab
    with tabs[4]:
        await show_earnings(symbol, limit)


async def show_company_profile(symbol: str):
    """Show company profile information."""
    st.header("Company Profile")

    with st.spinner(f"Loading company profile for {symbol}..."):
        try:
            # Get company profile
            profile = await fundamental_data_collector.get_company_profile(symbol)

            if not profile:
                st.error(f"No company profile found for {symbol}")
                return

            # Display company information
            col1, col2 = st.columns([1, 2])

            with col1:
                # Display company logo if available
                if 'image' in profile:
                    st.image(profile['image'], width=150)

                # Display basic info
                st.subheader(profile.get('name', symbol))
                st.write(f"**Symbol:** {profile.get('symbol', symbol)}")
                st.write(f"**Exchange:** {profile.get('exchange', 'N/A')}")
                st.write(f"**Industry:** {profile.get('industry', 'N/A')}")
                st.write(f"**Sector:** {profile.get('sector', 'N/A')}")

            with col2:
                # Display company description
                st.subheader("Description")
                st.write(profile.get('description', 'No description available.'))

                # Display additional info
                st.subheader("Additional Information")
                st.write(f"**CEO:** {profile.get('ceo', 'N/A')}")
                st.write(f"**Employees:** {profile.get('fullTimeEmployees', 'N/A')}")
                st.write(f"**Website:** {profile.get('website', 'N/A')}")
                st.write(f"**Country:** {profile.get('country', 'N/A')}")

            # Display financial overview
            st.subheader("Financial Overview")

            # Create columns for financial metrics
            metrics_cols = st.columns(4)

            with metrics_cols[0]:
                st.metric(
                    "Market Cap",
                    f"${profile.get('mktCap', 0):,.0f}"
                )

            with metrics_cols[1]:
                st.metric(
                    "Price",
                    f"${profile.get('price', 0):,.2f}"
                )

            with metrics_cols[2]:
                st.metric(
                    "P/E Ratio",
                    f"{profile.get('pe', 'N/A')}"
                )

            with metrics_cols[3]:
                st.metric(
                    "Dividend Yield",
                    f"{profile.get('dividendYield', 0) * 100:.2f}%"
                    if profile.get('dividendYield') is not None else "N/A"
                )

        except Exception as e:
            st.error(f"Error loading company profile: {str(e)}")
            logger.error(f"Error loading company profile for {symbol}: {str(e)}")


async def show_financial_statements(symbol: str, period: str, limit: int):
    """Show financial statements."""
    st.header("Financial Statements")

    # Create tabs for different financial statements
    statement_tabs = st.tabs([
        "Income Statement",
        "Balance Sheet",
        "Cash Flow"
    ])

    # Income Statement tab
    with statement_tabs[0]:
        await show_income_statement(symbol, period, limit)

    # Balance Sheet tab
    with statement_tabs[1]:
        await show_balance_sheet(symbol, period, limit)

    # Cash Flow tab
    with statement_tabs[2]:
        await show_cash_flow(symbol, period, limit)


async def show_income_statement(symbol: str, period: str, limit: int):
    """Show income statement."""
    st.subheader("Income Statement")

    with st.spinner(f"Loading income statement for {symbol}..."):
        try:
            # Get income statement
            df = await fundamental_data_collector.get_income_statement(symbol, period, limit)

            if df.empty:
                st.error(f"No income statement found for {symbol}")
                return

            # Display income statement
            st.dataframe(df)

            # Plot revenue and net income
            if 'revenue' in df.columns and 'netIncome' in df.columns:
                st.subheader("Revenue and Net Income")

                fig = go.Figure()

                # Add revenue bar
                fig.add_trace(
                    go.Bar(
                        x=df.index,
                        y=df['revenue'],
                        name="Revenue",
                        marker_color="blue"
                    )
                )

                # Add net income bar
                fig.add_trace(
                    go.Bar(
                        x=df.index,
                        y=df['netIncome'],
                        name="Net Income",
                        marker_color="green"
                    )
                )

                # Update layout
                fig.update_layout(
                    title=f"{symbol} Revenue and Net Income",
                    xaxis_title="Date",
                    yaxis_title="Amount ($)",
                    barmode="group"
                )

                st.plotly_chart(fig, use_container_width=True)

        except Exception as e:
            st.error(f"Error loading income statement: {str(e)}")
            logger.error(f"Error loading income statement for {symbol}: {str(e)}")


async def show_balance_sheet(symbol: str, period: str, limit: int):
    """Show balance sheet."""
    st.subheader("Balance Sheet")

    with st.spinner(f"Loading balance sheet for {symbol}..."):
        try:
            # Get balance sheet
            df = await fundamental_data_collector.get_balance_sheet(symbol, period, limit)

            if df.empty:
                st.error(f"No balance sheet found for {symbol}")
                return

            # Display balance sheet
            st.dataframe(df)

            # Plot assets, liabilities, and equity
            if all(col in df.columns for col in ['totalAssets', 'totalLiabilities', 'totalStockholdersEquity']):
                st.subheader("Assets, Liabilities, and Equity")

                fig = go.Figure()

                # Add assets bar
                fig.add_trace(
                    go.Bar(
                        x=df.index,
                        y=df['totalAssets'],
                        name="Total Assets",
                        marker_color="blue"
                    )
                )

                # Add liabilities bar
                fig.add_trace(
                    go.Bar(
                        x=df.index,
                        y=df['totalLiabilities'],
                        name="Total Liabilities",
                        marker_color="red"
                    )
                )

                # Add equity bar
                fig.add_trace(
                    go.Bar(
                        x=df.index,
                        y=df['totalStockholdersEquity'],
                        name="Total Equity",
                        marker_color="green"
                    )
                )

                # Update layout
                fig.update_layout(
                    title=f"{symbol} Assets, Liabilities, and Equity",
                    xaxis_title="Date",
                    yaxis_title="Amount ($)",
                    barmode="group"
                )

                st.plotly_chart(fig, use_container_width=True)

        except Exception as e:
            st.error(f"Error loading balance sheet: {str(e)}")
            logger.error(f"Error loading balance sheet for {symbol}: {str(e)}")


async def show_cash_flow(symbol: str, period: str, limit: int):
    """Show cash flow statement."""
    st.subheader("Cash Flow Statement")

    with st.spinner(f"Loading cash flow statement for {symbol}..."):
        try:
            # Get cash flow statement
            df = await fundamental_data_collector.get_cash_flow(symbol, period, limit)

            if df.empty:
                st.error(f"No cash flow statement found for {symbol}")
                return

            # Display cash flow statement
            st.dataframe(df)

            # Plot operating, investing, and financing cash flows
            cash_flow_columns = [
                'netCashProvidedByOperatingActivities',
                'netCashUsedForInvestingActivites',
                'netCashUsedProvidedByFinancingActivities'
            ]

            # Check if columns exist (column names might vary)
            available_columns = [col for col in cash_flow_columns if col in df.columns]

            if available_columns:
                st.subheader("Cash Flows")

                fig = go.Figure()

                colors = ["blue", "red", "green"]

                for i, col in enumerate(available_columns):
                    # Add cash flow bar
                    fig.add_trace(
                        go.Bar(
                            x=df.index,
                            y=df[col],
                            name=col.replace("netCash", "").replace("Activities", ""),
                            marker_color=colors[i % len(colors)]
                        )
                    )

                # Update layout
                fig.update_layout(
                    title=f"{symbol} Cash Flows",
                    xaxis_title="Date",
                    yaxis_title="Amount ($)",
                    barmode="group"
                )

                st.plotly_chart(fig, use_container_width=True)

        except Exception as e:
            st.error(f"Error loading cash flow statement: {str(e)}")
            logger.error(f"Error loading cash flow statement for {symbol}: {str(e)}")


async def show_financial_ratios(symbol: str, period: str, limit: int):
    """Show financial ratios."""
    st.subheader("Financial Ratios")

    with st.spinner(f"Loading financial ratios for {symbol}..."):
        try:
            # Get financial ratios
            df = await fundamental_data_collector.get_financial_ratios(symbol, period, limit)

            if df.empty:
                st.error(f"No financial ratios found for {symbol}")
                return

            # Display financial ratios
            st.dataframe(df)

            # Create tabs for different ratio categories
            ratio_tabs = st.tabs([
                "Profitability",
                "Liquidity",
                "Solvency",
                "Valuation"
            ])

            # Profitability ratios
            with ratio_tabs[0]:
                profitability_ratios = [
                    'returnOnAssets',
                    'returnOnEquity',
                    'profitMargin',
                    'operatingProfitMargin',
                    'grossProfitMargin'
                ]

                available_ratios = [ratio for ratio in profitability_ratios if ratio in df.columns]

                if available_ratios:
                    st.subheader("Profitability Ratios")

                    fig = go.Figure()

                    for ratio in available_ratios:
                        fig.add_trace(
                            go.Scatter(
                                x=df.index,
                                y=df[ratio],
                                mode='lines+markers',
                                name=ratio
                            )
                        )

                    fig.update_layout(
                        title=f"{symbol} Profitability Ratios",
                        xaxis_title="Date",
                        yaxis_title="Ratio",
                        legend=dict(
                            orientation="h",
                            yanchor="bottom",
                            y=1.02,
                            xanchor="right",
                            x=1
                        )
                    )

                    st.plotly_chart(fig, use_container_width=True)

            # Liquidity ratios
            with ratio_tabs[1]:
                liquidity_ratios = [
                    'currentRatio',
                    'quickRatio',
                    'cashRatio'
                ]

                available_ratios = [ratio for ratio in liquidity_ratios if ratio in df.columns]

                if available_ratios:
                    st.subheader("Liquidity Ratios")

                    fig = go.Figure()

                    for ratio in available_ratios:
                        fig.add_trace(
                            go.Scatter(
                                x=df.index,
                                y=df[ratio],
                                mode='lines+markers',
                                name=ratio
                            )
                        )

                    fig.update_layout(
                        title=f"{symbol} Liquidity Ratios",
                        xaxis_title="Date",
                        yaxis_title="Ratio",
                        legend=dict(
                            orientation="h",
                            yanchor="bottom",
                            y=1.02,
                            xanchor="right",
                            x=1
                        )
                    )

                    st.plotly_chart(fig, use_container_width=True)

            # Solvency ratios
            with ratio_tabs[2]:
                solvency_ratios = [
                    'debtRatio',
                    'debtEquityRatio',
                    'interestCoverage'
                ]

                available_ratios = [ratio for ratio in solvency_ratios if ratio in df.columns]

                if available_ratios:
                    st.subheader("Solvency Ratios")

                    fig = go.Figure()

                    for ratio in available_ratios:
                        fig.add_trace(
                            go.Scatter(
                                x=df.index,
                                y=df[ratio],
                                mode='lines+markers',
                                name=ratio
                            )
                        )

                    fig.update_layout(
                        title=f"{symbol} Solvency Ratios",
                        xaxis_title="Date",
                        yaxis_title="Ratio",
                        legend=dict(
                            orientation="h",
                            yanchor="bottom",
                            y=1.02,
                            xanchor="right",
                            x=1
                        )
                    )

                    st.plotly_chart(fig, use_container_width=True)

            # Valuation ratios
            with ratio_tabs[3]:
                valuation_ratios = [
                    'priceEarningsRatio',
                    'priceToBookRatio',
                    'priceToSalesRatio',
                    'enterpriseValueMultiple'
                ]

                available_ratios = [ratio for ratio in valuation_ratios if ratio in df.columns]

                if available_ratios:
                    st.subheader("Valuation Ratios")

                    fig = go.Figure()

                    for ratio in available_ratios:
                        fig.add_trace(
                            go.Scatter(
                                x=df.index,
                                y=df[ratio],
                                mode='lines+markers',
                                name=ratio
                            )
                        )

                    fig.update_layout(
                        title=f"{symbol} Valuation Ratios",
                        xaxis_title="Date",
                        yaxis_title="Ratio",
                        legend=dict(
                            orientation="h",
                            yanchor="bottom",
                            y=1.02,
                            xanchor="right",
                            x=1
                        )
                    )

                    st.plotly_chart(fig, use_container_width=True)

        except Exception as e:
            st.error(f"Error loading financial ratios: {str(e)}")
            logger.error(f"Error loading financial ratios for {symbol}: {str(e)}")


async def show_key_metrics(symbol: str, period: str, limit: int):
    """Show key metrics."""
    st.subheader("Key Metrics")

    with st.spinner(f"Loading key metrics for {symbol}..."):
        try:
            # Get key metrics
            df = await fundamental_data_collector.get_key_metrics(symbol, period, limit)

            if df.empty:
                st.error(f"No key metrics found for {symbol}")
                return

            # Display key metrics
            st.dataframe(df)

            # Plot selected key metrics
            selected_metrics = [
                'revenuePerShare',
                'netIncomePerShare',
                'operatingCashFlowPerShare',
                'freeCashFlowPerShare',
                'bookValuePerShare',
                'tangibleBookValuePerShare'
            ]

            available_metrics = [metric for metric in selected_metrics if metric in df.columns]

            if available_metrics:
                st.subheader("Per Share Metrics")

                fig = go.Figure()

                for metric in available_metrics:
                    fig.add_trace(
                        go.Scatter(
                            x=df.index,
                            y=df[metric],
                            mode='lines+markers',
                            name=metric
                        )
                    )

                fig.update_layout(
                    title=f"{symbol} Per Share Metrics",
                    xaxis_title="Date",
                    yaxis_title="Value ($)",
                    legend=dict(
                        orientation="h",
                        yanchor="bottom",
                        y=1.02,
                        xanchor="right",
                        x=1
                    )
                )

                st.plotly_chart(fig, use_container_width=True)

        except Exception as e:
            st.error(f"Error loading key metrics: {str(e)}")
            logger.error(f"Error loading key metrics for {symbol}: {str(e)}")


async def show_earnings(symbol: str, limit: int):
    """Show earnings data."""
    st.subheader("Earnings")

    with st.spinner(f"Loading earnings data for {symbol}..."):
        try:
            # Get earnings data
            df = await fundamental_data_collector.get_earnings(symbol, limit)

            if df.empty:
                st.error(f"No earnings data found for {symbol}")
                return

            # Display earnings data
            st.dataframe(df)

            # Plot earnings data
            if 'eps' in df.columns and 'epsEstimated' in df.columns:
                st.subheader("Earnings Per Share (EPS)")

                fig = go.Figure()

                # Add actual EPS bar
                fig.add_trace(
                    go.Bar(
                        x=df.index,
                        y=df['eps'],
                        name="Actual EPS",
                        marker_color="blue"
                    )
                )

                # Add estimated EPS bar
                fig.add_trace(
                    go.Bar(
                        x=df.index,
                        y=df['epsEstimated'],
                        name="Estimated EPS",
                        marker_color="orange"
                    )
                )

                # Add EPS surprise
                if 'epsSurprise' in df.columns:
                    fig.add_trace(
                        go.Scatter(
                            x=df.index,
                            y=df['epsSurprise'],
                            mode='lines+markers',
                            name="EPS Surprise",
                            yaxis="y2"
                        )
                    )

                # Update layout
                fig.update_layout(
                    title=f"{symbol} Earnings Per Share",
                    xaxis_title="Date",
                    yaxis_title="EPS ($)",
                    barmode="group",
                    yaxis2=dict(
                        title="EPS Surprise (%)",
                        overlaying="y",
                        side="right"
                    )
                )

                st.plotly_chart(fig, use_container_width=True)

        except Exception as e:
            st.error(f"Error loading earnings data: {str(e)}")
            logger.error(f"Error loading earnings data for {symbol}: {str(e)}")
