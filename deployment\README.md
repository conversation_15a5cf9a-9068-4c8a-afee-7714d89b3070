# Deployment

This directory contains deployment configurations for the Portfolio Optimization project.

## Directory Structure

- **docker/**: Docker deployment configuration
  - **Dockerfile**: Docker image definition
  - **docker-compose.yml**: Docker Compose configuration
  - **nginx/**: Nginx configuration
    - **nginx.conf**: Nginx configuration file
    - **ssl/**: SSL certificates
  - **scripts/**: Deployment scripts
    - **deploy.sh**: Deployment script
    - **backup.sh**: Backup script
    - **restore.sh**: Restore script

- **kubernetes/**: Kubernetes deployment configuration
  - **api/**: API deployment
  - **streamlit/**: Streamlit deployment
  - **db/**: Database deployment
  - **redis/**: Redis deployment
  - **ingress/**: Ingress configuration

- **scripts/**: Deployment scripts
  - **deploy.sh**: Deployment script
  - **backup.sh**: Backup script
  - **restore.sh**: Restore script

## Docker Deployment

### Prerequisites

- Docker
- Docker Compose

### Development Deployment

1. Build and start the containers:
   ```bash
   cd deployment/docker
   docker-compose up -d
   ```

2. Access the application:
   - API: http://localhost:8000
   - Streamlit: http://localhost:8501

3. Stop the containers:
   ```bash
   docker-compose down
   ```

### Production Deployment

1. Set up environment variables:
   ```bash
   export ENVIRONMENT=production
   export DATABASE_URL=**********************************/portfolio_optimization
   export REDIS_URL=redis://redis:6379/0
   export ALPHA_VANTAGE_API_KEY=your_api_key
   export FINANCIAL_MODELING_PREP_API_KEY=your_api_key
   export JWT_SECRET=your_jwt_secret
   ```

2. Build and start the containers:
   ```bash
   cd deployment/docker
   docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
   ```

3. Access the application:
   - API: https://api.portfolio-optimization.example.com
   - Streamlit: https://app.portfolio-optimization.example.com

4. Stop the containers:
   ```bash
   docker-compose -f docker-compose.yml -f docker-compose.prod.yml down
   ```

## Kubernetes Deployment

### Prerequisites

- Kubernetes cluster
- kubectl
- Helm

### Deployment

1. Set up environment variables:
   ```bash
   export ENVIRONMENT=production
   export DATABASE_URL=**********************************/portfolio_optimization
   export REDIS_URL=redis://redis:6379/0
   export ALPHA_VANTAGE_API_KEY=your_api_key
   export FINANCIAL_MODELING_PREP_API_KEY=your_api_key
   export JWT_SECRET=your_jwt_secret
   ```

2. Deploy the application:
   ```bash
   cd deployment/kubernetes
   kubectl apply -f .
   ```

3. Access the application:
   - API: https://api.portfolio-optimization.example.com
   - Streamlit: https://app.portfolio-optimization.example.com

4. Delete the deployment:
   ```bash
   kubectl delete -f .
   ```

## Backup and Restore

### Backup

1. Backup the database:
   ```bash
   cd deployment/scripts
   ./backup.sh
   ```

2. Backup files:
   ```bash
   cd deployment/scripts
   ./backup_files.sh
   ```

### Restore

1. Restore the database:
   ```bash
   cd deployment/scripts
   ./restore.sh backup_file.sql
   ```

2. Restore files:
   ```bash
   cd deployment/scripts
   ./restore_files.sh backup_file.tar.gz
   ```

## SSL Certificates

### Development

For development, you can generate self-signed SSL certificates:

```bash
cd deployment/docker/nginx/ssl
openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout server.key -out server.crt
```

### Production

For production, you should use Let's Encrypt or a similar service to obtain SSL certificates:

```bash
cd deployment/scripts
./setup_ssl.sh api.portfolio-optimization.example.com app.portfolio-optimization.example.com
```

## Monitoring

### Prometheus and Grafana

1. Deploy Prometheus and Grafana:
   ```bash
   cd deployment/monitoring
   docker-compose up -d
   ```

2. Access the monitoring dashboards:
   - Prometheus: http://localhost:9090
   - Grafana: http://localhost:3000

### Logging

1. Deploy the ELK stack:
   ```bash
   cd deployment/logging
   docker-compose up -d
   ```

2. Access the logging dashboards:
   - Kibana: http://localhost:5601
