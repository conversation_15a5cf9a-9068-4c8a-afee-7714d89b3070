"""
Black-Litterman portfolio optimization model.
"""
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
from scipy import optimize

from app.core.portfolio.optimization.base import OptimizationStrategy
from app.core.portfolio.optimization.mean_variance import MeanVarianceOptimization
from app.utils.constants import DEFAULT_RISK_FREE_RATE
from app.utils.logging import logger


class BlackLittermanOptimization(OptimizationStrategy):
    """
    Black-Litterman portfolio optimization model.
    
    This class implements the Black-Litterman model for incorporating
    investor views into the portfolio optimization process.
    """
    
    def __init__(self, risk_free_rate: float = DEFAULT_RISK_FREE_RATE, tau: float = 0.05):
        """
        Initialize the Black-Litterman optimization model.
        
        Args:
            risk_free_rate: Risk-free rate (default: 2%)
            tau: Scaling parameter for uncertainty in the prior (default: 0.05)
        """
        super().__init__("Black-Litterman")
        self.risk_free_rate = risk_free_rate
        self.tau = tau
        self.mv_optimizer = MeanVarianceOptimization(risk_free_rate=risk_free_rate)
    
    def optimize(
        self,
        returns: pd.DataFrame,
        market_weights: Optional[Dict[str, float]] = None,
        views: Optional[Dict[str, Dict[str, float]]] = None,
        view_confidences: Optional[Dict[str, float]] = None,
        objective: str = "sharpe",
        **kwargs
    ) -> Dict[str, float]:
        """
        Optimize portfolio weights using the Black-Litterman model.
        
        Args:
            returns: DataFrame of asset returns
            market_weights: Dictionary of market capitalization weights
            views: Dictionary of views, where each view is a dictionary mapping assets to weights
            view_confidences: Dictionary mapping view names to confidence levels (0-1)
            objective: Optimization objective for the final portfolio
            **kwargs: Additional parameters
            
        Returns:
            Dictionary mapping asset names to weights
        """
        # Validate inputs
        if returns is None or returns.empty:
            logger.error("Returns must be provided for Black-Litterman optimization")
            return {}
        
        # Get asset names
        assets = returns.columns.tolist()
        n_assets = len(assets)
        
        if n_assets == 0:
            logger.warning("No assets provided for optimization")
            return {}
        
        # Calculate covariance matrix
        cov_matrix = returns.cov()
        
        # Use equal weights if market weights not provided
        if market_weights is None or len(market_weights) == 0:
            logger.warning("No market weights provided, using equal weights")
            market_weights = {asset: 1.0 / n_assets for asset in assets}
        
        # Ensure market weights only include assets in returns
        market_weights = {asset: weight for asset, weight in market_weights.items() if asset in assets}
        
        # Normalize market weights to sum to 1
        total_weight = sum(market_weights.values())
        market_weights = {asset: weight / total_weight for asset, weight in market_weights.items()}
        
        # Convert market weights to numpy array
        market_weights_array = np.array([market_weights.get(asset, 0.0) for asset in assets])
        
        # Calculate implied returns using reverse optimization
        # π = δΣw, where π is implied returns, δ is risk aversion, Σ is covariance, w is weights
        risk_aversion = 2.5  # Typical value
        implied_returns = risk_aversion * np.dot(cov_matrix, market_weights_array)
        
        # If no views provided, use implied returns directly
        if views is None or len(views) == 0:
            logger.info("No views provided, using implied returns")
            # Convert implied returns to expected returns
            expected_returns = pd.Series(implied_returns, index=assets)
            
            # Use mean-variance optimization with the expected returns
            temp_returns = pd.DataFrame({asset: [ret] for asset, ret in zip(assets, implied_returns)})
            return self.mv_optimizer.optimize(temp_returns, objective=objective, **kwargs)
        
        # Process views
        view_names = list(views.keys())
        n_views = len(view_names)
        
        # Create view matrix P and view vector q
        P = np.zeros((n_views, n_assets))
        q = np.zeros(n_views)
        
        for i, view_name in enumerate(view_names):
            view = views[view_name]
            # Normalize view weights to sum to 0 for relative views or 1 for absolute views
            is_relative = sum(view.values()) < 0.001  # Close to 0 indicates relative view
            
            if is_relative:
                # Relative view (e.g., Asset A will outperform Asset B by 2%)
                for asset, weight in view.items():
                    if asset in assets:
                        asset_idx = assets.index(asset)
                        P[i, asset_idx] = weight
                
                # Get the expected return difference from the view
                # This is typically specified in the view itself
                q[i] = view.get('return_difference', 0.02)  # Default to 2% if not specified
            else:
                # Absolute view (e.g., Asset A will return 5%)
                for asset, weight in view.items():
                    if asset in assets:
                        asset_idx = assets.index(asset)
                        P[i, asset_idx] = 1.0
                        q[i] = weight  # The expected return
        
        # Create diagonal matrix of view uncertainties (Omega)
        if view_confidences is None:
            # Default to moderate confidence
            view_confidences = {view_name: 0.5 for view_name in view_names}
        
        # Convert confidence levels to uncertainties (higher confidence = lower uncertainty)
        uncertainties = [1.0 - view_confidences.get(view_name, 0.5) for view_name in view_names]
        
        # Scale uncertainties by the variance of the assets in the view
        omega = np.zeros((n_views, n_views))
        for i, view_name in enumerate(view_names):
            view = views[view_name]
            view_variance = 0.0
            for asset, weight in view.items():
                if asset in assets:
                    asset_idx = assets.index(asset)
                    view_variance += weight**2 * cov_matrix.iloc[asset_idx, asset_idx]
            
            # Set diagonal element of Omega
            omega[i, i] = view_variance * uncertainties[i]
        
        # Calculate posterior expected returns
        # μ_BL = [(τΣ)^(-1) + P'Ω^(-1)P]^(-1) * [(τΣ)^(-1)π + P'Ω^(-1)q]
        
        # Calculate precision matrices
        precision_prior = np.linalg.inv(self.tau * cov_matrix.values)
        precision_views = np.linalg.inv(omega)
        
        # Calculate posterior precision and expected returns
        precision_posterior = precision_prior + np.dot(P.T, np.dot(precision_views, P))
        temp1 = np.dot(precision_prior, implied_returns)
        temp2 = np.dot(P.T, np.dot(precision_views, q))
        expected_returns_bl = np.dot(np.linalg.inv(precision_posterior), temp1 + temp2)
        
        # Convert to pandas Series
        expected_returns = pd.Series(expected_returns_bl, index=assets)
        
        # Use mean-variance optimization with the Black-Litterman expected returns
        temp_returns = pd.DataFrame({asset: [ret] for asset, ret in zip(assets, expected_returns_bl)})
        return self.mv_optimizer.optimize(temp_returns, objective=objective, **kwargs)
    
    def generate_views(
        self,
        returns: pd.DataFrame,
        num_views: int = 3,
        view_type: str = "relative",
        **kwargs
    ) -> Tuple[Dict[str, Dict[str, float]], Dict[str, float]]:
        """
        Generate sample views based on historical returns.
        
        Args:
            returns: DataFrame of asset returns
            num_views: Number of views to generate
            view_type: Type of views to generate ('relative' or 'absolute')
            **kwargs: Additional parameters
            
        Returns:
            Tuple of (views, view_confidences)
        """
        # Validate inputs
        if returns is None or returns.empty:
            logger.error("Returns must be provided for view generation")
            return {}, {}
        
        # Get asset names
        assets = returns.columns.tolist()
        n_assets = len(assets)
        
        if n_assets == 0:
            logger.warning("No assets provided for view generation")
            return {}, {}
        
        # Calculate expected returns and volatilities
        expected_returns = returns.mean()
        volatilities = returns.std()
        
        # Generate views
        views = {}
        view_confidences = {}
        
        if view_type == "relative":
            # Generate relative views (Asset A will outperform Asset B)
            for i in range(min(num_views, n_assets // 2)):
                # Select two random assets
                asset_indices = np.random.choice(n_assets, 2, replace=False)
                asset1 = assets[asset_indices[0]]
                asset2 = assets[asset_indices[1]]
                
                # Calculate expected return difference
                return_diff = expected_returns[asset1] - expected_returns[asset2]
                
                # Add some noise to the view
                noise = np.random.normal(0, abs(return_diff) * 0.2)
                view_return_diff = return_diff + noise
                
                # Create the view
                view_name = f"view_{i+1}"
                views[view_name] = {
                    asset1: 1.0,
                    asset2: -1.0,
                    'return_difference': view_return_diff
                }
                
                # Set confidence based on historical volatility
                combined_vol = (volatilities[asset1]**2 + volatilities[asset2]**2)**0.5
                confidence = max(0.1, min(0.9, 1.0 - combined_vol))
                view_confidences[view_name] = confidence
        
        else:  # absolute views
            # Generate absolute views (Asset A will return X%)
            for i in range(min(num_views, n_assets)):
                # Select a random asset
                asset_idx = np.random.choice(n_assets)
                asset = assets[asset_idx]
                
                # Calculate expected return
                expected_return = expected_returns[asset]
                
                # Add some noise to the view
                noise = np.random.normal(0, abs(expected_return) * 0.2)
                view_return = expected_return + noise
                
                # Create the view
                view_name = f"view_{i+1}"
                views[view_name] = {asset: view_return}
                
                # Set confidence based on historical volatility
                confidence = max(0.1, min(0.9, 1.0 - volatilities[asset]))
                view_confidences[view_name] = confidence
        
        return views, view_confidences
