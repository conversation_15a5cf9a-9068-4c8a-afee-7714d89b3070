"""
Customizable dashboard page for Streamlit app.
"""
import streamlit as st
from datetime import date, datetime, timedelta

from app.streamlit.components.customizable_dashboard import CustomizableDashboard, render_dashboard_selector
from app.utils.logging import logger


async def show_custom_dashboard_page():
    """Show the customizable dashboard page."""
    st.title("Customizable Dashboard")
    
    # Render dashboard selector
    selected_dashboard = render_dashboard_selector()
    
    # Create dashboard
    dashboard = CustomizableDashboard(selected_dashboard)
    
    # Render dashboard manager
    dashboard.render_dashboard_manager()
