"""
Oscillator indicators for technical analysis.
"""
from typing import Optional, Union

import numpy as np
import pandas as pd


def relative_strength_index(
    data: Union[pd.Series, pd.DataFrame],
    window: int = 14,
    column: Optional[str] = None
) -> pd.Series:
    """
    Calculate Relative Strength Index (RSI).
    
    Args:
        data: Price data as Series or DataFrame
        window: Window size for RSI calculation
        column: Column name to use if data is a DataFrame
        
    Returns:
        Series with RSI values
    """
    if isinstance(data, pd.DataFrame) and column:
        series = data[column]
    else:
        series = data
    
    # Calculate price changes
    delta = series.diff()
    
    # Create gain and loss series
    gain = delta.copy()
    loss = delta.copy()
    
    gain[gain < 0] = 0
    loss[loss > 0] = 0
    loss = abs(loss)
    
    # Calculate average gain and average loss
    avg_gain = gain.rolling(window=window).mean()
    avg_loss = loss.rolling(window=window).mean()
    
    # Calculate RS and RSI
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    
    return rsi


def stochastic_oscillator(
    data: pd.DataFrame,
    k_window: int = 14,
    d_window: int = 3,
    high_column: str = 'high',
    low_column: str = 'low',
    close_column: str = 'close'
) -> pd.DataFrame:
    """
    Calculate Stochastic Oscillator.
    
    Args:
        data: Price data as DataFrame with high, low, close columns
        k_window: Window size for %K calculation
        d_window: Window size for %D calculation
        high_column: Column name for high price data
        low_column: Column name for low price data
        close_column: Column name for close price data
        
    Returns:
        DataFrame with %K and %D values
    """
    # Calculate %K
    lowest_low = data[low_column].rolling(window=k_window).min()
    highest_high = data[high_column].rolling(window=k_window).max()
    
    k = 100 * ((data[close_column] - lowest_low) / (highest_high - lowest_low))
    
    # Calculate %D (SMA of %K)
    d = k.rolling(window=d_window).mean()
    
    # Create result DataFrame
    result = pd.DataFrame({
        'k': k,
        'd': d
    }, index=data.index)
    
    return result


def money_flow_index(
    data: pd.DataFrame,
    window: int = 14,
    high_column: str = 'high',
    low_column: str = 'low',
    close_column: str = 'close',
    volume_column: str = 'volume'
) -> pd.Series:
    """
    Calculate Money Flow Index (MFI).
    
    Args:
        data: Price data as DataFrame with high, low, close, volume columns
        window: Window size for MFI calculation
        high_column: Column name for high price data
        low_column: Column name for low price data
        close_column: Column name for close price data
        volume_column: Column name for volume data
        
    Returns:
        Series with MFI values
    """
    # Calculate typical price
    typical_price = (data[high_column] + data[low_column] + data[close_column]) / 3
    
    # Calculate raw money flow
    raw_money_flow = typical_price * data[volume_column]
    
    # Calculate money flow direction
    money_flow_direction = np.sign(typical_price.diff())
    
    # Calculate positive and negative money flow
    positive_flow = raw_money_flow.copy()
    negative_flow = raw_money_flow.copy()
    
    positive_flow[money_flow_direction <= 0] = 0
    negative_flow[money_flow_direction >= 0] = 0
    
    # Calculate money flow ratio
    positive_flow_sum = positive_flow.rolling(window=window).sum()
    negative_flow_sum = negative_flow.rolling(window=window).sum()
    
    money_flow_ratio = positive_flow_sum / negative_flow_sum
    
    # Calculate MFI
    mfi = 100 - (100 / (1 + money_flow_ratio))
    
    return mfi


def commodity_channel_index(
    data: pd.DataFrame,
    window: int = 20,
    constant: float = 0.015,
    high_column: str = 'high',
    low_column: str = 'low',
    close_column: str = 'close'
) -> pd.Series:
    """
    Calculate Commodity Channel Index (CCI).
    
    Args:
        data: Price data as DataFrame with high, low, close columns
        window: Window size for CCI calculation
        constant: Constant multiplier (typically 0.015)
        high_column: Column name for high price data
        low_column: Column name for low price data
        close_column: Column name for close price data
        
    Returns:
        Series with CCI values
    """
    # Calculate typical price
    typical_price = (data[high_column] + data[low_column] + data[close_column]) / 3
    
    # Calculate SMA of typical price
    sma_tp = typical_price.rolling(window=window).mean()
    
    # Calculate mean deviation
    mean_deviation = typical_price.rolling(window=window).apply(
        lambda x: np.sum(np.abs(x - x.mean())) / len(x)
    )
    
    # Calculate CCI
    cci = (typical_price - sma_tp) / (constant * mean_deviation)
    
    return cci


def williams_r(
    data: pd.DataFrame,
    window: int = 14,
    high_column: str = 'high',
    low_column: str = 'low',
    close_column: str = 'close'
) -> pd.Series:
    """
    Calculate Williams %R.
    
    Args:
        data: Price data as DataFrame with high, low, close columns
        window: Window size for Williams %R calculation
        high_column: Column name for high price data
        low_column: Column name for low price data
        close_column: Column name for close price data
        
    Returns:
        Series with Williams %R values
    """
    # Calculate highest high and lowest low
    highest_high = data[high_column].rolling(window=window).max()
    lowest_low = data[low_column].rolling(window=window).min()
    
    # Calculate Williams %R
    williams_r = -100 * ((highest_high - data[close_column]) / (highest_high - lowest_low))
    
    return williams_r


def rate_of_change(
    data: Union[pd.Series, pd.DataFrame],
    window: int = 14,
    column: Optional[str] = None
) -> pd.Series:
    """
    Calculate Rate of Change (ROC).
    
    Args:
        data: Price data as Series or DataFrame
        window: Window size for ROC calculation
        column: Column name to use if data is a DataFrame
        
    Returns:
        Series with ROC values
    """
    if isinstance(data, pd.DataFrame) and column:
        series = data[column]
    else:
        series = data
    
    # Calculate ROC
    roc = 100 * ((series / series.shift(window)) - 1)
    
    return roc


def awesome_oscillator(
    data: pd.DataFrame,
    fast_window: int = 5,
    slow_window: int = 34,
    high_column: str = 'high',
    low_column: str = 'low'
) -> pd.Series:
    """
    Calculate Awesome Oscillator (AO).
    
    Args:
        data: Price data as DataFrame with high and low columns
        fast_window: Window size for fast SMA
        slow_window: Window size for slow SMA
        high_column: Column name for high price data
        low_column: Column name for low price data
        
    Returns:
        Series with AO values
    """
    # Calculate median price
    median_price = (data[high_column] + data[low_column]) / 2
    
    # Calculate fast and slow SMAs
    fast_sma = median_price.rolling(window=fast_window).mean()
    slow_sma = median_price.rolling(window=slow_window).mean()
    
    # Calculate AO
    ao = fast_sma - slow_sma
    
    return ao


def ultimate_oscillator(
    data: pd.DataFrame,
    short_window: int = 7,
    medium_window: int = 14,
    long_window: int = 28,
    short_weight: float = 4.0,
    medium_weight: float = 2.0,
    long_weight: float = 1.0,
    high_column: str = 'high',
    low_column: str = 'low',
    close_column: str = 'close'
) -> pd.Series:
    """
    Calculate Ultimate Oscillator.
    
    Args:
        data: Price data as DataFrame with high, low, close columns
        short_window: Window size for short-term average
        medium_window: Window size for medium-term average
        long_window: Window size for long-term average
        short_weight: Weight for short-term average
        medium_weight: Weight for medium-term average
        long_weight: Weight for long-term average
        high_column: Column name for high price data
        low_column: Column name for low price data
        close_column: Column name for close price data
        
    Returns:
        Series with Ultimate Oscillator values
    """
    # Calculate buying pressure (BP)
    bp = data[close_column] - pd.DataFrame({
        'prev_close': data[close_column].shift(1),
        'low': data[low_column]
    }).min(axis=1)
    
    # Calculate true range (TR)
    tr1 = data[high_column] - data[low_column]
    tr2 = abs(data[high_column] - data[close_column].shift(1))
    tr3 = abs(data[low_column] - data[close_column].shift(1))
    
    tr = pd.DataFrame({
        'tr1': tr1,
        'tr2': tr2,
        'tr3': tr3
    }).max(axis=1)
    
    # Calculate average values for different time periods
    bp_sum_short = bp.rolling(window=short_window).sum()
    tr_sum_short = tr.rolling(window=short_window).sum()
    
    bp_sum_medium = bp.rolling(window=medium_window).sum()
    tr_sum_medium = tr.rolling(window=medium_window).sum()
    
    bp_sum_long = bp.rolling(window=long_window).sum()
    tr_sum_long = tr.rolling(window=long_window).sum()
    
    # Calculate averages
    avg_short = bp_sum_short / tr_sum_short
    avg_medium = bp_sum_medium / tr_sum_medium
    avg_long = bp_sum_long / tr_sum_long
    
    # Calculate Ultimate Oscillator
    total_weight = short_weight + medium_weight + long_weight
    uo = 100 * ((short_weight * avg_short + medium_weight * avg_medium + long_weight * avg_long) / total_weight)
    
    return uo
