"""
Date and time utility functions.
"""
import datetime
from typing import Optional, Tuple

import pandas as pd


def get_date_range(
    period: str = "1y", 
    end_date: Optional[datetime.date] = None
) -> Tuple[datetime.date, datetime.date]:
    """
    Get start and end dates based on a period string.
    
    Args:
        period: A string representing the period (e.g., '1d', '1w', '1m', '1y')
        end_date: The end date (defaults to today)
        
    Returns:
        A tuple of (start_date, end_date)
    """
    if end_date is None:
        end_date = datetime.date.today()
    
    # Parse the period
    value = int(period[:-1])
    unit = period[-1].lower()
    
    if unit == 'd':
        start_date = end_date - datetime.timedelta(days=value)
    elif unit == 'w':
        start_date = end_date - datetime.timedelta(weeks=value)
    elif unit == 'm':
        # Use pandas for month arithmetic
        end_datetime = pd.Timestamp(end_date)
        start_datetime = end_datetime - pd.DateOffset(months=value)
        start_date = start_datetime.date()
    elif unit == 'y':
        # Use pandas for year arithmetic
        end_datetime = pd.Timestamp(end_date)
        start_datetime = end_datetime - pd.DateOffset(years=value)
        start_date = start_datetime.date()
    else:
        raise ValueError(f"Invalid period unit: {unit}. Use 'd', 'w', 'm', or 'y'.")
    
    return start_date, end_date


def is_market_open() -> bool:
    """
    Check if the US stock market is currently open.
    
    Returns:
        True if the market is open, False otherwise
    """
    now = datetime.datetime.now(datetime.timezone(datetime.timedelta(hours=-5)))  # Eastern Time
    
    # Check if it's a weekday
    if now.weekday() >= 5:  # 5 = Saturday, 6 = Sunday
        return False
    
    # Check if it's between 9:30 AM and 4:00 PM ET
    market_open = now.replace(hour=9, minute=30, second=0, microsecond=0)
    market_close = now.replace(hour=16, minute=0, second=0, microsecond=0)
    
    return market_open <= now <= market_close


def get_trading_days(start_date: datetime.date, end_date: datetime.date) -> int:
    """
    Get the number of trading days between two dates.
    
    Args:
        start_date: The start date
        end_date: The end date
        
    Returns:
        The number of trading days
    """
    # Create a date range with business days
    date_range = pd.date_range(start=start_date, end=end_date, freq='B')
    
    # TODO: Filter out holidays
    
    return len(date_range)
