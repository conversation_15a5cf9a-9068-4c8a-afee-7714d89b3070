"""
Database connection and session management.
"""
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

from app.settings import get_settings

# Get settings
settings = get_settings()

# Create engine
engine = create_engine(
    settings.database['url'],
    echo=settings.database['echo']
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create base model
Base = declarative_base()


def get_db():
    """
    Get a database session.
    
    Yields:
        Database session
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def init_db():
    """Initialize the database."""
    # Import models to ensure they are registered with the Base metadata
    from app.db.models import Asset, Portfolio, PriceHistory, Transaction, User, Watchlist
    
    # Create tables
    Base.metadata.create_all(bind=engine)
