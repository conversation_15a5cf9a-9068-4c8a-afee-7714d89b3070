"""
Cache implementation for data collectors.
"""
import pickle
import time
from typing import Any, Optional

import pandas as pd
import redis.asyncio as redis_async

from app.settings import get_settings
from app.utils.logging import logger

# Get settings
settings = get_settings()


class DataCache:
    """
    Cache for data collectors.

    This class provides a simple caching mechanism for data collectors.
    It supports both in-memory caching and file-based caching.
    """

    def __init__(self):
        """Initialize the cache based on settings."""
        self.cache_type = settings.cache['type']
        self.expiry = settings.cache.get('expiry', 3600)  # Default to 1 hour

        if self.cache_type == "redis":
            try:
                self.redis_client = redis_async.from_url(settings.cache['redis_url'])
                logger.info(f"Initialized Redis cache with URL: {settings.cache['redis_url']}")
            except Exception as e:
                logger.error(f"Failed to initialize Redis cache: {e}")
                # Fallback to memory cache
                self.cache_type = "memory"
                self.memory_cache = {}
                self.cache_times = {}
        else:
            logger.info("Initialized memory cache")
            self.memory_cache = {}
            self.cache_times = {}

    async def get(self, key: str) -> Any:
        """
        Get a value from the cache.

        Args:
            key: The cache key

        Returns:
            The cached value or None if not found
        """
        try:
            if self.cache_type == "redis":
                value = await self.redis_client.get(key)
                if value:
                    try:
                        # Try to deserialize as pickle
                        return pickle.loads(value)
                    except:
                        # If not pickle, return as is
                        return value
                return None
            else:
                # Check if key exists and not expired
                if key in self.memory_cache:
                    if key in self.cache_times and self.cache_times[key] < time.time():
                        # Expired, remove and return None
                        del self.memory_cache[key]
                        del self.cache_times[key]
                        return None
                    return self.memory_cache[key]
                return None
        except Exception as e:
            logger.error(f"Error getting from cache: {e}")
            return None

    async def set(self, key: str, value: Any, expiry: Optional[int] = None) -> bool:
        """
        Set a value in the cache.

        Args:
            key: The cache key
            value: The value to cache
            expiry: Optional expiry time in seconds

        Returns:
            True if successful, False otherwise
        """
        if expiry is None:
            expiry = self.expiry

        try:
            if self.cache_type == "redis":
                # Serialize complex objects
                if isinstance(value, (pd.DataFrame, dict, list)):
                    serialized_value = pickle.dumps(value)
                else:
                    serialized_value = value

                if expiry:
                    await self.redis_client.setex(key, expiry, serialized_value)
                else:
                    await self.redis_client.set(key, serialized_value)
            else:
                self.memory_cache[key] = value
                if expiry:
                    self.cache_times[key] = time.time() + expiry
            return True
        except Exception as e:
            logger.error(f"Error setting cache: {e}")
            return False

    async def delete(self, key: str) -> bool:
        """
        Delete a value from the cache.

        Args:
            key: The cache key

        Returns:
            True if successful, False otherwise
        """
        try:
            if self.cache_type == "redis":
                await self.redis_client.delete(key)
            else:
                if key in self.memory_cache:
                    del self.memory_cache[key]
                if key in self.cache_times:
                    del self.cache_times[key]
            return True
        except Exception as e:
            logger.error(f"Error deleting from cache: {e}")
            return False

    async def clear(self) -> bool:
        """
        Clear the entire cache.

        Returns:
            True if successful, False otherwise
        """
        try:
            if self.cache_type == "redis":
                await self.redis_client.flushdb()
            else:
                self.memory_cache.clear()
                self.cache_times.clear()
            return True
        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
            return False

# Create a global cache instance
cache = DataCache()

