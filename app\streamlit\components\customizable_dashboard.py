"""
Customizable dashboard component for Streamlit.
"""
import streamlit as st
import pandas as pd
import numpy as np
from datetime import date, datetime, timedelta
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import json
import os

from app.utils.logging import logger


class CustomizableDashboard:
    """
    Customizable dashboard component for Streamlit.
    
    This class provides methods for creating and managing customizable dashboards.
    """
    
    def __init__(self, dashboard_id: str = "main"):
        """
        Initialize the customizable dashboard.
        
        Args:
            dashboard_id: Unique identifier for the dashboard
        """
        self.dashboard_id = dashboard_id
        self.widgets = {}
        self.layout = {}
        
        # Load dashboard configuration if exists
        self.load_dashboard()
    
    def load_dashboard(self):
        """Load dashboard configuration from session state or file."""
        # Check if dashboard exists in session state
        if f"dashboard_{self.dashboard_id}" in st.session_state:
            dashboard_config = st.session_state[f"dashboard_{self.dashboard_id}"]
            self.widgets = dashboard_config.get("widgets", {})
            self.layout = dashboard_config.get("layout", {})
        else:
            # Try to load from file
            try:
                # Create dashboards directory if it doesn't exist
                os.makedirs("dashboards", exist_ok=True)
                
                # Check if dashboard file exists
                dashboard_file = f"dashboards/{self.dashboard_id}.json"
                if os.path.exists(dashboard_file):
                    with open(dashboard_file, "r") as f:
                        dashboard_config = json.load(f)
                        self.widgets = dashboard_config.get("widgets", {})
                        self.layout = dashboard_config.get("layout", {})
            except Exception as e:
                logger.error(f"Error loading dashboard: {str(e)}")
    
    def save_dashboard(self):
        """Save dashboard configuration to session state and file."""
        # Create dashboard configuration
        dashboard_config = {
            "widgets": self.widgets,
            "layout": self.layout
        }
        
        # Save to session state
        st.session_state[f"dashboard_{self.dashboard_id}"] = dashboard_config
        
        # Save to file
        try:
            # Create dashboards directory if it doesn't exist
            os.makedirs("dashboards", exist_ok=True)
            
            # Save dashboard configuration
            dashboard_file = f"dashboards/{self.dashboard_id}.json"
            with open(dashboard_file, "w") as f:
                json.dump(dashboard_config, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving dashboard: {str(e)}")
    
    def add_widget(self, widget_id: str, widget_type: str, widget_config: dict):
        """
        Add a widget to the dashboard.
        
        Args:
            widget_id: Unique identifier for the widget
            widget_type: Type of widget (e.g., 'chart', 'metric', 'table')
            widget_config: Widget configuration
        """
        self.widgets[widget_id] = {
            "type": widget_type,
            "config": widget_config
        }
        
        # Add to layout if not already present
        if widget_id not in self.layout:
            # Find next available position
            positions = [pos for pos in self.layout.values()]
            next_row = 0
            next_col = 0
            
            if positions:
                # Find maximum row and column
                max_row = max([pos["row"] for pos in positions])
                max_col = max([pos["col"] for pos in positions])
                
                # Find next position
                if max_col < 2:  # Assuming 3 columns
                    next_row = max_row
                    next_col = max_col + 1
                else:
                    next_row = max_row + 1
                    next_col = 0
            
            # Add to layout
            self.layout[widget_id] = {
                "row": next_row,
                "col": next_col,
                "width": 1,
                "height": 1
            }
        
        # Save dashboard
        self.save_dashboard()
    
    def remove_widget(self, widget_id: str):
        """
        Remove a widget from the dashboard.
        
        Args:
            widget_id: Unique identifier for the widget
        """
        # Remove widget
        if widget_id in self.widgets:
            del self.widgets[widget_id]
        
        # Remove from layout
        if widget_id in self.layout:
            del self.layout[widget_id]
        
        # Save dashboard
        self.save_dashboard()
    
    def update_widget_config(self, widget_id: str, widget_config: dict):
        """
        Update widget configuration.
        
        Args:
            widget_id: Unique identifier for the widget
            widget_config: New widget configuration
        """
        if widget_id in self.widgets:
            self.widgets[widget_id]["config"] = widget_config
            
            # Save dashboard
            self.save_dashboard()
    
    def update_layout(self, widget_id: str, row: int, col: int, width: int = 1, height: int = 1):
        """
        Update widget layout.
        
        Args:
            widget_id: Unique identifier for the widget
            row: Row position
            col: Column position
            width: Widget width
            height: Widget height
        """
        if widget_id in self.layout:
            self.layout[widget_id] = {
                "row": row,
                "col": col,
                "width": width,
                "height": height
            }
            
            # Save dashboard
            self.save_dashboard()
    
    def render_dashboard(self, edit_mode: bool = False):
        """
        Render the dashboard.
        
        Args:
            edit_mode: Whether to render in edit mode
        """
        # Check if dashboard is empty
        if not self.widgets:
            st.info("This dashboard is empty. Add widgets to get started.")
            return
        
        # Get sorted rows
        rows = sorted(set([pos["row"] for pos in self.layout.values()]))
        
        # Render each row
        for row in rows:
            # Get widgets in this row
            row_widgets = {
                widget_id: pos
                for widget_id, pos in self.layout.items()
                if pos["row"] == row
            }
            
            # Sort widgets by column
            sorted_widgets = sorted(row_widgets.items(), key=lambda x: x[1]["col"])
            
            # Create columns
            cols = st.columns([pos["width"] for _, pos in sorted_widgets])
            
            # Render widgets
            for i, (widget_id, pos) in enumerate(sorted_widgets):
                with cols[i]:
                    self._render_widget(widget_id, edit_mode)
    
    def _render_widget(self, widget_id: str, edit_mode: bool = False):
        """
        Render a widget.
        
        Args:
            widget_id: Unique identifier for the widget
            edit_mode: Whether to render in edit mode
        """
        # Get widget
        widget = self.widgets.get(widget_id)
        
        if not widget:
            return
        
        # Create container for widget
        with st.container():
            # Add edit controls if in edit mode
            if edit_mode:
                col1, col2 = st.columns([3, 1])
                
                with col1:
                    st.subheader(widget.get("config", {}).get("title", "Widget"))
                
                with col2:
                    # Add edit button
                    if st.button("Edit", key=f"edit_{widget_id}"):
                        st.session_state["editing_widget"] = widget_id
                    
                    # Add remove button
                    if st.button("Remove", key=f"remove_{widget_id}"):
                        self.remove_widget(widget_id)
                        st.rerun()
            else:
                # Display widget title
                st.subheader(widget.get("config", {}).get("title", "Widget"))
            
            # Render widget based on type
            widget_type = widget.get("type")
            widget_config = widget.get("config", {})
            
            if widget_type == "chart":
                self._render_chart(widget_config)
            elif widget_type == "metric":
                self._render_metric(widget_config)
            elif widget_type == "table":
                self._render_table(widget_config)
            else:
                st.warning(f"Unknown widget type: {widget_type}")
    
    def _render_chart(self, config: dict):
        """
        Render a chart widget.
        
        Args:
            config: Chart configuration
        """
        # Get chart type
        chart_type = config.get("chart_type", "line")
        
        # Get chart data
        data = config.get("data", {})
        
        # Create figure based on chart type
        if chart_type == "line":
            # Create line chart
            fig = go.Figure()
            
            # Add traces
            for series_name, series_data in data.items():
                fig.add_trace(
                    go.Scatter(
                        x=series_data.get("x", []),
                        y=series_data.get("y", []),
                        mode="lines",
                        name=series_name
                    )
                )
            
            # Update layout
            fig.update_layout(
                title=config.get("title", ""),
                xaxis_title=config.get("xaxis_title", ""),
                yaxis_title=config.get("yaxis_title", ""),
                height=config.get("height", 400)
            )
            
            # Display chart
            st.plotly_chart(fig, use_container_width=True)
        
        elif chart_type == "bar":
            # Create bar chart
            fig = go.Figure()
            
            # Add traces
            for series_name, series_data in data.items():
                fig.add_trace(
                    go.Bar(
                        x=series_data.get("x", []),
                        y=series_data.get("y", []),
                        name=series_name
                    )
                )
            
            # Update layout
            fig.update_layout(
                title=config.get("title", ""),
                xaxis_title=config.get("xaxis_title", ""),
                yaxis_title=config.get("yaxis_title", ""),
                height=config.get("height", 400)
            )
            
            # Display chart
            st.plotly_chart(fig, use_container_width=True)
        
        elif chart_type == "pie":
            # Create pie chart
            fig = go.Figure()
            
            # Add trace
            fig.add_trace(
                go.Pie(
                    labels=data.get("labels", []),
                    values=data.get("values", [])
                )
            )
            
            # Update layout
            fig.update_layout(
                title=config.get("title", ""),
                height=config.get("height", 400)
            )
            
            # Display chart
            st.plotly_chart(fig, use_container_width=True)
        
        else:
            st.warning(f"Unknown chart type: {chart_type}")
    
    def _render_metric(self, config: dict):
        """
        Render a metric widget.
        
        Args:
            config: Metric configuration
        """
        # Get metric value
        value = config.get("value", "")
        
        # Get metric delta
        delta = config.get("delta")
        
        # Display metric
        st.metric(
            label=config.get("title", ""),
            value=value,
            delta=delta
        )
    
    def _render_table(self, config: dict):
        """
        Render a table widget.
        
        Args:
            config: Table configuration
        """
        # Get table data
        data = config.get("data", [])
        
        # Create DataFrame
        df = pd.DataFrame(data)
        
        # Display table
        st.dataframe(df)
    
    def render_edit_form(self, widget_id: str = None):
        """
        Render the widget edit form.
        
        Args:
            widget_id: Unique identifier for the widget to edit
        """
        # Check if editing a widget
        if widget_id is None:
            widget_id = st.session_state.get("editing_widget")
        
        if widget_id is None:
            return
        
        # Get widget
        widget = self.widgets.get(widget_id)
        
        if not widget:
            return
        
        # Create form
        with st.form(key=f"edit_widget_{widget_id}"):
            st.subheader(f"Edit Widget: {widget_id}")
            
            # Get widget type
            widget_type = widget.get("type")
            widget_config = widget.get("config", {}).copy()
            
            # Widget title
            widget_config["title"] = st.text_input(
                "Widget Title",
                value=widget_config.get("title", "")
            )
            
            # Widget-specific configuration
            if widget_type == "chart":
                # Chart type
                chart_type = st.selectbox(
                    "Chart Type",
                    options=["line", "bar", "pie"],
                    index=["line", "bar", "pie"].index(widget_config.get("chart_type", "line"))
                )
                widget_config["chart_type"] = chart_type
                
                # Chart height
                widget_config["height"] = st.slider(
                    "Chart Height",
                    min_value=200,
                    max_value=800,
                    value=widget_config.get("height", 400),
                    step=50
                )
                
                # Axis titles
                if chart_type in ["line", "bar"]:
                    widget_config["xaxis_title"] = st.text_input(
                        "X-Axis Title",
                        value=widget_config.get("xaxis_title", "")
                    )
                    
                    widget_config["yaxis_title"] = st.text_input(
                        "Y-Axis Title",
                        value=widget_config.get("yaxis_title", "")
                    )
            
            elif widget_type == "metric":
                # Metric value
                widget_config["value"] = st.text_input(
                    "Metric Value",
                    value=widget_config.get("value", "")
                )
                
                # Metric delta
                widget_config["delta"] = st.text_input(
                    "Metric Delta",
                    value=widget_config.get("delta", "")
                )
            
            # Submit button
            if st.form_submit_button("Save Changes"):
                # Update widget configuration
                self.update_widget_config(widget_id, widget_config)
                
                # Clear editing state
                if "editing_widget" in st.session_state:
                    del st.session_state["editing_widget"]
                
                # Rerun to update dashboard
                st.rerun()
    
    def render_add_widget_form(self):
        """Render the add widget form."""
        with st.form(key="add_widget"):
            st.subheader("Add Widget")
            
            # Widget ID
            widget_id = st.text_input(
                "Widget ID",
                value=f"widget_{len(self.widgets) + 1}"
            )
            
            # Widget type
            widget_type = st.selectbox(
                "Widget Type",
                options=["chart", "metric", "table"]
            )
            
            # Widget title
            widget_title = st.text_input(
                "Widget Title",
                value="New Widget"
            )
            
            # Widget-specific configuration
            widget_config = {
                "title": widget_title
            }
            
            if widget_type == "chart":
                # Chart type
                chart_type = st.selectbox(
                    "Chart Type",
                    options=["line", "bar", "pie"]
                )
                widget_config["chart_type"] = chart_type
                
                # Chart height
                widget_config["height"] = st.slider(
                    "Chart Height",
                    min_value=200,
                    max_value=800,
                    value=400,
                    step=50
                )
                
                # Axis titles
                if chart_type in ["line", "bar"]:
                    widget_config["xaxis_title"] = st.text_input(
                        "X-Axis Title",
                        value=""
                    )
                    
                    widget_config["yaxis_title"] = st.text_input(
                        "Y-Axis Title",
                        value=""
                    )
                
                # Sample data
                widget_config["data"] = {
                    "Series 1": {
                        "x": [1, 2, 3, 4, 5],
                        "y": [10, 20, 15, 25, 30]
                    },
                    "Series 2": {
                        "x": [1, 2, 3, 4, 5],
                        "y": [5, 15, 10, 20, 25]
                    }
                }
            
            elif widget_type == "metric":
                # Metric value
                widget_config["value"] = st.text_input(
                    "Metric Value",
                    value="100"
                )
                
                # Metric delta
                widget_config["delta"] = st.text_input(
                    "Metric Delta",
                    value="10"
                )
            
            elif widget_type == "table":
                # Sample data
                widget_config["data"] = [
                    {"Column 1": "Value 1", "Column 2": "Value 2"},
                    {"Column 1": "Value 3", "Column 2": "Value 4"}
                ]
            
            # Submit button
            if st.form_submit_button("Add Widget"):
                # Check if widget ID already exists
                if widget_id in self.widgets:
                    st.error(f"Widget ID '{widget_id}' already exists.")
                else:
                    # Add widget
                    self.add_widget(widget_id, widget_type, widget_config)
                    
                    # Rerun to update dashboard
                    st.rerun()
    
    def render_dashboard_manager(self):
        """Render the dashboard manager."""
        st.subheader("Dashboard Manager")
        
        # Create tabs
        tabs = st.tabs(["View", "Edit", "Settings"])
        
        # View tab
        with tabs[0]:
            self.render_dashboard(edit_mode=False)
        
        # Edit tab
        with tabs[1]:
            # Add widget button
            if st.button("Add Widget"):
                st.session_state["adding_widget"] = True
            
            # Check if adding a widget
            if st.session_state.get("adding_widget"):
                self.render_add_widget_form()
            
            # Check if editing a widget
            if st.session_state.get("editing_widget"):
                self.render_edit_form()
            
            # Render dashboard in edit mode
            self.render_dashboard(edit_mode=True)
        
        # Settings tab
        with tabs[2]:
            st.subheader("Dashboard Settings")
            
            # Dashboard name
            dashboard_name = st.text_input(
                "Dashboard Name",
                value=self.dashboard_id
            )
            
            # Save button
            if st.button("Save Dashboard"):
                # Check if dashboard name changed
                if dashboard_name != self.dashboard_id:
                    # Create new dashboard with same configuration
                    new_dashboard = CustomizableDashboard(dashboard_name)
                    new_dashboard.widgets = self.widgets
                    new_dashboard.layout = self.layout
                    new_dashboard.save_dashboard()
                    
                    # Switch to new dashboard
                    st.session_state["current_dashboard"] = dashboard_name
                    
                    # Rerun to update dashboard
                    st.rerun()
                else:
                    # Save current dashboard
                    self.save_dashboard()
                    
                    st.success("Dashboard saved successfully.")
            
            # Export button
            if st.button("Export Dashboard"):
                # Create dashboard configuration
                dashboard_config = {
                    "widgets": self.widgets,
                    "layout": self.layout
                }
                
                # Convert to JSON
                dashboard_json = json.dumps(dashboard_config, indent=2)
                
                # Display download link
                st.download_button(
                    label="Download Dashboard Configuration",
                    data=dashboard_json,
                    file_name=f"{self.dashboard_id}.json",
                    mime="application/json"
                )
            
            # Import button
            uploaded_file = st.file_uploader("Import Dashboard", type="json")
            if uploaded_file is not None:
                try:
                    # Load dashboard configuration
                    dashboard_config = json.load(uploaded_file)
                    
                    # Update dashboard
                    self.widgets = dashboard_config.get("widgets", {})
                    self.layout = dashboard_config.get("layout", {})
                    
                    # Save dashboard
                    self.save_dashboard()
                    
                    st.success("Dashboard imported successfully.")
                    
                    # Rerun to update dashboard
                    st.rerun()
                except Exception as e:
                    st.error(f"Error importing dashboard: {str(e)}")
            
            # Clear button
            if st.button("Clear Dashboard"):
                # Confirm clear
                st.warning("Are you sure you want to clear this dashboard? This action cannot be undone.")
                
                # Confirm button
                if st.button("Yes, Clear Dashboard"):
                    # Clear dashboard
                    self.widgets = {}
                    self.layout = {}
                    
                    # Save dashboard
                    self.save_dashboard()
                    
                    st.success("Dashboard cleared successfully.")
                    
                    # Rerun to update dashboard
                    st.rerun()


def render_dashboard_selector():
    """Render the dashboard selector."""
    # Get available dashboards
    dashboards = []
    
    try:
        # Create dashboards directory if it doesn't exist
        os.makedirs("dashboards", exist_ok=True)
        
        # Get dashboard files
        dashboard_files = [f for f in os.listdir("dashboards") if f.endswith(".json")]
        
        # Extract dashboard IDs
        dashboards = [os.path.splitext(f)[0] for f in dashboard_files]
    except Exception as e:
        logger.error(f"Error getting dashboards: {str(e)}")
    
    # Add default dashboard if no dashboards found
    if not dashboards:
        dashboards = ["main"]
    
    # Get current dashboard
    current_dashboard = st.session_state.get("current_dashboard", dashboards[0])
    
    # Create dashboard selector
    selected_dashboard = st.selectbox(
        "Select Dashboard",
        options=dashboards,
        index=dashboards.index(current_dashboard) if current_dashboard in dashboards else 0
    )
    
    # Check if dashboard changed
    if selected_dashboard != current_dashboard:
        # Update current dashboard
        st.session_state["current_dashboard"] = selected_dashboard
        
        # Clear widget states
        if "adding_widget" in st.session_state:
            del st.session_state["adding_widget"]
        
        if "editing_widget" in st.session_state:
            del st.session_state["editing_widget"]
        
        # Rerun to update dashboard
        st.rerun()
    
    # Create new dashboard button
    if st.button("Create New Dashboard"):
        # Show create dashboard form
        st.session_state["creating_dashboard"] = True
    
    # Check if creating a dashboard
    if st.session_state.get("creating_dashboard"):
        with st.form(key="create_dashboard"):
            st.subheader("Create New Dashboard")
            
            # Dashboard name
            dashboard_name = st.text_input(
                "Dashboard Name",
                value=f"dashboard_{len(dashboards) + 1}"
            )
            
            # Submit button
            if st.form_submit_button("Create Dashboard"):
                # Check if dashboard name already exists
                if dashboard_name in dashboards:
                    st.error(f"Dashboard '{dashboard_name}' already exists.")
                else:
                    # Create new dashboard
                    new_dashboard = CustomizableDashboard(dashboard_name)
                    new_dashboard.save_dashboard()
                    
                    # Switch to new dashboard
                    st.session_state["current_dashboard"] = dashboard_name
                    
                    # Clear creating state
                    del st.session_state["creating_dashboard"]
                    
                    # Rerun to update dashboard
                    st.rerun()
    
    return selected_dashboard
