{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Data Exploration and Visualization\n", "\n", "This notebook demonstrates how to fetch, explore, and visualize financial data using the Portfolio Optimizer's data collection modules."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["import sys\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.graph_objects as go\n", "import plotly.express as px\n", "from datetime import datetime, timedelta\n", "import asyncio\n", "\n", "# Add project root to path to import app modules\n", "sys.path.append(os.path.abspath('..'))\n", "\n", "# Set plotting style\n", "plt.style.use('fivethirtyeight')\n", "sns.set_theme(style=\"darkgrid\")\n", "\n", "# Configure pandas display options\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', 1000)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import Data Collection Modules"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["from app.core.data_collection.data_manager import data_manager\n", "from app.settings import get_settings\n", "\n", "# Get settings\n", "settings = get_settings()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define Stocks and Time Period"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Define stocks to analyze\n", "tickers = ['AAPL', 'MSFT', 'AMZ<PERSON>', 'GOOGL', 'META', 'TSLA', 'NVDA', 'JPM', 'V', 'JN<PERSON>']\n", "\n", "# Define time period\n", "end_date = datetime.now().date()\n", "start_date = end_date - <PERSON><PERSON><PERSON>(days=365*3)  # 3 years of data\n", "\n", "print(f\"Fetching data from {start_date} to {end_date}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fetch Historical Stock Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["async def fetch_stock_data():\n", "    \"\"\"Fetch historical stock data for all tickers.\"\"\"\n", "    return await data_manager.get_stock_data_for_tickers(\n", "        tickers=tickers,\n", "        start_date=start_date,\n", "        end_date=end_date,\n", "        interval='1d'\n", "    )\n", "\n", "# Run the async function\n", "stock_data = await fetch_stock_data()\n", "\n", "# Display the first few rows of the data\n", "stock_data.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Exploration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Check for missing values\n", "print(\"Missing values in the dataset:\")\n", "print(stock_data.isna().sum())\n", "\n", "# Basic statistics\n", "print(\"\\nBasic statistics for Adjusted Close prices:\")\n", "adj_close_cols = [col for col in stock_data.columns if 'Adj Close' in col]\n", "print(stock_data[adj_close_cols].describe())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Calculate Daily Returns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Extract adjusted close prices\n", "prices = pd.DataFrame()\n", "for ticker in tickers:\n", "    prices[ticker] = stock_data[f'{ticker}_Adj Close']\n", "\n", "# Calculate daily returns\n", "returns = prices.pct_change().dropna()\n", "\n", "# Display the first few rows of returns\n", "returns.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualize Stock Prices"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Normalize prices to start at 100\n", "normalized_prices = prices / prices.iloc[0] * 100\n", "\n", "# Plot normalized prices\n", "plt.figure(figsize=(14, 8))\n", "for ticker in tickers:\n", "    plt.plot(normalized_prices.index, normalized_prices[ticker], label=ticker)\n", "plt.title('Normalized Stock Prices (Base = 100)')\n", "plt.xlabel('Date')\n", "plt.ylabel('Normalized Price')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Interactive Price Chart with Plotly"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Create interactive plot with <PERSON><PERSON><PERSON>\n", "fig = go.Figure()\n", "\n", "for ticker in tickers:\n", "    fig.add_trace(go.<PERSON>(\n", "        x=normalized_prices.index,\n", "        y=normalized_prices[ticker],\n", "        mode='lines',\n", "        name=ticker\n", "    ))\n", "\n", "fig.update_layout(\n", "    title='Interactive Normalized Stock Prices',\n", "    xaxis_title='Date',\n", "    yaxis_title='Normalized Price',\n", "    height=600,\n", "    width=1000,\n", "    legend=dict(orientation=\"h\", yanchor=\"bottom\", y=1.02, xanchor=\"right\", x=1)\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Correlation Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Calculate correlation matrix\n", "correlation_matrix = returns.corr()\n", "\n", "# Plot correlation heatmap\n", "plt.figure(figsize=(12, 10))\n", "sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', linewidths=0.5, fmt='.2f')\n", "plt.title('Correlation Matrix of Daily Returns')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Risk vs. Return Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Calculate annualized returns and volatility\n", "mean_daily_returns = returns.mean()\n", "daily_volatility = returns.std()\n", "\n", "# Annualize (assuming 252 trading days in a year)\n", "annual_returns = mean_daily_returns * 252\n", "annual_volatility = daily_volatility * np.sqrt(252)\n", "\n", "# Create a DataFrame for plotting\n", "risk_return = pd.DataFrame({\n", "    'Return': annual_returns,\n", "    'Risk': annual_volatility\n", "})\n", "\n", "# Plot risk vs. return\n", "plt.figure(figsize=(12, 8))\n", "plt.scatter(risk_return['Risk'], risk_return['Return'], s=100)\n", "\n", "# Add labels for each point\n", "for i, ticker in enumerate(tickers):\n", "    plt.annotate(ticker, (risk_return['Risk'][i], risk_return['Return'][i]),\n", "                 xytext=(10, 0), textcoords='offset points', fontsize=12)\n", "\n", "plt.title('Risk vs. Return Analysis')\n", "plt.xlabel('Annual Volatility (Risk)')\n", "plt.ylabel('Annual Return')\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Volume Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Extract volume data\n", "volume = pd.DataFrame()\n", "for ticker in tickers:\n", "    volume[ticker] = stock_data[f'{ticker}_Volume']\n", "\n", "# Calculate average daily volume\n", "avg_volume = volume.mean()\n", "avg_volume = avg_volume.sort_values(ascending=False)\n", "\n", "# Plot average daily volume\n", "plt.figure(figsize=(12, 6))\n", "avg_volume.plot(kind='bar')\n", "plt.title('Average Daily Trading Volume')\n", "plt.ylabel('Volume')\n", "plt.grid(True, axis='y')\n", "plt.xticks(rotation=45)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Rolling Statistics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Select a stock for rolling statistics\n", "selected_stock = 'AAPL'\n", "\n", "# Calculate 20-day and 50-day moving averages\n", "prices[f'{selected_stock}_MA20'] = prices[selected_stock].rolling(window=20).mean()\n", "prices[f'{selected_stock}_MA50'] = prices[selected_stock].rolling(window=50).mean()\n", "\n", "# Plot price with moving averages\n", "plt.figure(figsize=(14, 7))\n", "plt.plot(prices.index, prices[selected_stock], label=f'{selected_stock} Price')\n", "plt.plot(prices.index, prices[f'{selected_stock}_MA20'], label='20-day MA')\n", "plt.plot(prices.index, prices[f'{selected_stock}_MA50'], label='50-day MA')\n", "plt.title(f'{selected_stock} Price with Moving Averages')\n", "plt.xlabel('Date')\n", "plt.ylabel('Price')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Rolling Volatility"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Calculate 30-day rolling volatility\n", "rolling_volatility = returns.rolling(window=30).std() * np.sqrt(252)\n", "\n", "# Plot rolling volatility for all stocks\n", "plt.figure(figsize=(14, 8))\n", "for ticker in tickers:\n", "    plt.plot(rolling_volatility.index, rolling_volatility[ticker], label=ticker)\n", "plt.title('30-Day Rolling Volatility (Annualized)')\n", "plt.xlabel('Date')\n", "plt.ylabel('Volatility')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "In this notebook, we've explored historical stock data for a set of major companies. We've analyzed:\n", "\n", "1. Price trends and normalized performance\n", "2. Correlations between different stocks\n", "3. Risk-return profiles\n", "4. Trading volumes\n", "5. Moving averages and volatility\n", "\n", "This analysis provides a foundation for portfolio construction and optimization in subsequent notebooks."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}