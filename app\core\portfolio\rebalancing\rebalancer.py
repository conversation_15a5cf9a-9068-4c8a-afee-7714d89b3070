"""
Portfolio rebalancing functionality.
"""
from datetime import date
from typing import Dict, List, Optional, Any

import pandas as pd
import numpy as np

from app.core.portfolio.rebalancing.strategies import get_rebalancing_strategy
from app.core.portfolio.risk.portfolio_risk import PortfolioRiskAnalyzer
from app.utils.logging import logger


def process_stock_data(data_dict: Dict[str, pd.DataFrame]) -> pd.DataFrame:
    """
    Process and combine stock data from multiple symbols into a single DataFrame.

    Args:
        data_dict: Dictionary mapping symbols to DataFrames with historical stock data

    Returns:
        DataFrame with combined stock data, with 'close' prices for each symbol
    """
    if not data_dict:
        return pd.DataFrame()

    # Create a new DataFrame to store combined data
    combined_df = pd.DataFrame()

    # Process each symbol's data
    for symbol, df in data_dict.items():
        if df.empty:
            continue

        # Make sure the DataFrame has a datetime index
        if not isinstance(df.index, pd.DatetimeIndex):
            # If 'date' is a column, set it as index
            if 'date' in df.columns:
                df = df.set_index('date')
            # Otherwise, try to convert the current index to datetime
            else:
                try:
                    df.index = pd.to_datetime(df.index)
                except Exception as idx_error:
                    logger.warning(f"Could not convert index to datetime for {symbol}: {idx_error}")
                    continue

        # Extract close prices and add to combined DataFrame
        if 'close' in df.columns:
            combined_df[symbol] = df['close']
        elif 'Close' in df.columns:  # Handle different column naming
            combined_df[symbol] = df['Close']
        else:
            logger.warning(f"No close price column found for {symbol}")

    # Sort by date
    combined_df = combined_df.sort_index()

    return combined_df


def analyze_rebalancing(
    data: Dict[str, pd.DataFrame],
    symbols: List[str],
    initial_weights: Dict[str, float],
    start_date: date,
    end_date: date,
    strategy: str,
    strategy_params: Optional[Dict[str, Any]] = None,
    total_portfolio_value: Optional[float] = None
) -> Dict[str, Any]:
    """
    Analyze portfolio rebalancing strategies.

    Args:
        data: Dictionary mapping symbols to DataFrames with historical stock data
        symbols: List of stock symbols
        initial_weights: Initial portfolio weights
        start_date: Start date for historical data
        end_date: End date for historical data
        strategy: Rebalancing strategy
        strategy_params: Strategy-specific parameters
        total_portfolio_value: Total portfolio value

    Returns:
        Dictionary with rebalancing analysis results
    """
    try:
        logger.debug(f"Starting analyze_rebalancing with strategy: {strategy}")
        logger.debug(f"Parameters: symbols={symbols}, start_date={start_date}, end_date={end_date}")
        logger.debug(f"Strategy parameters: {strategy_params}")
        # Process and combine stock data
        prices_df = process_stock_data(data)

        # Calculate returns
        returns_df = prices_df.pct_change().dropna()

        # Get rebalancing strategy
        strategy_params = strategy_params or {}
        rebalancing_strategy = get_rebalancing_strategy(strategy, **strategy_params)

        # Initialize portfolio
        initial_portfolio_value = total_portfolio_value or 10000
        current_weights = initial_weights
        portfolio_values = []
        weights_over_time = {}
        rebalancing_events = []
        rebalancing_dates = []

        # Simulate portfolio over time
        dates = returns_df.index.tolist()
        current_portfolio_value = initial_portfolio_value

        for i, date_idx in enumerate(dates):
            date_str = date_idx.strftime('%Y-%m-%d')

            # Store current weights
            weights_over_time[date_str] = current_weights.copy()

            # Calculate portfolio return for the day using numpy dot product
            weights_array = np.array([current_weights.get(symbol, 0) for symbol in symbols])
            returns_array = returns_df.loc[date_idx, symbols].values
            daily_return = np.dot(weights_array, returns_array)

            # Update portfolio value
            current_portfolio_value *= (1 + daily_return)
            portfolio_values.append(current_portfolio_value)

            # Check if rebalancing is needed
            last_rebalance_date = None
            if rebalancing_dates:
                last_rebalance_date = rebalancing_dates[-1]
                logger.debug(f"Last rebalance date: {last_rebalance_date}, type: {type(last_rebalance_date)}")
            else:
                logger.debug("No previous rebalancing dates")

            logger.debug(f"Current date: {date_idx}, type: {type(date_idx)}")

            # For strategies other than optimal, we need to ensure proper date handling
            # The optimal strategy doesn't use dates in its decision-making process
            logger.debug(f"Calling should_rebalance for strategy: {strategy}")
            try:
                should_rebalance_result = rebalancing_strategy.should_rebalance(
                    current_weights,
                    initial_weights,
                    last_rebalance_date=last_rebalance_date,
                    current_date=date_idx,
                    # Pass additional parameters that might be needed by the optimal strategy
                    expected_returns=returns_df.mean().to_dict() if strategy == "optimal" else None,
                    covariance_matrix=returns_df.cov() if strategy == "optimal" else None
                )
                logger.debug(f"should_rebalance result: {should_rebalance_result}")

                # Add detailed logging for debugging ambiguous truth value errors
                logger.debug(f"Type of should_rebalance_result: {type(should_rebalance_result)}")
                if isinstance(should_rebalance_result, (pd.Series, pd.DataFrame)):
                    logger.debug(f"should_rebalance_result contents:\n{should_rebalance_result}")

                # Ensure should_rebalance_result is a boolean
                if not isinstance(should_rebalance_result, bool):
                    logger.warning(f"should_rebalance returned non-boolean value: {should_rebalance_result} (type: {type(should_rebalance_result)})")
                    # Defensive: if pandas Series or DataFrame, reduce to single boolean
                    if hasattr(should_rebalance_result, "any") and callable(should_rebalance_result.any):
                        should_rebalance_result = bool(should_rebalance_result.any())
                        logger.debug(f"Reduced should_rebalance_result using .any(): {should_rebalance_result}")
                    elif hasattr(should_rebalance_result, "all") and callable(should_rebalance_result.all):
                        should_rebalance_result = bool(should_rebalance_result.all())
                        logger.debug(f"Reduced should_rebalance_result using .all(): {should_rebalance_result}")
                    else:
                        should_rebalance_result = bool(should_rebalance_result)
            except Exception as e:
                logger.error(f"Exception in should_rebalance: {e}", exc_info=True)
                raise

            if i > 0 and should_rebalance_result:
                logger.debug(f"Rebalancing triggered at index {i} for date {date_idx}")
                # Rebalance portfolio
                current_weights = initial_weights.copy()
                rebalancing_events.append({
                    "date": date_str,
                    "action": "Rebalanced to target weights",
                    "transaction_cost": "$0.00"  # Simplified for now
                })
                rebalancing_dates.append(date_idx)
            else:
                # Update weights based on performance
                total_value = np.dot(
                    np.array([current_weights.get(symbol, 0) for symbol in symbols]),
                    1 + returns_df.loc[date_idx, symbols].values
                )

                if total_value > 0:
                    for symbol in symbols:
                        current_weights[symbol] = (
                            current_weights.get(symbol, 0) * (1 + returns_df.loc[date_idx, symbol])
                        ) / total_value

        # Calculate performance metrics
        risk_analyzer = PortfolioRiskAnalyzer()

        # Calculate portfolio returns
        portfolio_returns = [0]  # First day has no return
        for i in range(1, len(portfolio_values)):
            portfolio_returns.append((portfolio_values[i] / portfolio_values[i-1]) - 1)

        # Convert to numpy array for calculations
        portfolio_returns_array = portfolio_returns[1:]  # Skip the first zero

        # Calculate drawdown
        peak = [portfolio_values[0]]
        for i in range(1, len(portfolio_values)):
            peak.append(max(peak[i-1], portfolio_values[i]))

        drawdown = [(portfolio_values[i] - peak[i]) / peak[i] for i in range(len(portfolio_values))]

        # Calculate performance metrics
        total_return = (portfolio_values[-1] / portfolio_values[0]) - 1
        trading_days_per_year = 252
        years = len(portfolio_values) / trading_days_per_year
        annualized_return = (1 + total_return) ** (1 / years) - 1 if years > 0 else 0

        # Use the risk analyzer for other metrics
        metrics = {
            "total_return": total_return,
            "annualized_return": annualized_return,
            "volatility": risk_analyzer.calculate_volatility(returns_df, initial_weights, True),
            "sharpe_ratio": risk_analyzer.calculate_sharpe_ratio(returns_df, initial_weights, True),
            "sortino_ratio": risk_analyzer.calculate_sortino_ratio(returns_df, initial_weights, True),
            "max_drawdown": min(drawdown)
        }

        # Return results
        return {
            "strategy": strategy,
            "strategy_params": strategy_params,
            "performance_metrics": metrics,
            "rebalancing_events": rebalancing_events,
            "rebalancing_dates": [date_obj.strftime('%Y-%m-%d') for date_obj in rebalancing_dates],
            "weights_over_time": weights_over_time,
            "portfolio_value": {
                "dates": [date_idx.strftime('%Y-%m-%d') for date_idx in dates],
                "values": portfolio_values
            },
            "drawdown": {
                "dates": [date_idx.strftime('%Y-%m-%d') for date_idx in dates],
                "values": drawdown
            },
            "cumulative_returns": {
                "dates": [date_idx.strftime('%Y-%m-%d') for date_idx in dates],
                "values": [(portfolio_values[i] / portfolio_values[0]) - 1 for i in range(len(portfolio_values))]
            }
        }
    except Exception as e:
        logger.error(f"Error in analyze_rebalancing: {str(e)}")
        raise
