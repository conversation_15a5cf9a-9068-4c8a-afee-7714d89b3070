"""
Data service.
"""
from datetime import date
from typing import Dict, List, Optional

from app.core.data_collection.data_manager import data_manager
from app.utils.logging import logger


class DataService:
    """
    Data service.
    
    This service provides methods for retrieving financial data.
    """
    
    async def get_stock_data(
        self,
        symbol: str,
        start_date: date,
        end_date: date,
        interval: str = "1d"
    ) -> Dict:
        """
        Get historical stock data.
        
        Args:
            symbol: Stock symbol
            start_date: Start date
            end_date: End date
            interval: Data interval
            
        Returns:
            Dictionary with stock data
        """
        try:
            # Get stock data
            df = await data_manager.get_stock_data(
                symbol,
                start_date,
                end_date,
                interval
            )
            
            # Convert DataFrame to dictionary
            if not df.empty:
                # Reset index if date is the index
                if isinstance(df.index, date):
                    df = df.reset_index()
                
                # Convert to dictionary
                data = df.to_dict(orient="records")
                
                return {
                    "symbol": symbol,
                    "data": data
                }
            else:
                return {
                    "symbol": symbol,
                    "data": []
                }
        
        except Exception as e:
            logger.error(f"Error getting stock data: {e}")
            return {
                "symbol": symbol,
                "data": []
            }
    
    async def get_multiple_stock_data(
        self,
        symbols: List[str],
        start_date: date,
        end_date: date,
        interval: str = "1d"
    ) -> Dict:
        """
        Get historical data for multiple stocks.
        
        Args:
            symbols: List of stock symbols
            start_date: Start date
            end_date: End date
            interval: Data interval
            
        Returns:
            Dictionary mapping symbols to stock data
        """
        try:
            # Get stock data
            data_dict = await data_manager.get_multiple_stock_data(
                symbols,
                start_date,
                end_date,
                interval
            )
            
            # Convert DataFrames to dictionaries
            result = {}
            for symbol, df in data_dict.items():
                if not df.empty:
                    # Reset index if date is the index
                    if isinstance(df.index, date):
                        df = df.reset_index()
                    
                    # Convert to dictionary
                    result[symbol] = df.to_dict(orient="records")
                else:
                    result[symbol] = []
            
            return {
                "data": result
            }
        
        except Exception as e:
            logger.error(f"Error getting multiple stock data: {e}")
            return {
                "data": {}
            }
    
    async def get_company_info(self, symbol: str) -> Dict:
        """
        Get company information.
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Dictionary with company information
        """
        try:
            # Get company information
            info = await data_manager.get_company_info(symbol)
            
            return {
                "symbol": symbol,
                "info": info or {}
            }
        
        except Exception as e:
            logger.error(f"Error getting company information: {e}")
            return {
                "symbol": symbol,
                "info": {}
            }
    
    async def search_symbols(self, query: str) -> Dict:
        """
        Search for symbols.
        
        Args:
            query: Search query
            
        Returns:
            Dictionary with search results
        """
        try:
            # Search for symbols
            results = await data_manager.search_symbols(query)
            
            return {
                "results": results or []
            }
        
        except Exception as e:
            logger.error(f"Error searching symbols: {e}")
            return {
                "results": []
            }


# Create a singleton instance
data_service = DataService()
