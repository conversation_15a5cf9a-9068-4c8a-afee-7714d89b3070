"""
Stress testing for portfolio risk analysis.
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union
from datetime import date, timedelta

from app.utils.logging import logger


def historical_stress_test(
    returns: pd.DataFrame,
    weights: Dict[str, float],
    stress_periods: Optional[Dict[str, Tuple[date, date]]] = None,
    custom_scenarios: Optional[Dict[str, Dict[str, float]]] = None
) -> Dict[str, float]:
    """
    Perform historical stress testing on a portfolio.
    
    Args:
        returns: DataFrame of historical asset returns
        weights: Dictionary mapping assets to weights
        stress_periods: Dictionary mapping scenario names to (start_date, end_date) tuples
        custom_scenarios: Dictionary mapping scenario names to dictionaries of asset returns
        
    Returns:
        Dictionary mapping scenario names to portfolio returns
    """
    try:
        # Convert weights to Series
        weight_series = pd.Series(weights)
        
        # Ensure all assets in weights are in returns
        missing_assets = set(weight_series.index) - set(returns.columns)
        if missing_assets:
            logger.warning(f"Assets in weights but not in returns: {missing_assets}")
            # Filter out missing assets
            weight_series = weight_series[weight_series.index.isin(returns.columns)]
            
            # Renormalize weights
            if not weight_series.empty:
                weight_series = weight_series / weight_series.sum()
        
        # Initialize results
        results = {}
        
        # Process historical stress periods
        if stress_periods:
            for scenario_name, (start_date, end_date) in stress_periods.items():
                try:
                    # Filter returns for the stress period
                    period_returns = returns.loc[start_date:end_date]
                    
                    if period_returns.empty:
                        logger.warning(f"No data for stress period {scenario_name} ({start_date} to {end_date})")
                        continue
                    
                    # Calculate cumulative returns for each asset
                    cumulative_returns = (1 + period_returns).prod() - 1
                    
                    # Calculate portfolio return
                    portfolio_return = cumulative_returns[weight_series.index].dot(weight_series)
                    
                    # Store result
                    results[scenario_name] = portfolio_return
                except Exception as e:
                    logger.error(f"Error processing stress period {scenario_name}: {str(e)}")
        
        # Process custom scenarios
        if custom_scenarios:
            for scenario_name, scenario_returns in custom_scenarios.items():
                try:
                    # Convert scenario returns to Series
                    scenario_series = pd.Series(scenario_returns)
                    
                    # Ensure all assets in weights are in scenario
                    missing_assets = set(weight_series.index) - set(scenario_series.index)
                    if missing_assets:
                        logger.warning(f"Assets in weights but not in scenario {scenario_name}: {missing_assets}")
                        # Use 0 return for missing assets
                        for asset in missing_assets:
                            scenario_series[asset] = 0
                    
                    # Calculate portfolio return
                    portfolio_return = scenario_series[weight_series.index].dot(weight_series)
                    
                    # Store result
                    results[scenario_name] = portfolio_return
                except Exception as e:
                    logger.error(f"Error processing custom scenario {scenario_name}: {str(e)}")
        
        return results
    except Exception as e:
        logger.error(f"Error performing historical stress test: {str(e)}")
        return {}


def sensitivity_stress_test(
    returns: pd.DataFrame,
    weights: Dict[str, float],
    factor_shocks: Dict[str, float],
    factor_exposures: Optional[Dict[str, Dict[str, float]]] = None
) -> Dict[str, float]:
    """
    Perform sensitivity stress testing on a portfolio.
    
    Args:
        returns: DataFrame of historical asset returns
        weights: Dictionary mapping assets to weights
        factor_shocks: Dictionary mapping factors to shock values
        factor_exposures: Dictionary mapping assets to dictionaries of factor exposures
        
    Returns:
        Dictionary with stress test results
    """
    try:
        # Convert weights to Series
        weight_series = pd.Series(weights)
        
        # Ensure all assets in weights are in returns
        missing_assets = set(weight_series.index) - set(returns.columns)
        if missing_assets:
            logger.warning(f"Assets in weights but not in returns: {missing_assets}")
            # Filter out missing assets
            weight_series = weight_series[weight_series.index.isin(returns.columns)]
            
            # Renormalize weights
            if not weight_series.empty:
                weight_series = weight_series / weight_series.sum()
        
        # If factor exposures are not provided, estimate them from returns
        if factor_exposures is None:
            factor_exposures = _estimate_factor_exposures(returns, list(factor_shocks.keys()))
        
        # Calculate asset returns under stress
        asset_returns = {}
        
        for asset in weight_series.index:
            # Get factor exposures for the asset
            asset_exposures = factor_exposures.get(asset, {})
            
            # Calculate asset return under stress
            asset_return = 0
            for factor, shock in factor_shocks.items():
                # Get exposure to the factor
                exposure = asset_exposures.get(factor, 0)
                
                # Add contribution to return
                asset_return += exposure * shock
            
            # Store asset return
            asset_returns[asset] = asset_return
        
        # Calculate portfolio return under stress
        portfolio_return = sum(weight_series[asset] * asset_returns[asset] for asset in weight_series.index)
        
        # Calculate portfolio value change
        portfolio_value_change = portfolio_return
        
        # Calculate asset contributions
        asset_contributions = {
            asset: weight_series[asset] * asset_returns[asset] / portfolio_return if portfolio_return != 0 else 0
            for asset in weight_series.index
        }
        
        # Calculate factor contributions
        factor_contributions = {}
        for factor in factor_shocks.keys():
            factor_contribution = 0
            for asset in weight_series.index:
                exposure = factor_exposures.get(asset, {}).get(factor, 0)
                factor_contribution += weight_series[asset] * exposure * factor_shocks[factor]
            
            factor_contributions[factor] = factor_contribution
        
        # Prepare results
        results = {
            "portfolio_return": portfolio_return,
            "portfolio_value_change": portfolio_value_change,
            "asset_returns": asset_returns,
            "asset_contributions": asset_contributions,
            "factor_contributions": factor_contributions
        }
        
        return results
    except Exception as e:
        logger.error(f"Error performing sensitivity stress test: {str(e)}")
        return {}


def scenario_stress_test(
    returns: pd.DataFrame,
    weights: Dict[str, float],
    scenarios: Dict[str, Dict[str, float]]
) -> Dict[str, Dict[str, float]]:
    """
    Perform scenario stress testing on a portfolio.
    
    Args:
        returns: DataFrame of historical asset returns
        weights: Dictionary mapping assets to weights
        scenarios: Dictionary mapping scenario names to dictionaries of asset returns
        
    Returns:
        Dictionary mapping scenario names to dictionaries with stress test results
    """
    try:
        # Convert weights to Series
        weight_series = pd.Series(weights)
        
        # Ensure all assets in weights are in returns
        missing_assets = set(weight_series.index) - set(returns.columns)
        if missing_assets:
            logger.warning(f"Assets in weights but not in returns: {missing_assets}")
            # Filter out missing assets
            weight_series = weight_series[weight_series.index.isin(returns.columns)]
            
            # Renormalize weights
            if not weight_series.empty:
                weight_series = weight_series / weight_series.sum()
        
        # Initialize results
        results = {}
        
        # Process each scenario
        for scenario_name, scenario_returns in scenarios.items():
            try:
                # Convert scenario returns to Series
                scenario_series = pd.Series(scenario_returns)
                
                # Ensure all assets in weights are in scenario
                missing_assets = set(weight_series.index) - set(scenario_series.index)
                if missing_assets:
                    logger.warning(f"Assets in weights but not in scenario {scenario_name}: {missing_assets}")
                    # Use 0 return for missing assets
                    for asset in missing_assets:
                        scenario_series[asset] = 0
                
                # Calculate portfolio return
                portfolio_return = scenario_series[weight_series.index].dot(weight_series)
                
                # Calculate asset contributions
                asset_contributions = {
                    asset: weight_series[asset] * scenario_series[asset]
                    for asset in weight_series.index
                }
                
                # Store results
                results[scenario_name] = {
                    "portfolio_return": portfolio_return,
                    "asset_returns": {asset: scenario_series[asset] for asset in weight_series.index},
                    "asset_contributions": asset_contributions
                }
            except Exception as e:
                logger.error(f"Error processing scenario {scenario_name}: {str(e)}")
        
        return results
    except Exception as e:
        logger.error(f"Error performing scenario stress test: {str(e)}")
        return {}


def reverse_stress_test(
    returns: pd.DataFrame,
    weights: Dict[str, float],
    target_loss: float,
    n_iterations: int = 1000,
    method: str = "monte_carlo"
) -> Dict:
    """
    Perform reverse stress testing on a portfolio.
    
    Args:
        returns: DataFrame of historical asset returns
        weights: Dictionary mapping assets to weights
        target_loss: Target portfolio loss
        n_iterations: Number of iterations for Monte Carlo method
        method: Method to use ('monte_carlo' or 'optimization')
        
    Returns:
        Dictionary with reverse stress test results
    """
    try:
        # Convert weights to Series
        weight_series = pd.Series(weights)
        
        # Ensure all assets in weights are in returns
        missing_assets = set(weight_series.index) - set(returns.columns)
        if missing_assets:
            logger.warning(f"Assets in weights but not in returns: {missing_assets}")
            # Filter out missing assets
            weight_series = weight_series[weight_series.index.isin(returns.columns)]
            
            # Renormalize weights
            if not weight_series.empty:
                weight_series = weight_series / weight_series.sum()
        
        # Get relevant returns
        asset_returns = returns[weight_series.index]
        
        # Calculate mean and covariance of returns
        mu = asset_returns.mean().values
        cov = asset_returns.cov().values
        
        if method == "monte_carlo":
            # Generate random samples from multivariate normal distribution
            simulated_returns = np.random.multivariate_normal(
                mu, cov, size=n_iterations
            )
            
            # Calculate portfolio returns for each simulation
            portfolio_returns = np.zeros(n_iterations)
            
            for i in range(n_iterations):
                portfolio_returns[i] = simulated_returns[i] @ weight_series.values
            
            # Find the simulation closest to the target loss
            closest_idx = np.argmin(np.abs(portfolio_returns + target_loss))
            closest_return = portfolio_returns[closest_idx]
            closest_asset_returns = simulated_returns[closest_idx]
            
            # Prepare results
            results = {
                "method": "monte_carlo",
                "target_loss": target_loss,
                "achieved_loss": -closest_return,
                "asset_returns": {
                    asset: closest_asset_returns[i]
                    for i, asset in enumerate(weight_series.index)
                },
                "asset_contributions": {
                    asset: weight_series[asset] * closest_asset_returns[i]
                    for i, asset in enumerate(weight_series.index)
                }
            }
        else:
            # Use optimization method (not implemented)
            logger.warning("Optimization method not implemented, using Monte Carlo instead")
            # Call the function recursively with Monte Carlo method
            return reverse_stress_test(returns, weights, target_loss, n_iterations, "monte_carlo")
        
        return results
    except Exception as e:
        logger.error(f"Error performing reverse stress test: {str(e)}")
        return {}


def _estimate_factor_exposures(
    returns: pd.DataFrame,
    factors: List[str]
) -> Dict[str, Dict[str, float]]:
    """
    Estimate factor exposures from returns.
    
    Args:
        returns: DataFrame of asset returns
        factors: List of factor names
        
    Returns:
        Dictionary mapping assets to dictionaries of factor exposures
    """
    # This is a simplified implementation that assigns random exposures
    # In a real implementation, this would use regression or other methods
    
    # Initialize exposures
    exposures = {}
    
    # Generate random exposures for each asset
    for asset in returns.columns:
        asset_exposures = {}
        for factor in factors:
            # Generate random exposure between -1 and 1
            exposure = np.random.uniform(-1, 1)
            asset_exposures[factor] = exposure
        
        exposures[asset] = asset_exposures
    
    return exposures
