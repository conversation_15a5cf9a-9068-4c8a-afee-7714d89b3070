#!/bin/bash

# Portfolio Optimization Restore Script
# This script restores a backup of the Portfolio Optimization application.

set -e

# Configuration
ENVIRONMENT=${ENVIRONMENT:-production}
DEPLOY_DIR=${DEPLOY_DIR:-/opt/portfolio-optimization}
BACKUP_DIR=${BACKUP_DIR:-/opt/backups/portfolio-optimization}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Print usage
usage() {
    echo "Usage: $0 [options] <backup-file> <db-backup-file> [volumes-backup-file]"
    echo "Options:"
    echo "  -e, --environment <env>   Deployment environment (default: production)"
    echo "  -d, --deploy-dir <dir>    Deployment directory (default: /opt/portfolio-optimization)"
    echo "  -b, --backup-dir <dir>    Backup directory (default: /opt/backups/portfolio-optimization)"
    echo "  -h, --help                Show this help message"
    echo ""
    echo "Arguments:"
    echo "  <backup-file>             Application backup file (e.g., backup-20230101120000.tar.gz)"
    echo "  <db-backup-file>          Database backup file (e.g., db-backup-20230101120000.sql)"
    echo "  [volumes-backup-file]     Volumes backup file (e.g., volumes-backup-20230101120000.tar.gz)"
    exit 1
}

# Parse command-line arguments
while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        -e|--environment)
            ENVIRONMENT="$2"
            shift
            shift
            ;;
        -d|--deploy-dir)
            DEPLOY_DIR="$2"
            shift
            shift
            ;;
        -b|--backup-dir)
            BACKUP_DIR="$2"
            shift
            shift
            ;;
        -h|--help)
            usage
            ;;
        *)
            if [ -z "$BACKUP_FILE" ]; then
                BACKUP_FILE="$1"
            elif [ -z "$DB_BACKUP_FILE" ]; then
                DB_BACKUP_FILE="$1"
            elif [ -z "$VOLUMES_BACKUP_FILE" ]; then
                VOLUMES_BACKUP_FILE="$1"
            else
                echo -e "${RED}Error: Unknown argument $1${NC}"
                usage
            fi
            shift
            ;;
    esac
done

# Check if backup files are provided
if [ -z "$BACKUP_FILE" ] || [ -z "$DB_BACKUP_FILE" ]; then
    echo -e "${RED}Error: Backup file and database backup file are required${NC}"
    usage
fi

# Check if backup files exist
if [ ! -f "$BACKUP_DIR/$BACKUP_FILE" ]; then
    echo -e "${RED}Error: Backup file not found: $BACKUP_DIR/$BACKUP_FILE${NC}"
    exit 1
fi

if [ ! -f "$BACKUP_DIR/$DB_BACKUP_FILE" ]; then
    echo -e "${RED}Error: Database backup file not found: $BACKUP_DIR/$DB_BACKUP_FILE${NC}"
    exit 1
fi

if [ ! -z "$VOLUMES_BACKUP_FILE" ] && [ ! -f "$BACKUP_DIR/$VOLUMES_BACKUP_FILE" ]; then
    echo -e "${RED}Error: Volumes backup file not found: $BACKUP_DIR/$VOLUMES_BACKUP_FILE${NC}"
    exit 1
fi

# Check if Docker and Docker Compose are installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Error: Docker is not installed${NC}"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}Error: Docker Compose is not installed${NC}"
    exit 1
fi

# Confirm restore
echo -e "${YELLOW}Warning: This will overwrite the current deployment with the backup.${NC}"
read -p "Are you sure you want to continue? (y/n) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${RED}Restore cancelled${NC}"
    exit 1
fi

# Stop containers
echo -e "${YELLOW}Stopping containers${NC}"
cd "$DEPLOY_DIR/deployment/docker"
docker-compose -f docker-compose.yml -f docker-compose.prod.yml down

# Restore application files
echo -e "${YELLOW}Restoring application files${NC}"
rm -rf "$DEPLOY_DIR"/*
mkdir -p "$DEPLOY_DIR"
tar -xzf "$BACKUP_DIR/$BACKUP_FILE" -C "$DEPLOY_DIR"
echo -e "${GREEN}Application files restored${NC}"

# Restore Docker volumes
if [ ! -z "$VOLUMES_BACKUP_FILE" ]; then
    echo -e "${YELLOW}Restoring Docker volumes${NC}"
    
    # Extract volumes backup
    VOLUMES_BACKUP_DIR="$BACKUP_DIR/volumes-restore"
    rm -rf "$VOLUMES_BACKUP_DIR"
    mkdir -p "$VOLUMES_BACKUP_DIR"
    tar -xzf "$BACKUP_DIR/$VOLUMES_BACKUP_FILE" -C "$VOLUMES_BACKUP_DIR"
    
    # Get list of volume backup files
    VOLUME_BACKUPS=$(find "$VOLUMES_BACKUP_DIR" -name "*.tar.gz")
    
    for VOLUME_BACKUP in $VOLUME_BACKUPS; do
        VOLUME_NAME=$(basename "$VOLUME_BACKUP" .tar.gz)
        echo -e "${YELLOW}Restoring volume: $VOLUME_NAME${NC}"
        
        # Remove existing volume
        docker volume rm -f "$VOLUME_NAME" || true
        
        # Create new volume
        docker volume create "$VOLUME_NAME"
        
        # Create a temporary container to access the volume
        CONTAINER_ID=$(docker run -d -v $VOLUME_NAME:/volume busybox:latest sleep 60)
        
        # Copy backup to container
        docker cp "$VOLUME_BACKUP" $CONTAINER_ID:/tmp/volume.tar.gz
        
        # Extract backup in container
        docker exec $CONTAINER_ID sh -c "rm -rf /volume/* && tar -xzf /tmp/volume.tar.gz -C /volume"
        
        # Remove the temporary container
        docker rm -f $CONTAINER_ID
        
        echo -e "${GREEN}Volume restored: $VOLUME_NAME${NC}"
    done
    
    # Clean up
    rm -rf "$VOLUMES_BACKUP_DIR"
    echo -e "${GREEN}Volumes restored${NC}"
fi

# Start containers
echo -e "${YELLOW}Starting containers${NC}"
cd "$DEPLOY_DIR/deployment/docker"
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Restore database
echo -e "${YELLOW}Restoring database${NC}"
cat "$BACKUP_DIR/$DB_BACKUP_FILE" | docker-compose -f docker-compose.yml -f docker-compose.prod.yml exec -T db psql -U postgres -d portfolio_optimization
echo -e "${GREEN}Database restored${NC}"

# Check if services are running
echo -e "${YELLOW}Checking if services are running${NC}"
if docker-compose -f docker-compose.yml -f docker-compose.prod.yml ps | grep -q "Up"; then
    echo -e "${GREEN}Restore successful!${NC}"
    echo -e "${GREEN}API: https://api.portfolio-optimization.example.com${NC}"
    echo -e "${GREEN}Streamlit: https://app.portfolio-optimization.example.com${NC}"
else
    echo -e "${RED}Restore failed!${NC}"
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs
    exit 1
fi
