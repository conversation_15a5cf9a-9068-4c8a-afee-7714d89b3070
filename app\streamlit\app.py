"""
Streamlit application entry point.
"""
import asyncio
import streamlit as st
from app.streamlit.pages.analysis import show_analysis_page
from app.streamlit.pages.main import show_main_page
from app.streamlit.pages.portfolio import show_portfolio_page
from app.streamlit.pages.advanced_analysis import show_advanced_analysis_page
from app.streamlit.pages.fundamental_analysis import show_fundamental_analysis_page
from app.streamlit.pages.economic_analysis import show_economic_analysis_page
from app.streamlit.pages.sentiment_analysis import show_sentiment_analysis_page
from app.streamlit.pages.custom_dashboard import show_custom_dashboard_page
from app.streamlit.pages.advanced_indicators import show_advanced_indicators_page
from app.streamlit.pages.pattern_recognition import show_pattern_recognition_page
from app.streamlit.pages.real_time_market import show_real_time_market_page
from app.utils.logging import logger
from app.patches import apply_all_patches
import inspect

# Apply patches
apply_all_patches()

# Set page config
st.set_page_config(
    page_title="Portfolio Optimizer",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Define pages
PAGES = {
    "Market Dashboard": show_main_page,
    "Technical Analysis": show_analysis_page,
    "Fundamental Analysis": show_fundamental_analysis_page,
    "Portfolio Management": show_portfolio_page
}


def run_async_function(func):
    """Run an async function if it's async, otherwise just call it directly."""
    if inspect.iscoroutinefunction(func):
        return asyncio.run(func())
    else:
        return func()


def main():
    """Main function for the Streamlit app."""
    # Apply patches
    apply_all_patches()

    # Add a sidebar
    st.sidebar.title("Navigation")

    # Page selection
    page = st.sidebar.radio("Go to", list(PAGES.keys()))

    # Display the selected page - handle both async and sync functions
    run_async_function(PAGES[page])

    # Add a footer
    st.sidebar.markdown("---")
    st.sidebar.info(
        "This app is developed using Streamlit and FastAPI. "
        "It provides tools for technical analysis and portfolio optimization."
    )


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logger.error(f"Error in Streamlit app: {e}")
        st.error(f"An error occurred: {e}")


