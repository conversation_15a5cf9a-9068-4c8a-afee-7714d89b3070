version: '3.8'

services:
  api:
    build:
      context: ../../
      dockerfile: deployment/docker/Dockerfile
    command: api
    ports:
      - "8000:8000"
    volumes:
      - ../../:/app
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=**************************************/portfolio_optimization
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    networks:
      - portfolio-network

  streamlit:
    build:
      context: ../../
      dockerfile: deployment/docker/Dockerfile
    command: streamlit
    ports:
      - "8501:8501"
    volumes:
      - ../../:/app
    environment:
      - ENVIRONMENT=development
      - API_URL=http://api:8000
    depends_on:
      - api
    networks:
      - portfolio-network

  worker:
    build:
      context: ../../
      dockerfile: deployment/docker/Dockerfile
    command: worker
    volumes:
      - ../../:/app
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=**************************************/portfolio_optimization
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    networks:
      - portfolio-network

  db:
    image: postgres:13
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=portfolio_optimization
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - portfolio-network

  redis:
    image: redis:6
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - portfolio-network

  nginx:
    image: nginx:1.19
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - api
      - streamlit
    networks:
      - portfolio-network

networks:
  portfolio-network:
    driver: bridge

volumes:
  postgres-data:
  redis-data:
