"""
Portfolio rebalancing API endpoints.
"""
from datetime import date
from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from sqlalchemy.orm import Session
import pandas as pd
import numpy as np

from app.core.portfolio.rebalancing.strategies import get_rebalancing_strategy
from app.core.portfolio.risk.portfolio_risk import PortfolioRiskAnalyzer
from app.core.data_collection.data_manager import data_manager
from app.db.database import get_db
from app.utils.logging import logger


router = APIRouter()


class RebalancingRequest(BaseModel):
    """Portfolio rebalancing request model."""
    symbols: List[str]
    initial_weights: Dict[str, float]
    start_date: date
    end_date: date
    strategy: str
    strategy_params: Optional[Dict] = None
    total_portfolio_value: Optional[float] = None


@router.post("/rebalance")
async def rebalance_portfolio(
    request: RebalancingRequest,
    db: Session = Depends(get_db)
):
    """
    Analyze portfolio rebalancing strategies.

    Args:
        request: Rebalancing request
        db: Database session

    Returns:
        Rebalancing analysis results
    """
    try:
        # Get historical data
        data = await data_manager.get_multiple_stock_data(
            request.symbols,
            request.start_date,
            request.end_date,
            interval="1d"
        )

        if not data or len(data) == 0:
            raise HTTPException(status_code=404, detail="No data found for the specified symbols and date range")

        # Import the rebalancer here to avoid circular imports
        from app.core.portfolio.rebalancing.rebalancer import analyze_rebalancing

        # Use the rebalancer to analyze the portfolio
        result = analyze_rebalancing(
            data=data,
            symbols=request.symbols,
            initial_weights=request.initial_weights,
            start_date=request.start_date,
            end_date=request.end_date,
            strategy=request.strategy,
            strategy_params=request.strategy_params,
            total_portfolio_value=request.total_portfolio_value
        )

        # Return the result
        return result
    except Exception as e:
        logger.error(f"Error in portfolio rebalancing: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error in portfolio rebalancing: {str(e)}")
