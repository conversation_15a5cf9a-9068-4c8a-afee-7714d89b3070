"""
Monte Carlo simulation for portfolio risk analysis.
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union
from datetime import date, timedelta

from app.utils.logging import logger


def simulate_portfolio_returns(
    returns: pd.DataFrame,
    weights: Dict[str, float],
    n_simulations: int = 1000,
    n_periods: int = 252,
    return_type: str = "log",
    seed: Optional[int] = None
) -> pd.DataFrame:
    """
    Simulate future portfolio returns using Monte Carlo simulation.
    
    Args:
        returns: DataFrame of historical asset returns
        weights: Dictionary mapping assets to weights
        n_simulations: Number of simulations to run
        n_periods: Number of periods to simulate
        return_type: Type of returns ('log' or 'simple')
        seed: Random seed for reproducibility
        
    Returns:
        DataFrame with simulated portfolio returns
    """
    try:
        # Set random seed if provided
        if seed is not None:
            np.random.seed(seed)
        
        # Convert weights to Series
        weight_series = pd.Series(weights)
        
        # Ensure all assets in weights are in returns
        missing_assets = set(weight_series.index) - set(returns.columns)
        if missing_assets:
            logger.warning(f"Assets in weights but not in returns: {missing_assets}")
            # Filter out missing assets
            weight_series = weight_series[weight_series.index.isin(returns.columns)]
            
            # Renormalize weights
            if not weight_series.empty:
                weight_series = weight_series / weight_series.sum()
        
        # Get relevant returns
        asset_returns = returns[weight_series.index]
        
        # Calculate mean and covariance of returns
        if return_type == "log":
            # Convert to log returns if not already
            if (asset_returns < -1).any().any():
                logger.warning("Returns contain values < -1, assuming returns are already log returns")
            else:
                # Check if returns are simple returns
                if (asset_returns > 1).any().any():
                    logger.warning("Converting simple returns to log returns")
                    asset_returns = np.log(1 + asset_returns)
            
            mu = asset_returns.mean().values
            cov = asset_returns.cov().values
        else:
            # Use simple returns
            if (asset_returns < -1).any().any():
                logger.warning("Returns contain values < -1, converting log returns to simple returns")
                asset_returns = np.exp(asset_returns) - 1
            
            mu = asset_returns.mean().values
            cov = asset_returns.cov().values
        
        # Generate random samples from multivariate normal distribution
        simulated_returns = np.random.multivariate_normal(
            mu, cov, size=(n_simulations, n_periods)
        )
        
        # Calculate portfolio returns for each simulation
        portfolio_returns = np.zeros((n_simulations, n_periods))
        
        for i in range(n_simulations):
            # Calculate portfolio returns for each period
            for j in range(n_periods):
                portfolio_returns[i, j] = simulated_returns[i, j] @ weight_series.values
        
        # Convert to DataFrame
        simulated_df = pd.DataFrame(
            portfolio_returns,
            columns=[f"Period_{j+1}" for j in range(n_periods)]
        )
        
        # Convert back to simple returns if log returns were used
        if return_type == "log":
            simulated_df = np.exp(simulated_df) - 1
        
        return simulated_df
    except Exception as e:
        logger.error(f"Error simulating portfolio returns: {str(e)}")
        return pd.DataFrame()


def simulate_portfolio_values(
    returns: pd.DataFrame,
    weights: Dict[str, float],
    initial_value: float = 10000,
    n_simulations: int = 1000,
    n_periods: int = 252,
    return_type: str = "log",
    seed: Optional[int] = None
) -> pd.DataFrame:
    """
    Simulate future portfolio values using Monte Carlo simulation.
    
    Args:
        returns: DataFrame of historical asset returns
        weights: Dictionary mapping assets to weights
        initial_value: Initial portfolio value
        n_simulations: Number of simulations to run
        n_periods: Number of periods to simulate
        return_type: Type of returns ('log' or 'simple')
        seed: Random seed for reproducibility
        
    Returns:
        DataFrame with simulated portfolio values
    """
    try:
        # Simulate portfolio returns
        simulated_returns = simulate_portfolio_returns(
            returns, weights, n_simulations, n_periods, return_type, seed
        )
        
        if simulated_returns.empty:
            return pd.DataFrame()
        
        # Calculate portfolio values
        simulated_values = np.zeros((n_simulations, n_periods + 1))
        
        # Set initial value
        simulated_values[:, 0] = initial_value
        
        # Calculate portfolio values for each period
        for i in range(n_simulations):
            for j in range(n_periods):
                simulated_values[i, j+1] = simulated_values[i, j] * (1 + simulated_returns.iloc[i, j])
        
        # Convert to DataFrame
        simulated_df = pd.DataFrame(
            simulated_values,
            columns=["Initial"] + [f"Period_{j+1}" for j in range(n_periods)]
        )
        
        return simulated_df
    except Exception as e:
        logger.error(f"Error simulating portfolio values: {str(e)}")
        return pd.DataFrame()


def calculate_simulation_statistics(
    simulated_values: pd.DataFrame,
    confidence_levels: List[float] = [0.01, 0.05, 0.1]
) -> Dict[str, float]:
    """
    Calculate statistics from Monte Carlo simulation results.
    
    Args:
        simulated_values: DataFrame with simulated portfolio values
        confidence_levels: List of confidence levels for VaR and CVaR
        
    Returns:
        Dictionary with simulation statistics
    """
    try:
        if simulated_values.empty:
            return {}
        
        # Get initial and final values
        initial_value = simulated_values["Initial"].iloc[0]
        final_values = simulated_values.iloc[:, -1]
        
        # Calculate returns
        total_returns = (final_values / initial_value) - 1
        
        # Calculate statistics
        stats = {
            "mean_final_value": final_values.mean(),
            "median_final_value": final_values.median(),
            "min_final_value": final_values.min(),
            "max_final_value": final_values.max(),
            "std_final_value": final_values.std(),
            "mean_return": total_returns.mean(),
            "median_return": total_returns.median(),
            "min_return": total_returns.min(),
            "max_return": total_returns.max(),
            "std_return": total_returns.std(),
            "probability_positive_return": (total_returns > 0).mean(),
            "probability_negative_return": (total_returns < 0).mean()
        }
        
        # Calculate VaR and CVaR for each confidence level
        for cl in confidence_levels:
            # Calculate VaR
            var = np.percentile(total_returns, cl * 100)
            stats[f"var_{int(cl*100)}"] = -var
            
            # Calculate CVaR
            cvar = total_returns[total_returns <= var].mean()
            stats[f"cvar_{int(cl*100)}"] = -cvar
        
        return stats
    except Exception as e:
        logger.error(f"Error calculating simulation statistics: {str(e)}")
        return {}


def generate_simulation_report(
    returns: pd.DataFrame,
    weights: Dict[str, float],
    initial_value: float = 10000,
    n_simulations: int = 1000,
    n_periods: int = 252,
    confidence_levels: List[float] = [0.01, 0.05, 0.1],
    return_type: str = "log",
    seed: Optional[int] = None
) -> Dict:
    """
    Generate a comprehensive report from Monte Carlo simulation.
    
    Args:
        returns: DataFrame of historical asset returns
        weights: Dictionary mapping assets to weights
        initial_value: Initial portfolio value
        n_simulations: Number of simulations to run
        n_periods: Number of periods to simulate
        confidence_levels: List of confidence levels for VaR and CVaR
        return_type: Type of returns ('log' or 'simple')
        seed: Random seed for reproducibility
        
    Returns:
        Dictionary with simulation report
    """
    try:
        # Simulate portfolio values
        simulated_values = simulate_portfolio_values(
            returns, weights, initial_value, n_simulations, n_periods, return_type, seed
        )
        
        if simulated_values.empty:
            return {"error": "Failed to simulate portfolio values"}
        
        # Calculate statistics
        stats = calculate_simulation_statistics(simulated_values, confidence_levels)
        
        # Calculate percentiles
        percentiles = [1, 5, 10, 25, 50, 75, 90, 95, 99]
        final_values = simulated_values.iloc[:, -1]
        percentile_values = {
            f"percentile_{p}": np.percentile(final_values, p)
            for p in percentiles
        }
        
        # Generate report
        report = {
            "input_parameters": {
                "initial_value": initial_value,
                "n_simulations": n_simulations,
                "n_periods": n_periods,
                "return_type": return_type,
                "seed": seed
            },
            "portfolio_weights": weights,
            "statistics": stats,
            "percentiles": percentile_values,
            "sample_paths": simulated_values.head(10).to_dict()
        }
        
        return report
    except Exception as e:
        logger.error(f"Error generating simulation report: {str(e)}")
        return {"error": str(e)}
