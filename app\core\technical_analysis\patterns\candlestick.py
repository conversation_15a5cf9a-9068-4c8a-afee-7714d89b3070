"""
Candlestick pattern recognition.
"""
from typing import Dict, List, Optional, Union

import numpy as np
import pandas as pd
import pandas_ta as ta

from app.utils.logging import logger


class CandlestickPatterns:
    """
    Candlestick pattern recognition.
    
    This class provides methods for identifying common candlestick patterns
    in price data.
    """
    
    def __init__(self):
        """Initialize the candlestick pattern recognizer."""
        pass
    
    def recognize_patterns(
        self,
        data: pd.DataFrame,
        patterns: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """
        Recognize candlestick patterns in the data.
        
        Args:
            data: DataFrame with OHLCV data
            patterns: List of patterns to recognize (if None, recognizes all)
            
        Returns:
            DataFrame with pattern recognition columns
        """
        # Validate data
        if not self._validate_data(data):
            logger.warning("Invalid data for candlestick pattern recognition")
            return data
        
        # Make a copy of the data
        result = data.copy()
        
        # Define available patterns
        available_patterns = {
            "doji": self._recognize_doji,
            "hammer": self._recognize_hammer,
            "shooting_star": self._recognize_shooting_star,
            "engulfing": self._recognize_engulfing,
            "morning_star": self._recognize_morning_star,
            "evening_star": self._recognize_evening_star
        }
        
        # Determine which patterns to recognize
        if patterns is None:
            patterns = list(available_patterns.keys())
        else:
            # Validate requested patterns
            patterns = [p for p in patterns if p in available_patterns]
        
        # Recognize each pattern
        for pattern in patterns:
            if pattern in available_patterns:
                result = available_patterns[pattern](result)
        
        return result
    
    def _validate_data(self, data: pd.DataFrame) -> bool:
        """
        Validate input data.
        
        Args:
            data: DataFrame to validate
            
        Returns:
            True if the data is valid, False otherwise
        """
        # Check if DataFrame is empty
        if data is None or data.empty:
            return False
        
        # Check for required columns
        required_columns = ["open", "high", "low", "close"]
        if not all(col in data.columns for col in required_columns):
            return False
        
        return True
    
    def _recognize_doji(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Recognize Doji patterns.
        
        Args:
            data: DataFrame with OHLCV data
            
        Returns:
            DataFrame with Doji pattern column
        """
        # Make a copy of the data
        result = data.copy()
        
        # Calculate body size and shadow sizes
        result["body_size"] = abs(result["close"] - result["open"])
        result["total_range"] = result["high"] - result["low"]
        
        # Doji has a very small body compared to the total range
        result["doji"] = (result["body_size"] / result["total_range"]) < 0.1
        
        return result
    
    def _recognize_hammer(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Recognize Hammer patterns.
        
        Args:
            data: DataFrame with OHLCV data
            
        Returns:
            DataFrame with Hammer pattern column
        """
        # Make a copy of the data
        result = data.copy()
        
        # Calculate body size and shadow sizes
        result["body_size"] = abs(result["close"] - result["open"])
        result["upper_shadow"] = result["high"] - result[["open", "close"]].max(axis=1)
        result["lower_shadow"] = result[["open", "close"]].min(axis=1) - result["low"]
        
        # Hammer criteria:
        # 1. Small upper shadow
        # 2. Long lower shadow (at least 2x body size)
        # 3. Previous trend is downward
        result["hammer"] = (
            (result["upper_shadow"] < 0.1 * result["body_size"]) &
            (result["lower_shadow"] > 2 * result["body_size"]) &
            (result["close"].shift(1) < result["open"].shift(1)) &  # Previous candle is bearish
            (result["close"].shift(2) < result["close"].shift(1))   # Downward trend
        )
        
        return result
    
    def _recognize_shooting_star(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Recognize Shooting Star patterns.
        
        Args:
            data: DataFrame with OHLCV data
            
        Returns:
            DataFrame with Shooting Star pattern column
        """
        # Make a copy of the data
        result = data.copy()
        
        # Calculate body size and shadow sizes
        result["body_size"] = abs(result["close"] - result["open"])
        result["upper_shadow"] = result["high"] - result[["open", "close"]].max(axis=1)
        result["lower_shadow"] = result[["open", "close"]].min(axis=1) - result["low"]
        
        # Shooting Star criteria:
        # 1. Long upper shadow (at least 2x body size)
        # 2. Small lower shadow
        # 3. Previous trend is upward
        result["shooting_star"] = (
            (result["upper_shadow"] > 2 * result["body_size"]) &
            (result["lower_shadow"] < 0.1 * result["body_size"]) &
            (result["close"].shift(1) > result["open"].shift(1)) &  # Previous candle is bullish
            (result["close"].shift(2) < result["close"].shift(1))   # Upward trend
        )
        
        return result
    
    def _recognize_engulfing(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Recognize Bullish and Bearish Engulfing patterns.
        
        Args:
            data: DataFrame with OHLCV data
            
        Returns:
            DataFrame with Engulfing pattern columns
        """
        # Make a copy of the data
        result = data.copy()
        
        # Bullish Engulfing criteria:
        # 1. Previous candle is bearish (close < open)
        # 2. Current candle is bullish (close > open)
        # 3. Current candle's body completely engulfs previous candle's body
        result["bullish_engulfing"] = (
            (result["close"].shift(1) < result["open"].shift(1)) &  # Previous candle is bearish
            (result["close"] > result["open"]) &                    # Current candle is bullish
            (result["open"] < result["close"].shift(1)) &           # Current open below previous close
            (result["close"] > result["open"].shift(1))             # Current close above previous open
        )
        
        # Bearish Engulfing criteria:
        # 1. Previous candle is bullish (close > open)
        # 2. Current candle is bearish (close < open)
        # 3. Current candle's body completely engulfs previous candle's body
        result["bearish_engulfing"] = (
            (result["close"].shift(1) > result["open"].shift(1)) &  # Previous candle is bullish
            (result["close"] < result["open"]) &                    # Current candle is bearish
            (result["open"] > result["close"].shift(1)) &           # Current open above previous close
            (result["close"] < result["open"].shift(1))             # Current close below previous open
        )
        
        return result
    
    def _recognize_morning_star(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Recognize Morning Star patterns.
        
        Args:
            data: DataFrame with OHLCV data
            
        Returns:
            DataFrame with Morning Star pattern column
        """
        # Make a copy of the data
        result = data.copy()
        
        # Calculate body sizes
        result["body_size"] = abs(result["close"] - result["open"])
        result["body_size_prev"] = abs(result["close"].shift(1) - result["open"].shift(1))
        result["body_size_prev2"] = abs(result["close"].shift(2) - result["open"].shift(2))
        
        # Morning Star criteria:
        # 1. First candle is bearish with a large body
        # 2. Second candle has a small body and gaps down
        # 3. Third candle is bullish and closes above the midpoint of the first candle
        result["morning_star"] = (
            (result["close"].shift(2) < result["open"].shift(2)) &                  # First candle is bearish
            (result["body_size_prev"] < 0.3 * result["body_size_prev2"]) &          # Second candle has a small body
            (result[["open", "close"]].shift(1).max(axis=1) < result["close"].shift(2)) &  # Second candle gaps down
            (result["close"] > result["open"]) &                                    # Third candle is bullish
            (result["close"] > (result["open"].shift(2) + result["close"].shift(2)) / 2)  # Closes above midpoint of first candle
        )
        
        return result
    
    def _recognize_evening_star(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Recognize Evening Star patterns.
        
        Args:
            data: DataFrame with OHLCV data
            
        Returns:
            DataFrame with Evening Star pattern column
        """
        # Make a copy of the data
        result = data.copy()
        
        # Calculate body sizes
        result["body_size"] = abs(result["close"] - result["open"])
        result["body_size_prev"] = abs(result["close"].shift(1) - result["open"].shift(1))
        result["body_size_prev2"] = abs(result["close"].shift(2) - result["open"].shift(2))
        
        # Evening Star criteria:
        # 1. First candle is bullish with a large body
        # 2. Second candle has a small body and gaps up
        # 3. Third candle is bearish and closes below the midpoint of the first candle
        result["evening_star"] = (
            (result["close"].shift(2) > result["open"].shift(2)) &                  # First candle is bullish
            (result["body_size_prev"] < 0.3 * result["body_size_prev2"]) &          # Second candle has a small body
            (result[["open", "close"]].shift(1).min(axis=1) > result["close"].shift(2)) &  # Second candle gaps up
            (result["close"] < result["open"]) &                                    # Third candle is bearish
            (result["close"] < (result["open"].shift(2) + result["close"].shift(2)) / 2)  # Closes below midpoint of first candle
        )
        
        return result
