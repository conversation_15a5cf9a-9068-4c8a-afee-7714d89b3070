"""
Unit tests for Alpha Vantage data collector.
"""
import os
from datetime import date, timedelta
from unittest.mock import Async<PERSON>ock, MagicMock, patch

import pandas as pd
import pytest

from app.core.data_collection.alpha_vantage import AlphaVantageCollector


@pytest.fixture
def alpha_vantage_collector():
    """Create an Alpha Vantage collector for testing."""
    with patch.dict(os.environ, {"ALPHA_VANTAGE_API_KEY": "test_api_key"}):
        collector = AlphaVantageCollector()
        return collector


@pytest.fixture
def mock_response():
    """Create a mock response for testing."""
    mock = AsyncMock()
    mock.status = 200
    mock.json = AsyncMock(return_value={
        "Meta Data": {
            "1. Information": "Daily Prices (open, high, low, close) and Volumes",
            "2. Symbol": "AAPL",
            "3. Last Refreshed": "2023-01-05",
            "4. Output Size": "Compact",
            "5. Time Zone": "US/Eastern"
        },
        "Time Series (Daily)": {
            "2023-01-05": {
                "1. open": "126.0100",
                "2. high": "127.3300",
                "3. low": "124.7600",
                "4. close": "125.0200",
                "5. volume": "111598466"
            },
            "2023-01-04": {
                "1. open": "130.2800",
                "2. high": "131.0300",
                "3. low": "126.7600",
                "4. close": "126.3600",
                "5. volume": "97457142"
            }
        }
    })
    return mock


@pytest.mark.asyncio
async def test_get_stock_data(alpha_vantage_collector, mock_response):
    """Test getting stock data from Alpha Vantage."""
    # Mock the aiohttp ClientSession
    with patch("aiohttp.ClientSession") as mock_session:
        # Set up the mock
        mock_context = MagicMock()
        mock_context.__aenter__.return_value = mock_response
        mock_session.return_value.get.return_value = mock_context
        
        # Call the method
        start_date = date(2023, 1, 1)
        end_date = date(2023, 1, 5)
        df = await alpha_vantage_collector.get_stock_data("AAPL", start_date, end_date)
        
        # Check the result
        assert not df.empty
        assert len(df) == 2
        assert "open" in df.columns
        assert "high" in df.columns
        assert "low" in df.columns
        assert "close" in df.columns
        assert "volume" in df.columns
        assert df["close"].iloc[0] == 126.36
        assert df["close"].iloc[1] == 125.02


@pytest.mark.asyncio
async def test_get_company_info(alpha_vantage_collector):
    """Test getting company info from Alpha Vantage."""
    # Mock the aiohttp ClientSession
    with patch("aiohttp.ClientSession") as mock_session:
        # Set up the mock
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value={
            "Symbol": "AAPL",
            "Name": "Apple Inc",
            "Description": "Apple Inc. designs, manufactures, and markets smartphones, personal computers, tablets, wearables, and accessories worldwide.",
            "Exchange": "NASDAQ",
            "Currency": "USD",
            "Country": "USA",
            "Sector": "Technology",
            "Industry": "Consumer Electronics",
            "MarketCapitalization": "2100000000000",
            "PERatio": "20.5",
            "DividendYield": "0.0065",
            "52WeekHigh": "150.25",
            "52WeekLow": "124.17",
            "50DayMovingAverage": "135.60",
            "200DayMovingAverage": "140.25",
            "Beta": "1.2"
        })
        mock_context = MagicMock()
        mock_context.__aenter__.return_value = mock_response
        mock_session.return_value.get.return_value = mock_context
        
        # Call the method
        info = await alpha_vantage_collector.get_company_info("AAPL")
        
        # Check the result
        assert info["symbol"] == "AAPL"
        assert info["name"] == "Apple Inc"
        assert info["sector"] == "Technology"
        assert info["marketCap"] == 2100000000000
        assert info["peRatio"] == 20.5
        assert info["dividendYield"] == 0.65  # Converted to percentage


@pytest.mark.asyncio
async def test_search_symbols(alpha_vantage_collector):
    """Test searching symbols from Alpha Vantage."""
    # Mock the aiohttp ClientSession
    with patch("aiohttp.ClientSession") as mock_session:
        # Set up the mock
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value={
            "bestMatches": [
                {
                    "1. symbol": "AAPL",
                    "2. name": "Apple Inc",
                    "3. type": "Equity",
                    "4. region": "United States",
                    "5. marketOpen": "09:30",
                    "6. marketClose": "16:00",
                    "7. timezone": "UTC-05",
                    "8. currency": "USD",
                    "9. matchScore": "1.0000"
                },
                {
                    "1. symbol": "AAPL.LON",
                    "2. name": "Apple Inc",
                    "3. type": "Equity",
                    "4. region": "United Kingdom",
                    "5. marketOpen": "08:00",
                    "6. marketClose": "16:30",
                    "7. timezone": "UTC+00",
                    "8. currency": "GBP",
                    "9. matchScore": "0.8000"
                }
            ]
        })
        mock_context = MagicMock()
        mock_context.__aenter__.return_value = mock_response
        mock_session.return_value.get.return_value = mock_context
        
        # Call the method
        results = await alpha_vantage_collector.search_symbols("Apple")
        
        # Check the result
        assert len(results) == 2
        assert results[0]["symbol"] == "AAPL"
        assert results[0]["name"] == "Apple Inc"
        assert results[0]["type"] == "Equity"
        assert results[0]["region"] == "United States"
        assert results[0]["currency"] == "USD"
        assert results[0]["matchScore"] == 1.0
