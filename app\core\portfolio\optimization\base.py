"""
Base portfolio optimization strategy.
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple, Union

import pandas as pd


class OptimizationStrategy(ABC):
    """
    Abstract base class for portfolio optimization strategies.
    
    All optimization strategies should inherit from this class
    and implement the required methods.
    """
    
    def __init__(self, name: str):
        """
        Initialize the optimization strategy.
        
        Args:
            name: The name of the strategy
        """
        self.name = name
    
    @abstractmethod
    def optimize(
        self,
        returns: pd.DataFrame,
        **kwargs
    ) -> Dict[str, float]:
        """
        Optimize portfolio weights.
        
        Args:
            returns: DataFrame of asset returns
            **kwargs: Additional parameters
            
        Returns:
            Dictionary mapping asset names to weights
        """
        pass
    
    def validate_weights(self, weights: Dict[str, float]) -> bool:
        """
        Validate portfolio weights.
        
        Args:
            weights: Dictionary of asset weights
            
        Returns:
            True if weights are valid, False otherwise
        """
        # Check if weights sum to approximately 1
        weight_sum = sum(weights.values())
        if not (0.99 <= weight_sum <= 1.01):
            return False
        
        # Check if all weights are non-negative
        if any(w < 0 for w in weights.values()):
            return False
        
        return True
