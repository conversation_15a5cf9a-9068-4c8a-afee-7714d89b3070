# Data Collection Module

The data collection module is responsible for fetching financial data from various sources. It provides a unified interface for accessing data from different providers.

## Architecture

The data collection module follows a simple architecture:

1. **BaseDataCollector**: Abstract base class that defines the interface for all data collectors
2. **Specific Collectors**: Implementations for different data sources (Yahoo Finance, Alpha Vantage, etc.)
3. **DataManager**: Unified interface for accessing data from different sources with caching

## Available Data Sources

### Yahoo Finance

The Yahoo Finance collector uses the `yfinance` library to fetch data from Yahoo Finance. It provides access to historical stock data, company information, and more.

**Features:**
- Historical stock data (daily, weekly, monthly)
- Company information
- Symbol search

**Example:**
```python
from app.core.data_collection.yahoo_finance import YahooFinanceCollector

collector = YahooFinanceCollector()
df = await collector.get_stock_data("AAPL", start_date, end_date)
```

### Alpha Vantage

The Alpha Vantage collector uses the Alpha Vantage API to fetch financial data. It requires an API key, which can be obtained for free from the [Alpha Vantage website](https://www.alphavantage.co/support/#api-key).

**Features:**
- Historical stock data (intraday, daily, weekly, monthly)
- Company information
- Symbol search
- Rate limiting to respect API limits

**Example:**
```python
from app.core.data_collection.alpha_vantage import AlphaVantageCollector

collector = AlphaVantageCollector()
df = await collector.get_stock_data("AAPL", start_date, end_date)
```

### Financial Modeling Prep

The Financial Modeling Prep collector uses the Financial Modeling Prep API to fetch financial data. It requires an API key, which can be obtained for free from the [Financial Modeling Prep website](https://financialmodelingprep.com/developer/docs/).

**Features:**
- Historical stock data (intraday, daily, weekly, monthly)
- Company information and profiles
- Symbol search
- Rate limiting to respect API limits
- Additional financial data (not yet implemented)

**Example:**
```python
from app.core.data_collection.financial_modeling_prep import FinancialModelingPrepCollector

collector = FinancialModelingPrepCollector()
df = await collector.get_stock_data("AAPL", start_date, end_date)
```

## Using the Data Manager

The `DataManager` provides a unified interface for accessing data from different sources. It also handles caching to improve performance.

**Example:**
```python
from app.core.data_collection.data_manager import data_manager

# Get stock data (uses default source)
df = await data_manager.get_stock_data("AAPL", start_date, end_date)

# Get stock data from a specific source
df = await data_manager.get_stock_data("AAPL", start_date, end_date, source="alpha_vantage")

# Get stock data from Financial Modeling Prep
df = await data_manager.get_stock_data("AAPL", start_date, end_date, source="financial_modeling_prep")

# Get data for multiple symbols
data = await data_manager.get_multiple_stock_data(["AAPL", "MSFT", "GOOGL"], start_date, end_date)

# Get company information
info = await data_manager.get_company_info("AAPL")

# Search for symbols
results = await data_manager.search_symbols("Apple")
```

## Adding a New Data Source

To add a new data source, follow these steps:

1. Create a new class that inherits from `BaseDataCollector`
2. Implement the required methods:
   - `get_stock_data`
   - `get_multiple_stock_data`
   - `get_company_info`
   - `search_symbols`
3. Register the collector in the `DataManager.__init__` method

**Example:**
```python
from app.core.data_collection.base import BaseDataCollector

class MyDataCollector(BaseDataCollector):
    async def get_stock_data(self, symbol, start_date, end_date, interval="1d"):
        # Implementation
        pass

    async def get_multiple_stock_data(self, symbols, start_date, end_date, interval="1d"):
        # Implementation
        pass

    async def get_company_info(self, symbol):
        # Implementation
        pass

    async def search_symbols(self, query):
        # Implementation
        pass
```

Then register it in the `DataManager`:

```python
# In DataManager.__init__
if self.settings.data_sources.my_source.enabled:
    self.collectors["my_source"] = MyDataCollector()
```

## Caching

The data collection module uses a simple caching mechanism to improve performance. The cache can be configured in the application settings:

```yaml
cache:
  type: memory  # or "file"
  expiry: 3600  # seconds
```

The cache is used automatically by the `DataManager` when fetching data.

## Rate Limiting

Some data sources have rate limits on their APIs. The collectors implement rate limiting to respect these limits. For example, the Alpha Vantage collector uses a rate limiter to ensure that it doesn't exceed the API limits.

The rate limits can be configured in the application settings:

```yaml
data_sources:
  alpha_vantage:
    enabled: true
    rate_limit: 5  # requests per minute
  financial_modeling_prep:
    enabled: true
    rate_limit: 10  # requests per minute
```
