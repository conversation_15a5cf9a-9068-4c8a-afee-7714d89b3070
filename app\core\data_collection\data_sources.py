"""
Data sources manager for portfolio optimization.
"""
from typing import Dict, List, Optional, Union

import pandas as pd

from app.core.data_collection.base import BaseDataCollector
from app.core.data_collection.yahoo_finance import YahooFinanceCollector
from app.utils.logging import logger


class DataSourceManager:
    """
    Data sources manager.
    
    This class manages different data sources and provides a unified
    interface for retrieving financial data.
    """
    
    def __init__(self):
        """Initialize the data sources manager."""
        self.data_sources = {}
        self.default_source = None
        
        # Register built-in data sources
        self.register_data_source("yahoo", YahooFinanceCollector())
        self.set_default_source("yahoo")
    
    def register_data_source(self, name: str, data_source: BaseDataCollector) -> None:
        """
        Register a data source.
        
        Args:
            name: Name of the data source
            data_source: Data source instance
        """
        if not isinstance(data_source, BaseDataCollector):
            logger.error(f"Data source must be an instance of BaseDataCollector")
            return
        
        self.data_sources[name] = data_source
        logger.info(f"Registered data source: {name}")
    
    def unregister_data_source(self, name: str) -> None:
        """
        Unregister a data source.
        
        Args:
            name: Name of the data source
        """
        if name in self.data_sources:
            del self.data_sources[name]
            logger.info(f"Unregistered data source: {name}")
            
            # Update default source if needed
            if self.default_source == name:
                if len(self.data_sources) > 0:
                    self.default_source = list(self.data_sources.keys())[0]
                    logger.info(f"Set default data source to: {self.default_source}")
                else:
                    self.default_source = None
                    logger.warning("No default data source available")
    
    def set_default_source(self, name: str) -> None:
        """
        Set the default data source.
        
        Args:
            name: Name of the data source
        """
        if name in self.data_sources:
            self.default_source = name
            logger.info(f"Set default data source to: {name}")
        else:
            logger.error(f"Data source not found: {name}")
    
    def get_data_source(self, name: Optional[str] = None) -> Optional[BaseDataCollector]:
        """
        Get a data source by name.
        
        Args:
            name: Name of the data source (None for default)
            
        Returns:
            Data source instance or None if not found
        """
        if name is None:
            name = self.default_source
        
        if name in self.data_sources:
            return self.data_sources[name]
        else:
            logger.error(f"Data source not found: {name}")
            return None
    
    async def get_stock_data(
        self,
        symbol: str,
        start_date: Union[str, pd.Timestamp],
        end_date: Union[str, pd.Timestamp],
        interval: str = "1d",
        source: Optional[str] = None
    ) -> pd.DataFrame:
        """
        Get historical stock data for a symbol.
        
        Args:
            symbol: The stock symbol
            start_date: Start date for historical data
            end_date: End date for historical data
            interval: Data interval (e.g., '1d', '1wk', '1mo')
            source: Data source name (None for default)
            
        Returns:
            DataFrame with historical stock data
        """
        data_source = self.get_data_source(source)
        if data_source is None:
            return pd.DataFrame()
        
        # Convert dates if needed
        if isinstance(start_date, str):
            start_date = pd.Timestamp(start_date).date()
        elif isinstance(start_date, pd.Timestamp):
            start_date = start_date.date()
        
        if isinstance(end_date, str):
            end_date = pd.Timestamp(end_date).date()
        elif isinstance(end_date, pd.Timestamp):
            end_date = end_date.date()
        
        try:
            return await data_source.get_stock_data(symbol, start_date, end_date, interval)
        except Exception as e:
            logger.error(f"Error getting stock data for {symbol}: {str(e)}")
            return pd.DataFrame()
    
    async def get_multiple_stock_data(
        self,
        symbols: List[str],
        start_date: Union[str, pd.Timestamp],
        end_date: Union[str, pd.Timestamp],
        interval: str = "1d",
        source: Optional[str] = None
    ) -> Dict[str, pd.DataFrame]:
        """
        Get historical stock data for multiple symbols.
        
        Args:
            symbols: List of stock symbols
            start_date: Start date for historical data
            end_date: End date for historical data
            interval: Data interval (e.g., '1d', '1wk', '1mo')
            source: Data source name (None for default)
            
        Returns:
            Dictionary mapping symbols to DataFrames with historical stock data
        """
        data_source = self.get_data_source(source)
        if data_source is None:
            return {}
        
        # Convert dates if needed
        if isinstance(start_date, str):
            start_date = pd.Timestamp(start_date).date()
        elif isinstance(start_date, pd.Timestamp):
            start_date = start_date.date()
        
        if isinstance(end_date, str):
            end_date = pd.Timestamp(end_date).date()
        elif isinstance(end_date, pd.Timestamp):
            end_date = end_date.date()
        
        try:
            return await data_source.get_multiple_stock_data(symbols, start_date, end_date, interval)
        except Exception as e:
            logger.error(f"Error getting multiple stock data: {str(e)}")
            return {}
    
    async def get_company_info(
        self,
        symbol: str,
        source: Optional[str] = None
    ) -> Dict:
        """
        Get company information for a symbol.
        
        Args:
            symbol: The stock symbol
            source: Data source name (None for default)
            
        Returns:
            Dictionary with company information
        """
        data_source = self.get_data_source(source)
        if data_source is None:
            return {}
        
        try:
            return await data_source.get_company_info(symbol)
        except Exception as e:
            logger.error(f"Error getting company info for {symbol}: {str(e)}")
            return {}
    
    async def search_symbols(
        self,
        query: str,
        source: Optional[str] = None
    ) -> List[Dict]:
        """
        Search for symbols matching a query.
        
        Args:
            query: The search query
            source: Data source name (None for default)
            
        Returns:
            List of dictionaries with symbol information
        """
        data_source = self.get_data_source(source)
        if data_source is None:
            return []
        
        try:
            return await data_source.search_symbols(query)
        except Exception as e:
            logger.error(f"Error searching symbols for '{query}': {str(e)}")
            return []


# Create a global data source manager instance
data_source_manager = DataSourceManager()
