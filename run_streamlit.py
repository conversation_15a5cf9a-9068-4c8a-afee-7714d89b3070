"""
Wrapper script to run the Streamlit app with the correct Python path.
"""
import os
import sys
import subprocess

def main():
    # Get the absolute path to the project root
    project_root = os.path.abspath(os.path.dirname(__file__))

    # Set up the environment
    env = os.environ.copy()

    # Ensure project root is in PYTHONPATH
    # This is critical for the app module to be found
    env["PYTHONPATH"] = project_root

    print(f"Setting PYTHONPATH to: {project_root}")

    # Create a simple wrapper script that imports the app directly
    wrapper_content = """
import streamlit as st
import sys
import os

# Add the project root to sys.path to ensure imports work
project_root = os.path.abspath(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Now import from the app package
from app.streamlit.app import main

if __name__ == "__main__":
    main()
"""

    # Write the wrapper to a temporary file
    wrapper_path = os.path.join(project_root, "streamlit_wrapper.py")
    with open(wrapper_path, "w") as f:
        f.write(wrapper_content)

    # Run streamlit with the wrapper script
    cmd = [
        sys.executable, "-m", "streamlit", "run",
        wrapper_path,
        "--server.port=8501",
        "--server.address=0.0.0.0"
    ]

    try:
        subprocess.run(cmd, env=env, check=True)
    finally:
        # Clean up the temporary file
        if os.path.exists(wrapper_path):
            os.remove(wrapper_path)

if __name__ == "__main__":
    main()
