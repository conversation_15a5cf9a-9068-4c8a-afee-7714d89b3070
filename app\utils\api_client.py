"""
API client for making requests to the backend API.
"""
import asyncio
import json
from typing import Any, Dict, Optional, Union

import aiohttp
from pydantic import BaseModel

from app.settings import get_settings
from app.utils.logging import logger

# Get settings
settings = get_settings()


class APIResponse:
    """API response wrapper."""

    def __init__(self, status_code: int, text: str, json_data: Optional[Dict] = None):
        """Initialize the API response."""
        self.status_code = status_code
        self.text = text
        self._json = json_data

    def json(self) -> Dict:
        """Get the JSON data from the response."""
        if self._json is not None:
            return self._json

        try:
            return json.loads(self.text)
        except json.JSONDecodeError:
            logger.error(f"Failed to decode JSON response: {self.text}")
            return {}


class APIClient:
    """API client for making requests to the backend API."""

    def __init__(self, base_url: Optional[str] = None):
        """Initialize the API client."""
        if base_url is None:
            # Construct base URL from settings
            host = settings.api.host
            port = settings.api.port

            # Use localhost instead of 0.0.0.0 for client requests
            if host == "0.0.0.0":
                host = "localhost"

            self.base_url = f"http://{host}:{port}"
        else:
            self.base_url = base_url

    async def _request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict] = None,
        json: Optional[Union[Dict, BaseModel]] = None,
        headers: Optional[Dict] = None
    ) -> APIResponse:
        """Make a request to the API."""
        # Prepare URL
        # Add API prefix if not already included in the endpoint
        api_prefix = settings.api.prefix
        if not endpoint.startswith(api_prefix):
            endpoint = f"{api_prefix}{endpoint}"

        # Log the endpoint for debugging
        logger.info(f"Making request to endpoint: {endpoint}")
        logger.info(f"Full URL: {self.base_url}{endpoint}")

        url = f"{self.base_url}{endpoint}"

        # Prepare JSON data
        if isinstance(json, BaseModel):
            # Use model_dump instead of dict (which is deprecated)
            try:
                json = json.model_dump()
            except AttributeError:
                # Fallback for older versions of pydantic
                json = json.dict()

        # Prepare headers
        if headers is None:
            headers = {}

        # Add default headers
        headers.setdefault("Content-Type", "application/json")

        try:
            async with aiohttp.ClientSession() as session:
                async with session.request(
                    method=method,
                    url=url,
                    params=params,
                    json=json,
                    headers=headers
                ) as response:
                    text = await response.text()

                    # Try to parse JSON
                    json_data = None
                    try:
                        json_data = await response.json()
                    except aiohttp.ContentTypeError:
                        pass

                    return APIResponse(response.status, text, json_data)

        except aiohttp.ClientError as e:
            logger.error(f"API request error: {e}")
            return APIResponse(500, str(e))

    async def get(
        self,
        endpoint: str,
        params: Optional[Dict] = None,
        headers: Optional[Dict] = None
    ) -> APIResponse:
        """Make a GET request to the API."""
        return await self._request("GET", endpoint, params=params, headers=headers)

    async def post(
        self,
        endpoint: str,
        params: Optional[Dict] = None,
        json: Optional[Union[Dict, BaseModel]] = None,
        headers: Optional[Dict] = None
    ) -> APIResponse:
        """Make a POST request to the API."""
        return await self._request("POST", endpoint, params=params, json=json, headers=headers)

    async def put(
        self,
        endpoint: str,
        params: Optional[Dict] = None,
        json: Optional[Union[Dict, BaseModel]] = None,
        headers: Optional[Dict] = None
    ) -> APIResponse:
        """Make a PUT request to the API."""
        return await self._request("PUT", endpoint, params=params, json=json, headers=headers)

    async def delete(
        self,
        endpoint: str,
        params: Optional[Dict] = None,
        headers: Optional[Dict] = None
    ) -> APIResponse:
        """Make a DELETE request to the API."""
        return await self._request("DELETE", endpoint, params=params, headers=headers)


# Create a synchronous wrapper for the API client
class SyncAPIClient:
    """Synchronous wrapper for the API client."""

    def __init__(self, async_client: APIClient):
        """Initialize the synchronous API client."""
        self.async_client = async_client
        self.base_url = async_client.base_url
        logger.debug(f"SyncAPIClient initialized with base URL: {self.base_url}")

    def get(self, endpoint: str, params: Optional[Dict] = None, headers: Optional[Dict] = None) -> APIResponse:
        """Make a synchronous GET request to the API using requests library."""
        import requests
        url = f"{self.base_url}{endpoint}"
        logger.debug(f"Making GET request to {url}")
        response = requests.get(url, params=params, headers=headers)
        json_data = None
        if response.status_code < 400:
            try:
                json_data = response.json()
            except:
                pass
        return APIResponse(status_code=response.status_code, text=response.text, json_data=json_data)

    def post(self, endpoint: str, params: Optional[Dict] = None, json: Optional[Union[Dict, BaseModel]] = None, headers: Optional[Dict] = None) -> APIResponse:
        """Make a synchronous POST request to the API using requests library."""
        import requests
        url = f"{self.base_url}{endpoint}"
        logger.debug(f"Making POST request to {url}")

        # Convert Pydantic model to dict if needed
        if isinstance(json, BaseModel):
            try:
                # For newer versions of pydantic (v2+)
                json = json.model_dump()
            except AttributeError:
                # Fallback for older versions of pydantic
                json = json.dict()

        response = requests.post(url, params=params, json=json, headers=headers)
        json_data = None
        if response.status_code < 400:
            try:
                json_data = response.json()
            except:
                pass
        return APIResponse(status_code=response.status_code, text=response.text, json_data=json_data)

    def put(self, endpoint: str, params: Optional[Dict] = None, json: Optional[Union[Dict, BaseModel]] = None, headers: Optional[Dict] = None) -> APIResponse:
        """Make a synchronous PUT request to the API using requests library."""
        import requests
        url = f"{self.base_url}{endpoint}"
        logger.debug(f"Making PUT request to {url}")

        # Convert Pydantic model to dict if needed
        if isinstance(json, BaseModel):
            try:
                # For newer versions of pydantic (v2+)
                json = json.model_dump()
            except AttributeError:
                # Fallback for older versions of pydantic
                json = json.dict()

        response = requests.put(url, params=params, json=json, headers=headers)
        json_data = None
        if response.status_code < 400:
            try:
                json_data = response.json()
            except:
                pass
        return APIResponse(status_code=response.status_code, text=response.text, json_data=json_data)

    def delete(self, endpoint: str, params: Optional[Dict] = None, headers: Optional[Dict] = None) -> APIResponse:
        """Make a synchronous DELETE request to the API using requests library."""
        import requests
        url = f"{self.base_url}{endpoint}"
        logger.debug(f"Making DELETE request to {url}")
        response = requests.delete(url, params=params, headers=headers)
        json_data = None
        if response.status_code < 400:
            try:
                json_data = response.json()
            except:
                pass
        return APIResponse(status_code=response.status_code, text=response.text, json_data=json_data)


# Create singleton instances
async_api_client = APIClient()
api_client = SyncAPIClient(async_api_client)
