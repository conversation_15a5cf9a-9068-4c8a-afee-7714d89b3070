"""
Sentiment analysis API endpoints.
"""
from datetime import date
from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.data_collection.sentiment import sentiment_analysis_collector
from app.db.database import get_db
from app.db.models import User
from app.middleware.auth import get_current_user
from app.utils.logging import logger

router = APIRouter()


@router.get("/news/{symbol}")
async def get_news_sentiment(
    symbol: str,
    time_from: Optional[date] = Query(None, description="Start date for news"),
    time_to: Optional[date] = Query(None, description="End date for news"),
    limit: int = Query(50, description="Maximum number of news items to retrieve"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get news sentiment for a symbol.
    """
    try:
        df = await sentiment_analysis_collector.get_news_sentiment(symbol, time_from, time_to, limit)
        
        if df.empty:
            raise HTTPException(
                status_code=404,
                detail=f"News sentiment data not found for {symbol}"
            )
        
        # Convert DataFrame to dictionary
        result = df.reset_index().to_dict(orient="records")
        
        return {
            "symbol": symbol,
            "time_from": time_from,
            "time_to": time_to,
            "data": result
        }
    except Exception as e:
        logger.error(f"Error getting news sentiment for {symbol}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting news sentiment: {str(e)}"
        )


@router.get("/social/{symbol}")
async def get_social_media_sentiment(
    symbol: str,
    time_from: Optional[date] = Query(None, description="Start date for social media posts"),
    time_to: Optional[date] = Query(None, description="End date for social media posts"),
    limit: int = Query(50, description="Maximum number of posts to retrieve"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get social media sentiment for a symbol.
    """
    try:
        df = await sentiment_analysis_collector.get_social_media_sentiment(symbol, time_from, time_to, limit)
        
        if df.empty:
            raise HTTPException(
                status_code=404,
                detail=f"Social media sentiment data not found for {symbol}"
            )
        
        # Convert DataFrame to dictionary
        result = df.reset_index().to_dict(orient="records")
        
        return {
            "symbol": symbol,
            "time_from": time_from,
            "time_to": time_to,
            "data": result
        }
    except Exception as e:
        logger.error(f"Error getting social media sentiment for {symbol}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting social media sentiment: {str(e)}"
        )


@router.get("/analyst/{symbol}")
async def get_analyst_ratings(
    symbol: str,
    limit: int = Query(20, description="Maximum number of ratings to retrieve"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get analyst ratings for a symbol.
    """
    try:
        df = await sentiment_analysis_collector.get_analyst_ratings(symbol, limit)
        
        if df.empty:
            raise HTTPException(
                status_code=404,
                detail=f"Analyst ratings not found for {symbol}"
            )
        
        # Convert DataFrame to dictionary
        result = df.reset_index().to_dict(orient="records")
        
        return {
            "symbol": symbol,
            "data": result
        }
    except Exception as e:
        logger.error(f"Error getting analyst ratings for {symbol}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting analyst ratings: {str(e)}"
        )


@router.get("/insider/{symbol}")
async def get_insider_trading(
    symbol: str,
    limit: int = Query(20, description="Maximum number of transactions to retrieve"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get insider trading data for a symbol.
    """
    try:
        df = await sentiment_analysis_collector.get_insider_trading(symbol, limit)
        
        if df.empty:
            raise HTTPException(
                status_code=404,
                detail=f"Insider trading data not found for {symbol}"
            )
        
        # Convert DataFrame to dictionary
        result = df.reset_index().to_dict(orient="records")
        
        return {
            "symbol": symbol,
            "data": result
        }
    except Exception as e:
        logger.error(f"Error getting insider trading data for {symbol}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting insider trading data: {str(e)}"
        )


@router.get("/aggregate/{symbol}")
async def get_aggregate_sentiment(
    symbol: str,
    time_from: Optional[date] = Query(None, description="Start date for sentiment data"),
    time_to: Optional[date] = Query(None, description="End date for sentiment data"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get aggregate sentiment data for a symbol.
    """
    try:
        result = await sentiment_analysis_collector.get_aggregate_sentiment(symbol, time_from, time_to)
        
        if not result:
            raise HTTPException(
                status_code=404,
                detail=f"Aggregate sentiment data not found for {symbol}"
            )
        
        return {
            "symbol": symbol,
            "time_from": time_from,
            "time_to": time_to,
            "data": result
        }
    except Exception as e:
        logger.error(f"Error getting aggregate sentiment for {symbol}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting aggregate sentiment: {str(e)}"
        )
