"""
Sentiment analysis page for Streamlit app.
"""
import streamlit as st
import pandas as pd
import numpy as np
from datetime import date, datetime, timedelta
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots

from app.core.data_collection.sentiment import sentiment_analysis_collector
from app.utils.logging import logger


async def show_sentiment_analysis_page():
    """Show the sentiment analysis page."""
    st.title("Sentiment Analysis")
    
    # Get user inputs
    with st.sidebar:
        st.header("Settings")
        
        # Get symbol
        symbol = st.text_input(
            "Enter stock symbol",
            value="AAPL"
        ).upper()
        
        # Get date range
        col1, col2 = st.columns(2)
        with col1:
            start_date = st.date_input(
                "Start Date",
                value=date.today() - timedelta(days=30)
            )
        with col2:
            end_date = st.date_input(
                "End Date",
                value=date.today()
            )
    
    # Create tabs for different analysis types
    tabs = st.tabs([
        "Aggregate Sentiment",
        "News Sentiment",
        "Social Media",
        "Analyst Ratings",
        "Insider Trading"
    ])
    
    # Aggregate Sentiment tab
    with tabs[0]:
        await show_aggregate_sentiment(symbol, start_date, end_date)
    
    # News Sentiment tab
    with tabs[1]:
        await show_news_sentiment(symbol, start_date, end_date)
    
    # Social Media tab
    with tabs[2]:
        await show_social_media_sentiment(symbol, start_date, end_date)
    
    # Analyst Ratings tab
    with tabs[3]:
        await show_analyst_ratings(symbol)
    
    # Insider Trading tab
    with tabs[4]:
        await show_insider_trading(symbol)


async def show_aggregate_sentiment(symbol: str, start_date: date, end_date: date):
    """Show aggregate sentiment analysis."""
    st.header("Aggregate Sentiment Analysis")
    
    with st.spinner(f"Analyzing sentiment for {symbol}..."):
        try:
            # Get aggregate sentiment
            sentiment_data = await sentiment_analysis_collector.get_aggregate_sentiment(
                symbol, start_date, end_date
            )
            
            if not sentiment_data:
                st.error(f"No sentiment data found for {symbol}")
                return
            
            # Display overall sentiment
            if 'overall' in sentiment_data:
                overall = sentiment_data['overall']
                
                # Create columns for overall sentiment
                col1, col2 = st.columns(2)
                
                with col1:
                    # Display sentiment score
                    st.metric(
                        "Overall Sentiment Score",
                        f"{overall['score']:.2f}"
                    )
                
                with col2:
                    # Display sentiment category
                    sentiment_category = overall['sentiment']
                    
                    # Set color based on sentiment
                    if sentiment_category == 'positive':
                        sentiment_color = 'green'
                    elif sentiment_category == 'negative':
                        sentiment_color = 'red'
                    else:
                        sentiment_color = 'gray'
                    
                    st.markdown(
                        f"<h2 style='text-align: center; color: {sentiment_color};'>{sentiment_category.capitalize()}</h2>",
                        unsafe_allow_html=True
                    )
            
            # Display sentiment breakdown
            st.subheader("Sentiment Breakdown")
            
            # Create columns for different sentiment sources
            cols = st.columns(3)
            
            # News sentiment
            if 'news_sentiment' in sentiment_data:
                with cols[0]:
                    st.subheader("News Sentiment")
                    
                    news = sentiment_data['news_sentiment']
                    
                    # Display average score
                    st.metric(
                        "Average Score",
                        f"{news['average_score']:.2f}"
                    )
                    
                    # Display sentiment distribution
                    fig = go.Figure()
                    
                    # Add pie chart
                    fig.add_trace(
                        go.Pie(
                            labels=['Positive', 'Neutral', 'Negative'],
                            values=[
                                news['positive_count'],
                                news['neutral_count'],
                                news['negative_count']
                            ],
                            marker=dict(
                                colors=['green', 'gray', 'red']
                            )
                        )
                    )
                    
                    # Update layout
                    fig.update_layout(
                        title="News Sentiment Distribution",
                        height=300
                    )
                    
                    st.plotly_chart(fig, use_container_width=True)
            
            # Social media sentiment
            if 'social_sentiment' in sentiment_data:
                with cols[1]:
                    st.subheader("Social Media Sentiment")
                    
                    social = sentiment_data['social_sentiment']
                    
                    # Display average score
                    st.metric(
                        "Average Score",
                        f"{social['average_score']:.2f}"
                    )
                    
                    # Display sentiment distribution
                    fig = go.Figure()
                    
                    # Add pie chart
                    fig.add_trace(
                        go.Pie(
                            labels=['Positive', 'Neutral', 'Negative'],
                            values=[
                                social['positive_count'],
                                social['neutral_count'],
                                social['negative_count']
                            ],
                            marker=dict(
                                colors=['green', 'gray', 'red']
                            )
                        )
                    )
                    
                    # Update layout
                    fig.update_layout(
                        title="Social Media Sentiment Distribution",
                        height=300
                    )
                    
                    st.plotly_chart(fig, use_container_width=True)
            
            # Analyst sentiment
            if 'analyst_sentiment' in sentiment_data:
                with cols[2]:
                    st.subheader("Analyst Sentiment")
                    
                    analyst = sentiment_data['analyst_sentiment']
                    
                    # Display average rating
                    st.metric(
                        "Average Rating",
                        f"{analyst['average_rating']:.2f} / 5"
                    )
                    
                    # Display recommendation distribution
                    if 'recommendation_counts' in analyst:
                        recommendations = analyst['recommendation_counts']
                        
                        # Create DataFrame for plotting
                        rec_df = pd.DataFrame({
                            'Recommendation': list(recommendations.keys()),
                            'Count': list(recommendations.values())
                        })
                        
                        # Sort by recommendation
                        rec_order = ['Strong Buy', 'Buy', 'Hold', 'Sell', 'Strong Sell']
                        rec_df['Order'] = rec_df['Recommendation'].map(
                            {rec: i for i, rec in enumerate(rec_order)}
                        )
                        rec_df = rec_df.sort_values('Order')
                        
                        # Create bar chart
                        fig = px.bar(
                            rec_df,
                            x='Recommendation',
                            y='Count',
                            title="Analyst Recommendations",
                            color='Recommendation',
                            color_discrete_map={
                                'Strong Buy': 'darkgreen',
                                'Buy': 'green',
                                'Hold': 'gray',
                                'Sell': 'red',
                                'Strong Sell': 'darkred'
                            }
                        )
                        
                        # Update layout
                        fig.update_layout(
                            height=300,
                            showlegend=False
                        )
                        
                        st.plotly_chart(fig, use_container_width=True)
            
            # Display sentiment over time
            st.subheader("Sentiment Over Time")
            
            st.info("""
            This chart would show sentiment trends over time.
            Due to API limitations, we're not able to show historical sentiment data.
            In a production environment, this would display sentiment trends based on stored historical data.
            """)
            
        except Exception as e:
            st.error(f"Error analyzing sentiment: {str(e)}")
            logger.error(f"Error analyzing sentiment for {symbol}: {str(e)}")


async def show_news_sentiment(symbol: str, start_date: date, end_date: date):
    """Show news sentiment analysis."""
    st.header("News Sentiment Analysis")
    
    with st.spinner(f"Loading news sentiment for {symbol}..."):
        try:
            # Get news sentiment
            df = await sentiment_analysis_collector.get_news_sentiment(
                symbol, start_date, end_date
            )
            
            if df.empty:
                st.error(f"No news sentiment data found for {symbol}")
                return
            
            # Display sentiment statistics
            if 'sentiment_score' in df.columns:
                # Calculate statistics
                avg_score = df['sentiment_score'].mean()
                positive_count = (df['sentiment'] == 'positive').sum()
                neutral_count = (df['sentiment'] == 'neutral').sum()
                negative_count = (df['sentiment'] == 'negative').sum()
                total_count = len(df)
                
                # Create columns for statistics
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    st.metric(
                        "Average Score",
                        f"{avg_score:.2f}"
                    )
                
                with col2:
                    st.metric(
                        "Positive",
                        f"{positive_count} ({positive_count / total_count * 100:.1f}%)"
                    )
                
                with col3:
                    st.metric(
                        "Neutral",
                        f"{neutral_count} ({neutral_count / total_count * 100:.1f}%)"
                    )
                
                with col4:
                    st.metric(
                        "Negative",
                        f"{negative_count} ({negative_count / total_count * 100:.1f}%)"
                    )
                
                # Plot sentiment distribution
                st.subheader("Sentiment Distribution")
                
                fig = go.Figure()
                
                # Add pie chart
                fig.add_trace(
                    go.Pie(
                        labels=['Positive', 'Neutral', 'Negative'],
                        values=[positive_count, neutral_count, negative_count],
                        marker=dict(
                            colors=['green', 'gray', 'red']
                        )
                    )
                )
                
                # Update layout
                fig.update_layout(
                    title="News Sentiment Distribution"
                )
                
                st.plotly_chart(fig, use_container_width=True)
            
            # Display news articles
            st.subheader("News Articles")
            
            # Sort by date (newest first)
            if 'date' in df.columns:
                df = df.sort_values('date', ascending=False)
            
            # Display each news article
            for i, row in df.iterrows():
                # Create expander for each article
                with st.expander(f"{row.get('title', 'News Article')}"):
                    # Display article details
                    st.write(f"**Source:** {row.get('source', 'Unknown')}")
                    st.write(f"**Date:** {row.get('date', 'Unknown')}")
                    
                    # Display sentiment
                    if 'sentiment_score' in row:
                        sentiment = row['sentiment']
                        score = row['sentiment_score']
                        
                        # Set color based on sentiment
                        if sentiment == 'positive':
                            sentiment_color = 'green'
                        elif sentiment == 'negative':
                            sentiment_color = 'red'
                        else:
                            sentiment_color = 'gray'
                        
                        st.markdown(
                            f"**Sentiment:** <span style='color: {sentiment_color};'>{sentiment.capitalize()} ({score:.2f})</span>",
                            unsafe_allow_html=True
                        )
                    
                    # Display summary
                    if 'summary' in row:
                        st.write(f"**Summary:** {row['summary']}")
                    
                    # Display URL
                    if 'url' in row:
                        st.write(f"**URL:** [{row['url']}]({row['url']})")
            
        except Exception as e:
            st.error(f"Error loading news sentiment: {str(e)}")
            logger.error(f"Error loading news sentiment for {symbol}: {str(e)}")


async def show_social_media_sentiment(symbol: str, start_date: date, end_date: date):
    """Show social media sentiment analysis."""
    st.header("Social Media Sentiment Analysis")
    
    with st.spinner(f"Loading social media sentiment for {symbol}..."):
        try:
            # Get social media sentiment
            df = await sentiment_analysis_collector.get_social_media_sentiment(
                symbol, start_date, end_date
            )
            
            if df.empty:
                st.error(f"No social media sentiment data found for {symbol}")
                return
            
            # Display sentiment statistics
            if 'sentiment_score' in df.columns:
                # Calculate statistics
                avg_score = df['sentiment_score'].mean()
                positive_count = (df['sentiment'] == 'positive').sum()
                neutral_count = (df['sentiment'] == 'neutral').sum()
                negative_count = (df['sentiment'] == 'negative').sum()
                total_count = len(df)
                
                # Create columns for statistics
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    st.metric(
                        "Average Score",
                        f"{avg_score:.2f}"
                    )
                
                with col2:
                    st.metric(
                        "Positive",
                        f"{positive_count} ({positive_count / total_count * 100:.1f}%)"
                    )
                
                with col3:
                    st.metric(
                        "Neutral",
                        f"{neutral_count} ({neutral_count / total_count * 100:.1f}%)"
                    )
                
                with col4:
                    st.metric(
                        "Negative",
                        f"{negative_count} ({negative_count / total_count * 100:.1f}%)"
                    )
                
                # Plot sentiment distribution
                st.subheader("Sentiment Distribution")
                
                fig = go.Figure()
                
                # Add pie chart
                fig.add_trace(
                    go.Pie(
                        labels=['Positive', 'Neutral', 'Negative'],
                        values=[positive_count, neutral_count, negative_count],
                        marker=dict(
                            colors=['green', 'gray', 'red']
                        )
                    )
                )
                
                # Update layout
                fig.update_layout(
                    title="Social Media Sentiment Distribution"
                )
                
                st.plotly_chart(fig, use_container_width=True)
            
            # Display social media posts
            st.subheader("Social Media Posts")
            
            # Sort by date (newest first)
            if 'date' in df.columns:
                df = df.sort_values('date', ascending=False)
            
            # Display each post
            for i, row in df.iterrows():
                # Create expander for each post
                with st.expander(f"Post from {row.get('user', 'Unknown')}"):
                    # Display post details
                    st.write(f"**User:** {row.get('user', 'Unknown')}")
                    st.write(f"**Date:** {row.get('date', 'Unknown')}")
                    
                    # Display sentiment
                    if 'sentiment_score' in row:
                        sentiment = row['sentiment']
                        score = row['sentiment_score']
                        
                        # Set color based on sentiment
                        if sentiment == 'positive':
                            sentiment_color = 'green'
                        elif sentiment == 'negative':
                            sentiment_color = 'red'
                        else:
                            sentiment_color = 'gray'
                        
                        st.markdown(
                            f"**Sentiment:** <span style='color: {sentiment_color};'>{sentiment.capitalize()} ({score:.2f})</span>",
                            unsafe_allow_html=True
                        )
                    
                    # Display text
                    if 'text' in row:
                        st.write(f"**Text:** {row['text']}")
            
        except Exception as e:
            st.error(f"Error loading social media sentiment: {str(e)}")
            logger.error(f"Error loading social media sentiment for {symbol}: {str(e)}")


async def show_analyst_ratings(symbol: str):
    """Show analyst ratings."""
    st.header("Analyst Ratings")
    
    with st.spinner(f"Loading analyst ratings for {symbol}..."):
        try:
            # Get analyst ratings
            df = await sentiment_analysis_collector.get_analyst_ratings(symbol)
            
            if df.empty:
                st.error(f"No analyst ratings found for {symbol}")
                return
            
            # Display analyst ratings
            st.dataframe(df)
            
            # Plot analyst recommendations
            if 'recommendation' in df.columns:
                st.subheader("Analyst Recommendations")
                
                # Count recommendations
                recommendation_counts = df['recommendation'].value_counts().reset_index()
                recommendation_counts.columns = ['Recommendation', 'Count']
                
                # Sort by recommendation
                rec_order = ['Strong Buy', 'Buy', 'Hold', 'Sell', 'Strong Sell']
                recommendation_counts['Order'] = recommendation_counts['Recommendation'].map(
                    {rec: i for i, rec in enumerate(rec_order)}
                )
                recommendation_counts = recommendation_counts.sort_values('Order')
                
                # Create bar chart
                fig = px.bar(
                    recommendation_counts,
                    x='Recommendation',
                    y='Count',
                    title=f"Analyst Recommendations for {symbol}",
                    color='Recommendation',
                    color_discrete_map={
                        'Strong Buy': 'darkgreen',
                        'Buy': 'green',
                        'Hold': 'gray',
                        'Sell': 'red',
                        'Strong Sell': 'darkred'
                    }
                )
                
                st.plotly_chart(fig, use_container_width=True)
            
            # Plot price targets
            if 'priceTarget' in df.columns:
                st.subheader("Price Targets")
                
                # Calculate statistics
                avg_target = df['priceTarget'].mean()
                min_target = df['priceTarget'].min()
                max_target = df['priceTarget'].max()
                
                # Create columns for statistics
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.metric(
                        "Average Target",
                        f"${avg_target:.2f}"
                    )
                
                with col2:
                    st.metric(
                        "Minimum Target",
                        f"${min_target:.2f}"
                    )
                
                with col3:
                    st.metric(
                        "Maximum Target",
                        f"${max_target:.2f}"
                    )
                
                # Create histogram
                fig = px.histogram(
                    df,
                    x='priceTarget',
                    title=f"Price Target Distribution for {symbol}",
                    nbins=10
                )
                
                st.plotly_chart(fig, use_container_width=True)
            
        except Exception as e:
            st.error(f"Error loading analyst ratings: {str(e)}")
            logger.error(f"Error loading analyst ratings for {symbol}: {str(e)}")


async def show_insider_trading(symbol: str):
    """Show insider trading data."""
    st.header("Insider Trading")
    
    with st.spinner(f"Loading insider trading data for {symbol}..."):
        try:
            # Get insider trading data
            df = await sentiment_analysis_collector.get_insider_trading(symbol)
            
            if df.empty:
                st.error(f"No insider trading data found for {symbol}")
                return
            
            # Display insider trading data
            st.dataframe(df)
            
            # Plot insider trading activity
            if 'transactionType' in df.columns and 'transactionDate' in df.columns:
                st.subheader("Insider Trading Activity")
                
                # Convert date to datetime
                df['transactionDate'] = pd.to_datetime(df['transactionDate'])
                
                # Sort by date
                df = df.sort_values('transactionDate')
                
                # Create figure
                fig = go.Figure()
                
                # Add trace for each transaction type
                for transaction_type in df['transactionType'].unique():
                    # Filter by transaction type
                    type_df = df[df['transactionType'] == transaction_type]
                    
                    # Determine color
                    if 'buy' in transaction_type.lower() or 'purchase' in transaction_type.lower():
                        color = 'green'
                    elif 'sell' in transaction_type.lower():
                        color = 'red'
                    else:
                        color = 'gray'
                    
                    # Add scatter plot
                    fig.add_trace(
                        go.Scatter(
                            x=type_df['transactionDate'],
                            y=type_df['transactionShares'],
                            mode='markers',
                            name=transaction_type,
                            marker=dict(
                                color=color,
                                size=10
                            )
                        )
                    )
                
                # Update layout
                fig.update_layout(
                    title=f"Insider Trading Activity for {symbol}",
                    xaxis_title="Date",
                    yaxis_title="Shares",
                    legend=dict(
                        orientation="h",
                        yanchor="bottom",
                        y=1.02,
                        xanchor="right",
                        x=1
                    )
                )
                
                st.plotly_chart(fig, use_container_width=True)
            
            # Plot insider trading by insider
            if 'reportingName' in df.columns and 'transactionShares' in df.columns:
                st.subheader("Insider Trading by Insider")
                
                # Group by insider
                insider_df = df.groupby('reportingName')['transactionShares'].sum().reset_index()
                
                # Sort by shares
                insider_df = insider_df.sort_values('transactionShares', ascending=False)
                
                # Create bar chart
                fig = px.bar(
                    insider_df,
                    x='reportingName',
                    y='transactionShares',
                    title=f"Insider Trading by Insider for {symbol}"
                )
                
                # Update layout
                fig.update_layout(
                    xaxis_title="Insider",
                    yaxis_title="Shares",
                    xaxis=dict(
                        tickangle=45
                    )
                )
                
                st.plotly_chart(fig, use_container_width=True)
            
        except Exception as e:
            st.error(f"Error loading insider trading data: {str(e)}")
            logger.error(f"Error loading insider trading data for {symbol}: {str(e)}")
