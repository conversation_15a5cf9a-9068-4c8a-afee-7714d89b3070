name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      redis:
        image: redis:7
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
        cache: 'pip'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: Lint with flake8
      run: |
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
    
    - name: Check formatting with black
      run: |
        black --check .
    
    - name: Run unit tests
      run: |
        pytest tests/unit
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379/0
        ENVIRONMENT: test
    
    - name: Run integration tests
      run: |
        pytest tests/integration
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379/0
        ENVIRONMENT: test

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: Login to DockerHub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}
    
    - name: Extract branch name
      shell: bash
      run: echo "BRANCH_NAME=$(echo ${GITHUB_REF#refs/heads/})" >> $GITHUB_ENV
    
    - name: Build and push API image
      uses: docker/build-push-action@v4
      with:
        context: .
        file: ./deployment/docker/Dockerfile
        push: true
        tags: ${{ secrets.DOCKERHUB_USERNAME }}/portfolio-optimization-api:${{ env.BRANCH_NAME }}
        build-args: |
          ENVIRONMENT=${{ env.BRANCH_NAME }}
    
    - name: Build and push Streamlit image
      uses: docker/build-push-action@v4
      with:
        context: .
        file: ./deployment/docker/Dockerfile
        push: true
        tags: ${{ secrets.DOCKERHUB_USERNAME }}/portfolio-optimization-streamlit:${{ env.BRANCH_NAME }}
        build-args: |
          ENVIRONMENT=${{ env.BRANCH_NAME }}

  deploy-staging:
    needs: build
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/develop'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to staging
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.STAGING_HOST }}
        username: ${{ secrets.STAGING_USERNAME }}
        key: ${{ secrets.STAGING_SSH_KEY }}
        script: |
          cd /opt/portfolio-optimization
          git pull
          docker-compose -f deployment/docker/docker-compose.yml -f deployment/docker/docker-compose.prod.yml down
          docker-compose -f deployment/docker/docker-compose.yml -f deployment/docker/docker-compose.prod.yml pull
          docker-compose -f deployment/docker/docker-compose.yml -f deployment/docker/docker-compose.prod.yml up -d
          docker-compose -f deployment/docker/docker-compose.yml -f deployment/docker/docker-compose.prod.yml exec -T api alembic upgrade head

  deploy-production:
    needs: build
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to production
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.PRODUCTION_HOST }}
        username: ${{ secrets.PRODUCTION_USERNAME }}
        key: ${{ secrets.PRODUCTION_SSH_KEY }}
        script: |
          cd /opt/portfolio-optimization
          git pull
          docker-compose -f deployment/docker/docker-compose.yml -f deployment/docker/docker-compose.prod.yml down
          docker-compose -f deployment/docker/docker-compose.yml -f deployment/docker/docker-compose.prod.yml pull
          docker-compose -f deployment/docker/docker-compose.yml -f deployment/docker/docker-compose.prod.yml up -d
          docker-compose -f deployment/docker/docker-compose.yml -f deployment/docker/docker-compose.prod.yml exec -T api alembic upgrade head
