apiVersion: apps/v1
kind: Deployment
metadata:
  name: portfolio-worker
  labels:
    app: portfolio-worker
spec:
  replicas: 2
  selector:
    matchLabels:
      app: portfolio-worker
  template:
    metadata:
      labels:
        app: portfolio-worker
    spec:
      containers:
      - name: portfolio-worker
        image: portfolio-optimization/worker:latest
        imagePullPolicy: IfNotPresent
        command: ["/app/entrypoint.sh", "worker"]
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: portfolio-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: portfolio-secrets
              key: redis-url
        resources:
          limits:
            cpu: "1"
            memory: "1Gi"
          requests:
            cpu: "500m"
            memory: "512Mi"
