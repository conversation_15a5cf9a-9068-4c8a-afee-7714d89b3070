"""
Transaction repository for database operations.
"""
from datetime import datetime
from typing import Dict, List, Optional

from sqlalchemy.orm import Session

from app.db.models import Transaction
from app.db.repositories.base import BaseRepository


class TransactionRepository(BaseRepository[Transaction, Dict, Dict]):
    """
    Transaction repository for database operations.
    """
    
    def __init__(self):
        """Initialize the repository."""
        super().__init__(Transaction)
    
    def get_by_portfolio_id(self, db: Session, portfolio_id: int) -> List[Transaction]:
        """
        Get transactions by portfolio ID.
        
        Args:
            db: Database session
            portfolio_id: Portfolio ID
            
        Returns:
            List of transactions
        """
        return db.query(self.model).filter(self.model.portfolio_id == portfolio_id).all()
    
    def get_by_asset_id(self, db: Session, asset_id: int) -> List[Transaction]:
        """
        Get transactions by asset ID.
        
        Args:
            db: Database session
            asset_id: Asset ID
            
        Returns:
            List of transactions
        """
        return db.query(self.model).filter(self.model.asset_id == asset_id).all()
    
    def get_by_portfolio_and_asset(
        self,
        db: Session,
        portfolio_id: int,
        asset_id: int
    ) -> List[Transaction]:
        """
        Get transactions by portfolio ID and asset ID.
        
        Args:
            db: Database session
            portfolio_id: Portfolio ID
            asset_id: Asset ID
            
        Returns:
            List of transactions
        """
        return db.query(self.model).filter(
            self.model.portfolio_id == portfolio_id,
            self.model.asset_id == asset_id
        ).all()
    
    def create_transaction(
        self,
        db: Session,
        portfolio_id: int,
        asset_id: int,
        transaction_type: str,
        shares: float,
        price: float,
        transaction_date: datetime
    ) -> Transaction:
        """
        Create a new transaction.
        
        Args:
            db: Database session
            portfolio_id: Portfolio ID
            asset_id: Asset ID
            transaction_type: Transaction type ("buy" or "sell")
            shares: Number of shares
            price: Price per share
            transaction_date: Transaction date
            
        Returns:
            Created transaction
        """
        # Calculate total amount
        total_amount = shares * price
        
        # Create transaction
        transaction = Transaction(
            portfolio_id=portfolio_id,
            asset_id=asset_id,
            transaction_type=transaction_type,
            shares=shares,
            price=price,
            total_amount=total_amount,
            transaction_date=transaction_date
        )
        
        # Save transaction to database
        db.add(transaction)
        db.commit()
        db.refresh(transaction)
        
        return transaction
    
    def get_portfolio_transactions_by_date_range(
        self,
        db: Session,
        portfolio_id: int,
        start_date: datetime,
        end_date: datetime
    ) -> List[Transaction]:
        """
        Get transactions by portfolio ID and date range.
        
        Args:
            db: Database session
            portfolio_id: Portfolio ID
            start_date: Start date
            end_date: End date
            
        Returns:
            List of transactions
        """
        return db.query(self.model).filter(
            self.model.portfolio_id == portfolio_id,
            self.model.transaction_date >= start_date,
            self.model.transaction_date <= end_date
        ).all()


# Create a singleton instance
transaction_repository = TransactionRepository()
