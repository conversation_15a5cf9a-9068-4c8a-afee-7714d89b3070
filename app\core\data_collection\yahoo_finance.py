"""
Yahoo Finance data collector implementation using yfinance.
"""
import asyncio
import time
from datetime import date, timedelta
from typing import Dict, List

import pandas as pd
import yfinance as yf
from aiolimiter import AsyncLimiter

from app.core.data_collection.base import BaseDataCollector
from app.utils.logging import logger


class YahooFinanceCollector(BaseDataCollector):
    """
    Yahoo Finance data collector using yfinance.
    """

    def __init__(self):
        """Initialize the Yahoo Finance collector."""
        logger.info("Initializing Yahoo Finance collector")

        # Add rate limiting to avoid hitting Yahoo Finance limits
        # Yahoo Finance allows about 2000 requests per hour, so we'll be conservative
        # and limit to 30 requests per minute (1800 per hour)
        self.rate_limiter = AsyncLimiter(30, 60)  # 30 requests per minute

        # Cache for recent requests to avoid duplicate calls
        self._cache = {}
        self._cache_expiry = 1800  # 30 minutes cache (longer to reduce API calls)

        # Track rate limit status
        self._rate_limited_until = 0  # Timestamp when rate limit expires

    async def get_stock_data(
        self,
        symbol: str,
        start_date: date,
        end_date: date,
        interval: str = "1d"
    ) -> pd.DataFrame:
        """
        Get historical stock data for a symbol.

        Args:
            symbol: The stock symbol
            start_date: Start date for historical data
            end_date: End date for historical data
            interval: Data interval (e.g., '1d', '1wk', '1mo')

        Returns:
            DataFrame with historical stock data
        """
        try:
            logger.debug(f"Getting stock data for {symbol} from {start_date} to {end_date}")

            # Check cache first
            cache_key = f"{symbol}_{start_date}_{end_date}_{interval}"
            if cache_key in self._cache:
                cache_time, cached_data = self._cache[cache_key]
                if time.time() - cache_time < self._cache_expiry:
                    logger.debug(f"Returning cached data for {symbol}")
                    return cached_data

            # Check if we're currently rate limited
            if time.time() < self._rate_limited_until:
                logger.warning(f"Rate limited for {symbol}, using cached data or dummy data")
                # Try to return cached data even if expired
                if cache_key in self._cache:
                    _, cached_data = self._cache[cache_key]
                    logger.info(f"Returning expired cached data for {symbol} due to rate limiting")
                    return cached_data
                else:
                    logger.warning(f"No cached data available for {symbol}, creating dummy data")
                    return self._create_dummy_data(symbol)

            # Define standard columns we want in the output
            standard_columns = ['date', 'open', 'high', 'low', 'close', 'volume']

            # Add buffers to dates to ensure we get enough data
            buffer_end_date = end_date + timedelta(days=5)
            buffer_start_date = start_date - timedelta(days=5)

            # Try multiple approaches to get data
            df = await self._try_download_data(symbol, buffer_start_date, buffer_end_date, interval)

            # If we still don't have data, create dummy data
            if df is None or df.empty:
                logger.warning(f"Creating dummy data for {symbol}")
                df = self._create_dummy_data(symbol)

            # Standardize the DataFrame format
            df = self._standardize_dataframe(df)

            # Ensure all required columns exist
            df = self._ensure_required_columns(df, standard_columns)

            # Convert date column to datetime if needed
            if 'date' in df.columns and not pd.api.types.is_datetime64_any_dtype(df['date']):
                try:
                    df['date'] = pd.to_datetime(df['date'])
                except Exception as e:
                    logger.error(f"Error converting date column to datetime for {symbol}: {e}")
                    # Create a new date column
                    df['date'] = pd.date_range(end=pd.Timestamp.now(), periods=len(df) or 10)

            # Sort by date
            if 'date' in df.columns:
                df = df.sort_values('date')

            # Return only the standard columns
            for col in standard_columns:
                if col not in df.columns:
                    if col == 'date':
                        df['date'] = pd.date_range(end=pd.Timestamp.now(), periods=len(df) or 10)
                    else:
                        df[col] = 100.0 if col != 'volume' else 1000000

            # Cache the successful result
            cache_key = f"{symbol}_{start_date}_{end_date}_{interval}"
            self._cache[cache_key] = (time.time(), df[standard_columns])

            return df[standard_columns]

        except Exception as e:
            logger.error(f"Unexpected error getting stock data for {symbol}: {e}")
            # Return dummy data as a last resort
            return self._create_dummy_data(symbol)

    async def _try_download_data(self, symbol, start_date, end_date, interval):
        """Try different methods to download data."""
        # Method 1: Use yf.download with direct synchronous call to avoid any issues
        try:
            # Apply rate limiting before making the request
            async with self.rate_limiter:
                logger.debug(f"Rate limited request for {symbol}")

                # Add a small delay to be extra careful with rate limiting
                await asyncio.sleep(0.1)

                # Direct synchronous call for more reliable results
                import yfinance as yf
                df = yf.download(
                    symbol,
                    start=start_date,
                    end=end_date,
                    interval=interval,
                    progress=False,
                    prepost=True,
                    actions=True,
                    threads=False,  # Disable threading for more reliable results
                    auto_adjust=True
                )

            if not df.empty:
                logger.debug(f"Downloaded data for {symbol}: {df.shape[0]} rows, {df.shape[1]} columns")
                # Print column names for debugging
                logger.debug(f"Columns: {df.columns.tolist()}")

                # Create a new DataFrame with standardized columns
                standard_df = pd.DataFrame()

                # First, add the date column from the index
                if isinstance(df.index, pd.DatetimeIndex):
                    standard_df['date'] = df.index

                # For multi-level columns, we need special handling
                if isinstance(df.columns, pd.MultiIndex):
                    logger.debug(f"Processing multi-level columns for {symbol}")

                    # Print all columns for debugging
                    for col in df.columns:
                        logger.debug(f"Column: {col}")

                    # Direct mapping based on the exact format we're seeing in the logs
                    # The format appears to be (column_type, symbol_name)
                    column_mapping = {
                        'open': ('Open', symbol),
                        'high': ('High', symbol),
                        'low': ('Low', symbol),
                        'close': ('Close', symbol),
                        'volume': ('Volume', symbol)
                    }

                    # Map each column directly
                    for std_col, col_tuple in column_mapping.items():
                        if col_tuple in df.columns:
                            standard_df[std_col] = df[col_tuple].values
                            logger.debug(f"Directly mapped {col_tuple} to {std_col}")
                        else:
                            # Try case-insensitive match
                            for col in df.columns:
                                if isinstance(col, tuple) and len(col) >= 2:
                                    if col[0].lower() == col_tuple[0].lower() and col[1].lower() == col_tuple[1].lower():
                                        standard_df[std_col] = df[col].values
                                        logger.debug(f"Case-insensitive mapped {col} to {std_col}")
                                        break
                            else:
                                logger.warning(f"Could not find column for {std_col}, tried {col_tuple}")
                                # Use default values
                                if std_col == 'volume':
                                    standard_df[std_col] = 1000000
                                else:
                                    # For price columns, use reasonable values
                                    base_price = 100.0
                                    if std_col == 'open':
                                        standard_df[std_col] = base_price
                                    elif std_col == 'high':
                                        standard_df[std_col] = base_price * 1.01
                                    elif std_col == 'low':
                                        standard_df[std_col] = base_price * 0.99
                                    elif std_col == 'close':
                                        standard_df[std_col] = base_price
                else:
                    # For regular columns, map them directly
                    ohlcv_mapping = {
                        'open': ['Open', 'open', 'OPEN'],
                        'high': ['High', 'high', 'HIGH'],
                        'low': ['Low', 'low', 'LOW'],
                        'close': ['Close', 'close', 'CLOSE', 'Adj Close', 'adj close'],
                        'volume': ['Volume', 'volume', 'VOLUME']
                    }

                    # Process each column
                    for std_col, possible_names in ohlcv_mapping.items():
                        found = False
                        for possible_name in possible_names:
                            if possible_name in df.columns:
                                standard_df[std_col] = df[possible_name].values
                                found = True
                                logger.debug(f"Mapped {possible_name} to {std_col}")
                                break
                            # Try case-insensitive match
                            else:
                                for col in df.columns:
                                    if isinstance(col, str) and col.lower() == possible_name.lower():
                                        standard_df[std_col] = df[col].values
                                        found = True
                                        logger.debug(f"Mapped {col} to {std_col} (case-insensitive)")
                                        break
                            if found:
                                break

                        if not found:
                            logger.warning(f"Could not find column for {std_col}")

                # Log the standardized columns
                logger.debug(f"Standardized columns: {standard_df.columns.tolist()}")

                # Return the standardized DataFrame if it has data
                if not standard_df.empty and len(standard_df.columns) >= 2:  # At least date and one price column
                    return standard_df
                else:
                    logger.warning(f"Standardized DataFrame is empty or missing columns")
                    # Return the original DataFrame as a fallback
                    return df
        except Exception as e:
            logger.warning(f"Error using yf.download for {symbol}: {e}")
            # Check if this is a rate limiting error
            if "Rate limited" in str(e) or "Too Many Requests" in str(e):
                # Set rate limit timeout for 10 minutes
                self._rate_limited_until = time.time() + 600
                logger.warning(f"Rate limit detected, backing off for 10 minutes")

        # Method 2: Use Ticker.history with a longer period
        try:
            logger.info(f"Attempting to get data directly for {symbol}")
            ticker = yf.Ticker(symbol)
            # Try with a longer period to ensure we get data
            df = ticker.history(period="1y")

            if not df.empty:
                logger.info(f"Successfully retrieved data directly for {symbol} with {len(df)} rows")
                # Print column names for debugging
                logger.debug(f"Columns from history: {df.columns.tolist()}")

                # Create a new DataFrame with standardized columns
                standard_df = pd.DataFrame()

                # First, add the date column from the index
                if isinstance(df.index, pd.DatetimeIndex):
                    standard_df['date'] = df.index

                # Map OHLCV columns
                ohlcv_mapping = {
                    'open': ['Open', 'open', 'OPEN'],
                    'high': ['High', 'high', 'HIGH'],
                    'low': ['Low', 'low', 'LOW'],
                    'close': ['Close', 'close', 'CLOSE', 'Adj Close', 'adj close'],
                    'volume': ['Volume', 'volume', 'VOLUME']
                }

                # Process each column
                for std_col, possible_names in ohlcv_mapping.items():
                    found = False
                    for possible_name in possible_names:
                        if possible_name in df.columns:
                            standard_df[std_col] = df[possible_name].values
                            found = True
                            logger.debug(f"Mapped {possible_name} to {std_col}")
                            break
                        # Try case-insensitive match
                        else:
                            for col in df.columns:
                                if isinstance(col, str) and col.lower() == possible_name.lower():
                                    standard_df[std_col] = df[col].values
                                    found = True
                                    logger.debug(f"Mapped {col} to {std_col} (case-insensitive)")
                                    break
                        if found:
                            break

                # Log the standardized columns
                logger.debug(f"Standardized columns from history: {standard_df.columns.tolist()}")

                # Return the standardized DataFrame if it has data
                if not standard_df.empty and len(standard_df.columns) >= 2:  # At least date and one price column
                    return standard_df
                else:
                    logger.warning(f"Standardized DataFrame from history is empty or missing columns")
                    # Return the original DataFrame as a fallback
                    return df
        except Exception as e:
            logger.warning(f"Error using Ticker.history for {symbol}: {e}")
            # Check if this is a rate limiting error
            if "Rate limited" in str(e) or "Too Many Requests" in str(e):
                # Set rate limit timeout for 10 minutes
                self._rate_limited_until = time.time() + 600
                logger.warning(f"Rate limit detected, backing off for 10 minutes")

        # We could try other data sources here if needed
        logger.warning(f"All data retrieval methods failed for {symbol}")

        return None

    def _create_dummy_data(self, symbol):
        """Create dummy data for a symbol with realistic market values."""
        today = pd.Timestamp.now()
        dates = pd.date_range(end=today, periods=30)

        # Use more realistic values based on symbol type
        if symbol.startswith('^'):
            # Market indices - use realistic index values
            if symbol == '^GSPC':  # S&P 500
                base_price = 5800
            elif symbol == '^DJI':  # Dow Jones
                base_price = 39000
            elif symbol == '^IXIC':  # NASDAQ
                base_price = 16900
            elif symbol == '^RUT':  # Russell 2000
                base_price = 2040
            else:
                base_price = 1000
        else:
            # Individual stocks - use reasonable stock prices
            base_price = 150

        # Create slightly varying data to simulate real market movement
        import numpy as np
        np.random.seed(hash(symbol) % 2**32)  # Consistent seed based on symbol
        price_variation = np.random.normal(0, 0.02, 30)  # 2% daily variation

        prices = []
        current_price = base_price
        for i in range(30):
            current_price *= (1 + price_variation[i])
            prices.append(current_price)

        # Generate varied volume data instead of constant
        base_volume = 1000000
        volume_data = []
        for i in range(30):
            # Create significant volume variation
            daily_variation = np.random.uniform(0.2, 3.5)  # 20% to 350% of base
            # Add weekly patterns (higher volume on certain days)
            day_of_week = i % 7
            weekly_multiplier = [1.4, 0.7, 0.9, 1.1, 1.3, 0.6, 0.4][day_of_week]
            # Add price-based volume (higher volume on volatile days)
            if i > 0:
                price_change = abs(prices[i] - prices[i-1]) / prices[i-1]
                volatility_multiplier = 1.0 + price_change * 5
            else:
                volatility_multiplier = 1.0
            # Add random spikes
            spike = np.random.choice([1.0, 1.0, 1.0, 2.5], 1)[0]
            # Combine all factors
            volume = int(base_volume * daily_variation * weekly_multiplier * volatility_multiplier * spike)
            volume = max(volume, 50000)  # Minimum volume
            volume_data.append(volume)

        dummy_data = {
            'date': dates,
            'open': [p * 0.995 for p in prices],  # Open slightly lower
            'high': [p * 1.01 for p in prices],   # High slightly higher
            'low': [p * 0.99 for p in prices],    # Low slightly lower
            'close': prices,
            'volume': volume_data
        }

        df = pd.DataFrame(dummy_data)
        logger.info(f"Created realistic dummy data for {symbol} with base price {base_price:.2f}")
        return df

    def _standardize_dataframe(self, df):
        """Standardize DataFrame format."""
        # Make a copy to avoid modifying the original
        df = df.copy()

        # Log the original columns for debugging
        logger.debug(f"Original columns: {df.columns.tolist()}")
        logger.debug(f"Original index type: {type(df.index)}")

        # For multi-level columns, we need special handling
        if isinstance(df.columns, pd.MultiIndex):
            logger.debug("Processing multi-level columns in standardize_dataframe")

            # Create a new DataFrame with standardized columns
            new_df = pd.DataFrame()

            # First, add the date column from the index
            if isinstance(df.index, pd.DatetimeIndex):
                new_df['date'] = df.index
                logger.debug("Using DatetimeIndex as date column")
            else:
                # If we don't have a DatetimeIndex, try to find a date column
                date_found = False
                for col in df.columns:
                    if isinstance(col, tuple) and len(col) >= 2:
                        if col[1].lower() in ['date', 'datetime', 'time']:
                            new_df['date'] = df[col]
                            date_found = True
                            logger.debug(f"Found date column in multi-index: {col}")
                            break

                # If still no date, create one
                if not date_found:
                    new_df['date'] = pd.date_range(end=pd.Timestamp.now(), periods=len(df) or 10)
                    logger.debug("Created date range as date column")

            # Print all columns for debugging
            for col in df.columns:
                logger.debug(f"Column in standardize_dataframe: {col}")

            # Try to extract the symbol from the columns
            symbols = set()
            for col in df.columns:
                if isinstance(col, tuple) and len(col) >= 2:
                    symbols.add(col[1])

            if symbols:
                symbol = list(symbols)[0]  # Use the first symbol found
                logger.debug(f"Found symbol in columns: {symbol}")

                # Direct mapping based on the exact format we're seeing in the logs
                column_mapping = {
                    'open': ('Open', symbol),
                    'high': ('High', symbol),
                    'low': ('Low', symbol),
                    'close': ('Close', symbol),
                    'volume': ('Volume', symbol)
                }

                # Map each column directly
                for std_col, col_tuple in column_mapping.items():
                    if col_tuple in df.columns:
                        new_df[std_col] = df[col_tuple]
                        logger.debug(f"Directly mapped {col_tuple} to {std_col}")
                    else:
                        # Try case-insensitive match
                        for col in df.columns:
                            if isinstance(col, tuple) and len(col) >= 2:
                                if col[0].lower() == col_tuple[0].lower() and col[1].lower() == col_tuple[1].lower():
                                    new_df[std_col] = df[col]
                                    logger.debug(f"Case-insensitive mapped {col} to {std_col}")
                                    break
                        else:
                            logger.warning(f"No match found for {std_col} column, tried {col_tuple}")
                            # Use default values
                            if std_col == 'volume':
                                new_df[std_col] = 1000000
                            else:
                                # For price columns, use reasonable values
                                base_price = 100.0
                                if std_col == 'open':
                                    new_df[std_col] = base_price
                                elif std_col == 'high':
                                    new_df[std_col] = base_price * 1.01
                                elif std_col == 'low':
                                    new_df[std_col] = base_price * 0.99
                                elif std_col == 'close':
                                    new_df[std_col] = base_price
            else:
                # Fallback to the old approach if we can't find a symbol
                logger.warning("Could not find symbol in columns, using generic approach")
                standard_columns = {
                    'open': ['open', 'Open', 'OPEN'],
                    'high': ['high', 'High', 'HIGH'],
                    'low': ['low', 'Low', 'LOW'],
                    'close': ['close', 'Close', 'CLOSE', 'adj close', 'Adj Close', 'adjusted close', 'Adjusted Close'],
                    'volume': ['volume', 'Volume', 'VOLUME']
                }

                # Process each standard column
                for std_name, possible_names in standard_columns.items():
                    found = False

                    # Try to find the column in the multi-level columns
                    for possible_name in possible_names:
                        for col in df.columns:
                            if isinstance(col, tuple) and len(col) >= 2:
                                # Try exact match first
                                if col[1] == possible_name:
                                    new_df[std_name] = df[col]
                                    found = True
                                    logger.debug(f"Found {std_name} column in multi-index: {col}")
                                    break
                                # Then try case-insensitive match
                                elif col[1].lower() == possible_name.lower():
                                    new_df[std_name] = df[col]
                                    found = True
                                    logger.debug(f"Found {std_name} column in multi-index (case-insensitive): {col}")
                                    break

                        if found:
                            break

                    # If not found, add a default value
                    if not found:
                        logger.warning(f"No match found for {std_name} column")
                        if std_name == 'volume':
                            new_df[std_name] = 1000000
                        else:
                            # For price columns, use reasonable values
                            base_price = 100.0
                            if std_name == 'open':
                                new_df[std_name] = base_price
                            elif std_name == 'high':
                                new_df[std_name] = base_price * 1.01
                            elif std_name == 'low':
                                new_df[std_name] = base_price * 0.99
                            elif std_name == 'close':
                                new_df[std_name] = base_price

            # Log the standardized columns
            logger.debug(f"Standardized columns: {new_df.columns.tolist()}")

            return new_df

        # For regular columns, use the original approach
        else:
            # If we have a DatetimeIndex, convert it to a column
            if isinstance(df.index, pd.DatetimeIndex):
                df = df.reset_index()
                logger.debug(f"After reset_index, columns: {df.columns.tolist()}")

            # Define standard column mappings
            standard_columns = {
                'date': ['date', 'datetime', 'index', 'time', 'Date', 'Datetime', 'Index', 'Time'],
                'open': ['open', 'Open', 'OPEN'],
                'high': ['high', 'High', 'HIGH'],
                'low': ['low', 'Low', 'LOW'],
                'close': ['close', 'Close', 'CLOSE', 'adj close', 'Adj Close', 'adjusted close', 'Adjusted Close'],
                'volume': ['volume', 'Volume', 'VOLUME']
            }

            # Create a new DataFrame with standardized columns
            new_df = pd.DataFrame()

            # First, handle the date column
            date_found = False

            # Check if we have a column that could be the date
            for possible_name in standard_columns['date']:
                if possible_name in df.columns:
                    new_df['date'] = df[possible_name]
                    date_found = True
                    logger.debug(f"Found date column: {possible_name}")
                    break

            # If date not found but we have a DatetimeIndex, use that
            if not date_found and isinstance(df.index, pd.DatetimeIndex):
                new_df['date'] = df.index
                date_found = True
                logger.debug("Using DatetimeIndex as date column")

            # Now handle the other columns
            for std_name, possible_names in standard_columns.items():
                if std_name == 'date' and date_found:
                    continue  # Already handled date

                # Try to find the column in the DataFrame
                found = False

                # Check for exact and case-insensitive matches
                for possible_name in possible_names:
                    for col in df.columns:
                        col_str = col if isinstance(col, str) else str(col)
                        if col_str.lower() == possible_name.lower():
                            new_df[std_name] = df[col]
                            found = True
                            logger.debug(f"Found {std_name} column: {col}")
                            break
                    if found:
                        break

            # Log the standardized columns
            logger.debug(f"Standardized columns: {new_df.columns.tolist()}")

            return new_df

    def _ensure_required_columns(self, df, required_columns):
        """Ensure all required columns exist in the DataFrame."""
        # Check for missing columns
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            logger.warning(f"Missing columns: {missing_columns}")

            # If date is missing but we have a DatetimeIndex, use it
            if 'date' in missing_columns and isinstance(df.index, pd.DatetimeIndex):
                df['date'] = df.index
                missing_columns.remove('date')

            # Add missing columns with default values
            for col in missing_columns:
                if col == 'date':
                    df['date'] = pd.date_range(end=pd.Timestamp.now(), periods=len(df) or 30)
                elif col == 'volume':
                    df['volume'] = 1000000
                else:
                    # For price columns, use reasonable values
                    base_price = 100.0
                    if col == 'open':
                        df['open'] = base_price
                    elif col == 'high':
                        df['high'] = base_price * 1.01
                    elif col == 'low':
                        df['low'] = base_price * 0.99
                    elif col == 'close':
                        df['close'] = base_price

        return df

    async def get_multiple_stock_data(
        self,
        symbols: List[str],
        start_date: date,
        end_date: date,
        interval: str = "1d"
    ) -> Dict[str, pd.DataFrame]:
        """
        Get historical stock data for multiple symbols.

        Args:
            symbols: List of stock symbols
            start_date: Start date for historical data
            end_date: End date for historical data
            interval: Data interval (e.g., '1d', '1wk', '1mo')

        Returns:
            Dictionary mapping symbols to DataFrames with historical stock data
        """
        logger.debug(f"Getting stock data for {len(symbols)} symbols")

        # Define standard column mapping
        column_mapping = {
            'open': ['Open', 'open'],
            'high': ['High', 'high'],
            'low': ['Low', 'low'],
            'close': ['Close', 'close', 'Adj Close', 'adj close'],
            'volume': ['Volume', 'volume']
        }

        # yfinance is synchronous, so we run it in a thread pool
        loop = asyncio.get_event_loop()
        data = await loop.run_in_executor(
            None,
            lambda: yf.download(
                symbols,
                start=start_date,
                end=end_date,
                interval=interval,
                group_by="ticker",
                progress=False
            )
        )

        result = {}

        # If only one symbol is requested, yfinance doesn't group by ticker
        if len(symbols) == 1:
            symbol = symbols[0]
            # Create a copy of the data
            df = data.copy()

            # Log original columns for debugging
            logger.debug(f"Original columns: {df.columns.tolist()}")
            logger.debug(f"Original index type: {type(df.index)}")

            # Create a standard DataFrame with the required columns
            standard_df = pd.DataFrame()

            # First, handle the date column from the index
            if isinstance(df.index, pd.DatetimeIndex):
                standard_df['date'] = df.index
                logger.debug(f"Using DatetimeIndex as date column")
            else:
                logger.warning(f"Index is not DatetimeIndex for {symbol}")
                standard_df['date'] = pd.date_range(end=pd.Timestamp.now(), periods=len(df) or 10)

            # Map the standard columns from the data
            # For a single symbol, columns are not multi-level
            for std_col, possible_cols in column_mapping.items():
                found = False
                for possible_col in possible_cols:
                    if possible_col in df.columns:
                        logger.debug(f"Found {std_col} column: {possible_col}")
                        standard_df[std_col] = df[possible_col].values
                        found = True
                        break

                if not found:
                    logger.warning(f"No match found for {std_col} column in {symbol}")
                    # Use default values
                    if std_col == 'volume':
                        standard_df[std_col] = 1000000
                    else:
                        # For price columns, use reasonable values
                        base_price = 100.0
                        if std_col == 'open':
                            standard_df[std_col] = base_price
                        elif std_col == 'high':
                            standard_df[std_col] = base_price * 1.01
                        elif std_col == 'low':
                            standard_df[std_col] = base_price * 0.99
                        elif std_col == 'close':
                            standard_df[std_col] = base_price

            # Ensure date column is datetime type
            if not pd.api.types.is_datetime64_any_dtype(standard_df['date']):
                try:
                    standard_df['date'] = pd.to_datetime(standard_df['date'])
                except Exception as e:
                    logger.error(f"Error converting date column to datetime for {symbol}: {e}")
                    standard_df['date'] = pd.date_range(end=pd.Timestamp.now(), periods=len(standard_df) or 10)

            # Sort by date
            standard_df = standard_df.sort_values('date')

            # Log the shape of the data for debugging
            logger.debug(f"Creating chart with data shape: {standard_df.shape}")
            logger.debug(f"Columns: {standard_df.columns.tolist()}")
            logger.debug(f"First few rows of data:\n{standard_df.head()}")

            # Store the result
            result[symbol] = standard_df
        else:
            # Process each symbol
            for symbol in symbols:
                if symbol in data.columns.levels[0]:
                    # Extract data for this symbol
                    symbol_data = data[symbol].copy()

                    # Create a standard DataFrame with the required columns
                    standard_df = pd.DataFrame()

                    # First, handle the date column from the index
                    if isinstance(symbol_data.index, pd.DatetimeIndex):
                        standard_df['date'] = symbol_data.index
                        logger.debug(f"Using DatetimeIndex as date column for {symbol}")
                    else:
                        logger.warning(f"Index is not DatetimeIndex for {symbol}")
                        standard_df['date'] = pd.date_range(end=pd.Timestamp.now(), periods=len(symbol_data) or 10)

                    # Print all columns for debugging
                    for col in symbol_data.columns:
                        logger.debug(f"Column in get_multiple_stock_data for {symbol}: {col}")

                    # Direct mapping based on the exact format we're seeing in the logs
                    direct_mapping = {
                        'open': ('Open', symbol),
                        'high': ('High', symbol),
                        'low': ('Low', symbol),
                        'close': ('Close', symbol),
                        'volume': ('Volume', symbol)
                    }

                    # Map each column directly
                    for std_col, col_tuple in direct_mapping.items():
                        if col_tuple in symbol_data.columns:
                            standard_df[std_col] = symbol_data[col_tuple].values
                            logger.debug(f"Directly mapped {col_tuple} to {std_col}")
                        else:
                            # Try case-insensitive match
                            for col in symbol_data.columns:
                                if isinstance(col, tuple) and len(col) >= 2:
                                    if col[0].lower() == col_tuple[0].lower() and col[1].lower() == col_tuple[1].lower():
                                        standard_df[std_col] = symbol_data[col].values
                                        logger.debug(f"Case-insensitive mapped {col} to {std_col}")
                                        break
                            else:
                                # If still not found, try the old approach
                                found = False
                                for possible_col in column_mapping[std_col]:
                                    for col in symbol_data.columns:
                                        if isinstance(col, tuple) and len(col) >= 2:
                                            # For multi-level columns, check the second level
                                            if col[1] == possible_col:
                                                logger.debug(f"Found {std_col} column for {symbol}: {col}")
                                                standard_df[std_col] = symbol_data[col].values
                                                found = True
                                                break
                                        elif col == possible_col:
                                            # For regular columns
                                            logger.debug(f"Found {std_col} column for {symbol}: {col}")
                                            standard_df[std_col] = symbol_data[col].values
                                            found = True
                                            break

                                    if found:
                                        break

                                if not found:
                                    logger.warning(f"No match found for {std_col} column in {symbol}")
                                    # Use default values
                                    if std_col == 'volume':
                                        standard_df[std_col] = 1000000
                                    else:
                                        # For price columns, use reasonable values
                                        base_price = 100.0
                                        if std_col == 'open':
                                            standard_df[std_col] = base_price
                                        elif std_col == 'high':
                                            standard_df[std_col] = base_price * 1.01
                                        elif std_col == 'low':
                                            standard_df[std_col] = base_price * 0.99
                                        elif std_col == 'close':
                                            standard_df[std_col] = base_price

                        if not found:
                            logger.warning(f"No match found for {std_col} column in {symbol}")
                            # Use default values
                            if std_col == 'volume':
                                standard_df[std_col] = 1000000
                            else:
                                # For price columns, use reasonable values
                                base_price = 100.0
                                if std_col == 'open':
                                    standard_df[std_col] = base_price
                                elif std_col == 'high':
                                    standard_df[std_col] = base_price * 1.01
                                elif std_col == 'low':
                                    standard_df[std_col] = base_price * 0.99
                                elif std_col == 'close':
                                    standard_df[std_col] = base_price

                    # Ensure date column is datetime type
                    if not pd.api.types.is_datetime64_any_dtype(standard_df['date']):
                        try:
                            standard_df['date'] = pd.to_datetime(standard_df['date'])
                        except Exception as e:
                            logger.error(f"Error converting date column to datetime for {symbol}: {e}")
                            standard_df['date'] = pd.date_range(end=pd.Timestamp.now(), periods=len(standard_df) or 10)

                    # Sort by date
                    standard_df = standard_df.sort_values('date')

                    # Log the shape of the data for debugging
                    logger.debug(f"Data shape for {symbol}: {standard_df.shape}")

                    # Store the result
                    result[symbol] = standard_df
                else:
                    logger.warning(f"No data found for {symbol}")
                    # Create a dummy DataFrame with the required columns
                    today = pd.Timestamp.now()
                    dates = pd.date_range(end=today, periods=10)
                    dummy_data = {
                        'date': dates,
                        'open': [100.0] * 10,
                        'high': [105.0] * 10,
                        'low': [95.0] * 10,
                        'close': [102.0] * 10,
                        'volume': [1000000] * 10
                    }
                    result[symbol] = pd.DataFrame(dummy_data)

        return result

    async def get_company_info(self, symbol: str) -> Dict:
        """
        Get company information for a symbol.

        Args:
            symbol: The stock symbol

        Returns:
            Dictionary with company information
        """
        logger.debug(f"Getting company info for {symbol}")

        try:
            # yfinance is synchronous, so we run it in a thread pool
            loop = asyncio.get_event_loop()
            ticker = await loop.run_in_executor(None, lambda: yf.Ticker(symbol))

            # Get company information
            info = await loop.run_in_executor(None, lambda: ticker.info)

            # Check if info is empty, None, or has NoneType values
            if not info:
                logger.warning(f"No company info found for {symbol}")
                return {
                    "name": symbol,
                    "longName": symbol,
                    "symbol": symbol,
                    "sector": "Unknown",
                    "industry": "Unknown",
                    "marketCap": 0,
                    "fiftyTwoWeekLow": 0,
                    "fiftyTwoWeekHigh": 0
                }

            # Check for NoneType values in critical fields
            for key in ['longName', 'shortName', 'sector', 'industry']:
                if key in info and (info[key] is None or not isinstance(info[key], (str, int, float))):
                    logger.warning(f"Invalid value found for {key} in {symbol} info: {type(info[key])}")
                    # Replace None or invalid values with appropriate defaults
                    if key in ['longName', 'shortName']:
                        info[key] = symbol
                    else:
                        info[key] = "Unknown"

            # Ensure all required fields exist with valid values
            required_fields = {
                'name': symbol,
                'longName': symbol,
                'symbol': symbol,
                'sector': 'Unknown',
                'industry': 'Unknown',
                'marketCap': 0,
                'fiftyTwoWeekLow': 0,
                'fiftyTwoWeekHigh': 0
            }

            # Add any missing fields with default values
            for key, default_value in required_fields.items():
                if key not in info or info[key] is None or not isinstance(info[key], (str, int, float)):
                    info[key] = default_value

            # Ensure required fields exist
            if "longName" not in info:
                info["longName"] = info.get("shortName", symbol)

            if "name" not in info:
                info["name"] = info.get("longName", info.get("shortName", symbol))

            return info

        except Exception as e:
            logger.error(f"Error getting company info for {symbol}: {e}")
            # Return a minimal info dict to prevent UI errors
            return {
                "name": symbol,
                "longName": symbol,
                "symbol": symbol,
                "sector": "Unknown",
                "industry": "Unknown",
                "marketCap": 0,
                "fiftyTwoWeekLow": 0,
                "fiftyTwoWeekHigh": 0
            }

    async def search_symbols(self, query: str) -> List[Dict]:
        """
        Search for symbols matching a query.

        Args:
            query: The search query

        Returns:
            List of dictionaries with symbol information
        """
        logger.debug(f"Searching for symbols matching '{query}'")

        # yfinance doesn't have a built-in search function
        # We'll use a simple approach to search for tickers
        loop = asyncio.get_event_loop()
        tickers = await loop.run_in_executor(
            None,
            lambda: yf.Tickers(query).tickers
        )

        results = []
        for symbol, ticker in tickers.items():
            try:
                info = await loop.run_in_executor(None, lambda: ticker.info)
                if info and "longName" in info:
                    results.append({
                        "symbol": symbol,
                        "name": info.get("longName", ""),
                        "exchange": info.get("exchange", ""),
                        "type": info.get("quoteType", "")
                    })
            except Exception as e:
                logger.error(f"Error getting info for {symbol}: {e}")

        return results
