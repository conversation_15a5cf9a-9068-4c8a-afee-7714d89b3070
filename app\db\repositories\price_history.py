"""
Price history repository for database operations.
"""
from datetime import datetime
from typing import Dict, List, Optional

from sqlalchemy.orm import Session

from app.db.models import PriceHistory
from app.db.repositories.base import BaseRepository


class PriceHistoryRepository(BaseRepository[PriceHistory, Dict, Dict]):
    """
    Price history repository for database operations.
    """
    
    def __init__(self):
        """Initialize the repository."""
        super().__init__(PriceHistory)
    
    def get_by_asset_id(self, db: Session, asset_id: int) -> List[PriceHistory]:
        """
        Get price history by asset ID.
        
        Args:
            db: Database session
            asset_id: Asset ID
            
        Returns:
            List of price history records
        """
        return db.query(self.model).filter(self.model.asset_id == asset_id).all()
    
    def get_by_date_range(
        self,
        db: Session,
        asset_id: int,
        start_date: datetime,
        end_date: datetime
    ) -> List[PriceHistory]:
        """
        Get price history by asset ID and date range.
        
        Args:
            db: Database session
            asset_id: Asset ID
            start_date: Start date
            end_date: End date
            
        Returns:
            List of price history records
        """
        return db.query(self.model).filter(
            self.model.asset_id == asset_id,
            self.model.date >= start_date,
            self.model.date <= end_date
        ).all()
    
    def get_latest_price(self, db: Session, asset_id: int) -> Optional[PriceHistory]:
        """
        Get the latest price for an asset.
        
        Args:
            db: Database session
            asset_id: Asset ID
            
        Returns:
            Latest price history record if found, None otherwise
        """
        return db.query(self.model).filter(
            self.model.asset_id == asset_id
        ).order_by(self.model.date.desc()).first()
    
    def create_price_history(
        self,
        db: Session,
        asset_id: int,
        date: datetime,
        open_price: float,
        high: float,
        low: float,
        close: float,
        volume: int
    ) -> PriceHistory:
        """
        Create a new price history record.
        
        Args:
            db: Database session
            asset_id: Asset ID
            date: Date
            open_price: Open price
            high: High price
            low: Low price
            close: Close price
            volume: Volume
            
        Returns:
            Created price history record
        """
        # Create price history record
        price_history = PriceHistory(
            asset_id=asset_id,
            date=date,
            open=open_price,
            high=high,
            low=low,
            close=close,
            volume=volume
        )
        
        # Save price history record to database
        db.add(price_history)
        db.commit()
        db.refresh(price_history)
        
        return price_history
    
    def bulk_create_price_history(
        self,
        db: Session,
        asset_id: int,
        price_data: List[Dict]
    ) -> List[PriceHistory]:
        """
        Create multiple price history records.
        
        Args:
            db: Database session
            asset_id: Asset ID
            price_data: List of price data dictionaries
            
        Returns:
            List of created price history records
        """
        # Create price history records
        price_history_records = []
        for data in price_data:
            price_history = PriceHistory(
                asset_id=asset_id,
                date=data["date"],
                open=data["open"],
                high=data["high"],
                low=data["low"],
                close=data["close"],
                volume=data["volume"]
            )
            price_history_records.append(price_history)
        
        # Save price history records to database
        db.add_all(price_history_records)
        db.commit()
        
        return price_history_records


# Create a singleton instance
price_history_repository = PriceHistoryRepository()
