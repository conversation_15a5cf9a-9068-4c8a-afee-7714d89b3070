"""
Watchlist repository for database operations.
"""
from typing import Dict, List, Optional

from sqlalchemy import Table, <PERSON><PERSON>n, Integer, Foreign<PERSON>ey
from sqlalchemy.orm import Session

from app.db.models import Asset, Watchlist, Base
from app.db.repositories.base import BaseRepository


# Association table for many-to-many relationship between watchlists and assets
watchlist_assets = Table(
    "watchlist_assets",
    Base.metadata,
    Column("watchlist_id", Integer, ForeignKey("watchlists.id"), primary_key=True),
    Column("asset_id", Integer, ForeignKey("assets.id"), primary_key=True)
)


class WatchlistRepository(BaseRepository[Watchlist, Dict, Dict]):
    """
    Watchlist repository for database operations.
    """
    
    def __init__(self):
        """Initialize the repository."""
        super().__init__(Watchlist)
    
    def get_by_user_id(self, db: Session, user_id: int) -> List[Watchlist]:
        """
        Get watchlists by user ID.
        
        Args:
            db: Database session
            user_id: User ID
            
        Returns:
            List of watchlists
        """
        return db.query(self.model).filter(self.model.user_id == user_id).all()
    
    def get_by_name(self, db: Session, user_id: int, name: str) -> Optional[Watchlist]:
        """
        Get a watchlist by name and user ID.
        
        Args:
            db: Database session
            user_id: User ID
            name: Watchlist name
            
        Returns:
            Watchlist if found, None otherwise
        """
        return db.query(self.model).filter(
            self.model.user_id == user_id,
            self.model.name == name
        ).first()
    
    def add_asset(self, db: Session, watchlist_id: int, asset_id: int) -> None:
        """
        Add an asset to a watchlist.
        
        Args:
            db: Database session
            watchlist_id: Watchlist ID
            asset_id: Asset ID
        """
        # Check if the asset is already in the watchlist
        stmt = watchlist_assets.select().where(
            watchlist_assets.c.watchlist_id == watchlist_id,
            watchlist_assets.c.asset_id == asset_id
        )
        result = db.execute(stmt).first()
        
        if not result:
            # Add new asset
            stmt = watchlist_assets.insert().values(
                watchlist_id=watchlist_id,
                asset_id=asset_id
            )
            db.execute(stmt)
            db.commit()
    
    def remove_asset(self, db: Session, watchlist_id: int, asset_id: int) -> None:
        """
        Remove an asset from a watchlist.
        
        Args:
            db: Database session
            watchlist_id: Watchlist ID
            asset_id: Asset ID
        """
        stmt = watchlist_assets.delete().where(
            watchlist_assets.c.watchlist_id == watchlist_id,
            watchlist_assets.c.asset_id == asset_id
        )
        db.execute(stmt)
        db.commit()
    
    def get_assets(self, db: Session, watchlist_id: int) -> List[Asset]:
        """
        Get assets in a watchlist.
        
        Args:
            db: Database session
            watchlist_id: Watchlist ID
            
        Returns:
            List of assets
        """
        # Query assets in the watchlist
        query = db.query(Asset).join(
            watchlist_assets,
            Asset.id == watchlist_assets.c.asset_id
        ).filter(
            watchlist_assets.c.watchlist_id == watchlist_id
        )
        
        return query.all()


# Create a singleton instance
watchlist_repository = WatchlistRepository()
