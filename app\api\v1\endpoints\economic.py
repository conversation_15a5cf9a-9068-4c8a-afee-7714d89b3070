"""
Economic data API endpoints.
"""
from datetime import date
from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.data_collection.economic import economic_data_collector
from app.db.database import get_db
from app.db.models import User
from app.middleware.auth import get_current_user
from app.utils.logging import logger

router = APIRouter()


@router.get("/indicator/{indicator}")
async def get_economic_indicator(
    indicator: str,
    interval: str = Query("monthly", description="Data interval ('daily', 'weekly', 'monthly', 'quarterly', 'annual')"),
    start_date: Optional[date] = Query(None, description="Start date for historical data"),
    end_date: Optional[date] = Query(None, description="End date for historical data"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get economic indicator data.
    """
    try:
        df = await economic_data_collector.get_economic_indicator(indicator, interval, start_date, end_date)
        
        if df.empty:
            raise HTTPException(
                status_code=404,
                detail=f"Economic indicator data not found for {indicator}"
            )
        
        # Convert DataFrame to dictionary
        result = df.reset_index().to_dict(orient="records")
        
        return {
            "indicator": indicator,
            "interval": interval,
            "data": result
        }
    except Exception as e:
        logger.error(f"Error getting economic indicator {indicator}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting economic indicator: {str(e)}"
        )


@router.post("/indicators")
async def get_multiple_economic_indicators(
    indicators: List[str],
    interval: str = Query("monthly", description="Data interval ('daily', 'weekly', 'monthly', 'quarterly', 'annual')"),
    start_date: Optional[date] = Query(None, description="Start date for historical data"),
    end_date: Optional[date] = Query(None, description="End date for historical data"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get multiple economic indicators.
    """
    try:
        results = await economic_data_collector.get_multiple_economic_indicators(indicators, interval, start_date, end_date)
        
        if not results:
            raise HTTPException(
                status_code=404,
                detail="Economic indicator data not found"
            )
        
        # Convert DataFrames to dictionaries
        formatted_results = {}
        for indicator, df in results.items():
            formatted_results[indicator] = df.reset_index().to_dict(orient="records")
        
        return {
            "indicators": indicators,
            "interval": interval,
            "data": formatted_results
        }
    except Exception as e:
        logger.error(f"Error getting economic indicators: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting economic indicators: {str(e)}"
        )


@router.get("/yield-curve")
async def get_yield_curve(
    date_str: Optional[str] = Query(None, description="Date string in format 'YYYY-MM-DD'"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get Treasury yield curve data.
    """
    try:
        df = await economic_data_collector.get_yield_curve(date_str)
        
        if df.empty:
            raise HTTPException(
                status_code=404,
                detail="Yield curve data not found"
            )
        
        # Convert DataFrame to dictionary
        result = df.reset_index().to_dict(orient="records")
        
        return {
            "date": date_str,
            "data": result
        }
    except Exception as e:
        logger.error(f"Error getting yield curve: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting yield curve: {str(e)}"
        )
