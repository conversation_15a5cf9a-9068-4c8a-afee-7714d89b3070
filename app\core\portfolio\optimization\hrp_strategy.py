"""
Hierarchical Risk Parity (HRP) portfolio optimization strategy.

This module provides a wrapper class for the HRP implementation
to make it compatible with the OptimizationStrategy interface.
"""
from typing import Dict, Optional

import pandas as pd

from app.core.portfolio.optimization.base import OptimizationStrategy
from app.core.portfolio.optimization.hrp import optimize_portfolio_hrp
from app.utils.logging import logger


class HierarchicalRiskParityStrategy(OptimizationStrategy):
    """
    Hierarchical Risk Parity (HRP) portfolio optimization strategy.
    
    This strategy uses hierarchical clustering to allocate assets
    based on their risk characteristics.
    """
    
    def __init__(self, risk_free_rate: float = 0.0):
        """
        Initialize the HRP optimization strategy.
        
        Args:
            risk_free_rate: Risk-free rate
        """
        super().__init__("hierarchical_risk_parity")
        self.risk_free_rate = risk_free_rate
    
    def optimize(
        self,
        returns: pd.DataFrame,
        **kwargs
    ) -> Dict[str, float]:
        """
        Optimize portfolio weights using Hierarchical Risk Parity.
        
        Args:
            returns: DataFrame of asset returns
            **kwargs: Additional parameters
            
        Returns:
            Dictionary mapping asset names to weights
        """
        try:
            # Call the HRP implementation
            result = optimize_portfolio_hrp(
                returns=returns,
                risk_free_rate=self.risk_free_rate,
                **kwargs
            )
            
            # Extract weights
            weights = result.get("weights", {})
            
            # Validate weights
            if not self.validate_weights(weights):
                logger.warning("HRP optimization produced invalid weights, using equal weights")
                equal_weight = 1.0 / len(returns.columns)
                weights = {asset: equal_weight for asset in returns.columns}
            
            return weights
        
        except Exception as e:
            logger.error(f"Error in HRP optimization: {str(e)}")
            # Return equal weights as fallback
            equal_weight = 1.0 / len(returns.columns)
            return {asset: equal_weight for asset in returns.columns}
