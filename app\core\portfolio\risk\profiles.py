"""
User risk profiles.
"""
from typing import Dict, List, Optional, Tuple, Union

from app.utils.constants import (RISK_PROFILE_AGGRESSIVE, RISK_PROFILE_CONSERVATIVE,
                                RISK_PROFILE_MODERATE)


class RiskProfile:
    """
    User risk profile.
    
    This class represents a user's risk profile, which determines
    the appropriate asset allocation and investment strategy.
    """
    
    def __init__(
        self,
        name: str,
        description: str,
        max_volatility: float,
        min_return: float,
        asset_allocation: Dict[str, Tuple[float, float]]
    ):
        """
        Initialize a risk profile.
        
        Args:
            name: Name of the risk profile
            description: Description of the risk profile
            max_volatility: Maximum acceptable volatility
            min_return: Minimum acceptable return
            asset_allocation: Target asset allocation ranges (min, max)
        """
        self.name = name
        self.description = description
        self.max_volatility = max_volatility
        self.min_return = min_return
        self.asset_allocation = asset_allocation
    
    def get_optimization_constraints(self) -> Dict:
        """
        Get optimization constraints based on the risk profile.
        
        Returns:
            Dictionary with optimization constraints
        """
        constraints = {
            "weight_bounds": (0, 1),  # Default: no short selling, no leverage
            "sector_constraints": {}
        }
        
        # Add sector constraints based on asset allocation
        for asset_class, (min_weight, max_weight) in self.asset_allocation.items():
            constraints["sector_constraints"][asset_class] = (min_weight, max_weight)
        
        return constraints
    
    def get_optimization_params(self) -> Dict:
        """
        Get optimization parameters based on the risk profile.
        
        Returns:
            Dictionary with optimization parameters
        """
        params = {}
        
        # Set optimization criterion based on risk profile
        if self.name == RISK_PROFILE_CONSERVATIVE:
            params["optimization_criterion"] = "min_volatility"
        elif self.name == RISK_PROFILE_MODERATE:
            params["optimization_criterion"] = "max_sharpe"
        elif self.name == RISK_PROFILE_AGGRESSIVE:
            params["optimization_criterion"] = "efficient_return"
            params["target_return"] = self.min_return
        
        return params
    
    def __str__(self) -> str:
        """String representation of the risk profile."""
        return f"{self.name}: {self.description}"


# Define standard risk profiles
RISK_PROFILES = {
    RISK_PROFILE_CONSERVATIVE: RiskProfile(
        name=RISK_PROFILE_CONSERVATIVE,
        description="Conservative investors prioritize capital preservation over growth. "
                   "They prefer stable, low-risk investments with modest returns.",
        max_volatility=0.1,  # 10% maximum volatility
        min_return=0.03,     # 3% minimum return
        asset_allocation={
            "Equities": (0.1, 0.3),      # 10-30% in stocks
            "Fixed Income": (0.5, 0.8),   # 50-80% in bonds
            "Cash": (0.1, 0.3),           # 10-30% in cash
            "Alternatives": (0.0, 0.1)    # 0-10% in alternatives
        }
    ),
    
    RISK_PROFILE_MODERATE: RiskProfile(
        name=RISK_PROFILE_MODERATE,
        description="Moderate investors seek a balance between growth and capital preservation. "
                   "They are willing to accept moderate risk for moderate returns.",
        max_volatility=0.15,  # 15% maximum volatility
        min_return=0.05,      # 5% minimum return
        asset_allocation={
            "Equities": (0.4, 0.6),      # 40-60% in stocks
            "Fixed Income": (0.3, 0.5),   # 30-50% in bonds
            "Cash": (0.0, 0.2),           # 0-20% in cash
            "Alternatives": (0.0, 0.2)    # 0-20% in alternatives
        }
    ),
    
    RISK_PROFILE_AGGRESSIVE: RiskProfile(
        name=RISK_PROFILE_AGGRESSIVE,
        description="Aggressive investors prioritize growth over capital preservation. "
                   "They are willing to accept high risk for potentially high returns.",
        max_volatility=0.25,  # 25% maximum volatility
        min_return=0.08,      # 8% minimum return
        asset_allocation={
            "Equities": (0.6, 0.9),      # 60-90% in stocks
            "Fixed Income": (0.0, 0.3),   # 0-30% in bonds
            "Cash": (0.0, 0.1),           # 0-10% in cash
            "Alternatives": (0.0, 0.3)    # 0-30% in alternatives
        }
    )
}


def get_risk_profile(name: str) -> RiskProfile:
    """
    Get a risk profile by name.
    
    Args:
        name: Name of the risk profile
        
    Returns:
        Risk profile
        
    Raises:
        ValueError: If the risk profile is not found
    """
    if name not in RISK_PROFILES:
        raise ValueError(f"Risk profile '{name}' not found")
    
    return RISK_PROFILES[name]


def get_all_risk_profiles() -> Dict[str, RiskProfile]:
    """
    Get all available risk profiles.
    
    Returns:
        Dictionary mapping risk profile names to risk profiles
    """
    return RISK_PROFILES
