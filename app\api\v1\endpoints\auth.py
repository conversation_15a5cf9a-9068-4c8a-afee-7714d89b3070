"""
Authentication API endpoints.
"""
from datetime import <PERSON><PERSON><PERSON>
from typing import Dict

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from app.db.database import get_db
from app.middleware.auth import get_current_user
from app.services.auth import auth_service
from app.settings import get_settings
from app.utils.logging import logger

# Get settings
settings = get_settings()

# Create router
router = APIRouter()


@router.post("/token")
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """
    Get an access token.
    """
    # Authenticate user
    user = auth_service.authenticate_user(
        db,
        form_data.username,
        form_data.password
    )
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    # Create access token
    access_token = auth_service.create_access_token_for_user(user)
    
    # Return token
    return {
        "access_token": access_token,
        "token_type": "bearer"
    }


@router.post("/register")
async def register_user(
    username: str,
    email: str,
    password: str,
    db: Session = Depends(get_db)
):
    """
    Register a new user.
    """
    try:
        # Create user
        user = auth_service.create_user(
            db,
            username,
            email,
            password
        )
        
        # Create access token
        access_token = auth_service.create_access_token_for_user(user)
        
        # Return token
        return {
            "access_token": access_token,
            "token_type": "bearer"
        }
    
    except Exception as e:
        logger.error(f"Error registering user: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/me")
async def get_current_user_info(
    current_user = Depends(get_current_user)
):
    """
    Get information about the current user.
    """
    return {
        "id": current_user.id,
        "username": current_user.username,
        "email": current_user.email
    }
