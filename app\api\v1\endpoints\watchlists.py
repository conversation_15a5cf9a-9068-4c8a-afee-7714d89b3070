"""
Watchlist API endpoints.
"""
from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api.v1.models.watchlist import (
    WatchlistCreate,
    WatchlistResponse,
    WatchlistUpdate,
    AssetResponse
)
from app.db.database import get_db
from app.db.models import User
from app.middleware.auth import get_current_user
from app.services.watchlist import watchlist_service

router = APIRouter()


@router.post("", response_model=WatchlistResponse, status_code=status.HTTP_201_CREATED)
async def create_watchlist(
    watchlist_in: WatchlistCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create a new watchlist.
    """
    # Create watchlist
    watchlist = await watchlist_service.create_watchlist(
        db=db,
        user=current_user,
        name=watchlist_in.name,
        description=watchlist_in.description
    )
    
    # Return watchlist data
    return {
        "id": watchlist.id,
        "name": watchlist.name,
        "description": watchlist.description,
        "created_at": watchlist.created_at,
        "updated_at": watchlist.updated_at
    }


@router.get("", response_model=List[WatchlistResponse])
async def get_watchlists(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all watchlists for the current user.
    """
    # Get watchlists
    watchlists = await watchlist_service.get_watchlists(
        db=db,
        user=current_user
    )
    
    # Return watchlists data
    return [
        {
            "id": watchlist.id,
            "name": watchlist.name,
            "description": watchlist.description,
            "created_at": watchlist.created_at,
            "updated_at": watchlist.updated_at
        }
        for watchlist in watchlists
    ]


@router.get("/{watchlist_id}", response_model=WatchlistResponse)
async def get_watchlist(
    watchlist_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get a watchlist by ID.
    """
    # Get watchlist
    watchlist = await watchlist_service.get_watchlist(
        db=db,
        user=current_user,
        watchlist_id=watchlist_id
    )
    
    # Return watchlist data
    return {
        "id": watchlist.id,
        "name": watchlist.name,
        "description": watchlist.description,
        "created_at": watchlist.created_at,
        "updated_at": watchlist.updated_at
    }


@router.put("/{watchlist_id}", response_model=WatchlistResponse)
async def update_watchlist(
    watchlist_id: int,
    watchlist_in: WatchlistUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update a watchlist.
    """
    # Update watchlist
    watchlist = await watchlist_service.update_watchlist(
        db=db,
        user=current_user,
        watchlist_id=watchlist_id,
        name=watchlist_in.name,
        description=watchlist_in.description
    )
    
    # Return watchlist data
    return {
        "id": watchlist.id,
        "name": watchlist.name,
        "description": watchlist.description,
        "created_at": watchlist.created_at,
        "updated_at": watchlist.updated_at
    }


@router.delete("/{watchlist_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_watchlist(
    watchlist_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete a watchlist.
    """
    # Delete watchlist
    await watchlist_service.delete_watchlist(
        db=db,
        user=current_user,
        watchlist_id=watchlist_id
    )


@router.get("/{watchlist_id}/assets", response_model=List[AssetResponse])
async def get_watchlist_assets(
    watchlist_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get assets in a watchlist.
    """
    # Get watchlist assets
    assets = await watchlist_service.get_watchlist_assets(
        db=db,
        user=current_user,
        watchlist_id=watchlist_id
    )
    
    # Return assets data
    return assets


@router.post("/{watchlist_id}/assets/{symbol}", status_code=status.HTTP_204_NO_CONTENT)
async def add_asset_to_watchlist(
    watchlist_id: int,
    symbol: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Add an asset to a watchlist.
    """
    # Add asset to watchlist
    await watchlist_service.add_asset_to_watchlist(
        db=db,
        user=current_user,
        watchlist_id=watchlist_id,
        symbol=symbol
    )


@router.delete("/{watchlist_id}/assets/{symbol}", status_code=status.HTTP_204_NO_CONTENT)
async def remove_asset_from_watchlist(
    watchlist_id: int,
    symbol: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Remove an asset from a watchlist.
    """
    # Remove asset from watchlist
    await watchlist_service.remove_asset_from_watchlist(
        db=db,
        user=current_user,
        watchlist_id=watchlist_id,
        symbol=symbol
    )
