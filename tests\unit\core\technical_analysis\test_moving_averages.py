"""
Unit tests for moving averages indicators.
"""
import unittest
import pandas as pd
import numpy as np
from pandas.testing import assert_series_equal, assert_frame_equal

from app.core.technical_analysis.indicators.moving_averages import (
    simple_moving_average,
    exponential_moving_average,
    weighted_moving_average,
    bollinger_bands
)


class TestMovingAverages(unittest.TestCase):
    """Test cases for moving averages indicators."""

    def setUp(self):
        """Set up test data."""
        # Create sample price data
        self.dates = pd.date_range(start='2023-01-01', periods=10, freq='D')
        self.prices = pd.Series([100, 102, 104, 103, 105, 107, 108, 106, 104, 105], index=self.dates)
        self.df = pd.DataFrame({
            'open': [99, 101, 103, 102, 104, 106, 107, 105, 103, 104],
            'high': [101, 103, 105, 104, 106, 108, 109, 107, 105, 106],
            'low': [98, 100, 102, 101, 103, 105, 106, 104, 102, 103],
            'close': self.prices.values,
            'volume': [1000, 1200, 1100, 1300, 1400, 1500, 1600, 1700, 1800, 1900]
        }, index=self.dates)

    def test_simple_moving_average(self):
        """Test simple moving average calculation."""
        # Calculate SMA with window=3
        result = simple_moving_average(self.prices, window=3)
        
        # Expected values: NaN for the first 2 elements, then the 3-day average
        expected = pd.Series([np.nan, np.nan, 102.0, 103.0, 104.0, 105.0, 106.67, 107.0, 106.0, 105.0], index=self.dates)
        expected[6] = (107 + 108 + 105) / 3  # Fix the value for better precision
        
        # Compare results
        pd.testing.assert_series_equal(result, expected, rtol=1e-2)
        
        # Test with DataFrame input
        result_df = simple_moving_average(self.df, window=3, column='close')
        pd.testing.assert_series_equal(result_df, expected, rtol=1e-2)

    def test_exponential_moving_average(self):
        """Test exponential moving average calculation."""
        # Calculate EMA with window=3
        result = exponential_moving_average(self.prices, window=3)
        
        # First value should be the same as the first price
        self.assertAlmostEqual(result.iloc[0], self.prices.iloc[0])
        
        # EMA should be more responsive to recent prices than SMA
        sma = simple_moving_average(self.prices, window=3)
        
        # For the last few values, check if EMA responds more to recent price changes
        self.assertGreater(abs(result.iloc[-1] - self.prices.iloc[-1]), 
                          abs(sma.iloc[-1] - self.prices.iloc[-1]))
        
        # Test with DataFrame input
        result_df = exponential_moving_average(self.df, window=3, column='close')
        self.assertAlmostEqual(result_df.iloc[0], self.df['close'].iloc[0])

    def test_weighted_moving_average(self):
        """Test weighted moving average calculation."""
        # Calculate WMA with window=3
        result = weighted_moving_average(self.prices, window=3)
        
        # Expected values: NaN for the first 2 elements, then the weighted average
        # For window=3, weights are [1, 2, 3]
        expected = pd.Series([np.nan, np.nan, 
                             (100*1 + 102*2 + 104*3) / 6,
                             (102*1 + 104*2 + 103*3) / 6,
                             (104*1 + 103*2 + 105*3) / 6,
                             (103*1 + 105*2 + 107*3) / 6,
                             (105*1 + 107*2 + 108*3) / 6,
                             (107*1 + 108*2 + 106*3) / 6,
                             (108*1 + 106*2 + 104*3) / 6,
                             (106*1 + 104*2 + 105*3) / 6], index=self.dates)
        
        # Compare results
        pd.testing.assert_series_equal(result, expected, rtol=1e-2)
        
        # Test with DataFrame input
        result_df = weighted_moving_average(self.df, window=3, column='close')
        pd.testing.assert_series_equal(result_df, expected, rtol=1e-2)

    def test_bollinger_bands(self):
        """Test Bollinger Bands calculation."""
        # Calculate Bollinger Bands with window=5 and num_std=2
        result = bollinger_bands(self.prices, window=5, num_std=2)
        
        # Check if result is a DataFrame with the expected columns
        self.assertIsInstance(result, pd.DataFrame)
        self.assertIn('middle_band', result.columns)
        self.assertIn('upper_band', result.columns)
        self.assertIn('lower_band', result.columns)
        
        # Check if middle band is the SMA
        sma = simple_moving_average(self.prices, window=5)
        pd.testing.assert_series_equal(result['middle_band'], sma)
        
        # Check if upper band is middle band + 2*std
        std = self.prices.rolling(window=5).std()
        pd.testing.assert_series_equal(result['upper_band'], sma + 2*std, rtol=1e-10)
        
        # Check if lower band is middle band - 2*std
        pd.testing.assert_series_equal(result['lower_band'], sma - 2*std, rtol=1e-10)
        
        # Test with DataFrame input
        result_df = bollinger_bands(self.df, window=5, num_std=2, column='close')
        pd.testing.assert_frame_equal(result_df, result)


if __name__ == '__main__':
    unittest.main()
