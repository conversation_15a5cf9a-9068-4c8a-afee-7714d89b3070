"""
Advanced risk analysis API endpoints.
"""
from datetime import date
from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel
from sqlalchemy.orm import Session

from app.api.v1.models.requests import PortfolioRequest
from app.api.v1.models.responses import RiskMetricsResponse
from app.core.data_collection.data_manager import data_manager
from app.core.portfolio.risk.cvar import calculate_cvar, calculate_component_cvar, calculate_cvar_contribution
from app.core.portfolio.risk.monte_carlo import generate_simulation_report
from app.core.portfolio.risk.stress_testing import historical_stress_test, scenario_stress_test
from app.db.database import get_db
from app.db.models import User
from app.middleware.auth import get_current_user
from app.utils.logging import logger

router = APIRouter()


@router.post("/cvar", response_model=Dict)
async def calculate_portfolio_cvar(
    request: PortfolioRequest,
    alpha: float = Query(0.05, description="Confidence level for CVaR calculation"),
    window: Optional[int] = Query(None, description="Rolling window size for CVaR calculation"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Calculate Conditional Value at Risk (CVaR) for a portfolio.

    CVaR, also known as Expected Shortfall (ES), measures the expected loss
    in the worst alpha% of cases. It is a more conservative risk measure than VaR.
    """
    try:
        # Get historical data
        data_dict = await data_manager.get_multiple_stock_data(
            request.symbols,
            request.start_date,
            request.end_date,
            request.interval
        )

        # Combine data
        combined_data = data_manager.combine_stock_data(data_dict)

        if combined_data.empty:
            raise HTTPException(
                status_code=404,
                detail="No data found for the specified symbols and date range"
            )

        # Calculate returns
        returns = combined_data.pct_change().dropna()

        # Create weights dictionary
        weights = {symbol: weight for symbol, weight in zip(request.symbols, request.weights)}

        # Calculate CVaR
        cvar = calculate_cvar(returns, weights, alpha, window)

        # Calculate component CVaR
        component_cvar = calculate_component_cvar(returns, weights, alpha)

        # Calculate CVaR contribution
        cvar_contribution = calculate_cvar_contribution(returns, weights, alpha)

        # Prepare response
        response = {
            "cvar": cvar,
            "component_cvar": component_cvar,
            "cvar_contribution": cvar_contribution,
            "alpha": alpha
        }

        return response
    except Exception as e:
        logger.error(f"Error calculating CVaR: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error calculating CVaR: {str(e)}"
        )


@router.post("/monte-carlo", response_model=Dict)
async def run_monte_carlo_simulation(
    request: PortfolioRequest,
    n_simulations: int = Query(1000, description="Number of simulations to run"),
    n_periods: int = Query(252, description="Number of periods to simulate"),
    initial_value: float = Query(10000, description="Initial portfolio value"),
    confidence_levels: List[float] = Query([0.01, 0.05, 0.1], description="Confidence levels for VaR and CVaR"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Run Monte Carlo simulation for portfolio risk analysis.
    """
    try:
        # Get historical data
        data_dict = await data_manager.get_multiple_stock_data(
            request.symbols,
            request.start_date,
            request.end_date,
            request.interval
        )

        # Combine data
        combined_data = data_manager.combine_stock_data(data_dict)

        if combined_data.empty:
            raise HTTPException(
                status_code=404,
                detail="No data found for the specified symbols and date range"
            )

        # Calculate returns
        returns = combined_data.pct_change().dropna()

        # Create weights dictionary
        weights = {symbol: weight for symbol, weight in zip(request.symbols, request.weights)}

        # Generate simulation report
        report = generate_simulation_report(
            returns,
            weights,
            initial_value,
            n_simulations,
            n_periods,
            confidence_levels
        )

        return report
    except Exception as e:
        logger.error(f"Error running Monte Carlo simulation: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error running Monte Carlo simulation: {str(e)}"
        )


class StressTestRequest(BaseModel):
    """Request model for historical stress test."""
    portfolio: PortfolioRequest
    stress_periods: Dict[str, List[date]]

@router.post("/stress-test/historical", response_model=Dict)
async def run_historical_stress_test(
    request: StressTestRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Run historical stress test for portfolio risk analysis.
    """
    try:
        # Get historical data
        data_dict = await data_manager.get_multiple_stock_data(
            request.portfolio.symbols,
            request.portfolio.start_date,
            request.portfolio.end_date,
            request.portfolio.interval
        )

        # Combine data
        combined_data = data_manager.combine_stock_data(data_dict)

        if combined_data.empty:
            raise HTTPException(
                status_code=404,
                detail="No data found for the specified symbols and date range"
            )

        # Calculate returns
        returns = combined_data.pct_change().dropna()

        # Create weights dictionary
        weights = {symbol: weight for symbol, weight in zip(request.portfolio.symbols, request.portfolio.weights)}

        # Convert stress_periods to the required format
        formatted_stress_periods = {
            name: (period[0], period[1])
            for name, period in request.stress_periods.items()
        }

        # Run historical stress test
        results = historical_stress_test(returns, weights, formatted_stress_periods)

        return results
    except Exception as e:
        logger.error(f"Error running historical stress test: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error running historical stress test: {str(e)}"
        )


class ScenarioStressTestRequest(BaseModel):
    """Request model for scenario stress test."""
    portfolio: PortfolioRequest
    scenarios: Dict[str, Dict[str, float]]

@router.post("/stress-test/scenario", response_model=Dict)
async def run_scenario_stress_test(
    request: ScenarioStressTestRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Run scenario stress test for portfolio risk analysis.
    """
    try:
        # Get historical data
        data_dict = await data_manager.get_multiple_stock_data(
            request.portfolio.symbols,
            request.portfolio.start_date,
            request.portfolio.end_date,
            request.portfolio.interval
        )

        # Combine data
        combined_data = data_manager.combine_stock_data(data_dict)

        if combined_data.empty:
            raise HTTPException(
                status_code=404,
                detail="No data found for the specified symbols and date range"
            )

        # Calculate returns
        returns = combined_data.pct_change().dropna()

        # Create weights dictionary
        weights = {symbol: weight for symbol, weight in zip(request.portfolio.symbols, request.portfolio.weights)}

        # Run scenario stress test
        results = scenario_stress_test(returns, weights, request.scenarios)

        return results
    except Exception as e:
        logger.error(f"Error running scenario stress test: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error running scenario stress test: {str(e)}"
        )
