"""
Analysis API endpoints.
"""
from datetime import date
from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query

from app.api.v1.models.requests import IndicatorRequest, PatternRequest, ScreenerRequest
from app.api.v1.models.responses import IndicatorResponse, PatternResponse, ScreenerResponse
from app.core.data_collection.data_manager import data_manager
from app.core.technical_analysis.indicators.momentum import RSI, Stochastic
from app.core.technical_analysis.indicators.trend import MACD, MovingAverage
from app.core.technical_analysis.indicators.volatility import ATR, BollingerBands
from app.core.technical_analysis.indicators.volume import OBV, VWAP
from app.core.technical_analysis.patterns.candlestick import CandlestickPatterns
from app.core.technical_analysis.patterns.chart_patterns import ChartPatterns
from app.core.technical_analysis.screener.filters import FilterCondition, FilterGroup
from app.core.technical_analysis.screener.screener import screener
from app.utils.logging import logger

# Create router
router = APIRouter()

# Initialize indicators
indicators = {
    "sma": MovingAverage("sma"),
    "ema": MovingAverage("ema"),
    "wma": MovingAverage("wma"),
    "macd": MACD(),
    "rsi": RSI(),
    "stoch": Stochastic(),
    "bbands": BollingerBands(),
    "atr": ATR(),
    "obv": OBV(),
    "vwap": VWAP()
}

# Initialize pattern recognizers
candlestick_patterns = CandlestickPatterns()
chart_patterns = ChartPatterns()


@router.post("/indicator", response_model=IndicatorResponse)
async def calculate_indicator(request: IndicatorRequest):
    """
    Calculate a technical indicator for a symbol.
    """
    try:
        # Check if indicator is supported
        if request.indicator not in indicators:
            raise HTTPException(status_code=400, detail=f"Unsupported indicator: {request.indicator}")
        
        # Get stock data
        df = await data_manager.get_stock_data(
            request.symbol,
            request.start_date,
            request.end_date,
            request.interval
        )
        
        # Check if data was retrieved
        if df.empty:
            raise HTTPException(status_code=404, detail=f"No data found for {request.symbol}")
        
        # Calculate indicator
        indicator = indicators[request.indicator]
        df = indicator.calculate(df, **request.params)
        
        # Convert DataFrame to list of dictionaries
        data = df.to_dict(orient="records")
        
        # Return response
        return {
            "symbol": request.symbol,
            "indicator": request.indicator,
            "data": data
        }
    except Exception as e:
        logger.error(f"Error calculating indicator: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/pattern", response_model=PatternResponse)
async def recognize_pattern(request: PatternRequest):
    """
    Recognize patterns in a symbol's price data.
    """
    try:
        # Check if pattern type is supported
        if request.pattern_type not in ["candlestick", "chart"]:
            raise HTTPException(status_code=400, detail=f"Unsupported pattern type: {request.pattern_type}")
        
        # Get stock data
        df = await data_manager.get_stock_data(
            request.symbol,
            request.start_date,
            request.end_date,
            request.interval
        )
        
        # Check if data was retrieved
        if df.empty:
            raise HTTPException(status_code=404, detail=f"No data found for {request.symbol}")
        
        # Recognize patterns
        if request.pattern_type == "candlestick":
            df = candlestick_patterns.recognize_patterns(df, request.patterns)
        else:  # chart
            df = chart_patterns.recognize_patterns(df, request.patterns)
        
        # Get recognized patterns
        pattern_columns = [col for col in df.columns if col not in ["open", "high", "low", "close", "volume", "date"]]
        
        # Convert DataFrame to list of dictionaries
        data = df.to_dict(orient="records")
        
        # Return response
        return {
            "symbol": request.symbol,
            "pattern_type": request.pattern_type,
            "patterns": pattern_columns,
            "data": data
        }
    except Exception as e:
        logger.error(f"Error recognizing patterns: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/screen", response_model=ScreenerResponse)
async def screen_stocks(request: ScreenerRequest):
    """
    Screen stocks based on technical criteria.
    """
    try:
        # Parse filters
        filters = []
        for filter_name, filter_config in request.filters.items():
            if filter_config.get("type") == "condition":
                # Create filter condition
                condition = FilterCondition(
                    column=filter_config["column"],
                    operator=filter_config["operator"],
                    value=filter_config["value"],
                    name=filter_name
                )
                filters.append(condition)
            elif filter_config.get("type") == "group":
                # Create filter group
                conditions = []
                for condition_config in filter_config.get("conditions", []):
                    condition = FilterCondition(
                        column=condition_config["column"],
                        operator=condition_config["operator"],
                        value=condition_config["value"],
                        name=condition_config.get("name", f"{condition_config['column']}_{condition_config['operator']}")
                    )
                    conditions.append(condition)
                
                group = FilterGroup(
                    conditions=conditions,
                    operator=filter_config.get("operator", "and"),
                    name=filter_name
                )
                filters.append(group)
        
        # Screen stocks
        results = await screener.screen(
            request.symbols,
            filters,
            request.start_date,
            request.end_date,
            request.interval,
            request.indicators
        )
        
        # Convert DataFrames to lists of dictionaries
        data = {}
        for symbol, df in results.items():
            if not df.empty:
                data[symbol] = df.to_dict(orient="records")
        
        # Return response
        return {
            "symbols": request.symbols,
            "results": data
        }
    except Exception as e:
        logger.error(f"Error screening stocks: {e}")
        raise HTTPException(status_code=500, detail=str(e))
