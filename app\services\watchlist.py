"""
Watchlist service.
"""
from typing import Dict, List, Optional

from fastapi import HTT<PERSON>Exception, status
from sqlalchemy.orm import Session

from app.db.models import User, Watchlist
from app.db.repositories.asset import asset_repository
from app.db.repositories.watchlist import watchlist_repository


class WatchlistService:
    """
    Watchlist service.
    
    This service provides methods for watchlist management.
    """
    
    async def create_watchlist(
        self,
        db: Session,
        user: User,
        name: str,
        description: Optional[str] = None
    ) -> Watchlist:
        """
        Create a new watchlist.
        
        Args:
            db: Database session
            user: User
            name: Watchlist name
            description: Watchlist description
            
        Returns:
            Created watchlist
            
        Raises:
            HTTPException: If a watchlist with the same name already exists
        """
        # Check if watchlist with the same name already exists
        existing_watchlist = watchlist_repository.get_by_name(db, user.id, name)
        if existing_watchlist:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Watchlist with this name already exists"
            )
        
        # Create watchlist
        watchlist_data = {
            "user_id": user.id,
            "name": name,
            "description": description
        }
        
        # Save watchlist to database
        watchlist = watchlist_repository.create(db, obj_in=watchlist_data)
        
        return watchlist
    
    async def get_watchlist(
        self,
        db: Session,
        user: User,
        watchlist_id: int
    ) -> Watchlist:
        """
        Get a watchlist by ID.
        
        Args:
            db: Database session
            user: User
            watchlist_id: Watchlist ID
            
        Returns:
            Watchlist
            
        Raises:
            HTTPException: If the watchlist is not found or does not belong to the user
        """
        # Get watchlist from database
        watchlist = watchlist_repository.get(db, watchlist_id)
        
        # Check if watchlist exists and belongs to the user
        if not watchlist or watchlist.user_id != user.id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Watchlist not found"
            )
        
        return watchlist
    
    async def get_watchlists(
        self,
        db: Session,
        user: User
    ) -> List[Watchlist]:
        """
        Get all watchlists for a user.
        
        Args:
            db: Database session
            user: User
            
        Returns:
            List of watchlists
        """
        # Get watchlists from database
        watchlists = watchlist_repository.get_by_user_id(db, user.id)
        
        return watchlists
    
    async def update_watchlist(
        self,
        db: Session,
        user: User,
        watchlist_id: int,
        name: Optional[str] = None,
        description: Optional[str] = None
    ) -> Watchlist:
        """
        Update a watchlist.
        
        Args:
            db: Database session
            user: User
            watchlist_id: Watchlist ID
            name: New watchlist name
            description: New watchlist description
            
        Returns:
            Updated watchlist
            
        Raises:
            HTTPException: If the watchlist is not found or does not belong to the user
        """
        # Get watchlist from database
        watchlist = await self.get_watchlist(db, user, watchlist_id)
        
        # Update watchlist
        if name is not None:
            # Check if watchlist with the same name already exists
            existing_watchlist = watchlist_repository.get_by_name(db, user.id, name)
            if existing_watchlist and existing_watchlist.id != watchlist_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Watchlist with this name already exists"
                )
            
            watchlist.name = name
        
        if description is not None:
            watchlist.description = description
        
        # Save watchlist to database
        db.commit()
        db.refresh(watchlist)
        
        return watchlist
    
    async def delete_watchlist(
        self,
        db: Session,
        user: User,
        watchlist_id: int
    ) -> None:
        """
        Delete a watchlist.
        
        Args:
            db: Database session
            user: User
            watchlist_id: Watchlist ID
            
        Raises:
            HTTPException: If the watchlist is not found or does not belong to the user
        """
        # Get watchlist from database
        watchlist = await self.get_watchlist(db, user, watchlist_id)
        
        # Delete watchlist
        db.delete(watchlist)
        db.commit()
    
    async def add_asset_to_watchlist(
        self,
        db: Session,
        user: User,
        watchlist_id: int,
        symbol: str
    ) -> None:
        """
        Add an asset to a watchlist.
        
        Args:
            db: Database session
            user: User
            watchlist_id: Watchlist ID
            symbol: Asset symbol
            
        Raises:
            HTTPException: If the watchlist is not found or does not belong to the user
        """
        # Get watchlist from database
        watchlist = await self.get_watchlist(db, user, watchlist_id)
        
        # Get asset from database
        asset = asset_repository.get_by_symbol(db, symbol)
        
        # If asset does not exist, create it
        if not asset:
            asset_data = {
                "symbol": symbol,
                "name": symbol  # Use symbol as name for now
            }
            asset = asset_repository.create(db, obj_in=asset_data)
        
        # Add asset to watchlist
        watchlist_repository.add_asset(db, watchlist.id, asset.id)
    
    async def remove_asset_from_watchlist(
        self,
        db: Session,
        user: User,
        watchlist_id: int,
        symbol: str
    ) -> None:
        """
        Remove an asset from a watchlist.
        
        Args:
            db: Database session
            user: User
            watchlist_id: Watchlist ID
            symbol: Asset symbol
            
        Raises:
            HTTPException: If the watchlist is not found or does not belong to the user
        """
        # Get watchlist from database
        watchlist = await self.get_watchlist(db, user, watchlist_id)
        
        # Get asset from database
        asset = asset_repository.get_by_symbol(db, symbol)
        
        # If asset exists, remove it from the watchlist
        if asset:
            watchlist_repository.remove_asset(db, watchlist.id, asset.id)
    
    async def get_watchlist_assets(
        self,
        db: Session,
        user: User,
        watchlist_id: int
    ) -> List[Dict]:
        """
        Get assets in a watchlist.
        
        Args:
            db: Database session
            user: User
            watchlist_id: Watchlist ID
            
        Returns:
            List of assets
            
        Raises:
            HTTPException: If the watchlist is not found or does not belong to the user
        """
        # Get watchlist from database
        watchlist = await self.get_watchlist(db, user, watchlist_id)
        
        # Get assets from database
        assets = watchlist_repository.get_assets(db, watchlist.id)
        
        # Convert assets to dictionaries
        result = []
        for asset in assets:
            result.append({
                "id": asset.id,
                "symbol": asset.symbol,
                "name": asset.name,
                "asset_type": asset.asset_type,
                "sector": asset.sector,
                "industry": asset.industry
            })
        
        return result


# Create a singleton instance
watchlist_service = WatchlistService()
