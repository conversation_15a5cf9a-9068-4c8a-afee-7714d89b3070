"""
Asset repository for database operations.
"""
from typing import Dict, List, Optional

from sqlalchemy.orm import Session

from app.db.models import Asset
from app.db.repositories.base import BaseRepository


class AssetRepository(BaseRepository[Asset, Dict, Dict]):
    """
    Asset repository for database operations.
    """
    
    def __init__(self):
        """Initialize the repository."""
        super().__init__(Asset)
    
    def get_by_symbol(self, db: Session, symbol: str) -> Optional[Asset]:
        """
        Get an asset by symbol.
        
        Args:
            db: Database session
            symbol: Asset symbol
            
        Returns:
            Asset if found, None otherwise
        """
        return db.query(self.model).filter(self.model.symbol == symbol).first()
    
    def get_by_symbols(self, db: Session, symbols: List[str]) -> List[Asset]:
        """
        Get assets by symbols.
        
        Args:
            db: Database session
            symbols: List of asset symbols
            
        Returns:
            List of assets
        """
        return db.query(self.model).filter(self.model.symbol.in_(symbols)).all()
    
    def get_by_sector(self, db: Session, sector: str) -> List[Asset]:
        """
        Get assets by sector.
        
        Args:
            db: Database session
            sector: Asset sector
            
        Returns:
            List of assets
        """
        return db.query(self.model).filter(self.model.sector == sector).all()
    
    def get_by_industry(self, db: Session, industry: str) -> List[Asset]:
        """
        Get assets by industry.
        
        Args:
            db: Database session
            industry: Asset industry
            
        Returns:
            List of assets
        """
        return db.query(self.model).filter(self.model.industry == industry).all()
    
    def search(self, db: Session, query: str, limit: int = 10) -> List[Asset]:
        """
        Search assets by symbol or name.
        
        Args:
            db: Database session
            query: Search query
            limit: Maximum number of results
            
        Returns:
            List of assets
        """
        return db.query(self.model).filter(
            (self.model.symbol.ilike(f"%{query}%")) |
            (self.model.name.ilike(f"%{query}%"))
        ).limit(limit).all()


# Create a singleton instance
asset_repository = AssetRepository()
