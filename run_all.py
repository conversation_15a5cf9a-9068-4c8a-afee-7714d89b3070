"""
Script to start main and risk API components of the Portfolio Optimization application.
"""
import os
import subprocess
import sys
import time
from threading import Thread
from kill_ports import kill_port, is_port_in_use

# Define colors for console output
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    CYAN = '\033[96m'
    GREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

def print_colored(message, color):
    """Print a colored message to the console."""
    print(f"{color}{message}{Colors.ENDC}")

def wait_for_port(port, timeout=5):
    """Wait for a port to become available."""
    start_time = time.time()
    while time.time() - start_time < timeout:
        if not is_port_in_use(port):
            return True
        time.sleep(0.1)
    return False

def run_main_api():
    """Run the main FastAPI server."""
    if not wait_for_port(8000):
        print_colored("Error: Port 8000 is already in use", Colors.FAIL)
        return
    print_colored("Starting main FastAPI server on port 8000...", Colors.BLUE)
    subprocess.run(["uvicorn", "app.api.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"])

def run_risk_api():
    """Run the risk API server."""
    if not wait_for_port(8003):
        print_colored("Error: Port 8003 is already in use", Colors.FAIL)
        return
    print_colored("Starting risk API server on port 8003...", Colors.BLUE)
    # Make sure we're using the correct path to the risk_api module
    project_root = os.path.dirname(os.path.abspath(__file__))
    risk_api_path = os.path.join(project_root, "risk_api.py")
    if os.path.exists(risk_api_path):
        # Use the full path to the module to avoid import issues
        subprocess.run(["uvicorn", "risk_api:app", "--host", "0.0.0.0", "--port", "8003", "--reload", "--log-level", "debug"])
    else:
        print_colored(f"Error: Risk API file not found at {risk_api_path}", Colors.FAIL)

def run_streamlit_app_subprocess():
    """Run the Streamlit app as a subprocess."""
    print_colored("Starting Streamlit app as subprocess...", Colors.BLUE)
    env = os.environ.copy()
    project_root = os.path.dirname(os.path.abspath(__file__))

    # Add project root to PYTHONPATH - this is critical for imports to work
    env["PYTHONPATH"] = project_root

    # Create a simple wrapper script that imports the app directly
    wrapper_content = """
import streamlit as st
import sys
import os

# Add the project root to sys.path to ensure imports work
project_root = os.path.abspath(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Now import from the app package
from app.streamlit.app import main

if __name__ == "__main__":
    main()
"""

    # Write the wrapper to a temporary file
    wrapper_path = os.path.join(project_root, "streamlit_wrapper.py")
    with open(wrapper_path, "w") as f:
        f.write(wrapper_content)

    # Run streamlit with the wrapper script
    streamlit_cmd = [
        sys.executable, "-m", "streamlit", "run",
        wrapper_path,
        "--server.port=8501",
        "--server.address=0.0.0.0"
    ]

    # Return the process so it can be managed by the caller
    return subprocess.Popen(
        streamlit_cmd,
        env=env,
        cwd=project_root
    )

def kill_existing_ports():
    """Kill processes using our required ports."""
    ports = [8000, 8501]  # Main API and Streamlit ports
    for port in ports:
        if is_port_in_use(port):
            print_colored(f"Killing process on port {port}...", Colors.WARNING)
            kill_port(port)
            time.sleep(1)

def main():
    """Main function to run all services including Streamlit."""
    print_colored("=" * 80, Colors.HEADER)
    print_colored("Starting Portfolio Optimization Application (Main API and Streamlit)", Colors.HEADER)
    print_colored("=" * 80, Colors.HEADER)

    # Kill existing processes on our ports
    kill_existing_ports()

    # Create thread for main API service only
    main_api_thread = Thread(target=run_main_api)

    # Path to the temporary wrapper file
    project_root = os.path.dirname(os.path.abspath(__file__))
    wrapper_path = os.path.join(project_root, "streamlit_wrapper.py")

    try:
        # Start main API thread
        main_api_thread.start()
        time.sleep(3)  # Give API more time to start

        # Start Streamlit app as subprocess
        print_colored("Starting Streamlit application...", Colors.GREEN)
        streamlit_process = run_streamlit_app_subprocess()

        # Wait for API thread to finish
        main_api_thread.join()

        # Wait for Streamlit subprocess to finish
        streamlit_process.wait()

    except KeyboardInterrupt:
        print_colored("\nShutting down all services...", Colors.WARNING)
        if 'streamlit_process' in locals():
            streamlit_process.terminate()
        sys.exit(0)
    except Exception as e:
        print_colored(f"\nError: {str(e)}", Colors.FAIL)
        print_colored(f"Full error details: {repr(e)}", Colors.FAIL)
        sys.exit(1)
    finally:
        # Clean up the temporary wrapper file
        if os.path.exists(wrapper_path):
            try:
                os.remove(wrapper_path)
                print_colored("Cleaned up temporary files", Colors.GREEN)
            except Exception as e:
                print_colored(f"Error cleaning up temporary files: {e}", Colors.WARNING)

if __name__ == "__main__":
    main()








