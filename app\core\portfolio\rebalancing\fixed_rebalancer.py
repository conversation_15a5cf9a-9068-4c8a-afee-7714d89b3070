"""
Fixed portfolio rebalancing functionality.
"""
from datetime import date
from typing import Dict, List, Optional, Any

import pandas as pd
import numpy as np

from app.utils.logging import logger


def process_stock_data(data_dict: Dict[str, pd.DataFrame]) -> pd.DataFrame:
    """
    Process and combine stock data from multiple symbols into a single DataFrame.

    Args:
        data_dict: Dictionary mapping symbols to DataFrames with historical stock data

    Returns:
        DataFrame with combined stock data, with 'close' prices for each symbol
    """
    if not data_dict:
        return pd.DataFrame()

    # Create a new DataFrame to store combined data
    combined_df = pd.DataFrame()

    # Process each symbol's data
    for symbol, df in data_dict.items():
        if df.empty:
            continue

        # Make sure the DataFrame has a datetime index
        if not isinstance(df.index, pd.DatetimeIndex):
            # If 'date' is a column, set it as index
            if 'date' in df.columns:
                df = df.set_index('date')
            # Otherwise, try to convert the current index to datetime
            else:
                try:
                    df.index = pd.to_datetime(df.index)
                except Exception as idx_error:
                    logger.warning(f"Could not convert index to datetime for {symbol}: {idx_error}")
                    continue

        # Extract close prices and add to combined DataFrame
        if 'close' in df.columns:
            combined_df[symbol] = df['close']
        elif 'Close' in df.columns:  # Handle different column naming
            combined_df[symbol] = df['Close']
        else:
            logger.warning(f"No close price column found for {symbol}")

    # Sort by date
    combined_df = combined_df.sort_index()

    return combined_df


def should_rebalance_periodic(
    current_weights: Dict[str, float],
    target_weights: Dict[str, float],
    last_rebalance_date: Optional[pd.Timestamp] = None,
    current_date: Optional[pd.Timestamp] = None,
    frequency: str = "monthly",
    **kwargs
) -> bool:
    """
    Determine if the portfolio should be rebalanced based on the time since the last rebalance.

    Args:
        current_weights: Current portfolio weights
        target_weights: Target portfolio weights
        last_rebalance_date: Date of the last rebalance
        current_date: Current date
        frequency: Rebalancing frequency ('daily', 'weekly', 'monthly', 'quarterly', 'annually')
        **kwargs: Additional parameters

    Returns:
        True if the portfolio should be rebalanced, False otherwise
    """
    # If no last rebalance date or current date, rebalance
    if last_rebalance_date is None or current_date is None:
        return True

    # Convert to pandas Timestamp objects if they aren't already
    if not isinstance(last_rebalance_date, pd.Timestamp):
        last_rebalance_date = pd.Timestamp(last_rebalance_date)
    if not isinstance(current_date, pd.Timestamp):
        current_date = pd.Timestamp(current_date)

    # Ensure both dates have the same timezone or are both timezone-naive
    if last_rebalance_date.tzinfo is not None and current_date.tzinfo is None:
        current_date = current_date.tz_localize(last_rebalance_date.tzinfo)
    elif last_rebalance_date.tzinfo is None and current_date.tzinfo is not None:
        last_rebalance_date = last_rebalance_date.tz_localize(current_date.tzinfo)

    # Calculate time since last rebalance using a safer approach
    try:
        logger.debug(f"Calculating time difference between {current_date} and {last_rebalance_date}")
        # Convert to datetime.date objects to avoid timezone issues
        start_date = last_rebalance_date.date()
        end_date = current_date.date()

        # Use pandas DateOffset for safer date arithmetic
        days_since_last_rebalance = (pd.Timestamp(end_date) - pd.Timestamp(start_date)).days
        logger.debug(f"Time difference calculated: {days_since_last_rebalance} days")
    except Exception as e:
        logger.warning(f"Error calculating time difference: {e}")
        # Try another approach with string conversion
        logger.debug("Trying alternative approach with string conversion")
        try:
            # Convert to strings and back to timestamps
            start_str = last_rebalance_date.strftime('%Y-%m-%d')
            end_str = current_date.strftime('%Y-%m-%d')
            days_since_last_rebalance = (pd.Timestamp(end_str) - pd.Timestamp(start_str)).days
            logger.debug(f"Alternative approach result: {days_since_last_rebalance} days")
        except Exception as e2:
            logger.error(f"Alternative approach also failed: {e2}")
            # Last resort fallback - use a safe default
            days_since_last_rebalance = 91  # Slightly more than quarterly to trigger a rebalance

    # Determine if rebalance is needed based on frequency
    if frequency == "daily":
        return days_since_last_rebalance >= 1
    elif frequency == "weekly":
        return days_since_last_rebalance >= 7
    elif frequency == "monthly":
        # Approximate a month as 30 days
        return days_since_last_rebalance >= 30
    elif frequency == "quarterly":
        # Approximate a quarter as 90 days
        return days_since_last_rebalance >= 90
    elif frequency == "annually":
        # Approximate a year as 365 days
        return days_since_last_rebalance >= 365
    else:
        logger.warning(f"Unknown frequency: {frequency}, defaulting to quarterly")
        return days_since_last_rebalance >= 90


def should_rebalance_threshold(
    current_weights: Dict[str, float],
    target_weights: Dict[str, float],
    threshold: float = 0.05,
    **kwargs
) -> bool:
    """
    Determine if the portfolio should be rebalanced based on the deviation from target weights.

    Args:
        current_weights: Current portfolio weights
        target_weights: Target portfolio weights
        threshold: Rebalancing threshold (as a decimal, e.g., 0.05 for 5%)
        **kwargs: Additional parameters

    Returns:
        True if the portfolio should be rebalanced, False otherwise
    """
    # Check if any asset's weight deviates from its target by more than the threshold
    for asset, target_weight in target_weights.items():
        current_weight = current_weights.get(asset, 0)
        if abs(current_weight - target_weight) > threshold:
            return True

    return False


def should_rebalance_hybrid(
    current_weights: Dict[str, float],
    target_weights: Dict[str, float],
    last_rebalance_date: Optional[pd.Timestamp] = None,
    current_date: Optional[pd.Timestamp] = None,
    frequency: str = "quarterly",
    threshold: float = 0.05,
    **kwargs
) -> bool:
    """
    Determine if the portfolio should be rebalanced based on either time or weight deviation.

    Args:
        current_weights: Current portfolio weights
        target_weights: Target portfolio weights
        last_rebalance_date: Date of the last rebalance
        current_date: Current date
        frequency: Rebalancing frequency ('daily', 'weekly', 'monthly', 'quarterly', 'annually')
        threshold: Rebalancing threshold (as a decimal, e.g., 0.05 for 5%)
        **kwargs: Additional parameters

    Returns:
        True if the portfolio should be rebalanced, False otherwise
    """
    # Rebalance if either strategy says to rebalance
    return (
        should_rebalance_periodic(
            current_weights, target_weights, last_rebalance_date, current_date, frequency, **kwargs
        ) or
        should_rebalance_threshold(
            current_weights, target_weights, threshold, **kwargs
        )
    )


def should_rebalance_optimal(
    current_weights: Dict[str, float],
    target_weights: Dict[str, float],
    expected_returns: Optional[Dict[str, float]] = None,
    transaction_cost: float = 0.001,
    **kwargs
) -> bool:
    """
    Determine if the portfolio should be rebalanced based on a cost-benefit analysis.

    Args:
        current_weights: Current portfolio weights
        target_weights: Target portfolio weights
        expected_returns: Expected returns for each asset
        transaction_cost: Transaction cost as a fraction of the transaction amount
        **kwargs: Additional parameters

    Returns:
        True if the portfolio should be rebalanced, False otherwise
    """
    # If expected returns are not provided, use a threshold-based approach
    if expected_returns is None:
        return should_rebalance_threshold(current_weights, target_weights, 0.05, **kwargs)

    # Calculate the expected return of the current portfolio
    current_return = sum(current_weights.get(asset, 0) * expected_returns.get(asset, 0) for asset in expected_returns)

    # Calculate the expected return of the target portfolio
    target_return = sum(target_weights.get(asset, 0) * expected_returns.get(asset, 0) for asset in expected_returns)

    # Calculate the transaction cost of rebalancing
    transaction_amount = sum(
        abs(current_weights.get(asset, 0) - target_weights.get(asset, 0))
        for asset in set(current_weights) | set(target_weights)
    )
    rebalancing_cost = transaction_amount * transaction_cost

    # Rebalance if the expected benefit exceeds the cost
    return (target_return - current_return) > rebalancing_cost


def analyze_rebalancing(
    data: Dict[str, pd.DataFrame],
    symbols: List[str],
    initial_weights: Dict[str, float],
    start_date: date,
    end_date: date,
    strategy: str,
    strategy_params: Optional[Dict[str, Any]] = None,
    total_portfolio_value: Optional[float] = None
) -> Dict[str, Any]:
    """
    Analyze portfolio rebalancing strategies.

    Args:
        data: Dictionary mapping symbols to DataFrames with historical stock data
        symbols: List of stock symbols
        initial_weights: Initial portfolio weights
        start_date: Start date for historical data
        end_date: End date for historical data
        strategy: Rebalancing strategy
        strategy_params: Strategy-specific parameters
        total_portfolio_value: Total portfolio value

    Returns:
        Dictionary with rebalancing analysis results
    """
    try:
        logger.debug(f"Starting analyze_rebalancing with strategy: {strategy}")
        logger.debug(f"Parameters: symbols={symbols}, start_date={start_date}, end_date={end_date}")
        logger.debug(f"Strategy parameters: {strategy_params}")

        # Process and combine stock data
        prices_df = process_stock_data(data)

        # Calculate returns
        returns_df = prices_df.pct_change().dropna()

        # Initialize portfolio
        initial_portfolio_value = total_portfolio_value or 10000
        current_weights = initial_weights
        portfolio_values = []
        weights_over_time = {}
        rebalancing_events = []
        rebalancing_dates = []

        # Simulate portfolio over time
        dates = returns_df.index.tolist()
        current_portfolio_value = initial_portfolio_value

        # Get strategy parameters
        strategy_params = strategy_params or {}

        # Extract strategy-specific parameters
        frequency = strategy_params.get("frequency", "quarterly")
        threshold = strategy_params.get("threshold", 0.05)
        transaction_cost = strategy_params.get("transaction_cost", 0.001)

        for i, date_idx in enumerate(dates):
            date_str = date_idx.strftime('%Y-%m-%d')

            # Store current weights
            weights_over_time[date_str] = current_weights.copy()

            # Calculate portfolio return for the day
            daily_return = sum(current_weights.get(symbol, 0) * returns_df.loc[date_idx, symbol]
                              for symbol in symbols if symbol in returns_df.columns)

            # Update portfolio value
            current_portfolio_value *= (1 + daily_return)
            portfolio_values.append(current_portfolio_value)

            # Check if rebalancing is needed
            last_rebalance_date = None
            if rebalancing_dates:
                last_rebalance_idx = rebalancing_dates[-1]
                if 0 <= last_rebalance_idx < len(dates):
                    last_rebalance_date = dates[last_rebalance_idx]
                    logger.debug(f"Last rebalance date: {last_rebalance_date}, type: {type(last_rebalance_date)}")
                else:
                    logger.debug(f"Invalid last_rebalance_idx: {last_rebalance_idx}, len(dates): {len(dates)}")
            else:
                logger.debug("No previous rebalancing dates")

            logger.debug(f"Current date: {date_idx}, type: {type(date_idx)}")

            # Determine if rebalancing is needed based on strategy
            should_rebalance = False

            if i > 0:  # Skip first day
                logger.debug(f"Checking if rebalancing is needed for strategy: {strategy}")
                if strategy == "periodic":
                    logger.debug(f"Using periodic rebalancing with frequency: {frequency}")
                    should_rebalance = should_rebalance_periodic(
                        current_weights, initial_weights, last_rebalance_date, date_idx, frequency
                    )
                elif strategy == "threshold":
                    logger.debug(f"Using threshold rebalancing with threshold: {threshold}")
                    should_rebalance = should_rebalance_threshold(
                        current_weights, initial_weights, threshold
                    )
                elif strategy == "hybrid":
                    logger.debug(f"Using hybrid rebalancing with frequency: {frequency}, threshold: {threshold}")
                    should_rebalance = should_rebalance_hybrid(
                        current_weights, initial_weights, last_rebalance_date, date_idx, frequency, threshold
                    )
                elif strategy == "optimal":
                    logger.debug(f"Using optimal rebalancing with transaction_cost: {transaction_cost}")
                    should_rebalance = should_rebalance_optimal(
                        current_weights, initial_weights,
                        expected_returns=returns_df.mean().to_dict(),
                        transaction_cost=transaction_cost
                    )
                else:
                    logger.debug(f"Unknown strategy: {strategy}, defaulting to periodic quarterly rebalancing")
                    # Default to periodic quarterly rebalancing
                    should_rebalance = should_rebalance_periodic(
                        current_weights, initial_weights, last_rebalance_date, date_idx, "quarterly"
                    )
                logger.debug(f"Rebalancing decision: {should_rebalance}")

            if should_rebalance:
                # Rebalance portfolio
                current_weights = initial_weights.copy()
                rebalancing_events.append({
                    "date": date_str,
                    "action": f"Rebalanced to target weights ({strategy})",
                    "transaction_cost": f"${current_portfolio_value * transaction_cost:.2f}"
                })
                rebalancing_dates.append(i)
            else:
                # Update weights based on performance
                total_value = sum(current_weights.get(symbol, 0) * (1 + returns_df.loc[date_idx, symbol])
                                 for symbol in symbols if symbol in returns_df.columns)

                if total_value > 0:
                    for symbol in symbols:
                        if symbol in returns_df.columns:
                            current_weights[symbol] = (current_weights.get(symbol, 0) *
                                                     (1 + returns_df.loc[date_idx, symbol])) / total_value

        # Calculate drawdown
        peak = np.maximum.accumulate(portfolio_values)
        drawdown = [(portfolio_values[i] - peak[i]) / peak[i] for i in range(len(portfolio_values))]

        # Calculate performance metrics
        total_return = (portfolio_values[-1] / portfolio_values[0]) - 1
        trading_days_per_year = 252
        years = len(portfolio_values) / trading_days_per_year
        annualized_return = (1 + total_return) ** (1 / years) - 1 if years > 0 else 0

        # Calculate volatility
        returns = [(portfolio_values[i] / portfolio_values[i-1]) - 1 for i in range(1, len(portfolio_values))]
        volatility = np.std(returns) * np.sqrt(trading_days_per_year)

        # Calculate Sharpe ratio (assuming risk-free rate of 0.02)
        risk_free_rate = 0.02
        sharpe_ratio = (annualized_return - risk_free_rate) / volatility if volatility > 0 else 0

        # Calculate Sortino ratio (downside deviation)
        negative_returns = [r for r in returns if r < 0]
        downside_deviation = np.std(negative_returns) * np.sqrt(trading_days_per_year) if negative_returns else 0.01
        sortino_ratio = (annualized_return - risk_free_rate) / downside_deviation if downside_deviation > 0 else 0

        # Prepare metrics
        metrics = {
            "total_return": total_return,
            "annualized_return": annualized_return,
            "volatility": volatility,
            "sharpe_ratio": sharpe_ratio,
            "sortino_ratio": sortino_ratio,
            "max_drawdown": min(drawdown)
        }

        # Return results
        return {
            "strategy": strategy,
            "strategy_params": strategy_params,
            "performance_metrics": metrics,
            "rebalancing_events": rebalancing_events,
            "rebalancing_dates": [dates[idx].strftime('%Y-%m-%d') for idx in rebalancing_dates],
            "weights_over_time": weights_over_time,
            "portfolio_value": {
                "dates": [date_idx.strftime('%Y-%m-%d') for date_idx in dates],
                "values": portfolio_values
            },
            "drawdown": {
                "dates": [date_idx.strftime('%Y-%m-%d') for date_idx in dates],
                "values": drawdown
            },
            "cumulative_returns": {
                "dates": [date_idx.strftime('%Y-%m-%d') for date_idx in dates],
                "values": [(portfolio_values[i] / portfolio_values[0]) - 1 for i in range(len(portfolio_values))]
            }
        }
    except Exception as e:
        logger.error(f"Error in analyze_rebalancing: {str(e)}")
        raise
