#!/bin/bash

# Portfolio Optimization Backup Script
# This script creates a backup of the Portfolio Optimization application.

set -e

# Configuration
ENVIRONMENT=${ENVIRONMENT:-production}
DEPLOY_DIR=${DEPLOY_DIR:-/opt/portfolio-optimization}
BACKUP_DIR=${BACKUP_DIR:-/opt/backups/portfolio-optimization}
BACKUP_RETENTION=${BACKUP_RETENTION:-7}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Print usage
usage() {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  -e, --environment <env>   Deployment environment (default: production)"
    echo "  -d, --deploy-dir <dir>    Deployment directory (default: /opt/portfolio-optimization)"
    echo "  -b, --backup-dir <dir>    Backup directory (default: /opt/backups/portfolio-optimization)"
    echo "  -r, --retention <days>    Backup retention in days (default: 7)"
    echo "  -h, --help                Show this help message"
    exit 1
}

# Parse command-line arguments
while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        -e|--environment)
            ENVIRONMENT="$2"
            shift
            shift
            ;;
        -d|--deploy-dir)
            DEPLOY_DIR="$2"
            shift
            shift
            ;;
        -b|--backup-dir)
            BACKUP_DIR="$2"
            shift
            shift
            ;;
        -r|--retention)
            BACKUP_RETENTION="$2"
            shift
            shift
            ;;
        -h|--help)
            usage
            ;;
        *)
            echo -e "${RED}Error: Unknown option $1${NC}"
            usage
            ;;
    esac
done

# Check if Docker and Docker Compose are installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Error: Docker is not installed${NC}"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}Error: Docker Compose is not installed${NC}"
    exit 1
fi

# Create backup directory if it doesn't exist
if [ ! -d "$BACKUP_DIR" ]; then
    echo -e "${YELLOW}Creating backup directory: $BACKUP_DIR${NC}"
    mkdir -p "$BACKUP_DIR"
fi

# Create backup date
BACKUP_DATE=$(date +%Y%m%d%H%M%S)
BACKUP_FILE="$BACKUP_DIR/backup-$BACKUP_DATE.tar.gz"
DB_BACKUP_FILE="$BACKUP_DIR/db-backup-$BACKUP_DATE.sql"

# Backup database
echo -e "${YELLOW}Backing up database${NC}"
cd "$DEPLOY_DIR/deployment/docker"
docker-compose -f docker-compose.yml -f docker-compose.prod.yml exec -T db pg_dump -U postgres -d portfolio_optimization > "$DB_BACKUP_FILE"
echo -e "${GREEN}Database backup created: $DB_BACKUP_FILE${NC}"

# Backup application files
echo -e "${YELLOW}Backing up application files${NC}"
tar -czf "$BACKUP_FILE" -C "$DEPLOY_DIR" \
    --exclude=".git" \
    --exclude="venv" \
    --exclude="__pycache__" \
    --exclude="*.pyc" \
    --exclude="*.pyo" \
    --exclude="*.pyd" \
    --exclude=".pytest_cache" \
    --exclude=".coverage" \
    --exclude="htmlcov" \
    --exclude="node_modules" \
    --exclude="dist" \
    --exclude="build" \
    .
echo -e "${GREEN}Application backup created: $BACKUP_FILE${NC}"

# Backup Docker volumes
echo -e "${YELLOW}Backing up Docker volumes${NC}"
VOLUMES_BACKUP_DIR="$BACKUP_DIR/volumes-$BACKUP_DATE"
mkdir -p "$VOLUMES_BACKUP_DIR"

# Get list of volumes
VOLUMES=$(docker volume ls -q | grep "portfolio-optimization")

for VOLUME in $VOLUMES; do
    echo -e "${YELLOW}Backing up volume: $VOLUME${NC}"
    VOLUME_BACKUP_FILE="$VOLUMES_BACKUP_DIR/$VOLUME.tar.gz"
    
    # Create a temporary container to access the volume
    CONTAINER_ID=$(docker run -d -v $VOLUME:/volume busybox:latest sleep 60)
    
    # Backup the volume
    docker exec $CONTAINER_ID tar -czf /tmp/volume.tar.gz -C /volume .
    docker cp $CONTAINER_ID:/tmp/volume.tar.gz $VOLUME_BACKUP_FILE
    
    # Remove the temporary container
    docker rm -f $CONTAINER_ID
    
    echo -e "${GREEN}Volume backup created: $VOLUME_BACKUP_FILE${NC}"
done

# Create a single archive for all volume backups
VOLUMES_BACKUP_FILE="$BACKUP_DIR/volumes-backup-$BACKUP_DATE.tar.gz"
tar -czf "$VOLUMES_BACKUP_FILE" -C "$BACKUP_DIR" "volumes-$BACKUP_DATE"
rm -rf "$VOLUMES_BACKUP_DIR"
echo -e "${GREEN}Volumes backup created: $VOLUMES_BACKUP_FILE${NC}"

# Remove old backups
echo -e "${YELLOW}Removing backups older than $BACKUP_RETENTION days${NC}"
find "$BACKUP_DIR" -name "backup-*.tar.gz" -type f -mtime +$BACKUP_RETENTION -delete
find "$BACKUP_DIR" -name "db-backup-*.sql" -type f -mtime +$BACKUP_RETENTION -delete
find "$BACKUP_DIR" -name "volumes-backup-*.tar.gz" -type f -mtime +$BACKUP_RETENTION -delete

echo -e "${GREEN}Backup completed successfully!${NC}"
