"""
Technical filters for stock screening.
"""
from typing import Any, Callable, Dict, List, Optional, Union

import pandas as pd

from app.utils.logging import logger


class FilterCondition:
    """
    Filter condition for stock screening.
    
    This class represents a single filter condition that can be applied
    to stock data.
    """
    
    def __init__(
        self,
        column: str,
        operator: str,
        value: Any,
        name: Optional[str] = None
    ):
        """
        Initialize a filter condition.
        
        Args:
            column: Column to filter on
            operator: Comparison operator ('>', '<', '>=', '<=', '==', '!=')
            value: Value to compare against
            name: Name of the filter condition
        """
        self.column = column
        self.operator = operator
        self.value = value
        self.name = name or f"{column}_{operator}_{value}"
        
        # Validate operator
        valid_operators = ['>', '<', '>=', '<=', '==', '!=', 'in', 'not in', 'contains', 'between']
        if self.operator not in valid_operators:
            raise ValueError(f"Invalid operator: {operator}. Must be one of {valid_operators}")
        
        # Create the filter function
        self.filter_func = self._create_filter_function()
    
    def _create_filter_function(self) -> Callable[[pd.DataFrame], pd.Series]:
        """
        Create a filter function based on the operator.
        
        Returns:
            Function that applies the filter to a DataFrame
        """
        if self.operator == '>':
            return lambda df: df[self.column] > self.value
        elif self.operator == '<':
            return lambda df: df[self.column] < self.value
        elif self.operator == '>=':
            return lambda df: df[self.column] >= self.value
        elif self.operator == '<=':
            return lambda df: df[self.column] <= self.value
        elif self.operator == '==':
            return lambda df: df[self.column] == self.value
        elif self.operator == '!=':
            return lambda df: df[self.column] != self.value
        elif self.operator == 'in':
            return lambda df: df[self.column].isin(self.value)
        elif self.operator == 'not in':
            return lambda df: ~df[self.column].isin(self.value)
        elif self.operator == 'contains':
            return lambda df: df[self.column].str.contains(self.value, na=False)
        elif self.operator == 'between':
            if not isinstance(self.value, (list, tuple)) or len(self.value) != 2:
                raise ValueError("Value for 'between' operator must be a list or tuple of length 2")
            return lambda df: (df[self.column] >= self.value[0]) & (df[self.column] <= self.value[1])
        else:
            raise ValueError(f"Unsupported operator: {self.operator}")
    
    def apply(self, data: pd.DataFrame) -> pd.Series:
        """
        Apply the filter to the data.
        
        Args:
            data: DataFrame to filter
            
        Returns:
            Boolean Series indicating which rows match the filter
        """
        # Check if column exists
        if self.column not in data.columns:
            logger.warning(f"Column '{self.column}' not found in data")
            return pd.Series(False, index=data.index)
        
        try:
            return self.filter_func(data)
        except Exception as e:
            logger.error(f"Error applying filter {self.name}: {e}")
            return pd.Series(False, index=data.index)
    
    def __str__(self) -> str:
        """String representation of the filter condition."""
        return f"{self.name}: {self.column} {self.operator} {self.value}"


class FilterGroup:
    """
    Group of filter conditions for stock screening.
    
    This class represents a group of filter conditions that can be combined
    using logical operators (AND, OR).
    """
    
    def __init__(
        self,
        conditions: Optional[List[FilterCondition]] = None,
        operator: str = "and",
        name: Optional[str] = None
    ):
        """
        Initialize a filter group.
        
        Args:
            conditions: List of filter conditions
            operator: Logical operator to combine conditions ('and', 'or')
            name: Name of the filter group
        """
        self.conditions = conditions or []
        self.operator = operator.lower()
        self.name = name or f"FilterGroup_{id(self)}"
        
        # Validate operator
        if self.operator not in ['and', 'or']:
            raise ValueError(f"Invalid operator: {operator}. Must be 'and' or 'or'")
    
    def add_condition(self, condition: FilterCondition) -> None:
        """
        Add a filter condition to the group.
        
        Args:
            condition: Filter condition to add
        """
        self.conditions.append(condition)
    
    def apply(self, data: pd.DataFrame) -> pd.Series:
        """
        Apply the filter group to the data.
        
        Args:
            data: DataFrame to filter
            
        Returns:
            Boolean Series indicating which rows match the filter group
        """
        if not self.conditions:
            # No conditions, return all rows
            return pd.Series(True, index=data.index)
        
        # Apply each condition
        results = [condition.apply(data) for condition in self.conditions]
        
        # Combine results based on the operator
        if self.operator == 'and':
            return pd.concat(results, axis=1).all(axis=1)
        else:  # 'or'
            return pd.concat(results, axis=1).any(axis=1)
    
    def __str__(self) -> str:
        """String representation of the filter group."""
        conditions_str = '\n  '.join(str(c) for c in self.conditions)
        return f"{self.name} ({self.operator}):\n  {conditions_str}"


class TechnicalFilters:
    """
    Technical filters for stock screening.
    
    This class provides common technical filters that can be used
    for stock screening.
    """
    
    @staticmethod
    def price_above_ma(ma_type: str = "sma", period: int = 50) -> FilterCondition:
        """
        Filter for stocks with price above a moving average.
        
        Args:
            ma_type: Type of moving average ('sma', 'ema', 'wma')
            period: Period for the moving average
            
        Returns:
            Filter condition
        """
        column = f"{ma_type.lower()}_{period}"
        return FilterCondition(
            column="close",
            operator=">",
            value=column,
            name=f"Price_Above_{ma_type.upper()}_{period}"
        )
    
    @staticmethod
    def price_below_ma(ma_type: str = "sma", period: int = 50) -> FilterCondition:
        """
        Filter for stocks with price below a moving average.
        
        Args:
            ma_type: Type of moving average ('sma', 'ema', 'wma')
            period: Period for the moving average
            
        Returns:
            Filter condition
        """
        column = f"{ma_type.lower()}_{period}"
        return FilterCondition(
            column="close",
            operator="<",
            value=column,
            name=f"Price_Below_{ma_type.upper()}_{period}"
        )
    
    @staticmethod
    def ma_crossover(
        fast_ma_type: str = "sma",
        fast_period: int = 20,
        slow_ma_type: str = "sma",
        slow_period: int = 50
    ) -> FilterCondition:
        """
        Filter for stocks with a moving average crossover.
        
        Args:
            fast_ma_type: Type of fast moving average
            fast_period: Period for the fast moving average
            slow_ma_type: Type of slow moving average
            slow_period: Period for the slow moving average
            
        Returns:
            Filter condition
        """
        fast_column = f"{fast_ma_type.lower()}_{fast_period}"
        slow_column = f"{slow_ma_type.lower()}_{slow_period}"
        
        # Custom filter function for crossover
        def crossover_filter(df: pd.DataFrame) -> pd.Series:
            # Current fast MA is above slow MA
            current_above = df[fast_column] > df[slow_column]
            
            # Previous fast MA was below slow MA
            prev_below = df[fast_column].shift(1) <= df[slow_column].shift(1)
            
            # Crossover occurs when current is above and previous was below
            return current_above & prev_below
        
        # Create a custom filter condition
        condition = FilterCondition(
            column=fast_column,
            operator=">",
            value=slow_column,
            name=f"{fast_ma_type.upper()}_{fast_period}_Crossover_{slow_ma_type.upper()}_{slow_period}"
        )
        
        # Override the filter function
        condition.filter_func = crossover_filter
        
        return condition
    
    @staticmethod
    def rsi_oversold(period: int = 14, threshold: float = 30.0) -> FilterCondition:
        """
        Filter for stocks with RSI in oversold territory.
        
        Args:
            period: Period for the RSI calculation
            threshold: Oversold threshold
            
        Returns:
            Filter condition
        """
        return FilterCondition(
            column=f"rsi_{period}",
            operator="<",
            value=threshold,
            name=f"RSI_{period}_Oversold_{threshold}"
        )
    
    @staticmethod
    def rsi_overbought(period: int = 14, threshold: float = 70.0) -> FilterCondition:
        """
        Filter for stocks with RSI in overbought territory.
        
        Args:
            period: Period for the RSI calculation
            threshold: Overbought threshold
            
        Returns:
            Filter condition
        """
        return FilterCondition(
            column=f"rsi_{period}",
            operator=">",
            value=threshold,
            name=f"RSI_{period}_Overbought_{threshold}"
        )
    
    @staticmethod
    def macd_positive() -> FilterCondition:
        """
        Filter for stocks with positive MACD.
        
        Returns:
            Filter condition
        """
        return FilterCondition(
            column="macd",
            operator=">",
            value=0,
            name="MACD_Positive"
        )
    
    @staticmethod
    def macd_negative() -> FilterCondition:
        """
        Filter for stocks with negative MACD.
        
        Returns:
            Filter condition
        """
        return FilterCondition(
            column="macd",
            operator="<",
            value=0,
            name="MACD_Negative"
        )
    
    @staticmethod
    def macd_crossover() -> FilterCondition:
        """
        Filter for stocks with MACD crossing above signal line.
        
        Returns:
            Filter condition
        """
        # Custom filter function for MACD crossover
        def macd_crossover_filter(df: pd.DataFrame) -> pd.Series:
            # Current MACD is above signal line
            current_above = df["macd"] > df["macd_signal"]
            
            # Previous MACD was below signal line
            prev_below = df["macd"].shift(1) <= df["macd_signal"].shift(1)
            
            # Crossover occurs when current is above and previous was below
            return current_above & prev_below
        
        # Create a custom filter condition
        condition = FilterCondition(
            column="macd",
            operator=">",
            value="macd_signal",
            name="MACD_Crossover"
        )
        
        # Override the filter function
        condition.filter_func = macd_crossover_filter
        
        return condition
    
    @staticmethod
    def bollinger_band_squeeze(threshold: float = 0.1) -> FilterCondition:
        """
        Filter for stocks with Bollinger Bands squeeze.
        
        Args:
            threshold: Threshold for bandwidth
            
        Returns:
            Filter condition
        """
        return FilterCondition(
            column="bb_bandwidth",
            operator="<",
            value=threshold,
            name=f"BB_Squeeze_{threshold}"
        )
    
    @staticmethod
    def volume_spike(threshold: float = 2.0) -> FilterCondition:
        """
        Filter for stocks with volume spike.
        
        Args:
            threshold: Threshold for volume increase
            
        Returns:
            Filter condition
        """
        # Custom filter function for volume spike
        def volume_spike_filter(df: pd.DataFrame) -> pd.Series:
            # Calculate average volume over the last 20 days
            avg_volume = df["volume"].rolling(window=20).mean()
            
            # Check if current volume is above threshold times average
            return df["volume"] > threshold * avg_volume
        
        # Create a custom filter condition
        condition = FilterCondition(
            column="volume",
            operator=">",
            value=0,
            name=f"Volume_Spike_{threshold}x"
        )
        
        # Override the filter function
        condition.filter_func = volume_spike_filter
        
        return condition
    
    @staticmethod
    def candlestick_pattern(pattern: str) -> FilterCondition:
        """
        Filter for stocks with a specific candlestick pattern.
        
        Args:
            pattern: Name of the candlestick pattern
            
        Returns:
            Filter condition
        """
        return FilterCondition(
            column=pattern,
            operator="==",
            value=True,
            name=f"Candlestick_{pattern}"
        )
    
    @staticmethod
    def chart_pattern(pattern: str) -> FilterCondition:
        """
        Filter for stocks with a specific chart pattern.
        
        Args:
            pattern: Name of the chart pattern
            
        Returns:
            Filter condition
        """
        return FilterCondition(
            column=pattern,
            operator="==",
            value=True,
            name=f"Chart_{pattern}"
        )
