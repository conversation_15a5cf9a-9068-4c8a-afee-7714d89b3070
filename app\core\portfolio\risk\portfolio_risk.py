"""
Portfolio risk metrics calculation.

This module provides functions to calculate various risk metrics for portfolios,
including Sharpe ratio, Sortino ratio, maximum drawdown, and others.
"""
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
from scipy import stats

from app.utils.constants import DEFAULT_RISK_FREE_RATE
from app.utils.logging import logger


class PortfolioRiskAnalyzer:
    """
    Portfolio risk analyzer for calculating risk metrics.
    """
    
    def __init__(self, risk_free_rate: float = DEFAULT_RISK_FREE_RATE):
        """
        Initialize the portfolio risk analyzer.
        
        Args:
            risk_free_rate: Risk-free rate (default: from settings)
        """
        self.risk_free_rate = risk_free_rate
    
    def calculate_volatility(
        self,
        returns: pd.DataFrame,
        weights: Dict[str, float],
        annualize: bool = True,
        trading_days: int = 252
    ) -> float:
        """
        Calculate portfolio volatility.
        
        Args:
            returns: DataFrame of asset returns
            weights: Dictionary mapping assets to weights
            annualize: Whether to annualize the volatility
            trading_days: Number of trading days in a year
            
        Returns:
            Portfolio volatility
        """
        # Convert weights to numpy array
        weight_arr = np.array([weights.get(asset, 0) for asset in returns.columns])
        
        # Calculate covariance matrix
        cov_matrix = returns.cov()
        
        # Calculate portfolio volatility
        portfolio_volatility = np.sqrt(weight_arr.T @ cov_matrix @ weight_arr)
        
        # Annualize if requested
        if annualize:
            portfolio_volatility *= np.sqrt(trading_days)
        
        return portfolio_volatility
    
    def calculate_sharpe_ratio(
        self,
        returns: pd.DataFrame,
        weights: Dict[str, float],
        annualize: bool = True,
        trading_days: int = 252
    ) -> float:
        """
        Calculate portfolio Sharpe ratio.
        
        Args:
            returns: DataFrame of asset returns
            weights: Dictionary mapping assets to weights
            annualize: Whether to annualize the Sharpe ratio
            trading_days: Number of trading days in a year
            
        Returns:
            Portfolio Sharpe ratio
        """
        # Convert weights to numpy array
        weight_arr = np.array([weights.get(asset, 0) for asset in returns.columns])
        
        # Calculate expected return
        expected_return = returns.mean() @ weight_arr
        
        # Calculate volatility
        volatility = self.calculate_volatility(returns, weights, annualize=False)
        
        # Annualize if requested
        if annualize:
            expected_return *= trading_days
            volatility *= np.sqrt(trading_days)
        
        # Calculate Sharpe ratio
        sharpe_ratio = (expected_return - self.risk_free_rate) / volatility if volatility > 0 else 0
        
        return sharpe_ratio
    
    def calculate_sortino_ratio(
        self,
        returns: pd.DataFrame,
        weights: Dict[str, float],
        annualize: bool = True,
        trading_days: int = 252,
        target_return: float = 0
    ) -> float:
        """
        Calculate portfolio Sortino ratio.
        
        Args:
            returns: DataFrame of asset returns
            weights: Dictionary mapping assets to weights
            annualize: Whether to annualize the Sortino ratio
            trading_days: Number of trading days in a year
            target_return: Target return for downside deviation calculation
            
        Returns:
            Portfolio Sortino ratio
        """
        # Convert weights to numpy array
        weight_arr = np.array([weights.get(asset, 0) for asset in returns.columns])
        
        # Calculate portfolio returns
        portfolio_returns = returns @ weight_arr
        
        # Calculate expected return
        expected_return = portfolio_returns.mean()
        
        # Calculate downside deviation
        downside_returns = portfolio_returns.copy()
        downside_returns[downside_returns > target_return] = 0
        downside_deviation = np.sqrt((downside_returns ** 2).mean())
        
        # Annualize if requested
        if annualize:
            expected_return *= trading_days
            downside_deviation *= np.sqrt(trading_days)
        
        # Calculate Sortino ratio
        sortino_ratio = (expected_return - self.risk_free_rate) / downside_deviation if downside_deviation > 0 else 0
        
        return sortino_ratio
    
    def calculate_maximum_drawdown(
        self,
        returns: pd.DataFrame,
        weights: Dict[str, float]
    ) -> Tuple[float, pd.DataFrame]:
        """
        Calculate portfolio maximum drawdown.
        
        Args:
            returns: DataFrame of asset returns
            weights: Dictionary mapping assets to weights
            
        Returns:
            Tuple of (maximum drawdown, drawdown series)
        """
        # Convert weights to numpy array
        weight_arr = np.array([weights.get(asset, 0) for asset in returns.columns])
        
        # Calculate portfolio returns
        portfolio_returns = returns @ weight_arr
        
        # Calculate cumulative returns
        cumulative_returns = (1 + portfolio_returns).cumprod()
        
        # Calculate drawdown
        peak = cumulative_returns.cummax()
        drawdown = (cumulative_returns - peak) / peak
        
        # Get maximum drawdown
        max_drawdown = drawdown.min()
        
        # Create drawdown DataFrame
        drawdown_df = pd.DataFrame({
            "cumulative_returns": cumulative_returns,
            "peak": peak,
            "drawdown": drawdown
        })
        
        return max_drawdown, drawdown_df
    
    def calculate_var(
        self,
        returns: pd.DataFrame,
        weights: Dict[str, float],
        confidence_level: float = 0.95,
        method: str = "historical"
    ) -> float:
        """
        Calculate portfolio Value at Risk (VaR).
        
        Args:
            returns: DataFrame of asset returns
            weights: Dictionary mapping assets to weights
            confidence_level: Confidence level for VaR
            method: Method for calculating VaR ('historical', 'gaussian')
            
        Returns:
            Portfolio VaR
        """
        # Convert weights to numpy array
        weight_arr = np.array([weights.get(asset, 0) for asset in returns.columns])
        
        # Calculate portfolio returns
        portfolio_returns = returns @ weight_arr
        
        if method == "historical":
            # Calculate historical VaR
            var = portfolio_returns.quantile(1 - confidence_level)
        elif method == "gaussian":
            # Calculate Gaussian VaR
            mean = portfolio_returns.mean()
            std = portfolio_returns.std()
            var = mean + stats.norm.ppf(1 - confidence_level) * std
        else:
            logger.warning(f"Unknown VaR method: {method}, using historical")
            var = portfolio_returns.quantile(1 - confidence_level)
        
        return var
    
    def calculate_cvar(
        self,
        returns: pd.DataFrame,
        weights: Dict[str, float],
        confidence_level: float = 0.95
    ) -> float:
        """
        Calculate portfolio Conditional Value at Risk (CVaR).
        
        Args:
            returns: DataFrame of asset returns
            weights: Dictionary mapping assets to weights
            confidence_level: Confidence level for CVaR
            
        Returns:
            Portfolio CVaR
        """
        # Convert weights to numpy array
        weight_arr = np.array([weights.get(asset, 0) for asset in returns.columns])
        
        # Calculate portfolio returns
        portfolio_returns = returns @ weight_arr
        
        # Calculate VaR
        var = portfolio_returns.quantile(1 - confidence_level)
        
        # Calculate CVaR
        cvar = portfolio_returns[portfolio_returns <= var].mean()
        
        return cvar
    
    def calculate_all_risk_metrics(
        self,
        returns: pd.DataFrame,
        weights: Dict[str, float],
        annualize: bool = True,
        trading_days: int = 252
    ) -> Dict[str, float]:
        """
        Calculate all risk metrics for a portfolio.
        
        Args:
            returns: DataFrame of asset returns
            weights: Dictionary mapping assets to weights
            annualize: Whether to annualize metrics
            trading_days: Number of trading days in a year
            
        Returns:
            Dictionary with all risk metrics
        """
        # Convert weights to numpy array
        weight_arr = np.array([weights.get(asset, 0) for asset in returns.columns])
        
        # Calculate portfolio returns
        portfolio_returns = returns @ weight_arr
        
        # Calculate expected return
        expected_return = portfolio_returns.mean()
        if annualize:
            expected_return *= trading_days
        
        # Calculate volatility
        volatility = self.calculate_volatility(returns, weights, annualize)
        
        # Calculate Sharpe ratio
        sharpe_ratio = self.calculate_sharpe_ratio(returns, weights, annualize, trading_days)
        
        # Calculate Sortino ratio
        sortino_ratio = self.calculate_sortino_ratio(returns, weights, annualize, trading_days)
        
        # Calculate maximum drawdown
        max_drawdown, _ = self.calculate_maximum_drawdown(returns, weights)
        
        # Calculate VaR and CVaR
        var_95 = self.calculate_var(returns, weights, 0.95, "historical")
        var_99 = self.calculate_var(returns, weights, 0.99, "historical")
        cvar_95 = self.calculate_cvar(returns, weights, 0.95)
        cvar_99 = self.calculate_cvar(returns, weights, 0.99)
        
        # Return all metrics
        metrics = {
            "expected_return": expected_return,
            "volatility": volatility,
            "sharpe_ratio": sharpe_ratio,
            "sortino_ratio": sortino_ratio,
            "max_drawdown": max_drawdown,
            "var_95": var_95,
            "var_99": var_99,
            "cvar_95": cvar_95,
            "cvar_99": cvar_99
        }
        
        return metrics
