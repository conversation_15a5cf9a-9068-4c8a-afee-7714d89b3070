# API Documentation

This document provides detailed information about the Portfolio Optimizer API endpoints.

## Base URL

The base URL for all API endpoints is:

```
http://localhost:8000/api/v1
```

For production, the base URL will be:

```
https://portfolio-optimizer.example.com/api/v1
```

## Authentication

Some endpoints require authentication. To authenticate, include an `Authorization` header with a valid JWT token:

```
Authorization: Bearer <token>
```

To obtain a token, use the `/auth/token` endpoint.

## Error Handling

The API returns standard HTTP status codes to indicate the success or failure of a request:

- `200 OK`: The request was successful
- `201 Created`: The resource was successfully created
- `400 Bad Request`: The request was invalid
- `401 Unauthorized`: Authentication is required or failed
- `403 Forbidden`: The authenticated user does not have permission
- `404 Not Found`: The requested resource was not found
- `422 Unprocessable Entity`: The request was well-formed but could not be processed
- `500 Internal Server Error`: An error occurred on the server

Error responses include a JSON object with the following structure:

```json
{
  "detail": "Error message"
}
```

## Endpoints

### Data Collection

#### Get Stock Data

```
GET /data/stock/{symbol}
```

Get historical stock data for a single symbol.

**Parameters:**

- `symbol` (path, required): The stock symbol (e.g., "AAPL")
- `start_date` (query, optional): Start date in ISO format (YYYY-MM-DD)
- `end_date` (query, optional): End date in ISO format (YYYY-MM-DD)
- `interval` (query, optional): Data interval (1d, 1wk, 1mo)

**Response:**

```json
{
  "symbol": "AAPL",
  "data": [
    {
      "date": "2020-01-02",
      "open": 74.06,
      "high": 75.15,
      "low": 73.8,
      "close": 75.09,
      "adj_close": 74.27,
      "volume": 135480400
    },
    ...
  ]
}
```

#### Get Multiple Stock Data

```
POST /data/stocks
```

Get historical stock data for multiple symbols.

**Request Body:**

```json
{
  "symbols": ["AAPL", "MSFT", "GOOGL"],
  "start_date": "2020-01-01",
  "end_date": "2020-12-31",
  "interval": "1d"
}
```

**Response:**

```json
{
  "AAPL": [
    {
      "date": "2020-01-02",
      "open": 74.06,
      "high": 75.15,
      "low": 73.8,
      "close": 75.09,
      "adj_close": 74.27,
      "volume": 135480400
    },
    ...
  ],
  "MSFT": [
    ...
  ],
  "GOOGL": [
    ...
  ]
}
```

### Technical Analysis

#### Calculate Technical Indicators

```
POST /technical-analysis/indicators
```

Calculate technical indicators for a stock.

**Request Body:**

```json
{
  "symbol": "AAPL",
  "start_date": "2020-01-01",
  "end_date": "2020-12-31",
  "indicators": [
    {
      "name": "sma",
      "params": {
        "window": 20
      }
    },
    {
      "name": "rsi",
      "params": {
        "window": 14
      }
    }
  ]
}
```

**Response:**

```json
{
  "symbol": "AAPL",
  "data": [
    {
      "date": "2020-01-02",
      "close": 75.09,
      "sma_20": null,
      "rsi_14": null
    },
    ...
    {
      "date": "2020-01-30",
      "close": 80.97,
      "sma_20": 78.56,
      "rsi_14": 65.78
    },
    ...
  ]
}
```

### Portfolio Optimization

#### Optimize Portfolio

```
POST /portfolio/optimize
```

Optimize a portfolio based on historical data.

**Request Body:**

```json
{
  "symbols": ["AAPL", "MSFT", "GOOGL", "AMZN", "META"],
  "start_date": "2020-01-01",
  "end_date": "2020-12-31",
  "optimization_criterion": "max_sharpe",
  "risk_free_rate": 0.02,
  "constraints": {
    "min_weight": 0.05,
    "max_weight": 0.3
  }
}
```

**Response:**

```json
{
  "weights": {
    "AAPL": 0.25,
    "MSFT": 0.3,
    "GOOGL": 0.2,
    "AMZN": 0.2,
    "META": 0.05
  },
  "performance": {
    "expected_return": 0.15,
    "volatility": 0.18,
    "sharpe_ratio": 0.72
  }
}
```

#### Backtest Portfolio

```
POST /portfolio/backtest
```

Backtest a portfolio strategy.

**Request Body:**

```json
{
  "symbols": ["AAPL", "MSFT", "GOOGL", "AMZN", "META"],
  "start_date": "2018-01-01",
  "end_date": "2020-12-31",
  "initial_capital": 10000,
  "strategy": "max_sharpe",
  "rebalance_frequency": "quarterly",
  "risk_free_rate": 0.02
}
```

**Response:**

```json
{
  "portfolio_value": [
    {
      "date": "2018-01-01",
      "value": 10000
    },
    ...
    {
      "date": "2020-12-31",
      "value": 18500
    }
  ],
  "metrics": {
    "total_return": 0.85,
    "annualized_return": 0.23,
    "volatility": 0.18,
    "sharpe_ratio": 1.17,
    "max_drawdown": -0.25
  },
  "weights_history": [
    {
      "date": "2018-01-01",
      "AAPL": 0.25,
      "MSFT": 0.3,
      "GOOGL": 0.2,
      "AMZN": 0.2,
      "META": 0.05
    },
    ...
  ]
}
```

### User Management

#### Register User

```
POST /auth/register
```

Register a new user.

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "password": "securepassword",
  "first_name": "John",
  "last_name": "Doe"
}
```

**Response:**

```json
{
  "id": 1,
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe"
}
```

#### Login

```
POST /auth/token
```

Obtain an authentication token.

**Request Body:**

```json
{
  "username": "<EMAIL>",
  "password": "securepassword"
}
```

**Response:**

```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer"
}
```

### User Portfolios

#### Create Portfolio

```
POST /portfolios
```

Create a new portfolio.

**Request Body:**

```json
{
  "name": "My Tech Portfolio",
  "description": "A portfolio of tech stocks",
  "symbols": ["AAPL", "MSFT", "GOOGL", "AMZN", "META"]
}
```

**Response:**

```json
{
  "id": 1,
  "name": "My Tech Portfolio",
  "description": "A portfolio of tech stocks",
  "symbols": ["AAPL", "MSFT", "GOOGL", "AMZN", "META"],
  "created_at": "2023-01-01T12:00:00Z",
  "updated_at": "2023-01-01T12:00:00Z"
}
```

#### Get Portfolios

```
GET /portfolios
```

Get all portfolios for the authenticated user.

**Response:**

```json
[
  {
    "id": 1,
    "name": "My Tech Portfolio",
    "description": "A portfolio of tech stocks",
    "symbols": ["AAPL", "MSFT", "GOOGL", "AMZN", "META"],
    "created_at": "2023-01-01T12:00:00Z",
    "updated_at": "2023-01-01T12:00:00Z"
  },
  ...
]
```

#### Get Portfolio

```
GET /portfolios/{portfolio_id}
```

Get a specific portfolio.

**Parameters:**

- `portfolio_id` (path, required): The portfolio ID

**Response:**

```json
{
  "id": 1,
  "name": "My Tech Portfolio",
  "description": "A portfolio of tech stocks",
  "symbols": ["AAPL", "MSFT", "GOOGL", "AMZN", "META"],
  "created_at": "2023-01-01T12:00:00Z",
  "updated_at": "2023-01-01T12:00:00Z"
}
```

#### Update Portfolio

```
PUT /portfolios/{portfolio_id}
```

Update a portfolio.

**Parameters:**

- `portfolio_id` (path, required): The portfolio ID

**Request Body:**

```json
{
  "name": "My Updated Tech Portfolio",
  "description": "An updated portfolio of tech stocks",
  "symbols": ["AAPL", "MSFT", "GOOGL", "AMZN", "META", "NFLX"]
}
```

**Response:**

```json
{
  "id": 1,
  "name": "My Updated Tech Portfolio",
  "description": "An updated portfolio of tech stocks",
  "symbols": ["AAPL", "MSFT", "GOOGL", "AMZN", "META", "NFLX"],
  "created_at": "2023-01-01T12:00:00Z",
  "updated_at": "2023-01-02T12:00:00Z"
}
```

#### Delete Portfolio

```
DELETE /portfolios/{portfolio_id}
```

Delete a portfolio.

**Parameters:**

- `portfolio_id` (path, required): The portfolio ID

**Response:**

```
204 No Content
```
