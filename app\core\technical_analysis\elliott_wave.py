"""
Elliott Wave pattern recognition implementation.
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union, Tuple

from app.utils.logging import logger
from app.core.technical_analysis.fibonacci import find_swing_points


def identify_elliott_wave_pattern(
    df: pd.DataFrame,
    high_col: str = 'High',
    low_col: str = 'Low',
    close_col: str = 'Close',
    window: int = 5
) -> Dict:
    """
    Identify potential Elliott Wave patterns in price data.

    Args:
        df: DataFrame with price data
        high_col: Name of the high price column
        low_col: Name of the low price column
        close_col: Name of the close price column
        window: Window size for identifying swing points

    Returns:
        Dictionary with Elliott Wave pattern analysis
    """
    try:
        # Find swing points
        swing_highs, swing_lows = find_swing_points(df, high_col, low_col, window)

        # If not enough swing points, return empty result
        if len(swing_highs) < 3 or len(swing_lows) < 3:
            return {
                'pattern': 'Unknown',
                'confidence': 0,
                'wave_count': 0,
                'current_wave': 0,
                'analysis': 'Insufficient data to identify Elliott Wave pattern'
            }

        # Sort swing points by index (oldest first)
        swing_highs.sort(key=lambda x: x['index'])
        swing_lows.sort(key=lambda x: x['index'])

        # Merge swing points into a single list
        swing_points = []
        for high in swing_highs:
            swing_points.append({
                'index': high['index'],
                'price': high['price'],
                'date': high['date'],
                'type': 'high'
            })

        for low in swing_lows:
            swing_points.append({
                'index': low['index'],
                'price': low['price'],
                'date': low['date'],
                'type': 'low'
            })

        # Sort by index
        swing_points.sort(key=lambda x: x['index'])

        # Need at least 9 swing points for a complete 5-wave impulse pattern
        if len(swing_points) < 9:
            return {
                'pattern': 'Developing',
                'confidence': 0.3,
                'wave_count': len(swing_points) // 2,
                'current_wave': len(swing_points) // 2,
                'analysis': 'Pattern is still developing, not enough swing points for a complete Elliott Wave pattern'
            }

        # Analyze potential impulse pattern (5-wave)
        impulse_result = analyze_impulse_pattern(swing_points, df)

        # Analyze potential corrective pattern (3-wave)
        corrective_result = analyze_corrective_pattern(swing_points, df)

        # Determine which pattern has higher confidence
        if impulse_result['confidence'] > corrective_result['confidence']:
            result = impulse_result
        else:
            result = corrective_result

        # Add current price
        result['current_price'] = df.iloc[-1][close_col]

        return result

    except Exception as e:
        logger.error(f"Error identifying Elliott Wave pattern: {str(e)}")
        return {
            'pattern': 'Error',
            'confidence': 0,
            'wave_count': 0,
            'current_wave': 0,
            'analysis': f'Error: {str(e)}'
        }


def analyze_impulse_pattern(
    swing_points: List[Dict],
    df: pd.DataFrame
) -> Dict:
    """
    Analyze potential impulse pattern (5-wave).

    Args:
        swing_points: List of swing points
        df: DataFrame with price data

    Returns:
        Dictionary with impulse pattern analysis
    """
    try:
        # Initialize result
        result = {
            'pattern': 'Impulse',
            'confidence': 0,
            'wave_count': 0,
            'current_wave': 0,
            'analysis': ''
        }

        # Check if we have enough points for at least a partial impulse pattern
        if len(swing_points) < 5:
            result['analysis'] = 'Not enough swing points for an impulse pattern'
            return result

        # Extract potential waves
        # For an impulse pattern:
        # Wave 1: Low to High
        # Wave 2: High to Low (retracement)
        # Wave 3: Low to High (usually the longest)
        # Wave 4: High to Low (retracement)
        # Wave 5: Low to High

        # Find starting point (should be a significant low)
        start_idx = 0
        while start_idx < len(swing_points) and swing_points[start_idx]['type'] != 'low':
            start_idx += 1

        if start_idx >= len(swing_points) - 4:
            result['analysis'] = 'No valid starting point for an impulse pattern'
            return result

        # Extract potential waves
        waves = []
        current_idx = start_idx
        expected_type = 'low'  # Start with a low

        while current_idx < len(swing_points) - 1 and len(waves) < 9:  # Need 9 points for 5 waves
            if swing_points[current_idx]['type'] == expected_type:
                # Found expected point type
                next_idx = current_idx + 1
                while next_idx < len(swing_points) and swing_points[next_idx]['type'] == expected_type:
                    next_idx += 1

                if next_idx < len(swing_points):
                    # Add wave
                    waves.append({
                        'start_idx': current_idx,
                        'end_idx': next_idx,
                        'start': swing_points[current_idx],
                        'end': swing_points[next_idx],
                        'type': 'up' if expected_type == 'low' else 'down'
                    })

                    # Update for next wave
                    current_idx = next_idx
                    expected_type = 'high' if expected_type == 'low' else 'low'
                else:
                    break
            else:
                current_idx += 1

        # Check if we have enough waves
        if len(waves) < 5:
            result['wave_count'] = len(waves)
            result['current_wave'] = len(waves)
            result['analysis'] = f'Developing impulse pattern, currently in wave {len(waves)}'
            result['confidence'] = 0.3
            return result

        # Analyze the first 5 waves (impulse pattern)
        impulse_waves = waves[:5]

        # Calculate wave lengths
        for i, wave in enumerate(impulse_waves):
            wave['length'] = abs(wave['end']['price'] - wave['start']['price'])
            wave['index'] = str(i + 1)  # Store as string for consistent handling

        # Check impulse pattern rules
        # Rule 1: Wave 3 cannot be the shortest of waves 1, 3, and 5
        wave1_length = impulse_waves[0]['length']
        wave3_length = impulse_waves[2]['length']
        wave5_length = impulse_waves[4]['length']

        rule1_valid = wave3_length >= wave1_length or wave3_length >= wave5_length

        # Rule 2: Wave 4 cannot overlap with wave 1
        wave1_high = impulse_waves[0]['end']['price']
        wave4_low = impulse_waves[3]['end']['price']

        rule2_valid = wave4_low > wave1_high

        # Rule 3: Wave 2 cannot retrace more than 100% of wave 1
        wave2_low = impulse_waves[1]['end']['price']
        wave1_low = impulse_waves[0]['start']['price']

        rule3_valid = wave2_low > wave1_low

        # Calculate confidence based on rules
        confidence = 0.5  # Base confidence

        if rule1_valid:
            confidence += 0.1

        if rule2_valid:
            confidence += 0.2

        if rule3_valid:
            confidence += 0.1

        # Check wave 3 extension (common in impulse patterns)
        if wave3_length > wave1_length and wave3_length > wave5_length:
            confidence += 0.1

        # Check if wave 5 is extended
        if wave5_length > wave1_length and wave5_length > wave3_length:
            confidence += 0.05

        # Check if we have a potential wave 5 extension
        if len(waves) > 5 and waves[5]['type'] == 'up':
            confidence += 0.05

        # Update result
        result['confidence'] = min(confidence, 1.0)
        result['wave_count'] = 5

        # Determine current wave
        if len(waves) == 5:
            result['current_wave'] = 5
            result['analysis'] = 'Complete 5-wave impulse pattern identified'
        elif len(waves) > 5:
            # Potential correction after impulse
            result['current_wave'] = 5 + (len(waves) - 5)
            result['analysis'] = f'Completed impulse pattern, now in corrective wave {len(waves) - 5}'

        # Add rule validation
        result['rules'] = {
            'rule1': rule1_valid,
            'rule2': rule2_valid,
            'rule3': rule3_valid
        }

        # Add wave data
        result['waves'] = [{
            'index': wave['index'],
            'type': wave['type'],
            'start_price': wave['start']['price'],
            'end_price': wave['end']['price'],
            'length': wave['length']
        } for wave in impulse_waves]

        return result

    except Exception as e:
        logger.error(f"Error analyzing impulse pattern: {str(e)}")
        return {
            'pattern': 'Impulse',
            'confidence': 0,
            'wave_count': 0,
            'current_wave': 0,
            'analysis': f'Error: {str(e)}'
        }


def analyze_corrective_pattern(
    swing_points: List[Dict],
    df: pd.DataFrame
) -> Dict:
    """
    Analyze potential corrective pattern (3-wave).

    Args:
        swing_points: List of swing points
        df: DataFrame with price data

    Returns:
        Dictionary with corrective pattern analysis
    """
    try:
        # Initialize result
        result = {
            'pattern': 'Corrective',
            'confidence': 0,
            'wave_count': 0,
            'current_wave': 0,
            'analysis': ''
        }

        # Check if we have enough points for at least a partial corrective pattern
        if len(swing_points) < 3:
            result['analysis'] = 'Not enough swing points for a corrective pattern'
            return result

        # Extract potential waves
        # For a corrective pattern:
        # Wave A: High to Low
        # Wave B: Low to High (retracement)
        # Wave C: High to Low

        # Find starting point (should be a significant high)
        start_idx = 0
        while start_idx < len(swing_points) and swing_points[start_idx]['type'] != 'high':
            start_idx += 1

        if start_idx >= len(swing_points) - 2:
            result['analysis'] = 'No valid starting point for a corrective pattern'
            return result

        # Extract potential waves
        waves = []
        current_idx = start_idx
        expected_type = 'high'  # Start with a high

        while current_idx < len(swing_points) - 1 and len(waves) < 5:  # Need 5 points for 3 waves
            if swing_points[current_idx]['type'] == expected_type:
                # Found expected point type
                next_idx = current_idx + 1
                while next_idx < len(swing_points) and swing_points[next_idx]['type'] == expected_type:
                    next_idx += 1

                if next_idx < len(swing_points):
                    # Add wave
                    waves.append({
                        'start_idx': current_idx,
                        'end_idx': next_idx,
                        'start': swing_points[current_idx],
                        'end': swing_points[next_idx],
                        'type': 'down' if expected_type == 'high' else 'up'
                    })

                    # Update for next wave
                    current_idx = next_idx
                    expected_type = 'low' if expected_type == 'high' else 'high'
                else:
                    break
            else:
                current_idx += 1

        # Check if we have enough waves
        if len(waves) < 3:
            result['wave_count'] = len(waves)
            result['current_wave'] = len(waves)
            result['analysis'] = f'Developing corrective pattern, currently in wave {["A", "B", "C"][len(waves) - 1]}'
            result['confidence'] = 0.3
            return result

        # Analyze the first 3 waves (corrective pattern)
        corrective_waves = waves[:3]

        # Calculate wave lengths
        for i, wave in enumerate(corrective_waves):
            wave['length'] = abs(wave['end']['price'] - wave['start']['price'])
            wave['index'] = ["A", "B", "C"][i]  # Already a string, so no need to convert

        # Check corrective pattern rules
        # Rule 1: Wave B should not retrace more than 100% of wave A
        wave_a_start = corrective_waves[0]['start']['price']
        wave_b_end = corrective_waves[1]['end']['price']

        rule1_valid = wave_b_end <= wave_a_start

        # Rule 2: Wave C should be at least 61.8% of wave A
        wave_a_length = corrective_waves[0]['length']
        wave_c_length = corrective_waves[2]['length']

        rule2_valid = wave_c_length >= 0.618 * wave_a_length

        # Calculate confidence based on rules
        confidence = 0.5  # Base confidence

        if rule1_valid:
            confidence += 0.2

        if rule2_valid:
            confidence += 0.2

        # Check common wave relationships
        # Wave C is often 1.618 times wave A
        if 1.5 < (wave_c_length / wave_a_length) < 1.8:
            confidence += 0.1

        # Wave B often retraces 50% or 61.8% of wave A
        wave_a_retracement = (wave_b_end - corrective_waves[0]['end']['price']) / wave_a_length
        if 0.45 < wave_a_retracement < 0.65:
            confidence += 0.1

        # Update result
        result['confidence'] = min(confidence, 1.0)
        result['wave_count'] = 3

        # Determine current wave
        if len(waves) == 3:
            result['current_wave'] = 3
            result['analysis'] = 'Complete 3-wave corrective pattern identified'
        elif len(waves) > 3:
            # Potential new pattern after correction
            result['current_wave'] = 3
            result['analysis'] = 'Completed corrective pattern, potentially starting a new pattern'

        # Add rule validation
        result['rules'] = {
            'rule1': rule1_valid,
            'rule2': rule2_valid
        }

        # Add wave data
        result['waves'] = [{
            'index': wave['index'],
            'type': wave['type'],
            'start_price': wave['start']['price'],
            'end_price': wave['end']['price'],
            'length': wave['length']
        } for wave in corrective_waves]

        return result

    except Exception as e:
        logger.error(f"Error analyzing corrective pattern: {str(e)}")
        return {
            'pattern': 'Corrective',
            'confidence': 0,
            'wave_count': 0,
            'current_wave': 0,
            'analysis': f'Error: {str(e)}'
        }


def get_elliott_wave_projection(
    df: pd.DataFrame,
    high_col: str = 'High',
    low_col: str = 'Low',
    close_col: str = 'Close',
    window: int = 5
) -> Dict:
    """
    Get Elliott Wave projection based on identified pattern.

    Args:
        df: DataFrame with price data
        high_col: Name of the high price column
        low_col: Name of the low price column
        close_col: Name of the close price column
        window: Window size for identifying swing points

    Returns:
        Dictionary with Elliott Wave projection
    """
    try:
        # Identify Elliott Wave pattern
        pattern_result = identify_elliott_wave_pattern(df, high_col, low_col, close_col, window)

        # If pattern confidence is too low, return basic result
        if pattern_result['confidence'] < 0.5:
            return {
                'pattern': pattern_result['pattern'],
                'confidence': pattern_result['confidence'],
                'current_wave': pattern_result['current_wave'],
                'projection': 'Insufficient confidence to make a projection',
                'target_price': None,
                'analysis': pattern_result['analysis']
            }

        # Get current price
        current_price = df.iloc[-1][close_col]

        # Make projection based on pattern type and current wave
        if pattern_result['pattern'] == 'Impulse':
            projection = project_impulse_pattern(pattern_result, current_price)
        else:  # Corrective
            projection = project_corrective_pattern(pattern_result, current_price)

        # Combine results
        result = {
            'pattern': pattern_result['pattern'],
            'confidence': pattern_result['confidence'],
            'current_wave': pattern_result['current_wave'],
            'waves': pattern_result.get('waves', []),
            'projection': projection['projection'],
            'target_price': projection['target_price'],
            'analysis': f"{pattern_result['analysis']}. {projection['analysis']}"
        }

        return result

    except Exception as e:
        logger.error(f"Error getting Elliott Wave projection: {str(e)}")
        return {
            'pattern': 'Error',
            'confidence': 0,
            'current_wave': 0,
            'projection': 'Error',
            'target_price': None,
            'analysis': f'Error: {str(e)}'
        }


def project_impulse_pattern(
    pattern_result: Dict,
    current_price: float
) -> Dict:
    """
    Project price targets based on impulse pattern.

    Args:
        pattern_result: Elliott Wave pattern analysis result
        current_price: Current price

    Returns:
        Dictionary with projection details
    """
    try:
        # Initialize result
        result = {
            'projection': 'Unknown',
            'target_price': None,
            'analysis': ''
        }

        # Get wave data
        waves = pattern_result.get('waves', [])
        current_wave = pattern_result['current_wave']

        # If no wave data, return basic result
        if not waves:
            result['analysis'] = 'No wave data available for projection'
            return result

        # Make projection based on current wave (ensure it's an integer for comparison)
        try:
            # First, ensure current_wave is a number or string that can be converted to a number
            if isinstance(current_wave, str):
                # Try to convert string to int, handling non-numeric strings
                try:
                    current_wave_int = int(current_wave)
                except ValueError:
                    # If it's a non-numeric string like 'A', 'B', 'C', convert to corresponding number
                    if current_wave.lower() == 'a':
                        current_wave_int = 1
                    elif current_wave.lower() == 'b':
                        current_wave_int = 2
                    elif current_wave.lower() == 'c':
                        current_wave_int = 3
                    else:
                        # Default to 0 if we can't determine the wave
                        logger.warning(f"Unknown wave identifier: {current_wave}")
                        current_wave_int = 0
            else:
                # If it's already a number, use it directly
                current_wave_int = int(current_wave)
        except Exception as e:
            logger.warning(f"Error converting current_wave to int: {str(e)}")
            current_wave_int = 0  # Default to 0 if conversion fails

        if current_wave_int == 1:
            # In wave 1, project potential wave 2 target
            wave1_start = waves[0]['start_price']
            wave1_end = waves[0]['end_price']
            wave1_length = waves[0]['length']

            # Wave 2 typically retraces 50-61.8% of wave 1
            target_50 = wave1_end - (0.5 * wave1_length)
            target_618 = wave1_end - (0.618 * wave1_length)

            result['projection'] = 'Wave 2 target'
            result['target_price'] = (target_50 + target_618) / 2
            result['analysis'] = f'Wave 1 complete, expecting wave 2 to retrace 50-61.8% of wave 1. Target zone: {target_618:.2f} to {target_50:.2f}'

        elif current_wave_int == 2:
            # In wave 2, project potential wave 3 target
            wave1_length = waves[0]['length']
            wave2_end = waves[1]['end_price']

            # Wave 3 is typically 1.618 - 2.618 times wave 1
            target_1618 = wave2_end + (1.618 * wave1_length)
            target_2618 = wave2_end + (2.618 * wave1_length)

            result['projection'] = 'Wave 3 target'
            result['target_price'] = (target_1618 + target_2618) / 2
            result['analysis'] = f'Wave 2 complete, expecting wave 3 to extend 1.618-2.618 times wave 1. Target zone: {target_1618:.2f} to {target_2618:.2f}'

        elif current_wave_int == 3:
            # In wave 3, project potential wave 4 target
            wave1_end = waves[0]['end_price']
            wave3_start = waves[2]['start_price']
            wave3_end = waves[2]['end_price']
            wave3_length = waves[2]['length']

            # Wave 4 typically retraces 38.2% of wave 3, but should not overlap with wave 1
            target_382 = wave3_end - (0.382 * wave3_length)

            # Ensure wave 4 target doesn't overlap with wave 1
            if target_382 < wave1_end:
                target_382 = (wave1_end + wave3_end) / 2

            result['projection'] = 'Wave 4 target'
            result['target_price'] = target_382
            result['analysis'] = f'Wave 3 complete, expecting wave 4 to retrace to around {target_382:.2f} without overlapping wave 1'

        elif current_wave_int == 4:
            # In wave 4, project potential wave 5 target
            wave1_length = waves[0]['length']
            wave3_length = waves[2]['length']
            wave4_end = waves[3]['end_price']

            # Wave 5 is typically 0.618 - 1.0 times wave 1, or equal to wave 1
            target_618 = wave4_end + (0.618 * wave1_length)
            target_100 = wave4_end + wave1_length

            # If wave 3 was extended, wave 5 is often shorter
            if wave3_length > 1.5 * wave1_length:
                result['target_price'] = target_618
                result['analysis'] = f'Wave 4 complete, expecting wave 5 to reach around {target_618:.2f} (since wave 3 was extended)'
            else:
                result['target_price'] = target_100
                result['analysis'] = f'Wave 4 complete, expecting wave 5 to reach around {target_100:.2f}'

            result['projection'] = 'Wave 5 target'

        elif current_wave_int == 5:
            # In wave 5, project potential reversal
            result['projection'] = 'Reversal'
            result['target_price'] = waves[0]['start_price']  # Back to the start of wave 1
            result['analysis'] = 'Wave 5 complete, expecting a reversal or corrective pattern to begin'

        else:
            # After wave 5, project corrective pattern
            result['projection'] = 'Corrective pattern'
            result['target_price'] = waves[2]['end_price']  # Back to the end of wave 3
            result['analysis'] = 'Impulse pattern complete, expecting a corrective pattern (A-B-C) to begin'

        return result

    except Exception as e:
        logger.error(f"Error projecting impulse pattern: {str(e)}")
        return {
            'projection': 'Error',
            'target_price': None,
            'analysis': f'Error: {str(e)}'
        }


def project_corrective_pattern(
    pattern_result: Dict,
    current_price: float
) -> Dict:
    """
    Project price targets based on corrective pattern.

    Args:
        pattern_result: Elliott Wave pattern analysis result
        current_price: Current price

    Returns:
        Dictionary with projection details
    """
    try:
        # Initialize result
        result = {
            'projection': 'Unknown',
            'target_price': None,
            'analysis': ''
        }

        # Get wave data
        waves = pattern_result.get('waves', [])
        current_wave = pattern_result['current_wave']

        # If no wave data, return basic result
        if not waves:
            result['analysis'] = 'No wave data available for projection'
            return result

        # Make projection based on current wave (ensure it's an integer for comparison)
        try:
            # First, ensure current_wave is a number or string that can be converted to a number
            if isinstance(current_wave, str):
                # Try to convert string to int, handling non-numeric strings
                try:
                    current_wave_int = int(current_wave)
                except ValueError:
                    # If it's a non-numeric string like 'A', 'B', 'C', convert to corresponding number
                    if current_wave.lower() == 'a':
                        current_wave_int = 1
                    elif current_wave.lower() == 'b':
                        current_wave_int = 2
                    elif current_wave.lower() == 'c':
                        current_wave_int = 3
                    else:
                        # Default to 0 if we can't determine the wave
                        logger.warning(f"Unknown wave identifier: {current_wave}")
                        current_wave_int = 0
            else:
                # If it's already a number, use it directly
                current_wave_int = int(current_wave)
        except Exception as e:
            logger.warning(f"Error converting current_wave to int: {str(e)}")
            current_wave_int = 0  # Default to 0 if conversion fails

        if current_wave_int == 1:  # Wave A
            # In wave A, project potential wave B target
            wave_a_start = waves[0]['start_price']
            wave_a_end = waves[0]['end_price']
            wave_a_length = waves[0]['length']

            # Wave B typically retraces 50-75% of wave A
            target_50 = wave_a_end + (0.5 * wave_a_length)
            target_75 = wave_a_end + (0.75 * wave_a_length)

            result['projection'] = 'Wave B target'
            result['target_price'] = (target_50 + target_75) / 2
            result['analysis'] = f'Wave A complete, expecting wave B to retrace 50-75% of wave A. Target zone: {target_50:.2f} to {target_75:.2f}'

        elif current_wave_int == 2:  # Wave B
            # In wave B, project potential wave C target
            wave_a_length = waves[0]['length']
            wave_b_end = waves[1]['end_price']

            # Wave C is typically 1.0 - 1.618 times wave A
            target_100 = wave_b_end - wave_a_length
            target_1618 = wave_b_end - (1.618 * wave_a_length)

            result['projection'] = 'Wave C target'
            result['target_price'] = (target_100 + target_1618) / 2
            result['analysis'] = f'Wave B complete, expecting wave C to extend 1.0-1.618 times wave A. Target zone: {target_100:.2f} to {target_1618:.2f}'

        elif current_wave_int == 3:  # Wave C
            # After wave C, project potential new impulse pattern
            result['projection'] = 'New impulse pattern'
            result['target_price'] = waves[0]['start_price']  # Back to the start of wave A (or higher)
            result['analysis'] = 'Corrective pattern complete, expecting a new impulse pattern to begin'

        else:
            # After corrective pattern, project new trend
            result['projection'] = 'New trend'
            result['target_price'] = None
            result['analysis'] = 'Corrective pattern complete, watching for signs of a new trend direction'

        return result

    except Exception as e:
        logger.error(f"Error projecting corrective pattern: {str(e)}")
        return {
            'projection': 'Error',
            'target_price': None,
            'analysis': f'Error: {str(e)}'
        }
